# GitHub Secrets 設置指南

## 🔑 需要設置的 Secrets

請前往 GitHub 倉庫的 Settings > Secrets and variables > Actions，添加以下 secrets：

### 1. TURBO_TOKEN
```
i1WqvWBr7sg2WKJTngWivX7L
```

### 2. TURBO_TEAM
```
team_yruTFhfn1Kutz61PLhGnCoMm
```

## 🧪 本地測試結果

✅ **遠程緩存驗證成功**：

### 第一次構建 (cache miss)：
```
• Remote caching enabled
┌─ @novelwebsite/web#build > cache miss, executing 18f423a8cd6eee33
Time: 7.005s
```

### 第二次構建 (cache hit)：
```
• Remote caching enabled
┌─ @novelwebsite/web#build > cache hit, replaying logs e8d03c77ba79bf80
Time: 265ms >>> FULL TURBO
```

**性能提升**: 7.005s → 265ms (96% 提升！)

## 🎯 CI 驗證目標

在 GitHub Actions 的 `quality-and-build` job 中，應該看到：

```
✓ @novelwebsite/web:build: cache hit (remote), replaying logs
✓ @novelwebsite/web:lint: cache hit (remote), replaying logs
✓ @novelwebsite/web:test: cache hit (remote), replaying logs
```

## 📋 測試 PR 信息

- **分支**: `test/turborepo-remote-cache`
- **變更**: 在 `apps/web/src/App.tsx` 添加測試註釋
- **目的**: 驗證 CI 中的遠程緩存功能
- **預期**: 觸發 `app_web_changed: 'true'`，運行 `quality-and-build` job

## ⚠️ 故障排除

如果 CI 中遠程緩存不工作：

1. **檢查 Secrets**: 確認 TURBO_TOKEN 和 TURBO_TEAM 已正確設置
2. **檢查網路**: 確認 GitHub Actions runner 可以訪問 Vercel
3. **檢查配置**: 確認 `turbo.json` 中的 `remoteCache` 配置正確
4. **檢查權限**: 確認 token 有足夠的權限

## 🔍 監控指標

成功的指標：
- CI 執行時間顯著減少
- 日誌中顯示 "cache hit (remote)"
- 構建步驟快速完成

失敗的指標：
- 每次都顯示 "cache miss"
- 構建時間沒有改善
- 出現網路或權限錯誤
