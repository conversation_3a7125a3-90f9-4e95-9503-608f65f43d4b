{"$schema": "https://raw.githubusercontent.com/modelcontextprotocol/spec/main/schema/mcp_config_schema.json", "_instructions": {"description": "MCP (Model Context Protocol) Configuration Template for NovelWebsite Project", "setup_steps": ["1. Copy this file to .cursor/mcp.json", "2. Replace ALL placeholder values (YOUR_*, ${YOUR_*}) with actual credentials", "3. Update environment variables to match your project setup", "4. Test the connection with your MCP client", "5. Ensure .cursor/mcp.json is in .gitignore to prevent credential leaks"], "security_notes": ["Never commit .cursor/mcp.json with actual credentials", "Use environment variables for sensitive data", "Regularly rotate API keys and tokens", "Review access permissions for each MCP server"]}, "mcpServers": {"taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "enabled": true, "env": {"ANTHROPIC_API_KEY": "${ANTHROPIC_API_KEY}"}, "description": "Task Master AI for project management and task breakdown"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "enabled": true, "description": "Context management and memory for AI conversations"}, "devcontext": {"command": "npx", "args": ["-y", "devcontext@latest"], "enabled": true, "env": {"TURSO_DATABASE_URL": "${TURSO_DATABASE_URL}", "TURSO_AUTH_TOKEN": "${TURSO_AUTH_TOKEN}"}, "description": "Development context and database management"}, "playwright": {"command": "npx", "args": ["-y", "@execute-command/mcp-playwright"], "enabled": true, "description": "Web automation and testing with Playwright"}, "desktop-commander": {"command": "npx", "args": ["-y", "desktop-commander-mcp@latest"], "enabled": false, "description": "Desktop automation and system control"}, "sequential-thinking": {"command": "uvx", "args": ["server-sequential-thinking"], "enabled": true, "description": "Sequential reasoning and problem-solving"}, "vscode-mcp": {"command": "node", "args": ["${VSCODE_MCP_PATH}"], "enabled": false, "env": {}, "description": "VSCode integration for code editing"}, "exa-search": {"command": "npx", "args": ["-y", "exa-mcp@latest"], "enabled": true, "description": "Exa search engine integration for web research"}, "github": {"command": "uvx", "args": ["mcp-server-github"], "description": "GitHub integration for repository management"}}}