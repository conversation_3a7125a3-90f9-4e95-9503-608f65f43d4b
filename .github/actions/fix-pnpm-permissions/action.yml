name: 'Fix pnpm Permissions'
description: 'Fix pnpm-related directory permissions to prevent cleanup errors'
author: 'NovelWebsite Team'

inputs:
  additional-paths:
    description: 'Additional paths to fix permissions for (comma-separated)'
    required: false
    default: ''

runs:
  using: 'composite'
  steps:
    - name: Fix pnpm permissions before cleanup
      shell: bash
      if: always() # 無論成功或失敗都執行
      run: |
        echo "🔧 Fixing pnpm-related directory permissions..."

        # 標準 pnpm 路徑列表
        PNPM_PATHS=(
          "/home/<USER>/setup-pnpm"
          "/home/<USER>/.pnpm"
          "/home/<USER>/.local/share/pnpm"
          "$HOME/.pnpm"
        )

        # 添加額外路徑 (如果提供)
        if [ -n "${{ inputs.additional-paths }}" ]; then
          IFS=',' read -ra EXTRA_PATHS <<< "${{ inputs.additional-paths }}"
          PNPM_PATHS+=("${EXTRA_PATHS[@]}")
        fi

        # 修復每個路徑的權限
        for pnpm_path in "${PNPM_PATHS[@]}"; do
          if [ -d "$pnpm_path" ]; then
            echo "📂 Found pnpm directory: $pnpm_path"
            sudo chown -R $USER:$USER "$pnpm_path" 2>/dev/null || true
            sudo chmod -R u+rwX "$pnpm_path" 2>/dev/null || true
            echo "✅ Permissions fixed for $pnpm_path"
          fi
        done

        echo "🧹 pnpm permission cleanup completed"

branding:
  icon: 'shield'
  color: 'blue'
