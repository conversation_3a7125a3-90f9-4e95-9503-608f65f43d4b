name: 'Smart CI Cleanup'
description: '統一的 CI 磁碟空間清理 Action，替代各 job 中重複的清理步驟'
author: 'NovelWebsite DevOps Team'

inputs:
  cleanup_level:
    description: '清理級別：basic, standard, aggressive'
    required: false
    default: 'standard'
  preserve_cache:
    description: '是否保留重要快取 (pnpm store, pip cache)'
    required: false
    default: 'true'
  max_cleanup_time:
    description: '最大清理時間 (秒)'
    required: false
    default: '120'

outputs:
  space_freed:
    description: '釋放的磁碟空間 (MB)'
    value: ${{ steps.cleanup.outputs.space_freed }}
  cleanup_duration:
    description: '清理耗時 (秒)'
    value: ${{ steps.cleanup.outputs.duration }}
  cleanup_status:
    description: '清理狀態：success, partial, failed'
    value: ${{ steps.cleanup.outputs.status }}

runs:
  using: 'composite'
  steps:
    - name: Smart CI Cleanup
      id: cleanup
      shell: bash
      run: |
        set -e

        # 記錄開始時間和磁碟使用量
        START_TIME=$(date +%s)
        BEFORE_SPACE=$(df / | tail -1 | awk '{print $4}')

        echo "🧹 開始智能 CI 清理程序..."
        echo "清理級別: ${{ inputs.cleanup_level }}"
        echo "保留快取: ${{ inputs.preserve_cache }}"
        echo "最大清理時間: ${{ inputs.max_cleanup_time }} 秒"
        echo "=========================================="

        # 顯示清理前狀況
        echo "📊 清理前磁碟使用狀況："
        df -h | head -2
        echo ""

        # 設置清理超時
        timeout_cmd=""
        if command -v timeout &> /dev/null; then
          timeout_cmd="timeout ${{ inputs.max_cleanup_time }}"
        fi

        # 執行 Docker 清理
        echo "🐳 清理 Docker 資源..."

        # 清理停止的容器
        $timeout_cmd docker container prune -f 2>/dev/null || true

        # 清理未使用的網路
        $timeout_cmd docker network prune -f 2>/dev/null || true

        # 根據清理級別決定映像清理策略
        case "${{ inputs.cleanup_level }}" in
          "basic")
            echo "🖼️ 基礎清理：僅清理 dangling 映像"
            $timeout_cmd docker image prune -f 2>/dev/null || true
            ;;
          "standard")
            echo "🖼️ 標準清理：清理未使用的映像"
            $timeout_cmd docker image prune -a -f 2>/dev/null || true
            $timeout_cmd docker builder prune -f 2>/dev/null || true
            ;;
          "aggressive")
            echo "🖼️ 積極清理：清理所有未使用資源 (保留 BuildKit 快取)"
            # 🚀 關鍵修復：保留 BuildKit 快取卷，避免破壞跨映像快取共享
            $timeout_cmd docker image prune -af 2>/dev/null || true
            $timeout_cmd docker builder prune -f 2>/dev/null || true
            # 注意：不使用 --volumes 選項，保留 pnpm-store-shared 等重要快取卷
            $timeout_cmd docker container prune -f 2>/dev/null || true
            $timeout_cmd docker network prune -f 2>/dev/null || true
            echo "💡 已保留 BuildKit 快取卷以維持跨映像快取共享效率"
            ;;
        esac

        echo "✅ Docker 清理完成"

        # 執行 Git 清理
        echo "📚 清理 Git 資源..."

        # Git 垃圾回收 (安全的方式清理 pack 檔案)
        $timeout_cmd git gc --aggressive --prune=now 2>/dev/null || true

        echo "✅ Git 清理完成"

        # 執行套件管理器清理
        echo "📦 清理套件管理器快取..."

        if [[ "${{ inputs.preserve_cache }}" == "false" ]]; then
          # 清理 pnpm 快取
          if command -v pnpm &> /dev/null; then
            echo "清理 pnpm 快取..."
            pnpm store prune 2>/dev/null || true
          fi

          # 清理 npm 快取
          if command -v npm &> /dev/null; then
            echo "清理 npm 快取..."
            npm cache clean --force 2>/dev/null || true
          fi

          # 清理 pip 快取
          if command -v pip &> /dev/null; then
            echo "清理 pip 快取..."
            pip cache purge 2>/dev/null || true
          fi
        else
          echo "⏭️ 跳過套件管理器快取清理 (preserve_cache=true)"
        fi

        echo "✅ 套件管理器清理完成"

        # 執行 Python 清理
        echo "🐍 清理 Python 快取..."

        # 清理 __pycache__ 目錄
        find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
        find . -name "*.pyc" -delete 2>/dev/null || true

        echo "✅ Python 快取清理完成"

        # 執行臨時檔案清理
        echo "🗑️ 清理臨時檔案..."

        # 清理系統臨時檔案 (僅清理超過 60 分鐘的檔案，避免刪除正在使用的檔案)
        sudo find /tmp /var/tmp -mindepth 1 -mmin +60 -exec rm -rf {} + 2>/dev/null || true

        # 清理用戶快取
        rm -rf ~/.cache/tmp-* 2>/dev/null || true

        # 清理 GitHub Actions 相關臨時檔案
        rm -rf ~/.local/share/pnpm/store/v3/tmp-* 2>/dev/null || true
        rm -rf .lighthouseci/temp-* 2>/dev/null || true

        echo "✅ 臨時檔案清理完成"

        # 設置清理狀態
        cleanup_status="success"

        # 計算清理結果
        END_TIME=$(date +%s)
        AFTER_SPACE=$(df / | tail -1 | awk '{print $4}')
        DURATION=$((END_TIME - START_TIME))
        SPACE_FREED=$(((AFTER_SPACE - BEFORE_SPACE) / 1024))  # 轉換為 MB

        # 顯示清理後狀況
        echo ""
        echo "🎉 清理完成！"
        echo "=========================================="
        echo "📊 清理後磁碟使用狀況："
        df -h | head -2
        echo ""
        echo "📈 清理統計："
        echo "- 耗時: ${DURATION} 秒"
        echo "- 釋放空間: ${SPACE_FREED} MB"
        echo "- 清理狀態: ${cleanup_status}"

        # 設置輸出
        echo "space_freed=${SPACE_FREED}" >> $GITHUB_OUTPUT
        echo "duration=${DURATION}" >> $GITHUB_OUTPUT
        echo "status=${cleanup_status}" >> $GITHUB_OUTPUT

branding:
  icon: 'trash-2'
  color: 'green'
