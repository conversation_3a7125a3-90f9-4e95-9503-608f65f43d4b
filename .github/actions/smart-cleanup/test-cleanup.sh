#!/bin/bash
# 測試 smart-cleanup action 的功能
# 🎯 目標：驗證清理邏輯的正確性和效果

set -e

echo "🧪 開始測試 Smart Cleanup Action..."
echo "=========================================="

# 創建測試環境
setup_test_environment() {
    echo "🏗️ 設置測試環境..."

    # 創建測試目錄
    mkdir -p test-cleanup-env
    cd test-cleanup-env

    # 創建一些測試檔案
    echo "📁 創建測試檔案..."

    # Python 快取檔案
    mkdir -p test_module/__pycache__
    echo "test" > test_module/__pycache__/test.pyc
    echo "test" > test.pyc

    # 臨時檔案
    mkdir -p temp-test
    echo "temp" > temp-test/temp.txt

    # Git pack 檔案 (模擬)
    mkdir -p .git/objects/pack
    dd if=/dev/zero of=.git/objects/pack/test.pack bs=1M count=60 2>/dev/null || true

    echo "✅ 測試環境設置完成"
}

# 測試清理功能
test_cleanup_functions() {
    echo "🧪 測試清理功能..."

    # 記錄清理前的檔案數量
    BEFORE_PYC=$(find . -name "*.pyc" | wc -l)
    BEFORE_PYCACHE=$(find . -type d -name "__pycache__" | wc -l)
    BEFORE_PACK=$(find . -name "*.pack" -size +50M | wc -l)

    echo "清理前統計："
    echo "- .pyc 檔案: $BEFORE_PYC"
    echo "- __pycache__ 目錄: $BEFORE_PYCACHE"
    echo "- 大型 pack 檔案: $BEFORE_PACK"

    # 執行清理邏輯 (模擬 action 中的清理步驟)
    echo "🧹 執行清理..."

    # Python 快取清理
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true

    # Git pack 檔案清理 (測試環境中移除 mtime 條件)
    find . -name "*.pack" -size +50M -delete 2>/dev/null || true

    # 臨時檔案清理
    rm -rf temp-* 2>/dev/null || true

    # 記錄清理後的檔案數量
    AFTER_PYC=$(find . -name "*.pyc" | wc -l)
    AFTER_PYCACHE=$(find . -type d -name "__pycache__" | wc -l)
    AFTER_PACK=$(find . -name "*.pack" -size +50M | wc -l)

    echo "清理後統計："
    echo "- .pyc 檔案: $AFTER_PYC"
    echo "- __pycache__ 目錄: $AFTER_PYCACHE"
    echo "- 大型 pack 檔案: $AFTER_PACK"

    # 驗證清理效果
    if [[ $AFTER_PYC -eq 0 && $AFTER_PYCACHE -eq 0 && $AFTER_PACK -eq 0 ]]; then
        echo "✅ 清理功能測試通過"
        return 0
    else
        echo "❌ 清理功能測試失敗"
        return 1
    fi
}

# 測試不同清理級別
test_cleanup_levels() {
    echo "🧪 測試不同清理級別..."

    # 創建測試 Docker 映像來驗證清理效果
    echo "創建測試 Docker 映像..."
    docker build -t test-cleanup-basic:latest -f - . <<EOF
FROM alpine:latest
RUN echo "test basic image"
EOF

    docker build -t test-cleanup-standard:latest -f - . <<EOF
FROM alpine:latest
RUN echo "test standard image"
EOF

    # 測試基礎清理級別
    echo "測試基礎清理級別..."
    CLEANUP_LEVEL="basic"
    echo "清理級別: $CLEANUP_LEVEL"

    # 模擬基礎清理邏輯
    if command -v docker &> /dev/null; then
        echo "執行基礎清理：僅清理 dangling 映像"
        docker image prune -f 2>/dev/null || true
        echo "✅ 基礎清理測試完成"
    fi

    # 測試標準清理級別
    echo "測試標準清理級別..."
    CLEANUP_LEVEL="standard"
    echo "清理級別: $CLEANUP_LEVEL"

    # 模擬標準清理邏輯
    if command -v docker &> /dev/null; then
        echo "執行標準清理：清理未使用的映像"
        docker image prune -a -f 2>/dev/null || true
        docker builder prune -f 2>/dev/null || true
        echo "✅ 標準清理測試完成"
    fi

    # 測試積極清理級別
    echo "測試積極清理級別..."
    CLEANUP_LEVEL="aggressive"
    echo "清理級別: $CLEANUP_LEVEL"

    # 模擬積極清理邏輯
    if command -v docker &> /dev/null; then
        echo "執行積極清理：清理所有未使用資源 (保留 BuildKit 快取)"
        docker image prune -af 2>/dev/null || true
        docker builder prune -f 2>/dev/null || true
        docker container prune -f 2>/dev/null || true
        docker network prune -f 2>/dev/null || true
        echo "✅ 積極清理測試完成"
    fi

    echo "✅ 清理級別測試完成"
}

# 測試快取保留功能
test_cache_preservation() {
    echo "🧪 測試快取保留功能..."

    # 創建模擬快取目錄和檔案
    mkdir -p ~/.local/share/pnpm/store/v3
    mkdir -p ~/.cache/pip
    echo "test-pnpm-cache" > ~/.local/share/pnpm/store/v3/test-cache
    echo "test-pip-cache" > ~/.cache/pip/test-cache

    # 測試 1: 保留快取 (preserve_cache=true)
    echo "測試 1: 保留快取模式 (preserve_cache=true)"
    PRESERVE_CACHE="true"
    echo "快取保留設定: $PRESERVE_CACHE"

    if [[ "$PRESERVE_CACHE" == "true" ]]; then
        echo "⏭️ 跳過套件管理器快取清理 (preserve_cache=true)"
        # 不執行清理命令
    else
        # 模擬清理命令
        if command -v pnpm &> /dev/null; then
            echo "清理 pnpm 快取..."
            # pnpm store prune 2>/dev/null || true
        fi
    fi

    # 驗證快取檔案是否存在
    if [[ -f ~/.local/share/pnpm/store/v3/test-cache ]]; then
        echo "✅ pnpm 快取保留功能正常"
    else
        echo "❌ pnpm 快取保留功能異常"
    fi

    # 測試 2: 不保留快取 (preserve_cache=false)
    echo "測試 2: 不保留快取模式 (preserve_cache=false)"
    PRESERVE_CACHE="false"
    echo "快取保留設定: $PRESERVE_CACHE"

    if [[ "$PRESERVE_CACHE" == "false" ]]; then
        echo "🧹 執行套件管理器快取清理..."
        # 模擬實際的清理邏輯
        if [[ -f ~/.cache/pip/test-cache ]]; then
            rm -f ~/.cache/pip/test-cache
            echo "已清理 pip 快取"
        fi
    fi

    # 驗證清理效果
    if [[ ! -f ~/.cache/pip/test-cache ]]; then
        echo "✅ pip 快取清理功能正常"
    else
        echo "❌ pip 快取清理功能異常"
    fi

    # 清理測試快取
    rm -f ~/.local/share/pnpm/store/v3/test-cache
    rm -rf ~/.cache/pip/test-cache
    echo "✅ 快取保留功能測試完成"
}

# 測試超時機制
test_timeout_mechanism() {
    echo "🧪 測試超時機制..."

    # 檢查 timeout 命令是否可用
    if ! command -v timeout &> /dev/null; then
        echo "⚠️ timeout 命令不可用，跳過超時機制測試"
        return 0
    fi

    MAX_CLEANUP_TIME=3
    echo "最大清理時間: $MAX_CLEANUP_TIME 秒"

    # 測試 1: 正常情況 (命令在時限內完成)
    echo "測試 1: 正常情況 (命令在時限內完成)"
    START_TIME=$(date +%s)
    if timeout $MAX_CLEANUP_TIME sleep 1; then
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        echo "✅ 正常情況測試通過 (耗時: ${DURATION}s)"
    else
        echo "❌ 正常情況測試失敗"
    fi

    # 測試 2: 超時情況 (命令超過時限)
    echo "測試 2: 超時情況 (命令超過時限)"
    START_TIME=$(date +%s)
    if timeout $MAX_CLEANUP_TIME sleep 5; then
        echo "❌ 超時測試失敗：命令應該被終止"
    else
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        if [[ $DURATION -le $((MAX_CLEANUP_TIME + 1)) ]]; then
            echo "✅ 超時機制測試通過 (命令在 ${DURATION}s 後被終止)"
        else
            echo "⚠️ 超時機制測試警告 (耗時: ${DURATION}s，可能超過預期)"
        fi
    fi

    echo "✅ 超時機制測試完成"
}

# 清理測試環境
cleanup_test_environment() {
    echo "🧹 清理測試環境..."
    cd ..
    rm -rf test-cleanup-env
    echo "✅ 測試環境清理完成"
}

# 主測試流程
main() {
    echo "🚀 開始 Smart Cleanup Action 測試套件"
    echo "=========================================="

    # 記錄測試開始時間
    TEST_START_TIME=$(date +%s)

    # 執行測試
    setup_test_environment
    test_cleanup_functions
    test_cleanup_levels
    test_cache_preservation
    test_timeout_mechanism
    cleanup_test_environment

    # 計算測試耗時
    TEST_END_TIME=$(date +%s)
    TEST_DURATION=$((TEST_END_TIME - TEST_START_TIME))

    echo ""
    echo "🎉 所有測試完成！"
    echo "=========================================="
    echo "📊 測試統計："
    echo "- 總耗時: ${TEST_DURATION} 秒"
    echo "- 測試狀態: 成功"
    echo ""
    echo "✅ Smart Cleanup Action 功能驗證通過"
}

# 執行主函數
main "$@"
