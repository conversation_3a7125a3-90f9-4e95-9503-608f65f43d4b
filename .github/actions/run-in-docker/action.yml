name: 'Run command in ECR-based Docker container'
description: |
  Execute commands in a Docker container using ECR images with isolated workspace.

  ⚠️ **SECURITY WARNING:** The `run_command` and `environment_variables` inputs are executed directly
  in shell context. This action should ONLY be used in workflows triggered by trusted sources:
  - Direct pushes to main/protected branches
  - PRs from trusted collaborators
  - Workflows with environment protection rules for external contributions

  DO NOT use in workflows triggered by `pull_request` events from untrusted forks without
  additional security controls (e.g., `pull_request_target` + environment approval).

inputs:
  ecr_registry:
    description: 'ECR registry URL'
    required: true
  image_name_with_tag:
    description: 'Docker image name with tag (e.g., novel-web-frontend:latest)'
    required: true
  run_command:
    description: 'Command to execute in the container'
    required: true
  environment_variables:
    description: 'Environment variables to pass to container (optional)'
    required: false
    default: ''
  pnpm_store_path:
    description: 'Path to the host pnpm store to mount into the container'
    required: false
    default: ''

runs:
  using: "composite"
  steps:
    - name: Set work directory
      shell: bash
      run: |
        # 直接使用 GitHub workspace 作為工作目錄
        WORK_DIR="${{ github.workspace }}"
        echo "WORK_DIR=$WORK_DIR" >> $GITHUB_ENV
        echo "✅ Using GitHub workspace as work directory: $WORK_DIR"

    - name: Pull image from ECR
      shell: bash
      run: |
        ECR_IMAGE="${{ inputs.ecr_registry }}/${{ inputs.image_name_with_tag }}"
        echo "📥 Pulling image: $ECR_IMAGE"
        docker pull $ECR_IMAGE

    - name: Execute command in container
      shell: bash
      run: |
        ECR_IMAGE="${{ inputs.ecr_registry }}/${{ inputs.image_name_with_tag }}"
        echo "🚀 Running command in container..."
        start_time=$(date +%s)

        # Prepare environment variables
        ENV_ARGS=""
        if [ -n "${{ inputs.environment_variables }}" ]; then
          ENV_ARGS="${{ inputs.environment_variables }}"
        fi

        # Prepare pnpm store mount option with enhanced configuration
        PNPM_MOUNT_OPTION=""
        PNPM_ENV_VARS=""
        if [[ -n "${{ inputs.pnpm_store_path }}" ]]; then
          # Container pnpm store path (for non-root user)
          CONTAINER_PNPM_STORE_PATH="/workspace/.pnpm-store"
          PNPM_MOUNT_OPTION="--volume ${{ inputs.pnpm_store_path }}:${CONTAINER_PNPM_STORE_PATH}"
          # 🚀 重要修復：設置環境變數確保 pnpm 使用掛載的 store
          PNPM_ENV_VARS="--env PNPM_STORE_DIR=${CONTAINER_PNPM_STORE_PATH}"
          echo "✅ Mounting pnpm store: Host[${{ inputs.pnpm_store_path }}] -> Container[${CONTAINER_PNPM_STORE_PATH}]"
          echo "🔧 Setting PNPM_STORE_DIR environment variable"
        else
          echo "ℹ️ No pnpm store path provided, skipping mount"
        fi

        # Create a temporary script file to safely handle multi-line commands
        SCRIPT_FILE=$(mktemp)
        cat <<'EOF' > "$SCRIPT_FILE"
          ${{ inputs.run_command }}
          EOF

        docker run --rm \
          --volume "$WORK_DIR:/workspace" \
          --volume "$SCRIPT_FILE:/tmp/script.sh" \
          --workdir /workspace \
          --user "$(id -u):$(id -g)" \
          $PNPM_MOUNT_OPTION \
          $PNPM_ENV_VARS \
          $ENV_ARGS \
          $ECR_IMAGE \
          bash /tmp/script.sh

        # Cleanup temporary script
        rm -f "$SCRIPT_FILE"

        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "⏱️ Command completed in ${duration} seconds"

    - name: Cleanup artifacts
      shell: bash
      if: always()
      run: |
        echo "🧹 Cleaning up temporary artifacts..."
        # No need for chown anymore since container runs with correct user
        rm -rf .lighthouseci/temp-* 2>/dev/null || true
        rm -rf "$WORK_DIR/.pnpm-store" 2>/dev/null || true
        echo "✅ Cleanup completed"
