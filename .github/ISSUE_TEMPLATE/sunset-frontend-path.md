---
name: 🌅 Sunset Frontend Path Issue
about: 報告前端路徑不一致或需要統一的問題
title: '[Sunset] '
labels: ['Sunset frontend/', 'MVP:技術基礎']
assignees: ''
---

# 🌅 Sunset Frontend Path Issue

## 📋 **問題描述**

### 🎯 **發現的路徑問題**
<!-- 描述發現的路徑不一致問題 -->

### 📁 **涉及的文件/目錄**
<!-- 列出涉及的文件或目錄 -->
- [ ] CI/CD 配置 (`.github/workflows/`)
- [ ] Docker 配置 (`Dockerfile`, `infra/docker/`)
- [ ] 腳本文件 (`scripts/`)
- [ ] 配置文件 (ESLint, TypeScript, pre-commit)
- [ ] 文檔 (`docs/`, `README.md`)
- [ ] 其他：

### 🔍 **當前狀況**
```
當前路徑：<請填寫當前路徑，例如：frontend/src/index.js>
期望路徑：<請填寫期望路徑，例如：apps/web-next/src/index.js>
```

## 🎯 **期望結果**

### ✅ **統一目標**
- [ ] 所有路徑引用使用 `apps/web-next/`
- [ ] 移除 `frontend/` 舊路徑引用
- [ ] 移除 `apps/web/` CRA 路徑引用
- [ ] 確保 CI/CD 路徑一致性

### 🔧 **技術要求**
- [ ] Turbo 配置使用 `@novelwebsite/web-next`
- [ ] Docker 配置指向正確應用
- [ ] 快取鍵使用 v2 版本
- [ ] 腳本路徑更新

## 📊 **影響評估**

### 🎯 **影響範圍**
<!-- 評估這個路徑問題的影響範圍 -->

### ⚠️ **風險評估**
- [ ] 低風險 (僅影響開發體驗)
- [ ] 中風險 (影響 CI/CD 流程)
- [ ] 高風險 (影響生產部署)

### 🚨 **緊急程度**
- [ ] 低 (可以延後處理)
- [ ] 中 (應該盡快處理)
- [ ] 高 (需要立即處理)

## 🔧 **建議解決方案**

### 💡 **實作建議**
<!-- 提供解決這個路徑問題的建議 -->

### 📋 **檢查清單**
- [ ] 備份當前配置
- [ ] 更新路徑引用
- [ ] 測試變更影響
- [ ] 更新相關文檔
- [ ] 驗證 CI/CD 流程

## 📝 **額外資訊**

### 🔗 **相關 Issue/PR**
<!-- 列出相關的 Issue 或 PR -->

### 📸 **截圖/日誌**
<!-- 如果有錯誤截圖或日誌，請提供 -->

### 🌐 **環境資訊**
- OS:
- Node.js:
- pnpm:
- Docker:

---

### 📋 **Assignee 檢查清單**
> 處理此 Issue 時請確認：

- [ ] 理解路徑問題的根本原因
- [ ] 評估變更的影響範圍
- [ ] 制定詳細的實作計畫
- [ ] 確保測試覆蓋充分
- [ ] 更新相關文檔
- [ ] 驗證 Sunset frontend/ 一致性

### 🎯 **完成標準**
- [ ] 所有路徑引用統一為 `apps/web-next/`
- [ ] CI/CD 流程正常運作
- [ ] 無破壞性變更
- [ ] 相關文檔已更新
- [ ] 測試通過
