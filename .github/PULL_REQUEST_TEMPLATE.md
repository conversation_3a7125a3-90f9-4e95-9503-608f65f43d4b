# Pull Request Template

## 📋 **PR 基本資訊**

### 🎯 **相關 Issue**
- Closes #<!-- Issue 編號 -->
- Related to #<!-- 相關 Issue 編號 (如有) -->

### 🏷️ **PR 類型** (請勾選適用項目)
- [ ] 🐛 Bug fix (修復問題)
- [ ] ✨ New feature (新功能)
- [ ] 💄 UI/UX (介面優化)
- [ ] ♻️ Refactor (重構)
- [ ] 📝 Documentation (文檔)
- [ ] 🔧 Configuration (配置)
- [ ] 🌅 Sunset frontend/ (前端路徑統一)

### 🎯 **變更範圍** (請勾選適用項目)
- [ ] Frontend (apps/web-next)
- [ ] Backend (Django)
- [ ] Infrastructure (Docker/CI)
- [ ] Configuration (ESLint/TypeScript)
- [ ] Documentation
- [ ] Tests

## 🔧 **變更描述**

### 📝 **主要變更**
<!-- 簡述這個 PR 的主要變更內容 -->

### 🎯 **解決的問題**
<!-- 描述這個 PR 解決了什麼問題 -->

### 💡 **實作方法**
<!-- 說明你的實作方法和技術選擇 -->

## 🌅 **Sunset Frontend/ 檢查清單**
> 如果此 PR 涉及前端路徑變更，請確保以下項目：

### 📁 **路徑一致性**
- [ ] 所有路徑引用使用 `apps/web-next/` (不是 `frontend/` 或 `apps/web/`)
- [ ] CI workflow 路徑與實際目錄結構一致
- [ ] Docker 配置使用正確的應用路徑
- [ ] 腳本和配置文件路徑已更新

### 🏷️ **標籤要求**
- [ ] 已添加 "Sunset frontend/" 標籤 (如適用)
- [ ] 已添加適當的優先級標籤
- [ ] 已添加功能分類標籤

### 🔧 **技術一致性**
- [ ] Turbo 配置使用 `@novelwebsite/web-next` filter
- [ ] ESLint 和 TypeScript 配置指向正確目錄
- [ ] pre-commit hooks 使用 Turbo pipeline
- [ ] 快取鍵使用 v2 版本 (如適用)

## ✅ **測試與驗證**

### 🧪 **測試完成**
- [ ] 本地建置測試通過
- [ ] 單元測試通過
- [ ] 整合測試通過 (如適用)
- [ ] E2E 測試通過 (如適用)

### 🔍 **手動驗證**
- [ ] 功能正常運作
- [ ] 無破壞性變更
- [ ] 性能無明顯退化
- [ ] 相容性確認

### 🚀 **CI/CD 檢查**
- [ ] 所有 CI 檢查通過
- [ ] 建置成功
- [ ] 部署測試通過 (如適用)
- [ ] 安全掃描通過

## 📊 **影響評估**

### 🎯 **影響範圍**
<!-- 描述這個變更會影響哪些部分 -->

### ⚠️ **潛在風險**
<!-- 列出可能的風險和緩解措施 -->

### 📈 **性能影響**
<!-- 描述對性能的影響 (正面/負面/無影響) -->

## 📸 **截圖/演示** (如適用)
<!-- 如果是 UI 變更，請提供截圖或 GIF -->

## 📝 **額外說明**
<!-- 任何其他需要說明的內容 -->

## 🔗 **相關連結**
- Documentation: <!-- 相關文檔連結 -->
- Design: <!-- 設計稿連結 (如適用) -->
- Demo: <!-- 演示連結 (如適用) -->

---

### 📋 **Reviewer 檢查清單**
> Reviewer 請確認以下項目：

- [ ] 程式碼品質符合標準
- [ ] 測試覆蓋率足夠
- [ ] 文檔已更新 (如需要)
- [ ] 無安全性問題
- [ ] 符合專案架構原則
- [ ] Sunset frontend/ 路徑一致性 (如適用)

### 🎯 **合併前確認**
- [ ] 所有 CI 檢查通過
- [ ] 至少一位 reviewer 批准
- [ ] 衝突已解決
- [ ] 分支已更新至最新
