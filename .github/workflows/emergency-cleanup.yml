name: Emergency Disk Cleanup

on:
  workflow_dispatch:
    inputs:
      cleanup_level:
        description: 'Cleanup level (basic/aggressive)'
        required: true
        default: 'basic'
        type: choice
        options:
          - basic
          - aggressive

jobs:
  emergency-cleanup:
    runs-on: [self-hosted, linux, x64]
    steps:
      - name: Check disk space before cleanup
        run: |
          echo "=== 磁碟使用狀況 (清理前) ==="
          df -h
          echo ""
          echo "=== 最大文件/目錄 ==="
          sudo du -h --max-depth=1 /home/<USER>/ | sort -hr | head -10

      - name: Basic cleanup
        run: |
          echo "🧹 開始基礎清理..."

          # 清理 Docker
          echo "清理 Docker 映像和容器..."
          docker system prune -af --volumes || true

          # 清理 GitHub Actions cache
          echo "清理 GitHub Actions 暫存..."
          sudo rm -rf /home/<USER>/actions-runner/_work/_actions || true
          sudo rm -rf /home/<USER>/actions-runner/_diag || true
          sudo rm -rf /home/<USER>/actions-runner/_temp || true

          # 清理系統暫存
          echo "清理系統暫存..."
          sudo rm -rf /tmp/* || true
          sudo rm -rf /var/tmp/* || true

          # 清理 APT cache
          echo "清理 APT cache..."
          sudo apt-get clean || true
          sudo apt-get autoremove -y || true

      - name: Aggressive cleanup (if selected)
        if: inputs.cleanup_level == 'aggressive'
        run: |
          echo "🚨 執行積極清理..."

          # 清理所有 log 文件
          echo "清理 log 文件..."
          sudo find /var/log -type f -name "*.log" -mtime +1 -delete || true
          sudo find /var/log -type f -name "*.gz" -delete || true

          # 清理 pip cache
          echo "清理 pip cache..."
          rm -rf ~/.cache/pip || true

          # 清理 npm/pnpm cache
          echo "清理 npm/pnpm cache..."
          rm -rf ~/.npm || true
          rm -rf ~/.pnpm-store || true
          rm -rf ~/.local/share/pnpm || true

          # 清理 journald logs
          echo "清理 systemd logs..."
          sudo journalctl --vacuum-time=1d || true

      - name: Check disk space after cleanup
        run: |
          echo "=== 磁碟使用狀況 (清理後) ==="
          df -h
          echo ""
          echo "=== 剩餘空間 ==="
          df -h | grep "/$" | awk '{print "可用空間: " $4}'
