name: 🧹 Runner Maintenance

on:
  schedule:
    # 每日台灣時間凌晨 2:00 (UTC 18:00) 執行維護
    - cron: '0 18 * * *'
  workflow_dispatch:
    # 允許手動觸發維護
    inputs:
      force_cleanup:
        description: '強制執行完整清理'
        required: false
        default: 'false'
        type: boolean

jobs:
  runner-maintenance:
    name: 🧹 Self-hosted Runner 維護
    runs-on: [self-hosted, linux, x64]
    timeout-minutes: 30

    steps:
      - name: 📋 維護開始
        run: |
          echo "🧹 開始 Self-hosted Runner 維護作業"
          echo "🕐 維護時間: $(date)"
          echo "💾 維護前磁碟使用狀況:"
          df -h
          echo ""

      - name: 🗑️ Docker 系統清理
        run: |
          echo "🐳 清理 Docker 系統..."

          # 停止所有運行中的容器（除了系統容器）
          echo "⏹️ 停止非系統容器..."
          docker ps -q --filter "label!=system" | xargs -r docker stop || true

          # 清理未使用的容器
          echo "🗑️ 清理停止的容器..."
          docker container prune -f || true

          # 清理未使用的映像（保留最近 24 小時的）
          echo "🖼️ 清理未使用的映像..."
          docker image prune -a -f --filter "until=24h" || true

          # 清理未使用的網路
          echo "🌐 清理未使用的網路..."
          docker network prune -f || true

          # 清理未使用的卷（謹慎操作）
          echo "💾 清理未使用的卷..."
          docker volume prune -f || true

          # 清理構建快取
          echo "🔨 清理構建快取..."
          docker builder prune -a -f || true

      - name: 🧹 系統暫存清理
        run: |
          echo "🗂️ 清理系統暫存..."

          # 清理 APT 快取
          sudo apt-get clean || true
          sudo apt-get autoclean || true
          sudo apt-get autoremove -y || true

          # 清理暫存目錄
          sudo find /tmp -type f -atime +7 -delete 2>/dev/null || true
          sudo find /var/tmp -type f -atime +7 -delete 2>/dev/null || true

          # 清理日誌檔案（保留最近 7 天）
          sudo journalctl --vacuum-time=7d || true

          # 清理 GitHub Actions Runner 快取（保留最近 3 天）
          find /home/<USER>/actions-runner/_work -type f -mtime +3 -delete 2>/dev/null || true

      - name: 🔍 Node.js 和 pnpm 快取清理
        run: |
          echo "📦 清理 Node.js 和 pnpm 快取..."

          # 清理 npm 快取
          npm cache clean --force 2>/dev/null || true

          # 清理 pnpm 快取（保留最近使用的）
          pnpm store prune || true

          # 清理 node_modules（如果存在孤立的）
          find /home -name "node_modules" -type d -mtime +7 -exec rm -rf {} + 2>/dev/null || true

      - name: 🐍 Python 快取清理
        run: |
          echo "🐍 清理 Python 快取..."

          # 清理 pip 快取
          pip cache purge 2>/dev/null || true
          python3 -m pip cache purge 2>/dev/null || true

          # 清理 Python 編譯快取
          find /home -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
          find /home -name "*.pyc" -type f -delete 2>/dev/null || true

      - name: 🔧 強制清理 (如果啟用)
        if: ${{ github.event.inputs.force_cleanup == 'true' }}
        run: |
          echo "💪 執行強制清理..."

          # 強制清理所有 Docker 資源
          docker system prune -a -f --volumes || true

          # 清理更多系統快取
          sudo sync
          echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null || true

          # 清理更多暫存檔案
          sudo find /var/cache -type f -delete 2>/dev/null || true

      - name: 📊 維護完成報告
        run: |
          echo "✅ Runner 維護作業完成"
          echo "🕐 完成時間: $(date)"
          echo "💾 維護後磁碟使用狀況:"
          df -h
          echo ""
          echo "🐳 Docker 系統狀況:"
          docker system df || true
          echo ""
          echo "📈 系統資源狀況:"
          free -h
          echo ""
          echo "🎯 維護作業摘要:"
          echo "  - Docker 系統清理: ✅"
          echo "  - 系統暫存清理: ✅"
          echo "  - Node.js/pnpm 快取清理: ✅"
          echo "  - Python 快取清理: ✅"
          if [[ "${{ github.event.inputs.force_cleanup }}" == "true" ]]; then
            echo "  - 強制清理: ✅"
          fi
          echo ""
          echo "🚀 Runner 已準備好處理新的 CI 作業"

      - name: 📧 維護通知 (失敗時)
        if: failure()
        run: |
          echo "❌ Runner 維護作業失敗"
          echo "🕐 失敗時間: $(date)"
          echo "💾 當前磁碟使用狀況:"
          df -h
          echo ""
          echo "⚠️ 請檢查 Runner 狀態並手動處理"
