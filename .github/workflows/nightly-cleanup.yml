name: Nightly Runner Maintenance

on:
  schedule:
    # 每天凌晨 3:15 (UTC+8) 執行
    - cron: '15 19 * * *'  # UTC 19:15 = 台北時間 03:15
  workflow_dispatch: # 允許手動觸發

jobs:
  cleanup:
    runs-on: self-hosted
    name: Self-hosted Runner Maintenance

    steps:
      - name: Show disk usage before cleanup
        run: |
          echo "📊 Disk usage before cleanup:"
          df -h /
          echo ""
          echo "🗂️ Runner directory usage:"
          du -sh /home/<USER>/actions-runner/ || echo "Runner directory check skipped"
          echo ""

      - name: Clean Docker system
        run: |
          echo "🐳 Starting aggressive Docker cleanup..."

          # 1. 移除所有dangling映像 (最重要!)
          echo "🗑️ 清理dangling映像..."
          docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || echo "No dangling images"

          # 2. 移除超過24小時未使用的映像
          echo "🗑️ 清理24小時未使用的映像..."
          docker image prune -af --filter "until=24h" || echo "Image prune skipped"

          # 3. 系統全面清理
          echo "🗑️ Docker系統全面清理..."
          docker system prune -af --volumes || echo "System prune skipped"

          # 4. 顯示清理結果
          echo "📊 清理後Docker使用狀況:"
          docker system df
          echo "✅ 激進Docker cleanup completed"

      - name: Clean GitHub Actions runner logs
        run: |
          echo "📝 Cleaning runner logs..."
          # Keep logs from last 3 days, remove older ones
          find /home/<USER>/actions-runner/_diag/ -type f -mtime +3 -delete 2>/dev/null || echo "Log cleanup skipped"
          echo "✅ Runner logs cleanup completed"

      - name: Clean temporary work files
        run: |
          echo "🗑️ Cleaning temporary files..."
          # Clean temporary work directories
          rm -rf /home/<USER>/actions-runner/_work/_temp/* 2>/dev/null || echo "Temp cleanup skipped"
          # Clean any leftover build artifacts
          find /home/<USER>/actions-runner/_work/ -name "node_modules" -type d -mtime +1 -exec rm -rf {} + 2>/dev/null || echo "Node modules cleanup skipped"
          # Force clean pnpm-store directories with permission fix
          echo "🔧 Force cleaning pnpm-store directories..."
          find /home/<USER>/actions-runner/_work/ -name ".pnpm-store" -type d -exec sudo chown -R ec2-user:ec2-user {} + 2>/dev/null || echo "pnpm-store permission fix skipped"
          find /home/<USER>/actions-runner/_work/ -name ".pnpm-store" -type d -exec sudo rm -rf {} + 2>/dev/null || echo "pnpm-store cleanup skipped"
          echo "✅ Temporary files cleanup completed"

      - name: Clean package manager caches
        run: |
          echo "📦 Cleaning package manager caches..."
          # Clean pip cache
          rm -rf ~/.cache/pip/* 2>/dev/null || echo "Pip cache cleanup skipped"
          # Clean npm cache
          npm cache clean --force 2>/dev/null || echo "NPM cache cleanup skipped"
          # Clean yarn cache if exists
          yarn cache clean 2>/dev/null || echo "Yarn cache cleanup skipped"
          # Clean pnpm cache and store
          pnpm store prune 2>/dev/null || echo "PNPM store cleanup skipped"
          rm -rf ~/.pnpm-store 2>/dev/null || echo "PNPM store directory cleanup skipped"
          echo "✅ Package manager caches cleanup completed"

      - name: Clean system logs and temporary files
        run: |
          echo "🧹 Cleaning system files..."
          # Clean system temporary files (with caution)
          sudo find /tmp -type f -mtime +7 -delete 2>/dev/null || echo "System temp cleanup skipped"
          # Clean journal logs older than 7 days
          sudo journalctl --vacuum-time=7d 2>/dev/null || echo "Journal cleanup skipped"
          echo "✅ System cleanup completed"

      - name: Show disk usage after cleanup
        run: |
          echo ""
          echo "📊 Disk usage after cleanup:"
          df -h /
          echo ""
          echo "🗂️ Runner directory usage after cleanup:"
          du -sh /home/<USER>/actions-runner/ || echo "Runner directory check skipped"
          echo ""
          echo "🎉 Nightly maintenance completed successfully!"

      - name: Send cleanup summary
        run: |
          echo "📋 Maintenance Summary:"
          echo "- Docker system cleaned (containers, images, volumes, networks)"
          echo "- Runner logs older than 3 days removed"
          echo "- Temporary work files cleared"
          echo "- Package manager caches cleaned (pip, npm, yarn)"
          echo "- System temporary files older than 7 days removed"
          echo "- Journal logs older than 7 days removed"
          echo ""
          echo "✅ Self-hosted runner is ready for the next day!"
