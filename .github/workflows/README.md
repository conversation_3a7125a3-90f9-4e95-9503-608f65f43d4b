# GitHub Workflows 說明

## 📋 活躍的 Workflows

- **main-ci.yml** - 主要 CI/CD 流程
  - 狀態：✅ 活躍
  - 功能：測試、構建、安全檢查

## 🚫 停用的 Workflows

### docs.yml.disabled
- **停用時間**: 2025-06-26
- **停用原因**:
  - 依賴文件不存在 (docs/requirements.txt)
  - 構建路徑過時 (docs/crawler/ → docs/03_DEVELOPMENT_GUIDES/crawler/)
  - 專案重心轉向 AI 操作文檔
- **恢復方式**:
  1. 重新命名為 docs.yml
  2. 修正路徑：`docs/03_DEVELOPMENT_GUIDES/crawler/`
  3. 創建 requirements.txt
  4. 更新 conf.py 中的路徑配置

## 🔄 如需恢復 Sphinx 文檔構建

```bash
# 1. 創建 requirements.txt
echo "sphinx>=4.0.0\nsphinx-rtd-theme" > docs/03_DEVELOPMENT_GUIDES/crawler/requirements.txt

# 2. 修正構建路徑
sphinx-build -b html docs/03_DEVELOPMENT_GUIDES/crawler docs/03_DEVELOPMENT_GUIDES/crawler/_build

# 3. 重新啟用 workflow
mv .github/workflows/docs.yml.disabled .github/workflows/docs.yml
```
