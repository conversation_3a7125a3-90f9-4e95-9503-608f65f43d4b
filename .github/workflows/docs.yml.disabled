name: Build Docs

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

# 設定權限以支援 PR context 和 forked repo
permissions:
  contents: read
  pull-requests: read

jobs:
  # 檢查 commit 級別的文檔變更
  check-doc-change:
    runs-on: self-hosted
    # 修改後的 if 條件：支援 push、PR 和手動觸發
    if: |
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'push' ||
      (github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository)
    permissions:
      contents: read
      pull-requests: read
    outputs:
      has_doc_change: ${{ steps.check.outputs.has_doc_change }}
    steps:
      - uses: actions/checkout@v4
        with:
          # 根據事件類型設定 ref
          ref: ${{ github.event_name == 'pull_request' && github.event.pull_request.head.sha || github.sha }}
          # 對於 push 事件，我們需要更多的歷史來比較
          fetch-depth: ${{ github.event_name == 'pull_request' && 2 || 10 }}

      - name: Check docs changed (Smart Diff)
        id: check
        run: |
          echo "Event: ${{ github.event_name }}, Action: ${{ github.event.action }}"

          # 智能差異檢測邏輯
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            # 當有新的 commit push 到 PR 時，action 為 'synchronize'
            if [ "${{ github.event.action }}" = "synchronize" ]; then
              # 只檢查這次推送的新 commits
              base=${{ github.event.before }}
              head=${{ github.event.after }}
              echo "PR Sync mode: checking only new commits ($base...$head)"
            else
              # PR 剛建立 ('opened') 或重新開啟 ('reopened')
              base=${{ github.event.pull_request.base.sha }}
              head=${{ github.event.pull_request.head.sha }}
              echo "PR Open/Reopen mode: checking all changes ($base...$head)"
              # 確保我們有 base commit
              git fetch origin $base --depth=1
            fi
          elif [ "${{ github.event_name }}" = "push" ]; then
            # Push 模式：使用 GitHub 提供的 before/after SHA
            base=${{ github.event.before }}
            head=${{ github.event.after }}
            echo "Push mode: comparing changes from $base to $head"
            # 如果是新分支或第一次 push，before 可能是 0000000...
            if [ "$base" = "0000000000000000000000000000000000000000" ]; then
              echo "New branch detected, comparing last commit only"
              base=HEAD~1
              head=HEAD
            fi
          else
            # 手動觸發：檢查最後一個 commit
            base=HEAD~1
            head=HEAD
            echo "Manual trigger: comparing last commit ($base...$head)"
          fi

          # 獲取變更的檔案列表
          changed_files=$(git diff --name-only $base $head)
          echo "Files changed: $changed_files"

          # 預設為 false
          echo "has_doc_change=false" >> $GITHUB_OUTPUT

          # 檢查是否有文檔相關文件變更
          if echo "$changed_files" | grep -qE '\.(md|mdx)$|^docs/|\.github/workflows/docs\.yml$'; then
            echo "✅ Doc-related change found. Triggering build."
            echo "has_doc_change=true" >> $GITHUB_OUTPUT
          else
            echo "ℹ️ No doc-related changes found. Skipping build."
          fi

  build:
    runs-on: self-hosted
    needs: check-doc-change
    if: always() && (needs.check-doc-change.outputs.has_doc_change == 'true' || github.event_name == 'workflow_dispatch')
    permissions:
      contents: read
    steps:
      - name: Clean workspace permissions
        run: |
          # Fix any permission issues from previous runs
          sudo chown -R $USER:$USER ${{ github.workspace }} 2>/dev/null || true
          # 更精確的清理：只清理可能衝突的目錄
          find ${{ github.workspace }} -name ".pnpm-store" -type d -exec sudo rm -rf {} + 2>/dev/null || true
          find ${{ github.workspace }} -name "node_modules" -type d -exec sudo rm -rf {} + 2>/dev/null || true

      - uses: actions/checkout@v4

      - name: Verify Python installation
        run: python3.11 --version

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: docs-${{ hashFiles('docs/requirements.txt') }}
          restore-keys: |
            docs-

      - name: Install dependencies
        run: |
          python3.11 -m pip install -r docs/requirements.txt

      - name: Build Sphinx HTML
        run: sphinx-build -b html docs/crawler docs/crawler/_build

      - name: Upload HTML artifact
        uses: actions/upload-artifact@v4
        with:
          name: crawler-docs-html
          path: docs/crawler/_build

  # 處理來自 forked repo 的 PR (只做檢查，不構建)
  check-forked-pr:
    runs-on: self-hosted
    if: github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name != github.repository
    steps:
      - name: Forked PR notification
        run: |
          echo "📝 這是來自 forked repository 的 PR"
          echo "出於安全考量，跳過文檔構建"
          echo "合併後將在 main 分支自動構建"
