# 技術債務清單 (Technical Debt Tracker)

## 🔐 安全修復記錄

### CVE-2017-14158: Scrapy 記憶體耗盡漏洞 ✅ 已修復

**CVE ID**: CVE-2017-14158
**CVSS 評分**: 7.5 (高危)
**受影響組件**: scrapy<=2.11.1
**狀態**: **已修復** ✅
**修復日期**: 2025-06-23

**漏洞描述**:
Scrapy 1.4+ 允許遠端攻擊者通過大文件造成拒絕服務（記憶體消耗）。惡意用戶發送大文件可能導致任意多的文件被讀入記憶體中。

**修復策略**: **保守升級** (遵循最小變更原則)
- **升級範圍**: 僅升級主要套件 scrapy==2.11.1 → 2.11.2
- **依賴策略**: 保持其他 scrapy-* 套件版本不變
- **風險評估**: PATCH 版本升級，SemVer 向後相容保證
- **修復發布**: 2024年5月14日 (Scrapy 2.11.2)

**依賴影響分析**:
- `scrapy-djangoitem==1.1.1`: 保持不變，避免 Django 整合風險
- `scrapy-redis==0.9.1`: 保持不變，避免佇列機制影響
- `scrapy-playwright==0.0.33`: 保持不變，避免瀏覽器渲染問題

**驗證計畫**:
- [x] 安全漏洞修復驗證
- [ ] 現有爬蟲功能測試 (ttkan, hjwzw)
- [ ] CI/CD 流程驗證
- [ ] 容器化環境測試

**關鍵洞察**:
初始 GitHub Dependabot API 顯示無修復版本不準確。通過深度調研發現 2.11.2 確實包含 CVE-2017-14158 修復。展現了超越 API 限制的主動安全研究價值。

---

## ✅ 已解決技術債務 (基於 main 穩定版本 - 更新至 2025-06-28)

### Django 配置重構完成 ✅ RESOLVED
**解決日期**: 2025-06-28
**解決範圍**: PR #180 - Django 配置重構適配 Monorepo + T3 Stack
- ✅ Settings 模組化拆分 (backend/settings/{base,dev,prod,testing}.py)
- ✅ CORS/CSRF 多域名配置
- ✅ API 版本控制 (/api/v1/)
- ✅ Pydantic Settings 環境變數管理
**狀態**: 已合併到 main 分支，配置現代化完成

### CI/CD 穩定性問題解決 ✅ RESOLVED
**解決日期**: 2025-06-28
**解決範圍**: PR #182, #177 - ECR 映像管理和 CI 時序優化
- ✅ ECR 映像可用性問題修復
- ✅ 容器名稱衝突清理機制
- ✅ CI/CD 依賴時序問題解決
**狀態**: 已合併到 main 分支，CI 穩定性大幅提升

### Vercel 部署配置現代化 ✅ RESOLVED
**解決日期**: 2025-06-28
**解決範圍**: PR #181 - Vercel 配置支援 Next.js 15 monorepo
- ✅ 支援 pnpm workspace 架構
- ✅ Next.js 15 App Router 配置
- ✅ Turborepo 整合優化
**狀態**: 已合併到 main 分支，Vercel 部署正常

### 自動化構建清理系統 ✅ RESOLVED
**解決日期**: 2025-06-28
**解決範圍**: PR #179 - 自動化構建清理系統上線
- ✅ 智能磁盤空間管理
- ✅ pnpm 生態整合
- ✅ 構建產物清理自動化
**狀態**: 已合併到 main 分支，資源管理最佳化

## ✅ 歷史已解決技術債務

### CVE-2017-14158 等全部安全漏洞 ✅ 已修復
**修復日期**: 2025-06-25
**修復範圍**: 全部 7 個安全漏洞完成修復 (PR #123)
**狀態**: 已合併到 main 分支，100% 安全狀態

### 智能文檔同步系統啟動 ✅ 已完成
**完成日期**: 2025-06-26
**實現內容**: 三層式文檔更新策略首次執行，自動推送工作流
**狀態**: 已合併到 main 分支，doc-sync.md 命令成功部署

### Backend/Novel 模組日落遷移 ✅ 已完成
**完成日期**: 2025-06-27
**實現內容**: Django 架構統一化，消除歷史技術債務 (PR #158)
**修復範圍**:
- 設定檔統一: novel.settings.test → config.django_settings
- WSGI 路徑更新: novel.wsgi.application → config.wsgi.application
- 部署配置修復: Docker Compose、部署腳本全面同步
- CI 部署安全防護: 創建 Issue #159 預防未來風險
**狀態**: 已合併到 main 分支，實現 Backend 配置標準化

### CI/CD 故障修復 ✅ 已修復
**修復日期**: 2025-06-23
**修復內容**: react-scripts + npm權限問題 (PR #122)
**狀態**: 已合併到 main 分支，100% CI 通過率

### Node.js 依賴優化 ✅ 已完成
**完成日期**: 2025-06-22
**優化成果**: 1.3GB → 769MB，節省 531MB (PR #118)
**狀態**: 已合併到 main 分支，穩定運行

### ESLint 現代化 ✅ 已完成
**完成日期**: 2025-06-22
**實現內容**: 代碼質量基礎架構完成 (PR #116)
**狀態**: 已合併到 main 分支，代碼品質大幅提升

---

## 🚨 高優先級債務

### 1. 框架遷移：從 Create React App 到 Next.js 🆕 **高優先級**

**狀態**: **高優先級技術債務** - Create React App 已被 React 團隊棄用
**問題**: 當前專案使用 Create React App (CRA)，但 React 官方已不再推薦使用 CRA
**影響**:
- **SEO 限制**: CRA 僅支援客戶端渲染，對搜尋引擎優化不利
- **Storybook 相容性**: Storybook 9 對 CRA 支援有限，需要使用 legacy 設定
- **生態系統支援**: 新的 React 功能和工具優先支援現代框架
- **長期維護風險**: CRA 缺乏積極維護，安全更新和新功能支援受限

**React 官方推薦替代方案**:
1. **Next.js (App Router)** - 全端 React 框架，完整 SSR/SSG 支援
2. **React Router v7** - 與 Vite 結合的現代路由解決方案
3. **Expo** - 適用於原生應用開發

**遷移到 Next.js 的 SEO 優勢**:
- ✅ **伺服器端渲染 (SSR)**: 搜尋引擎可以直接索引完整的 HTML 內容
- ✅ **靜態生成 (SSG)**: 小說頁面可以預先生成，提升載入速度和 SEO 排名
- ✅ **自動程式碼分割**: 改善頁面載入性能，提升 Core Web Vitals 分數
- ✅ **內建 SEO 優化**: 自動處理 meta 標籤、Open Graph、結構化資料
- ✅ **圖片優化**: 自動 WebP 轉換和響應式圖片，提升頁面速度

**當前 CRA 的 SEO 限制**:
- ❌ **空白首頁**: 搜尋引擎爬蟲看到的是空白頁面，需要執行 JavaScript 才能看到內容
- ❌ **無法預渲染**: 小說內容無法被搜尋引擎直接索引
- ❌ **慢速首次載入**: 所有 JavaScript 必須下載並執行後才能顯示內容
- ❌ **社群分享問題**: 無法動態生成 Open Graph 標籤，分享連結缺乏預覽

**預估工作量**: 8-12 小時
**建議執行時機**: 當前 CI 修復完成後，作為下一個主要技術升級項目

**遷移策略**:
1. **階段性遷移**: 先建立 Next.js 專案，逐步遷移現有組件
2. **保持 API 相容**: 後端 API 無需變更，僅前端框架升級
3. **SEO 優先**: 優先遷移小說詳情和閱讀頁面以獲得 SEO 效益
4. **測試覆蓋**: 確保所有現有功能在新框架中正常運作

**優先級**: 🔴 **高優先級** - 影響產品的搜尋引擎可見性和長期技術可持續性

---

## 📊 技術債評估項目

### pnpm workspace 配置優化 🔍 **待評估**

**狀態**: **技術債評估項目** - 根據後續 CI 時間表現決定必要性
**當前狀態**: pnpm workspace 已配置並穩定運行 (main 分支)
**評估指標**:
- CI 執行時間變化趨勢
- 依賴安裝效率
- workspace 內部包管理效果

**評估計畫**:
1. **監控週期**: 持續觀察 2 週的 CI 性能數據
2. **基準數據**: 當前 Tier 2 架構下的執行時間
3. **優化觸發條件**:
   - CI 時間增長 >20%
   - workspace 依賴衝突頻率 >1次/週
   - 構建緩存命中率 <80%

**決策時機**: 2025-07-08 進行正式評估和決策

**立即行動記錄 (2025-06-24)**:
- ✅ **暫時禁用 Percy 視覺測試**: 在 CI 中註釋掉 percy-visual-regression job
- ✅ **根因確認**: Storybook 9 + CRA 的 TypeScript 配置衝突無法快速解決
- 🎯 **戰略轉向**: 停止修補 CRA 問題，專注於 Next.js 遷移規劃
- ✅ **Dockerfile 相容性確認**: 當前容器化架構支援平滑 Next.js 遷移

**遷移執行計畫**:
1. **PoC 分支創建**: `feat/nextjs-poc` 用於隔離實驗
2. **文檔建立**: `docs/CRA_TO_NEXTJS_MIGRATION_PLAN.md` 記錄遷移步驟
3. **測試策略調整**:
   - Babel preset 更新: `react-app` → `next/babel`
   - 客戶端組件標記 `'use client'` 保持現有測試
   - 伺服器組件使用 integration testing 方式
4. **容器化優勢**:
   - 保持雙映像架構 (CI + Production)
   - 移除 `--legacy-peer-deps` 依賴
   - 利用 Next.js 更佳的依賴管理

### 2. CI 映像依賴衝突與環境不匹配 🔄 重構中

**文件**: `.github/workflows/main-ci.yml` (已整合單一工作流)
**狀態**: **積極解決中** - 採用單一工作流架構 + 熱修復
**問題**: 多層技術問題，已逐步解決 90%
**影響**: 即將恢復完整 CI 品質門檻
**預估剩餘工作量**: 0.5-1 小時（驗證階段）

**已解決的問題層級**:

1. ✅ **映像標籤不一致**: 統一使用 `:latest` 標籤
2. ✅ **工作流架構**: 整合為單一工作流，添加 needs 依賴管理
3. ✅ **工作目錄掛載**: 修正 run-in-docker action 使用 GitHub workspace
4. ✅ **檔案引用錯誤**: 更新 Dockerfile 使用實際的 package.json
5. ✅ **npm 依賴衝突**: 添加 --legacy-peer-deps 標誌解決 Storybook 版本衝突

**最新修復方案（2025-06-23）**:
```dockerfile
# 立即生效的熱修復
RUN npm install --no-audit --no-fund --legacy-peer-deps && \
    npm cache clean --force
```

**技術債務狀態**:
- 🚀 **短期解決**: CI/CD 流程已可正常執行
- 📋 **長期優化**: Storybook 版本統一計劃（見後續章節）

**解決方案選項**:

#### 🥇 **選項 A: 最小可行熱修 (推薦立即採用)**
- **在 host 上執行構建**: `cd frontend && npm ci --legacy-peer-deps && npm run build`
- **使用 LHCI 官方 Docker**: `lhci/lighthouse-ci:0.13.x` (內建 Chromium)
- **優點**: 10 秒解決瀏覽器+權限問題，避免依賴衝突
- **缺點**: 執行時間略長，但可快速恢復 CI 綠燈

#### 🥈 **選項 B: 專用 CI 映像 (長期方案)**
- **建立**: `novel-web-frontend:ci` 映像 (含 devDependencies + Chromium)
- **Docker**: 多階段構建，包含所有測試依賴
- **優點**: 最快執行速度，符合 Docker 最佳實踐
- **缺點**: 額外維護複雜度，映像大小增加

**立即行動建議**:
```yaml
# 範例：使用 LHCI 官方映像的熱修方案
- name: LHCI via official image
  run: |
    cd frontend && npm ci --legacy-peer-deps && npm run build
    docker run --rm \
      -v "$PWD/frontend:/usr/src/app" \
      --workdir /usr/src/app \
      lhci/lighthouse-ci:0.13.x \
      autorun --collect.staticDistDir=./build \
              --upload.target=temporary-public-storage
```

**關鍵洞察**:
「在錯誤的映像裝錯的東西」- 嘗試在純 runtime 映像中強加開發工具，違反了容器化設計原則。

**優先級**: 🔴 **極高** - 阻塞 CI 品質門檻，影響開發流程

**執行時機**: 當前 PR 合併後立即修復

---

### 2. CZBooks Spider 完整實現

**文件**: `backend/novel/crawler/spiders/czbooks_spider.py.disabled`
**狀態**: 已隔離，等待重構
**問題**: 當前實現使用hardcoded placeholder數據，會污染數據庫
**影響**: 無法從czbooks.net獲取真實數據
**預估工作量**: 2-3小時

**解決方案**:

1. 分析czbooks.net的真實HTML結構
2. 實現正確的選擇器提取author、description等字段
3. 確保source_id可靠提取（從URL pattern）
4. 添加對應的單元測試
5. 重新啟用spider

**優先級理由**:

- 目前hjwzw已足夠MVP需求
- 但czbooks是第一階段戰略目標之一
- 需要在Phase 2開始前完成

## 🟡 中優先級債務

### 1. Storybook 版本不一致修復 🆕

**文件**: `frontend/package.json`
**狀態**: **新增技術債務** - 需要統一版本
**問題**: Storybook 相關套件版本混雜，當前使用 --legacy-peer-deps 繞過
**影響**: 潛在的功能不相容風險，依賴管理複雜度增加
**預估工作量**: 2-3 小時

**當前版本衝突**:
```json
{
  "storybook": "^9.0.12",
  "@storybook/react": "^9.0.12",
  "@storybook/react-webpack5": "^9.0.12",
  "@storybook/addon-essentials": "^8.6.14",    // ← 版本不一致
  "@storybook/addon-interactions": "^7.6.17",  // ← 版本不一致
  "@storybook/addon-links": "^7.6.17",         // ← 版本不一致
  "@storybook/blocks": "^8.6.14",              // ← 版本不一致
  "@storybook/testing-library": "0.2.0"        // ← 版本不一致
}
```

**解決方案選項**:

#### 選項 A: 統一升級到 9.x (推薦)
- 將所有 @storybook/* 套件升級到 ^9.0.x
- 檢查 addon 的 9.x 相容性
- 測試現有 Storybook 配置

#### 選項 B: 統一降級到 8.x
- 將 storybook 核心降級到 ^8.6.x
- 保持 addon 版本不變
- 風險較低但功能可能受限

**執行計劃**:
1. 調研各 addon 的最新版本相容性
2. 選擇統一版本策略（9.x 或 8.x）
3. 更新 package.json 所有 storybook 相關依賴
4. 移除 --legacy-peer-deps 並測試構建
5. 驗證 Storybook 功能正常

**優先級**: 中 - 當前 CI 已可運行，但需要清理技術債務

### 2. Legacy Code Cleanup Schedule (2025-06-22)

**狀態**: ✅ Phase 1-2 已完成，等待評估期
**歸檔位置**: `archive/legacy-code-backup/`

**清理記錄**:
- 移動 17 個備份檔案 (.bak/.disabled) 到歸檔目錄
- 清理 3,973 個 `__pycache__` 目錄 (節省 ~500MB)
- 移除錯誤追蹤的臨時檔案 (dump.rdb, page.html)

**評估期**: 2025-06-22 至 2025-07-22 (1個月)
**最終清理日期**: 2025-07-22 (確認新 Adapter 架構穩定後永久刪除)

**包含檔案**:
- 13個舊爬蟲實現 (.bak)
- 3個停用測試 (.disabled)
- 1個舊Dockerfile (.bak)

### 3. CI Cache Management

**問題**: `__pycache__` 目錄累積，需要 CI 自動清理
**狀態**: ✅ 已實現 (2025-06-22)
**優先級**: 中

**實施方案**:
```yaml
# 已添加到 main-ci.yml - quality-summary job
- name: Clean Python Cache (Prevent Accumulation)
  if: always()
  run: |
    cache_count=$(find . -type d -name "__pycache__" | wc -l)
    if [ "$cache_count" -gt 0 ]; then
      find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
      find . -name "*.pyc" -delete 2>/dev/null || true
    fi
```

**效果**: 每次 CI 運行後自動清理 Python 快取，防止累積

### 4. Legacy Spider清理

**文件**: `backend/novel/crawler/spiders/*.bak`
**狀態**: 已重命名為.bak但未刪除
**問題**: 舊的spider檔案造成代碼庫混亂
**影響**: 新開發者可能誤用廢棄代碼
**預估工作量**: 30分鐘

**解決方案**:

1. 確認所有.bak檔案都已無用
2. 刪除或移動到archive目錄
3. 更新文檔說明current active spiders

### 3. CI/CD 並行執行優化策略

**狀態**: 待決策
**問題**: 當前 self-hosted runner 為單一實例，CI jobs 依序執行而非並行
**影響**: CI 執行時間可能從 10 分鐘延長至 15 分鐘
**預估工作量**: 依選擇方案而定

**可選方案**:

#### 選項 A (維持現狀 - 務實的選擇)

- **說明**: 對於個人專案，如果 CI 總執行時間（例如從並行的 10 分鐘變成串行的 15 分鐘）是在可接受範圍內，維持現狀讓任務依序執行是完全合理的選擇
- **優點**: 最簡單，最穩定
- **缺點**: 較長的 CI 等待時間
- **工作量**: 0 小時

#### 選項 B (垂直擴展 - 實驗性選擇)

- **說明**: GitHub Runner 軟體本身允許在同一台機器上運行多個並行任務（通過修改 `.runner` 文件）
- **風險**: t3.medium 的 2 個 vCPU 和 4GB 記憶體將被兩個 Job 瓜分，可能導致每個 Job 都變慢，甚至再次出現記憶體不足問題
- **工作量**: 1-2 小時實驗

#### 選項 C (水平擴展 - 專業級方案)

- **說明**: 最專業的解決方案是增加 Runner 數量，調整 Auto Scaling Group (ASG) 設定，例如將 max capacity 改為 2，並設定策略在需要時自動增加一台 EC2 主機
- **優點**: 解決高併發 CI 需求的最終方案
- **缺點**: 最複雜的實現
- **工作量**: 4-6 小時

**當前決策**: 採用選項 A（維持現狀）
**理由**: 剛完成穩定的單一 Runner 部署，應先在穩定基礎上專注開發，感受當前 CI/CD 速度是否真的影響開發節奏。先運行一兩週收集數據和體感，如果等待時間確實無法忍受，再挑戰選項 C。

## 🟢 低優先級債務

### 4. 前端 UI 測試環境限制 (JSDOM)

**文件**: `frontend/src/hooks/__tests__/useMobileMenu.test.ts`, `frontend/src/components/__tests__/Header.test.tsx`
**狀態**: 已整合前端 TECH_DEBT.md 內容，共計 6+ 個測試標記為 .skip
**問題**: JSDOM 環境無法完整模擬複雜的瀏覽器行為，影響測試覆蓋率
**影響**: 某些真實使用者互動行為無法在單元測試層級驗證
**預估工作量**: 4-6 小時 (Playwright E2E 測試實現)

**跳過的測試案例分類**:

**A. useMobileMenu Hook (3 個)**:

- `restores body scroll when menu closes - JSDOM scrollTo issue`
- `closes menu on escape key press - JSDOM event handling issue`
- `closes menu when clicking outside - JSDOM ref mocking issue`

**B. Header Component (6+ 個)**:

- `closes mobile menu when clicking menu button again`
- `closes mobile menu when clicking outside`
- `navigates to home when clicking logo`
- `navigates using navigation links`
- `applies active state to current route`
- `closes mobile menu when navigating`

**根本原因**:

- JSDOM 不支援：點擊事件傳播、React Router 導航測試、文檔級事件監聽器
- 真實瀏覽器環境中這些功能運作正常

**解決方案 (未來優化階段)**:

1. 引入 Playwright E2E 測試框架 (專案中已配置)
2. 在真實瀏覽器環境中重新實現這些測試案例
3. E2E 測試可以完美驗證：
   - 真實的 scrollTo 行為和滾動位置恢復
   - 全域鍵盤事件監聽 (Escape 鍵)
   - 跨元件的複雜使用者互動 (外部點擊檢測)
   - 行動端視窗模擬和響應式行為
   - 真實的 DOM 操作和事件傳播
   - React Router 導航流程

**優先級理由**:

- 功能在實際產品中正常運作，僅缺乏自動化測試覆蓋
- E2E 測試屬於「從 1 到 1.1」的品質優化
- Playwright 在真實瀏覽器中能完美驗證這些互動

**重新評估時機**: Sprint 3 (優化提升階段)

### 5. 重新啟用 Lighthouse 和 Percy 自動化測試

**文件**: `.github/workflows/lighthouse.yml`, `.github/workflows/percy.yml`
**狀態**: 已暫時停用 (觸發器已註解)
**問題**: 為聚焦核心開發並節省 GitHub Actions 額度，暫時停用了性能和視覺回歸測試
**影響**: 無法即時發現前端性能降低或 UI 佈局問題
**預估工作量**: 30分鐘 (取消註解 + 驗證運行)

**重新啟用時機**: Sprint 3 (優化提升階段)
**解決方案**:

1. 取消 `on:` 區塊的註解
2. 驗證 Percy token 和 Lighthouse 配置仍然有效
3. 運行測試確保正常工作
4. 考慮調整觸發條件以進一步優化資源使用

**優先級理由**:

- 目前 P0/P1 階段專注於內容獲取和用戶系統
- 性能和視覺測試屬於從 1 到 1.1 的優化
- 當核心功能完成後，品質保證將變得更重要

### 6. 前端測試 `act` 過期警告 🆕
- **問題**: 測試運行時出現 `ReactDOMTestUtils.act` 過期警告
- **狀態**: ✅ **已歸檔**。已創建 Issue `#135` 進行追蹤
- **優先級**: P3 (優化)
- **觸發條件**: 在下一次對前端測試框架進行大規模重構時，或在 Sprint 間隙期處理
- **關聯報告**: `docs/04_AI_OPERATIONS/validator/2025-06-26_Issue-134_Validation-Report.html`
- **來源**: Validator 在驗證 Issue #134 時自動識別
- **影響檔案**: `src/pages/__tests__/Home.test.tsx`, `src/hooks/__tests__/useMobileMenu.test.ts`, `src/hooks/__tests__/useSettings.test.tsx`
- **建議方案**: 從 `react` 而非 `react-dom/test-utils` 導入 `act`，或使用現代的 `waitFor`/`findBy*` 查詢

## 📋 追蹤原則

- 每次重構後更新此清單
- 高優先級債務阻擋新功能開發
- 中低優先級可在Sprint末處理
- 所有債務必須有明確的解決方案和工作量估算

---

## 🔧 結構性技術債務

### 6. 遺留模塊清理：`backend/novel` 日落計劃 (Sunset Plan)

- **狀態**:  **高優先級債務** - 影響新開發者效率和專案長期可維護性
- **目標**: 在 Sprint 3 結束前，完全移除 `backend/novel` 目錄。
- **關聯史詩 Issue**: `[EPIC] Sunset and remove legacy 'backend/novel' application` **(待創建)**

#### 6.1 遷移清單與進度 (Migration Checklist)
這是對 `backend/novel` 內容的完整分析，以及需要執行的具體遷移/刪除任務。

- **`backend/novel/adapters/`**: 包含舊的爬蟲適配器。
  - **`piaotian_adapter.py`**:
    - [ ] **分析**: 檢查其邏輯是否已被新的 `crawler_engine` 或 `apps/catalog` 中的適配器覆蓋。
    - [ ] **行動**: 如果已覆蓋，則刪除。如果沒有，則創建一個 P2 級任務，將其重構為新的 `BaseAdapter` 模式。
  - **`czbooks_adapter.py`**:
    - [ ] **分析**: 根據 `TECH_DEBT.md`，這是一個未完整實現的適配器。
    - [ ] **行動**: 創建 P1 級任務 `#XXX - [Feature] Implement CZBooks adapter based on new architecture`，完成後刪除此舊文件。

- **`backend/novel/management/commands/`**: 包含大量舊的管理命令。
  - [ ] **審計 (Audit)**: 逐一審查以下命令，確定其當前狀態：`crawl_czbooks.py`, `crawl_hjwzw.py`, `crawl_ttkan.py`, `crawl_yuepiao.py`, `export_settings_schema.py`, `migrate_data_to_catalog.py`, `run_distributed_crawler.py`。
  - [ ] **分類 (Categorize)**: 將審計後的命令分為三類：
    1.  **可廢棄 (Obsolete)**: 功能已被新系統替代。
    2.  **需遷移 (To be Migrated)**: 功能仍有價值，需重構並遷移到 `apps/catalog/management/commands/`。
    3.  **已遷移 (Migrated)**: 功能已存在於新模塊中。
  - [ ] **行動 (Action)**: 對於「可廢棄」的命令，直接刪除。對於「需遷移」的命令，創建對應的技術債務 Issue。

- **`backend/novel/models.py`**: 舊的數據模型。
  - [ ] **比對**: 與 `apps/catalog/models.py` 進行 `diff` 比對，確保所有字段和邏輯都已遷移。
  - [ ] **數據遷移驗證**: 確認 `migrate_data_to_catalog.py` 命令已成功執行且數據無損。
  - [ ] **行動**: 在確認數據安全後，刪除此文件。

- **`backend/novel/tests/`**: 舊的測試文件。
  - [ ] **審計**: 檢查其中的測試用例是否已被 `apps/catalog/tests.py` 或其他新測試覆蓋。
  - [ ] **行動**: 遷移有價值的測試用例，然後刪除整個目錄。

- **`backend/novel/urls.py` 和 `views.py`**: 舊的視圖和路由。
  - [ ] **分析**: 檢查是否還有任何 API 端點指向這些舊的視圖。
  - [ ] **行動**: 將所有流量遷移到新的 `apps/catalog/api/` 端點後，安全刪除。

### 7. 停用測試檔案管理

**文件**: `backend/novel/tests/test_async_ttkanspider.py.disabled`, `backend/crawler_engine/spiders/*.disabled`
**狀態**: 多個測試檔案被重命名為 .disabled
**問題**: 停用的測試檔案缺乏文檔說明停用原因和重啟條件
**影響**: 未來開發者不確定這些檔案的狀態和用途
**預估工作量**: 1-2 小時

**解決方案**:

1. 為每個 .disabled 檔案添加 README 或註釋說明停用原因
2. 建立重新啟用的條件和時程
3. 定期檢視是否可以安全刪除

### 8. 配置檔案複雜度

**文件**: `backend/config/` 目錄中的多個設定檔案
**問題**: 設定檔案分散在多個檔案中，依賴關係不清晰
**影響**: 配置管理複雜，新手難以理解設定架構
**預估工作量**: 3-4 小時重構

**具體問題**:

- `django_settings.py`, `legacy.py`, `minimal_settings.py` 等多個設定檔
- 設定間的繼承和覆蓋關係不夠明確

**解決方案**:

1. 創建配置架構文檔
2. 簡化設定檔案間的依賴關係
3. 標準化環境特定的設定模式

## 🔄 前端模組特定債務

### 9. ReaderSettings 未來增強

**狀態**: MVP 實現完成，規劃未來迭代功能
**預估工作量**: 待評估

**計劃功能**:

1. **額外設定控制項**:

   - 行距調整滑桿
   - 主題選擇器 (Light/Dark/Sepia)
   - 字體族選擇器 (Serif/Sans-serif)

2. **進階功能**:

   - 認證用戶的後端 API 同步
   - 預設配置
   - 閱讀進度追蹤
   - 自訂色彩方案

3. **UI 改進**:
   - 設定面板動畫轉場
   - 即時調整預覽面板
   - 行動端優化設定抽屜

### 10. 依賴項目管理

**狀態**: 需要定期更新
**影響**: 低 - 主要影響瀏覽器兼容性資料

**具體項目**:

- `caniuse-lite`: 瀏覽器資料過時 6 個月
  - 解決方案: `npx update-browserslist-db@latest`
- `punycode` 模組在 Node.js 中被廢棄
  - 影響: 低 - 由依賴項使用，非直接代碼
- `ReactDOMTestUtils.act` 廢棄警告
  - 解決方案: 升級 React Testing Library 時處理

---

### 11. GitHub App 自動化 Runner 註冊

**狀態**: 技術債務 - 當前使用手動 token
**問題**: 目前使用手動生成的 GitHub Runner 註冊 token，存在安全風險且不適合自動化
**影響**: 無法實現完全自動化的基礎設施部署，需要人工干預
**預估工作量**: 4-6 小時

**當前方案 (臨時)**:
- 手動從 GitHub UI 生成 Runner 註冊 token
- 手動 SSH 到實例執行 `./config.sh --token MANUAL_TOKEN`
- 可工作但不適合生產環境

**目標方案 (GitHub App)**:
1. **創建 GitHub App**:
   - 權限：`Self-hosted runners (Read and write)`
   - 範圍：僅限 `MumuTW/novel-web` 倉庫
   - 獲得 App ID 和私鑰

2. **安全存儲**:
   - 將 GitHub App 私鑰存放在 AWS Secrets Manager
   - IAM 角色允許 EC2 實例讀取 Secret

3. **自動化流程**:
   - EC2 UserData 腳本從 Secrets Manager 獲取私鑰
   - 使用私鑰生成短期 Installation Access Token (幾分鐘有效)
   - 使用該 token 呼叫 GitHub API 獲取 Runner 註冊 token
   - 自動配置 Runner

**安全優勢**:
- 最小權限原則：只有 Runner 管理權限
- 短期憑證：所有 token 都是臨時的
- 零永久密鑰：沒有長期有效的認證資料
- 完全自動化：適合 Auto Scaling Group

**整合點**:
- 黃金 AMI 的 user-data 腳本
- ASG 自動啟動新實例的場景
- 實現真正的 "push-button" 基礎設施部署

**優先級**: 中高 - 完整 Tier 2 架構的最後一塊拼圖

### 12. Docker Multi-stage Build 映像優化

**狀態**: 技術債務 - 當前使用 Tier 1.5 映像，效能已達標
**問題**: ECR 中的映像使用舊版 Dockerfile，後端映像大小為 2.28GB 而非優化後的 596MB
**影響**: 映像拉取時間較長，儲存成本較高，但不影響核心 CI/CD 性能
**預估工作量**: 2-3 小時

**當前狀況**:
- ✅ **CI/CD 性能**: 前端 1 秒，後端 1 秒（已達標）
- ✅ **功能完整性**: 所有測試通過，映像完全可用
- ⚠️ **映像大小**: 後端 2.28GB vs 目標 596MB

**技術背景**:
- 本地測試的 `backend-tier2.Dockerfile` 實現了多階段構建優化
- ECR 中推送的是使用 `backend.Dockerfile` 構建的映像
- GitHub Actions 工作流程已配置使用 Tier 2 Dockerfile，但因 Runner 問題未執行

**優化方案**:
1. **更新構建腳本**:
   - 修改 `infra/docker/build-images.sh` 使用 `*-tier2.Dockerfile`
   - 確保本地和 CI 環境使用相同的優化 Dockerfile

2. **重新構建並推送**:
   - 在 Runner 上使用更新的腳本重新構建映像
   - 推送優化後的映像到 ECR，覆蓋現有版本

3. **預期成果**:
   - 後端映像：2.28GB → 596MB（74% 減少）
   - 前端映像：674MB → 預期 <500MB（進一步優化）
   - ECR 拉取時間縮短，儲存成本降低

**優先級理由**:
- **不阻塞開發**: 當前性能已滿足所有需求
- **增量優化**: 屬於「從 1.0 到 1.1」的細化改進
- **資源效益**: 主要影響基礎設施成本而非開發體驗

**執行時機**: 在 Tier 2 架構穩定運行 1-2 週後執行

### 13. Elastic IP 自動綁定實現

**狀態**: 技術債務 - 當前手動管理 Runner IP 變更
**問題**: ASG 重新啟動 EC2 實例時，公網 IP 會變更，需手動更新 Makefile 和重新配置
**影響**: 基礎設施維護成本，無法實現真正的自動化部署
**預估工作量**: 2-3 小時

**當前問題**:
- 每次 ASG 重啟實例，都需要手動更新 `Makefile` 中的 `RUNNER_HOST`
- 無法實現完全自動化的基礎設施管理
- 影響災難恢復和自動擴展能力

**解決方案**:

1. **預先分配 Elastic IP**:
   ```bash
   # 在 AWS EC2 控制台預先分配 Elastic IP
   # EC2 > Elastic IPs > Allocate Elastic IP address
   # 記錄分配到的 IP 地址和 Allocation ID
   ```

2. **更新 user-data 腳本**:
   ```bash
   #!/bin/bash
   # ... 其他設置 ...

   set -e

   # --- EIP Self-Association ---
   echo "🔗 正在綁定 Elastic IP..."

   # 從 AWS 元數據服務獲取當前實例的 ID 和 Region
   INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
   REGION=$(curl -s http://***************/latest/meta-data/placement/region)

   # 預先分配好的 Elastic IP 的 Allocation ID
   EIP_ALLOCATION_ID="eipalloc-xxxxxxxxxxxxxxxxx" # <--- 請替換成實際 Allocation ID

   # 執行綁定命令
   aws ec2 associate-address --instance-id "$INSTANCE_ID" --allocation-id "$EIP_ALLOCATION_ID" --region "$REGION"

   echo "✅ Elastic IP 綁定成功！"
   # --- EIP 綁定結束 ---

   # --- 後續的 Runner 設置 ... ---
   ```

3. **更新 IAM 角色權限**:
   - 確保 Runner 實例的 IAM Role 包含 `ec2:AssociateAddress` 權限
   - 添加到現有的 IAM 政策中

**實施步驟**:
1. 在 AWS 控制台分配 Elastic IP 並記錄 Allocation ID
2. 修改 `infra/aws-ecr/user-data-v2-longlived.sh` 添加 EIP 綁定邏輯
3. 更新 IAM 角色權限添加 `ec2:AssociateAddress`
4. 測試 ASG 重啟實例時的自動 IP 綁定
5. 更新 `Makefile` 使用固定的 Elastic IP

**預期效果**:
- ✅ ASG 啟動的任何新實例都會自動綁定固定公網 IP
- ✅ 無需手動更新 Makefile 或重新配置連接
- ✅ 實現真正的「一勞永逸」自動化基礎設施
- ✅ 支援災難恢復和自動擴展場景

**優先級**: 中 - 提升基礎設施自動化完整度
**關聯**: 與 GitHub App 自動化 (#11) 結合可實現完全自動化部署

---

### 14. 從封存文檔中提取價值內容

**狀態**: 新增 - 文檔清理後的整合任務
**問題**: 封存到 `docs/archive/` 的文檔中包含有價值的設置步驟和實驗結果，需要提取整合到主要文檔中
**影響**: 未來開發者可能需要重複探索已解決的問題
**預估工作量**: 3-4 小時

**封存的文檔類別**:

1. **實驗結果** (`docs/archive/experiments/`):
   - `TIER1_EXPERIMENT_STATUS.md` - 包含性能對比數據
   - `TIER1_5_EXPERIMENT_DESIGN.md` - 架構設計思路
   - `tier1-docker-experiment.md` - Docker 優化經驗

2. **設置指南** (`docs/archive/setup-guides/`):
   - `doppler-setup.md` - 環境變數管理經驗
   - `DevContext-Setup.md` - 開發環境配置步驟
   - `docker-act-setup.md` - 本地 CI 測試設置

3. **~~Oracle 相關~~** ✅ **已刪除**:
   - ~~完整的 Oracle 雲端設置文檔~~ - 已刪除，專案採用 AWS 架構

**整合計劃**:
- 提取實驗結果中的性能基準數據更新到主要架構文檔
- 將重要的設置步驟整合到開發者入門指南
- 在適當位置添加 "已知問題與解決方案" 索引，指向封存文檔中的解決方案

**執行時機**: Sprint 3 (文檔優化階段)

### 15. Legacy Taskmaster 系統整合

**狀態**: 新增 - TXT 檔案清理後的整合任務
**問題**: 封存的 Legacy Taskmaster 系統包含 28個歷史任務記錄，其中可能有未完成的重要任務需要轉移到現代系統
**影響**: 確保沒有遺漏重要的功能需求或技術任務
**預估工作量**: 2-3 小時

**封存內容**:
- `docs/archive/legacy-taskmaster/tasks/` - 28個任務檔案 (task_001.txt ~ task_028.txt)
- `docs/archive/legacy-taskmaster/docs/` - 早期 PRD 文檔
- `docs/archive/legacy-taskmaster/templates/` - 模板檔案

**整合計劃**:
1. **任務狀態審查**:
   - 檢查 task_001.txt ~ task_028.txt 中標記為 "pending" 或 "in_progress" 的任務
   - 識別尚未在當前系統中體現的功能需求

2. **需求轉移**:
   - 將未完成的核心功能需求轉為 GitHub Issues
   - 將技術任務整合到 TECH_DEBT.md 相應區域
   - 更新當前 PRD.txt 以包含遺漏的需求點

3. **歷史文檔價值提取**:
   - 從早期 PRD 文檔中提取仍然相關的市場分析和技術選型理由
   - 將有價值的架構決策記錄整合到主要文檔中

**優先級**: 低 - 不影響當前開發，但有助於確保需求完整性

**執行時機**: 當前 Sprint 結束後的回顧階段

### 16. Dockerfile 清理完成記錄 ✅

**狀態**: **已完成** - 遺留檔案清理完成
**完成日期**: 2025-06-24
**問題**: 存在無實際引用的遺留 Dockerfile 文件，影響程式碼庫整潔度
**解決方案**: 基於徹底的使用情況分析，安全刪除確認遺留的文件

**清理記錄**:
- ❌ **已刪除**: `infra/docker/frontend-lean.Dockerfile` (無任何程式碼引用)
- ❌ **已刪除**: `infra/docker/backend-debug.Dockerfile` (無任何程式碼引用)
- ✅ **保留活躍**: `frontend-tier2.Dockerfile` + `frontend-ci.Dockerfile` (CI/CD 核心組件)
- ✅ **技術債務管理**: `backend.Dockerfile` 等過渡期文件按既定時程處理

**分析方法**:
1. **程式碼引用搜索**: 使用 `grep` 全專案搜索檔案引用
2. **CI/CD 整合檢查**: 驗證 GitHub Actions 工作流程使用情況
3. **ECR 部署腳本確認**: 檢查 AWS 部署腳本中的實際使用

**清理效益**:
- 🧹 **程式碼庫更整潔**: 移除無用文件，降低維護負擔
- 📋 **文檔同步**: 避免文檔與實際文件不一致的問題
- 🎯 **聚焦核心**: 開發者只需關注實際使用的容器化文件

**後續預防措施**:
- 定期執行 Dockerfile 使用情況分析
- 新增文件時明確標記用途和引用位置
- 建立廢棄文件的標準流程

**Next.js 遷移準備**: 此次清理為即將到來的框架遷移掃清了障礙，確保容器化架構的清晰性。

---

---

## 🔍 基於穩定代碼庫新發現技術債務

### 1. Django 配置完整性檢查缺失
**發現位置**: backend/settings/ 模組
**問題描述**: 缺少環境變數完整性檢查機制
**建議方案**: 實現 REQUIRED_ENV_VARS 檢查（已創建 Issue #184）
**優先級**: P2:加速

### 2. API 監控機制待實現
**發現位置**: Django middleware 配置
**問題描述**: 缺少請求性能監控和錯誤追蹤
**建議方案**: 添加 API 監控 middleware（已創建 Issue #185）
**優先級**: P2:加速

### 3. 爬蟲程式碼中的 TODO 標記
**發現位置**: 4 個檔案包含 TODO/FIXME 標記
- backend/crawler_engine/pipelines.py
- backend/crawler_engine/settings.py
- backend/crawler_engine/spiders/hjwzw_spider.py
- docs/03_DEVELOPMENT_GUIDES/crawler/conf.py
**建議方案**: 逐步清理待辦項目，提升程式碼品質
**優先級**: P3:優化

---

**最後更新**: 2025-06-28 (基於遠端 main 穩定版本 commit: 5ae3d73ad)
**分析基準**: 遠端 main 分支穩定版本代碼
**下次Review**: 完成 Issue #184, #185 後
