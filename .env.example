# ==================================================
# AI 工具鏈 API Keys (Required to enable respective provider)
# ==================================================
ANTHROPIC_API_KEY="your_anthropic_api_key_here"       # Required: Format: sk-ant-api03-...  # gitleaks:allow
PERPLEXITY_API_KEY="your_perplexity_api_key_here"     # Optional: Format: pplx-...  # gitleaks:allow
OPENAI_API_KEY="your_openai_api_key_here"             # Optional, for OpenAI/OpenRouter models. Format: sk-proj-...  # gitleaks:allow
GOOGLE_API_KEY="your_google_api_key_here"             # Optional, for Google Gemini models.  # gitleaks:allow
MISTRAL_API_KEY="your_mistral_key_here"               # Optional, for Mistral AI models.  # gitleaks:allow
XAI_API_KEY="YOUR_XAI_KEY_HERE"                       # Optional, for xAI AI models.  # gitleaks:allow
AZURE_OPENAI_API_KEY="your_azure_key_here"            # Optional, for Azure OpenAI models (requires endpoint in .taskmaster/config.json).  # gitleaks:allow
OLLAMA_API_KEY="your_ollama_api_key_here"             # Optional: For remote Ollama servers that require authentication.  # gitleaks:allow

# ==================================================
# TaskMaster 配置 (TaskMaster Configuration)
# ==================================================
TASKMASTER_USER_ID="your_unique_user_id_here"         # Required: Unique identifier for TaskMaster user tracking

# ==================================================
# Django 後端配置 (Django Backend Configuration)
# ==================================================
CORS_ALLOWED_ORIGINS="http://localhost:8000,http://127.0.0.1:8000,http://localhost:3000,http://127.0.0.1:3000,http://localhost:3001,http://127.0.0.1:3001"  # CRA (3000) + Next.js (3001) + Django (8000)
CSRF_TRUSTED_ORIGINS="http://localhost:8000,http://127.0.0.1:8000,http://localhost:3000,http://127.0.0.1:3000,http://localhost:3001,http://127.0.0.1:3001"  # 與 CORS 同步
ALLOWED_HOSTS="localhost,127.0.0.1,0.0.0.0,backend"  # 包含 Docker service name

# ==================================================
# 爬蟲系統配置 (Crawler Configuration)
# 詳細配置請參考 .env.crawler.example
# ==================================================

# ===== Redis 配置 (Unified) =====
REDIS_HOST=localhost                                   # Redis server host
REDIS_PORT=6379                                       # Redis server port (default: 6379)
REDIS_DB=0                                            # Redis database number
REDIS_URL=redis://localhost:${REDIS_PORT}/${REDIS_DB} # Complete Redis URL (overrides above if set)
REDIS_MAX_CONNECTIONS=20                              # Maximum Redis connections for crawler

# ===== PostgreSQL 配置 =====
CRAWLER_POSTGRES_HOST=localhost
CRAWLER_POSTGRES_PORT=5432
CRAWLER_POSTGRES_DB=novel_db
CRAWLER_POSTGRES_USER=postgres
CRAWLER_POSTGRES_PASSWORD=your_postgres_password_here

# ===== 爬蟲業務配置 =====
CRAWLER_MAX_WORKERS=3
CRAWLER_CHUNK_SIZE=10
CRAWLER_DELAY=1.0
CRAWLER_TIMEOUT=30
CRAWLER_RETRIES=3

# ===== 日誌配置 =====
CRAWLER_LOG_LEVEL=INFO
CRAWLER_LOG_FILE=crawler.log

# ===== 緩存配置 =====
CRAWLER_CACHE_ENABLED=true
CRAWLER_CACHE_EXPIRE=86400
CRAWLER_CACHE_PREFIX=novel_cache:
