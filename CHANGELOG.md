# Changelog

## 🚀 2025-06-21 - 重大 CI/CD 架構優化

### ✨ AWS Spot Instance 自託管 Runner 部署
- 🏗️ **部署完成**: 在 AWS EC2 Spot Instance 上成功部署 GitHub Actions Runner
- 💰 **成本優化**: 相較 GitHub 託管 Runner 節省 70% 成本
- 🔄 **自動恢復**: 使用 Auto Scaling Group 確保高可用性
- 🛡️ **環境標準化**: Amazon Linux 2023 + Python 3.11 + 完整依賴預安裝

### 🎯 Super-Job 架構重構
- 🔧 **架構革新**: 將 3 個 Job 合併為 1 個高效的 `build-and-test` Super-Job
- ⚡ **性能提升**: 消除 artifact 上傳/下載瓶頸 (主要效能提升)
- 🔄 **流程簡化**: security-check → build-and-test (線性執行)
- 📊 **執行階段**: 前端測試 → 後端測試 → 整合測試

### 🛠️ 環境一致性修正
- ✅ **pip 指令修正**: `pip` → `pip3.11` 確保指令存在
- ✅ **PostgreSQL 預安裝**: 移除 CI 中的 `apt-get` 依賴安裝
- ✅ **Playwright 優化**: 移除 `--with-deps` 避免系統套件衝突

### 📈 效能優化成果
- ⏱️ **執行時間**: CI 總時間約 5 分鐘 (Super-Job 優化)
- 🗂️ **資源複用**: 單次 checkout、service 啟動
- 🎯 **DRY 原則**: 環境變數統一管理
- 🔄 **零傳輸成本**: 完全消除 Job 間 artifact 傳輸

---

## 🔄 歷史更新

- ⚡ Switched ttkan_spider to aiohttp + Semaphore (4× faster)
- 🚚 Deprecated Node/Express backend; Django API handles all routes
