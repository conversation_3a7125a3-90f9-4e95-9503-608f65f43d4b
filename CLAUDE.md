# CLAUDE.md - NovelWebsite 開發原則

本文件定義了 NovelWebsite 專案的核心開發原則，所有開發者必須遵循。

## 🚨 核心開發原則

### 1. 異步優先 (Async-First)
- 後端使用異步框架 (Scrapy, Django 4.2+)
- **嚴禁**在 Scrapy 相關模組中使用阻塞 I/O (`requests`, 同步 `os` 調用, `asyncio.run()`)
- 所有網路 I/O 必須使用 `scrapy.Request` 或 `aiohttp`
- **關鍵**: 絕不在 Scrapy spiders 中使用 `asyncio.run()` - 會造成事件循環衝突

### 2. 遵循介面 (Interface Compliance)
- 所有爬蟲適配器必須繼承 `BaseAdapter` 介面 (`backend/novel/adapters/base_adapter.py`)
- 實作前必須閱讀並理解基礎介面定義

### 3. 配置外部化 (Externalized Configuration)
- 嚴禁在前端硬編碼業務邏輯選項（如分類、狀態）
- 所有業務配置必須透過後端 API 提供

### 4. CI 合規 (CI Compliance)
- 所有程式碼必須通過 `make ci-check`
- 不要添加不必要的 linter 忽略（特別是 `F401`）
- 使用 `logging` 模組進行調試，而非 `print()`
- CI 執行時間目標：< 2 秒（Tier 2 架構）
- **commit push完要確認CI被正確觸發並運行**

### 5. 測試覆蓋 (Test Coverage)
- 新業務邏輯必須配備對應的單元測試
- 適配器解析方法必須使用本地 fixtures 進行測試
- PR 合併前所有 CI 檢查必須通過

### 6. 依賴管理 (Dependency Management)
- 遵循「零依賴原則」：避免為少量使用引入大型依賴
- 圖示使用內聯 SVG，而非臃腫的圖示庫
- 定期審查並清理未使用的依賴
- 專案使用pnpm而非npm

### 7. 安全最佳實踐 (Security Best Practices)
- 永不提交敏感資訊（密鑰、密碼、令牌）
- 使用環境變數或 Doppler 管理敏感配置
- 可重用 Actions 必須包含安全警告和使用限制
- **安全漏洞修復流程**：
  - 使用 `pnpm audit` 定期檢查依賴漏洞
  - 高危漏洞必須立即修復（P0 優先級）
  - 使用 `pnpm install --fix-lockfile` 重新生成依賴樹
  - CI 流程包含自動安全檢查，阻塊高危漏洞

### 8. 程式碼品質 (Code Quality)
- 遵循專案既有的程式碼風格
- 使用現有的工具和函式庫，而非重新發明輪子
- 保持程式碼簡潔可讀，必要時添加註解

### 9. 效能考量 (Performance Considerations)
- 優先考慮非阻塞操作
- 使用批量操作而非單一操作循環
- 利用快取減少重複計算和請求

### 10. 文件同步 (Documentation Sync)
- 重大變更後更新相關文件
- 保持 API 文件與實際實作同步
- 複雜邏輯必須有清晰的註解說明

### 11. 本地開發工作流程 (Local Development Workflow)
- 推送到遠端分支前確保本地測試通過
- 使用專用的 frontend-dev.Dockerfile 或 docker-compose.yml
- 使用一致的 Node 版本（與 CI 環境相同）
- 直接運行 ARM64，避免跨平台問題
- 保留 turbo.json / pnpm-workspace.yaml 做快取
- 本地使用 pnpm install / pnpm dev
- 使用 volume 掛載程式碼目錄
- 支持程式碼熱重載：修改代碼 -> 容器立即重載 -> 快速查看結果
- 調試 build：直接運行 `docker build -t my-local-test -f infra/docker/frontend-tier2.Dockerfile .`
- 檢查 BuildKit Cache layer 是否有效
- 非必要時不需要每次 push 都測試全部流程

### 12. 本地開發建議
- 平時：
  - 用 local-dev-test.sh 當作大多數日常開發啟動的 sanity check。
  - 不必每次都推 PR 才看 CI 結果，尤其是 ECR Cache Layer，可以先在本機測到快取命中率正常。
- 變更底層構建邏輯（如 Base Image / Dockerfile 重構）：
  - 先執行 local-build-test.sh，快速跑兩次看快取是否有效。
  - 再切換 local-dev-test.sh 確認開發容器起得來。

### 13. 開發工作流程詳細指南
- 開發 → 測試 → 部署的主要流程
- 本地開發:
  - 本地寫代碼
  - 多個 Git worktree 開多分支同步開發
  - 每個分支對應一個功能/Issue
- 本機測試:
  - 不是每次都推上 GitHub Actions，而是先：
    - 跑 local-build-test.sh → 快速檢查 BuildKit Cache 有效、Base Image 沒壞
    - 跑 local-dev-test.sh → docker-compose.dev.yml 內啟動 apps/web-next，測試熱重載、端口開啟、頁面可訪問
    - ARM 架構本機沒問題，只要 Dockerfile 沒綁死 AMD 指令集
- 本機 Git 提交:
  - commit 後先在本機做一次 turbo run build + turbo run lint，保證 Turbo pipeline 在本地沒爛掉
  - 用 pre-commit 勾住格式化與最小檢查（可選）

---

[... 文件的其餘部分保持不變 ...]
