module.exports = {
  ci: {
    collect: {
      staticDistDir: './build',
      url: ['http://localhost/'],
      numberOfRuns: 1, // Minimal setup - only run once
      settings: {
        preset: 'desktop',
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
    assert: {
      preset: 'lighthouse:recommended',
      assertions: {
        // --- 暫時關閉當前階段不關心的 PWA 相關檢查 ---
        'installable-manifest': 'off',
        'maskable-icon': 'off',

        // --- 將某些錯誤降級為警告，這樣它們就不會讓 CI 失敗 ---
        'csp-xss': ['warn', { minScore: 0 }],
        'errors-in-console': ['warn', { minScore: 0 }],
        'is-on-https': ['warn', { minScore: 0 }],

        // --- 為性能設定一個更合理的初始門檻 ---
        'categories:performance': ['error', { minScore: 0.5 }], // 初期目標是50分
        'categories:accessibility': ['warn', { minScore: 0.5 }],
        'categories:best-practices': ['warn', { minScore: 0.5 }],
        'categories:seo': ['warn', { minScore: 0.5 }],

        // --- 放寬具體的性能指標 ---
        'total-byte-weight': ['warn', { maxNumericValue: 2000000 }], // 2MB
        'unused-javascript': ['warn', { maxNumericValue: 200000 }], // 200KB
        'bootup-time': ['warn', { minScore: 0 }],
        'mainthread-work-breakdown': ['warn', { minScore: 0 }],
        'dom-size': ['warn', { minScore: 0 }],
        'server-response-time': ['warn', { minScore: 0 }],
      },
    },
  },
};
