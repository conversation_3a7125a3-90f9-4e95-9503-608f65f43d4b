import React from 'react';
import { ReactNode } from 'react';

interface LayoutProps {
  /** 子組件內容 */
  children: ReactNode;
  /** 最大寬度變體 */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'reading';
  /** 內邊距變體 */
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  /** 是否居中對齊 */
  centered?: boolean;
  /** 是否包含安全區域內邊距 */
  safeArea?: boolean;
  /** 自定義 CSS 類別 */
  className?: string;
  /** 測試識別標記 */
  testId?: string;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  maxWidth = 'lg',
  padding = 'md',
  centered = true,
  safeArea = false,
  className = '',
  testId = 'layout',
}) => {
  // 最大寬度配置
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full',
    reading: 'max-w-reading',
  };

  // 內邊距配置
  const paddingClasses = {
    none: '',
    sm: 'p-2 sm:p-4',
    md: 'p-4 sm:p-6 lg:p-8',
    lg: 'p-6 sm:p-8 lg:p-12',
    xl: 'p-8 sm:p-12 lg:p-16',
  };

  // 組合 CSS 類別
  const containerClasses = [
    'w-full',
    maxWidthClasses[maxWidth],
    paddingClasses[padding],
    centered ? 'mx-auto' : '',
    safeArea ? 'safe-area-padding' : '',
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={containerClasses} data-testid={testId}>
      {children}
    </div>
  );
};

export default Layout;
