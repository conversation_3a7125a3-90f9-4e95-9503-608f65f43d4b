import React from 'react';
import { render, screen } from '@testing-library/react';
import Layout from '../Layout';

describe('Layout', () => {
  it('renders children correctly', () => {
    render(
      <Layout>
        <div>Test content</div>
      </Layout>
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies default props correctly', () => {
    render(
      <Layout testId="test-layout">
        <div>Content</div>
      </Layout>
    );

    const layout = screen.getByTestId('test-layout');
    expect(layout).toHaveClass('max-w-4xl'); // lg maxWidth
    expect(layout).toHaveClass('p-4', 'sm:p-6', 'lg:p-8'); // md padding
    expect(layout).toHaveClass('mx-auto'); // centered
    expect(layout).toHaveClass('w-full');
  });

  it('applies different max-width variants correctly', () => {
    const { rerender } = render(
      <Layout maxWidth="sm" testId="layout">
        <div>Content</div>
      </Layout>
    );

    let layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('max-w-sm');

    rerender(
      <Layout maxWidth="xl" testId="layout">
        <div>Content</div>
      </Layout>
    );
    layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('max-w-6xl');

    rerender(
      <Layout maxWidth="reading" testId="layout">
        <div>Content</div>
      </Layout>
    );
    layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('max-w-reading');

    rerender(
      <Layout maxWidth="full" testId="layout">
        <div>Content</div>
      </Layout>
    );
    layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('max-w-full');
  });

  it('applies different padding variants correctly', () => {
    const { rerender } = render(
      <Layout padding="none" testId="layout">
        <div>Content</div>
      </Layout>
    );

    let layout = screen.getByTestId('layout');
    expect(layout).not.toHaveClass('p-2', 'p-4', 'p-6', 'p-8');

    rerender(
      <Layout padding="sm" testId="layout">
        <div>Content</div>
      </Layout>
    );
    layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('p-2', 'sm:p-4');

    rerender(
      <Layout padding="xl" testId="layout">
        <div>Content</div>
      </Layout>
    );
    layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('p-8', 'sm:p-12', 'lg:p-16');
  });

  it('handles centered prop correctly', () => {
    const { rerender } = render(
      <Layout centered={true} testId="layout">
        <div>Content</div>
      </Layout>
    );

    let layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('mx-auto');

    rerender(
      <Layout centered={false} testId="layout">
        <div>Content</div>
      </Layout>
    );
    layout = screen.getByTestId('layout');
    expect(layout).not.toHaveClass('mx-auto');
  });

  it('handles safe area prop correctly', () => {
    const { rerender } = render(
      <Layout safeArea={false} testId="layout">
        <div>Content</div>
      </Layout>
    );

    let layout = screen.getByTestId('layout');
    expect(layout).not.toHaveClass('safe-area-padding');

    rerender(
      <Layout safeArea={true} testId="layout">
        <div>Content</div>
      </Layout>
    );
    layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('safe-area-padding');
  });

  it('applies custom className correctly', () => {
    render(
      <Layout className="custom-class another-class" testId="layout">
        <div>Content</div>
      </Layout>
    );

    const layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('custom-class', 'another-class');
  });

  it('combines all props correctly', () => {
    render(
      <Layout
        maxWidth="reading"
        padding="lg"
        centered={false}
        safeArea={true}
        className="bg-gray-100"
        testId="complex-layout"
      >
        <div>Complex content</div>
      </Layout>
    );

    const layout = screen.getByTestId('complex-layout');
    expect(layout).toHaveClass(
      'w-full',
      'max-w-reading',
      'p-6',
      'sm:p-8',
      'lg:p-12',
      'safe-area-padding',
      'bg-gray-100'
    );
    expect(layout).not.toHaveClass('mx-auto');
  });

  it('uses default testId when not provided', () => {
    render(
      <Layout>
        <div>Content</div>
      </Layout>
    );

    expect(screen.getByTestId('layout')).toBeInTheDocument();
  });

  it('handles empty className correctly', () => {
    render(
      <Layout className="" testId="layout">
        <div>Content</div>
      </Layout>
    );

    const layout = screen.getByTestId('layout');
    // Should still have default classes
    expect(layout).toHaveClass('w-full', 'max-w-4xl');
  });

  it('renders complex child structures', () => {
    render(
      <Layout testId="layout">
        <div>
          <h1>Title</h1>
          <p>Paragraph</p>
          <button>Action</button>
        </div>
      </Layout>
    );

    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.getByText('Paragraph')).toBeInTheDocument();
    expect(screen.getByText('Action')).toBeInTheDocument();
  });

  it('applies responsive padding classes correctly', () => {
    render(
      <Layout padding="md" testId="layout">
        <div>Content</div>
      </Layout>
    );

    const layout = screen.getByTestId('layout');
    expect(layout).toHaveClass('p-4'); // base padding
    expect(layout).toHaveClass('sm:p-6'); // small screen padding
    expect(layout).toHaveClass('lg:p-8'); // large screen padding
  });

  it('handles all maxWidth options', () => {
    const maxWidthOptions = ['sm', 'md', 'lg', 'xl', '2xl', 'full', 'reading'] as const;
    const expectedClasses = [
      'max-w-sm',
      'max-w-md',
      'max-w-4xl',
      'max-w-6xl',
      'max-w-7xl',
      'max-w-full',
      'max-w-reading',
    ];

    maxWidthOptions.forEach((maxWidth, index) => {
      const { unmount } = render(
        <Layout maxWidth={maxWidth} testId={`layout-${maxWidth}`}>
          <div>Content</div>
        </Layout>
      );

      const layout = screen.getByTestId(`layout-${maxWidth}`);
      expect(layout).toHaveClass(expectedClasses[index]);
      unmount();
    });
  });

  it('handles all padding options', () => {
    const paddingOptions = ['none', 'sm', 'md', 'lg', 'xl'] as const;

    paddingOptions.forEach(padding => {
      const { unmount } = render(
        <Layout padding={padding} testId={`layout-${padding}`}>
          <div>Content</div>
        </Layout>
      );

      const layout = screen.getByTestId(`layout-${padding}`);

      // Verify the layout exists and has w-full class (common to all)
      expect(layout).toBeInTheDocument();
      expect(layout).toHaveClass('w-full');

      unmount();
    });
  });
});
