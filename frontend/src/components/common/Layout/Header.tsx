import React from 'react';
import { AppBar, Toolbar, Typo<PERSON>, Button } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

const Header: React.FC = () => {
  return (
    <AppBar position="static">
      <Toolbar>
        <Typography
          variant="h6"
          component={RouterLink}
          to="/"
          sx={{ flexGrow: 1, textDecoration: 'none', color: 'inherit' }}
        >
          小說網站
        </Typography>
        <Button color="inherit" component={RouterLink} to="/search">
          搜尋
        </Button>
        <Button color="inherit" component={RouterLink} to="/login">
          登入
        </Button>
        <Button color="inherit" component={RouterLink} to="/register">
          註冊
        </Button>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
