/**
 * FontSizeSlider Component
 *
 * 一個受控的字體大小調整滑桿組件
 * 遵循 Controlled Component 模式，不包含內部狀態
 */

import React from 'react';
import styles from './FontSizeSlider.module.css';

interface FontSizeSliderProps {
  /** 當前字體大小值 */
  value: number;
  /** 字體大小變更回調 */
  onChange: (size: number) => void;
  /** 最小值 */
  min?: number;
  /** 最大值 */
  max?: number;
  /** 步進值 */
  step?: number;
  /** 是否顯示當前值標籤 */
  showValue?: boolean;
  /** 是否顯示最小/最大標籤 */
  showMinMax?: boolean;
  /** 無障礙標籤 */
  ariaLabel?: string;
  /** 自定義 CSS 類別 */
  className?: string;
  /** 測試識別標記 */
  testId?: string;
}

const FontSizeSlider: React.FC<FontSizeSliderProps> = ({
  value,
  onChange,
  min = 12,
  max = 32,
  step = 1,
  showValue = true,
  showMinMax = true,
  ariaLabel = '字體大小調整',
  className = '',
  testId = 'font-size-slider',
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(event.target.value, 10);
    onChange(newValue);
  };

  // 計算滑桿進度百分比（用於視覺化）
  const percentage = ((value - min) / (max - min)) * 100;

  return (
    <div className={`font-size-slider ${className}`} data-testid={testId}>
      <div className="flex items-center justify-between mb-2">
        <label htmlFor={`${testId}-input`} className="text-sm font-medium text-gray-700">
          字體大小
        </label>
        {showValue && (
          <span className="text-sm font-mono text-gray-600" data-testid={`${testId}-value`}>
            {value}px
          </span>
        )}
      </div>

      <div className="relative">
        <input
          id={`${testId}-input`}
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={handleChange}
          aria-label={ariaLabel}
          aria-valuemin={min}
          aria-valuemax={max}
          aria-valuenow={value}
          className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer ${styles.slider}`}
          style={{
            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${percentage}%, #e5e7eb ${percentage}%, #e5e7eb 100%)`,
          }}
        />

        {showMinMax && (
          <div className="flex justify-between mt-1">
            <span className="text-xs text-gray-500">{min}</span>
            <span className="text-xs text-gray-500">{max}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default FontSizeSlider;
