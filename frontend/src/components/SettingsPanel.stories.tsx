import type { Meta, StoryObj } from '@storybook/react-webpack5';
import { useState } from 'react';
import SettingsPanel from './SettingsPanel';
import { SettingsProvider } from '../contexts/SettingsContext';
import { CSSVariablesBridge } from './CSSVariablesBridge';
import ReaderPreview from './ReaderPreview';

const meta: Meta<typeof SettingsPanel> = {
  title: 'Components/SettingsPanel',
  component: SettingsPanel,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
SettingsPanel 是閱讀器設定的主要界面組件。

### 功能特點：
- **整合 useSettings Hook** - 自動連接到全域設定狀態
- **包含所有設定控制項** - 目前 MVP 版本僅包含字體大小調整
- **重置功能** - 可恢復到預設設定
- **響應式設計** - 適應不同螢幕尺寸

### MVP 版本功能：
- 字體大小調整滑桿
- 重置按鈕（智能禁用）
- 可選的關閉按鈕

### 未來擴展：
- 行高調整
- 主題選擇（淺色/深色/棕黃色）
- 字體族選擇（襯線體/無襯線體）
        `,
      },
    },
  },
  decorators: [
    Story => (
      <SettingsProvider>
        <CSSVariablesBridge />
        <div className="min-h-[400px] flex items-center justify-center bg-gray-50 p-8">
          <Story />
        </div>
      </SettingsProvider>
    ),
  ],
  argTypes: {
    isOpen: {
      control: 'boolean',
      description: '是否顯示面板',
      defaultValue: true,
    },
    onClose: {
      action: 'onClose',
      description: '關閉面板回調函數',
    },
    className: {
      control: 'text',
      description: '自定義 CSS 類別',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基本用法
export const Default: Story = {
  args: {
    isOpen: true,
  },
};

// 帶關閉按鈕
export const WithCloseButton: Story = {
  args: {
    isOpen: true,
    onClose: () => {}, // 使用 action 捕獲
  },
};

// 配合預覽的完整示例
export const WithPreview: Story = {
  render: function Render(args) {
    const [isPanelOpen, setIsPanelOpen] = useState(true);

    return (
      <div className="w-full max-w-6xl flex gap-8">
        {/* 設定面板 */}
        <div className="flex-shrink-0 w-80">
          <SettingsPanel {...args} isOpen={isPanelOpen} onClose={() => setIsPanelOpen(false)} />
          {!isPanelOpen && (
            <button
              onClick={() => setIsPanelOpen(true)}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              開啟設定
            </button>
          )}
        </div>

        {/* 預覽區域 */}
        <div className="flex-1">
          <ReaderPreview />
        </div>
      </div>
    );
  },
};

// 行動版布局（覆蓋式）
export const MobileLayout: Story = {
  render: function Render(args) {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <div className="relative w-full h-96">
        {/* 內容區域 */}
        <div className="p-4">
          <button
            onClick={() => setIsOpen(true)}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            開啟設定
          </button>
          <div className="mt-4">
            <ReaderPreview />
          </div>
        </div>

        {/* 覆蓋式面板 */}
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* 設定面板 */}
            <div className="fixed inset-x-0 bottom-0 z-50 animate-slide-up">
              <SettingsPanel
                {...args}
                isOpen={true}
                onClose={() => setIsOpen(false)}
                className="rounded-t-xl"
              />
            </div>
          </>
        )}
      </div>
    );
  },
  parameters: {
    layout: 'fullscreen',
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

// 關閉狀態
export const Closed: Story = {
  args: {
    isOpen: false,
  },
};

// 自定義樣式
export const CustomStyled: Story = {
  args: {
    isOpen: true,
    className: 'border-2 border-blue-300 bg-blue-50',
  },
};
