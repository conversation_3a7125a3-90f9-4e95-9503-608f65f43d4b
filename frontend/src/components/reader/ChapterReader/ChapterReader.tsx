import React from 'react';
import { Paper, Typography, Box, IconButton } from '@mui/material';
// Using inline SVG icons to avoid react-icons TypeScript issues
import { useNavigate } from 'react-router-dom';

interface ChapterReaderProps {
  title: string;
  content: string;
  prevChapter?: string;
  nextChapter?: string;
}

const ChapterReader: React.FC<ChapterReaderProps> = ({
  title,
  content,
  prevChapter,
  nextChapter,
}) => {
  const navigate = useNavigate();

  return (
    <Paper sx={{ p: 4, maxWidth: 800, mx: 'auto', my: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {title}
      </Typography>
      <Box sx={{ whiteSpace: 'pre-wrap', lineHeight: 1.8, my: 4 }}>{content}</Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
        <IconButton
          onClick={() => prevChapter && navigate(`/chapter/${prevChapter}`)}
          disabled={!prevChapter}
          aria-label="previous chapter"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
          </svg>
        </IconButton>
        <IconButton
          onClick={() => nextChapter && navigate(`/chapter/${nextChapter}`)}
          disabled={!nextChapter}
          aria-label="next chapter"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z" />
          </svg>
        </IconButton>
      </Box>
    </Paper>
  );
};

export default ChapterReader;
