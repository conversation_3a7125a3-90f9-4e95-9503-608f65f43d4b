# ReaderSettings Feature Documentation

## Overview

The ReaderSettings feature provides a customizable reading experience for users, allowing them to adjust various visual parameters to suit their preferences.

## Features

### Current (MVP)

- **Font Size Adjustment**: Users can adjust font size from 12px to 32px
- **Persistent Settings**: All settings are saved to localStorage
- **Real-time Preview**: Changes are applied immediately via CSS variables
- **Responsive Design**: Works seamlessly on desktop and mobile devices

### Planned Features

- Line height adjustment
- Theme selection (Light/Dark/Sepia)
- Font family selection (Serif/Sans-serif)
- Settings sync for authenticated users

## Architecture

```
ReaderSettings/
├── Types & Interfaces
│   └── types/reader-settings.ts
├── State Management
│   ├── contexts/SettingsContext.tsx
│   └── hooks/useSettings.ts
├── UI Components
│   ├── components/FontSizeSlider.tsx
│   ├── components/SettingsPanel.tsx
│   └── components/CSSVariablesBridge.tsx
└── Integration
    ├── App.tsx (Provider setup)
    └── pages/ReaderDemo.tsx (Usage example)
```

## Usage

### 1. Basic Setup

The ReaderSettings feature is automatically initialized in `App.tsx`:

```tsx
import { SettingsProvider } from './contexts/SettingsContext';
import { CSSVariablesBridge } from './components/CSSVariablesBridge';

function App() {
  return (
    <SettingsProvider>
      <CSSVariablesBridge />
      {/* Your app content */}
    </SettingsProvider>
  );
}
```

### 2. Using Settings in Components

```tsx
import { useSettings } from '../hooks/useSettings';

function MyReaderComponent() {
  const { settings, updateSetting } = useSettings();

  return (
    <div
      style={{
        fontSize: `${settings.fontSize}px`,
        // Or use CSS variables
        color: 'var(--reader-text-color)',
      }}
    >
      {/* Content */}
    </div>
  );
}
```

### 3. Available CSS Variables

The following CSS variables are automatically maintained:

- `--reader-font-size`: Current font size with px unit
- `--reader-line-height`: Line height multiplier
- `--reader-font-family`: Font family stack
- `--reader-bg-color`: Background color based on theme
- `--reader-text-color`: Text color based on theme
- `--reader-border-color`: Border color based on theme

### 4. Integrating the Settings Panel

```tsx
import SettingsPanel from '../components/SettingsPanel';

function ReaderPage() {
  const [showSettings, setShowSettings] = useState(false);

  return (
    <>
      <button onClick={() => setShowSettings(true)}>Settings</button>

      <SettingsPanel isOpen={showSettings} onClose={() => setShowSettings(false)} />
    </>
  );
}
```

## Testing

### Unit Tests

- `useSettings` hook: 100% coverage
- All UI components have comprehensive tests
- Run tests: `npm test`

### Visual Testing

- Storybook stories available for all components
- Run Storybook: `npm run storybook`
- View at: http://localhost:6006

## Performance Considerations

1. **Optimized Re-renders**: Settings updates only trigger re-renders in components that use the specific settings
2. **CSS Variables**: Using CSS variables avoids inline style recalculations
3. **LocalStorage Debouncing**: Consider implementing debouncing for frequent updates

## Browser Support

- All modern browsers (Chrome, Firefox, Safari, Edge)
- CSS Variables are supported in all target browsers
- LocalStorage fallback for older browsers

## Future Enhancements

1. **Backend Sync**: Sync settings with user account
2. **Presets**: Quick theme presets (Day/Night/Sepia)
3. **Advanced Typography**: Letter spacing, word spacing
4. **Reading Progress**: Track and save reading position
5. **Accessibility**: High contrast mode, dyslexia-friendly fonts

## Demo

Visit `/reader-demo` to see the ReaderSettings in action with a sample chapter.

---

_Last Updated: 2025-06-22_
