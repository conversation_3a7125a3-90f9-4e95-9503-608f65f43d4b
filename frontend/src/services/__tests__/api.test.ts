import { Chapter, Novel } from '../../types/novel';
import { getChapter<PERSON>ontent, getNovelList, getNovelDetail, searchNovels } from '../api';

// Mock axios with inline mock instance definition
jest.mock('axios', () => {
  const mockAxiosInstance = {
    get: jest.fn(),
    post: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  };

  return {
    __esModule: true,
    default: {
      create: jest.fn(() => mockAxiosInstance),
    },
    mockAxiosInstance, // Export for testing
  };
});

// Get the mock instance for assertions
// eslint-disable-next-line @typescript-eslint/no-var-requires
const axios = require('axios');
const mockAxiosInstance = axios.mockAxiosInstance;

describe('API Service Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getChapterContent', () => {
    const mockChapter: Chapter = {
      id: 1,
      title: '第一章',
      chapter_number: 1,
      content: '這是第一章的內容。\n\n這是第二段。',
      views: 100,
      updated_at: '2023-01-01T00:00:00Z',
    };

    it('should successfully fetch chapter content with valid chapterId', async () => {
      // Arrange
      const chapterId = '1';
      mockAxiosInstance.get.mockResolvedValue({ data: mockChapter });

      // Act
      const result = await getChapterContent(chapterId);

      // Assert
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(`/chapters/${chapterId}/`);
      expect(result).toEqual(mockChapter);
    });

    it('should handle empty content gracefully', async () => {
      // Arrange
      const chapterId = '2';
      const chapterWithoutContent: Chapter = {
        ...mockChapter,
        id: 2,
        content: undefined,
      };
      mockAxiosInstance.get.mockResolvedValue({ data: chapterWithoutContent });

      // Act
      const result = await getChapterContent(chapterId);

      // Assert
      expect(result).toEqual(chapterWithoutContent);
      expect(result.content).toBeUndefined();
    });

    it('should handle null response data', async () => {
      // Arrange
      const chapterId = '3';
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockAxiosInstance.get.mockResolvedValue(null);

      // Act & Assert
      await expect(getChapterContent(chapterId)).rejects.toThrow('無法載入章節內容');
      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });

    it('should handle network errors', async () => {
      // Arrange
      const chapterId = '4';
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const networkError = new Error('Network Error');
      mockAxiosInstance.get.mockRejectedValue(networkError);

      // Act & Assert
      await expect(getChapterContent(chapterId)).rejects.toThrow('無法載入章節內容');
      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });

    it('should handle server errors with detailed messages', async () => {
      // Arrange
      const chapterId = '5';
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const serverError = {
        response: {
          status: 404,
          data: { detail: '章節不存在' },
        },
      };
      mockAxiosInstance.get.mockRejectedValue(serverError);

      // Act & Assert
      await expect(getChapterContent(chapterId)).rejects.toThrow('無法載入章節內容');
      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });

    it('should log errors properly', async () => {
      // Arrange
      const chapterId = '6';
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      const error = new Error('Test error');
      mockAxiosInstance.get.mockRejectedValue(error);

      // Act
      try {
        await getChapterContent(chapterId);
      } catch (error) {
        // Expected to throw
      }

      // Assert
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching chapter content for chapterId 6:',
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe('getNovelList', () => {
    const mockNovels: Novel[] = [
      {
        id: 1,
        title: '測試小說1',
        slug: 'test-novel-1',
        author: '作者1',
        description: '測試描述1',
        cover_url: 'https://example.com/cover1.jpg',
        total_chapters: 100,
        views: 1000,
        favorites: 50,
        status: 'ongoing',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
      },
    ];

    it('should fetch novel list successfully', async () => {
      // Arrange
      const mockResponse = {
        novels: mockNovels,
        total: 1,
        page: 1,
        limit: 10,
      };
      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });

      // Act
      const result = await getNovelList(1, 10);

      // Assert
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/novels/', {
        params: { page: 1, limit: 10 },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle network errors gracefully', async () => {
      // Arrange
      const networkError = new Error('Network Error');
      mockAxiosInstance.get.mockRejectedValue(networkError);
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act
      const result = await getNovelList();

      // Assert
      expect(result).toEqual({
        novels: [],
        total: 0,
        page: 1,
        limit: 10,
      });
      expect(consoleErrorSpy).toHaveBeenCalled();

      consoleErrorSpy.mockRestore();
    });
  });

  describe('getNovelDetail', () => {
    const mockNovelDetail: Novel = {
      id: 1,
      title: '測試小說詳情',
      slug: 'test-novel-detail',
      author: '作者',
      description: '詳細描述',
      cover_url: 'https://example.com/cover.jpg',
      total_chapters: 50,
      views: 500,
      favorites: 25,
      status: 'ongoing',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-02T00:00:00Z',
      chapters: [
        {
          id: 1,
          title: '第一章',
          chapter_number: 1,
          views: 100,
          updated_at: '2023-01-01T00:00:00Z',
        },
      ],
    };

    it('should fetch novel detail successfully', async () => {
      // Arrange
      const novelSlug = 'test-novel-detail';
      mockAxiosInstance.get.mockResolvedValue({ data: mockNovelDetail });

      // Act
      const result = await getNovelDetail(novelSlug);

      // Assert
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(`/novels/${novelSlug}/`);
      expect(result).toEqual(mockNovelDetail);
    });

    it('should handle errors with specific error message', async () => {
      // Arrange
      const novelSlug = 'non-existent';
      mockAxiosInstance.get.mockRejectedValue(new Error('Not found'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act & Assert
      await expect(getNovelDetail(novelSlug)).rejects.toThrow('無法載入小說詳情');
      expect(consoleErrorSpy).toHaveBeenCalled();

      consoleErrorSpy.mockRestore();
    });
  });

  describe('searchNovels', () => {
    it('should search novels successfully', async () => {
      // Arrange
      const query = '測試';
      const mockSearchResult = {
        novels: [],
        total: 0,
        page: 1,
        limit: 10,
      };
      mockAxiosInstance.get.mockResolvedValue({ data: mockSearchResult });

      // Act
      const result = await searchNovels(query);

      // Assert
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/novels/search', { params: { query } });
      expect(result).toEqual(mockSearchResult);
    });

    it('should handle search errors gracefully', async () => {
      // Arrange
      const query = '錯誤測試';
      const searchError = new Error('Search failed');
      mockAxiosInstance.get.mockRejectedValue(searchError);
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Act
      const result = await searchNovels(query);

      // Assert
      expect(result).toEqual({ novels: [], total: 0, page: 1, limit: 10 });
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error searching novels:', searchError);

      consoleErrorSpy.mockRestore();
    });
  });

  describe('API Configuration', () => {
    it('should create axios instance with correct configuration', () => {
      // This test verifies that the API module loads correctly
      expect(getChapterContent).toBeDefined();
      expect(getNovelList).toBeDefined();
      expect(getNovelDetail).toBeDefined();
      expect(searchNovels).toBeDefined();
    });
  });

  describe('Error Handling Integration', () => {
    it('should provide consistent error messages across different API methods', async () => {
      // Arrange
      const error = new Error('Generic API Error');
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockAxiosInstance.get.mockRejectedValue(error);

      // Act & Assert
      await expect(getChapterContent('1')).rejects.toThrow('無法載入章節內容');
      await expect(getNovelDetail('test')).rejects.toThrow('無法載入小說詳情');

      // For getNovelList and searchNovels, they should return default values instead of throwing
      const novelListResult = await getNovelList();
      const searchResult = await searchNovels('test');

      expect(novelListResult).toEqual({ novels: [], total: 0, page: 1, limit: 10 });
      expect(searchResult).toEqual({ novels: [], total: 0, page: 1, limit: 10 });
      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });
  });
});
