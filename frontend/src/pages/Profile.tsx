import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const Profile: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return <div>請先登入</div>;
  }

  return (
    <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold mb-6">個人資料</h1>
      <div className="space-y-4">
        <div>
          <label className="font-medium">使用者名稱:</label>
          <p>{user.username}</p>
        </div>
        <div>
          <label className="font-medium">電子郵件:</label>
          <p>{user.email}</p>
        </div>
      </div>
    </div>
  );
};

export default Profile;
