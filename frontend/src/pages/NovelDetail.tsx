import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { getNovelDetail } from '../services/api'; // Removed getWenxianNovel
import { Novel } from '../types/novel'; // Using global Novel type

// Removed local Novel interface and NovelDetailProps that uses it

const NovelDetail: React.FC = () => {
  // Simplified props
  const { novelSlug } = useParams<{ novelSlug: string }>(); // Changed id to novelSlug
  const [novel, setNovel] = useState<Novel | null>(null);
  const [loading, setLoading] = useState(true); // Default to true
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNovelDetails = async () => {
      // Renamed function
      if (!novelSlug) return;

      try {
        setLoading(true);
        // Removed special handling for "wenxian"
        const data = await getNovelDetail(novelSlug); // Use novelSlug
        setNovel(data);
      } catch (err) {
        const errorMessage = '無法載入小說資訊';
        setError(errorMessage);
        console.error(`Error fetching novel details for ${novelSlug}:`, err); // Use novelSlug in error
      } finally {
        setLoading(false);
      }
    };

    fetchNovelDetails();
  }, [novelSlug]); // Updated dependency array

  if (loading) return <div className="text-center p-4">載入中...</div>;
  if (error) return <div className="text-center text-red-500 p-4">{error}</div>;
  if (!novel) return <div className="text-center p-4">找不到小說</div>;

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex flex-col md:flex-row gap-6">
          <img
            src={novel.cover_url || novel.coverUrl || novel.coverImage || '/default-cover.jpg'}
            alt={novel.title}
            className="w-48 h-64 object-cover rounded-lg shadow-md"
          />
          <div className="flex-1">
            <h1 className="text-3xl font-bold mb-2">{novel.title}</h1>
            <p className="text-gray-600 mb-4">作者：{novel.author}</p>
            <div className="space-y-2">
              <p className="text-gray-700">{novel.description || '暫無簡介'}</p>
              {/* Removed status and totalChapters as they are not in the global Novel type for now */}
              {/* These can be added back if the global type is updated */}
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h2 className="text-xl font-bold mb-4">章節列表</h2>
          {novel.chapters && novel.chapters.length > 0 ? (
            <ul className="space-y-2">
              {novel.chapters.map(chapter => (
                <li key={chapter.id} className="border-b last:border-b-0 py-2">
                  <Link
                    to={`/novels/${novelSlug}/chapters/${chapter.id}`} // Use novelSlug and chapter.id
                    className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                  >
                    {chapter.title || `第 ${chapter.chapter_number || chapter.chapterNumber} 章`}
                  </Link>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-600">暫無章節。</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default NovelDetail;
