# 🎨 NovelWebsite 樣式指南

本文檔定義了 NovelWebsite 前端開發的樣式規範和決策框架。

## 📋 核心原則

### 1. 技術棧組合

我們採用「MUI + Tailwind CSS + styled-components」的組合方案：

- **Material-UI (MUI)**: 提供完整的組件庫和設計系統
- **Tailwind CSS**: 處理快速樣式調整和響應式設計
- **styled-components**: 處理複雜的動態樣式和主題定制

### 2. 五個黃金規則

在決定使用哪種樣式方案時，請遵循以下規則：

#### 規則 1: 優先使用 MUI 組件和其內建樣式系統

```tsx
// ✅ 推薦：使用 MUI 的 sx prop
<Button
  sx={{
    mt: 2,
    bgcolor: 'primary.main',
    '&:hover': { bgcolor: 'primary.dark' }
  }}
>
  點擊我
</Button>

// ❌ 避免：混用多種方式
<Button className="mt-4 bg-blue-500" style={{ marginTop: '16px' }}>
  點擊我
</Button>
```

#### 規則 2: 使用 Tailwind 處理布局和間距

```tsx
// ✅ 推薦：Tailwind 處理響應式布局
<div className="container mx-auto px-4 md:px-6 lg:px-8">
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {/* 內容 */}
  </div>
</div>

// ✅ 推薦：結合 MUI 組件
<Card className="p-4 md:p-6">
  <CardContent>
    {/* 內容 */}
  </CardContent>
</Card>
```

#### 規則 3: 使用 styled-components 處理複雜動態樣式

```tsx
// ✅ 推薦：複雜動畫和條件樣式
const AnimatedCard = styled(Card)<{ isActive: boolean }>`
  transition: all 0.3s ease;
  transform: ${props => (props.isActive ? 'scale(1.05)' : 'scale(1)')};
  box-shadow: ${props =>
    props.isActive ? '0 8px 16px rgba(0,0,0,0.2)' : '0 2px 4px rgba(0,0,0,0.1)'};
`;

// ✅ 推薦：主題相關的複雜樣式
const ThemedSection = styled.section`
  background: ${props =>
    props.theme.palette.mode === 'dark'
      ? 'linear-gradient(45deg, #1a1a1a 0%, #2d2d2d 100%)'
      : 'linear-gradient(45deg, #f5f5f5 0%, #ffffff 100%)'};
`;
```

#### 規則 4: 避免樣式衝突

```tsx
// ✅ 推薦：使用 MUI 的 styled 來確保優先級
import { styled } from '@mui/material/styles';

const CustomButton = styled(Button)(({ theme }) => ({
  // 這樣可以確保樣式優先級正確
  padding: theme.spacing(2),
  '&.custom-class': {
    backgroundColor: theme.palette.primary.main,
  }
}));

// ❌ 避免：直接覆蓋 MUI 樣式
.MuiButton-root {
  padding: 16px !important; /* 避免使用 !important */
}
```

#### 規則 5: 保持一致性

```tsx
// ✅ 推薦：在同一組件中保持樣式方法一致
function NovelCard({ novel }) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardMedia sx={{ height: 200 }} image={novel.cover} />
      <CardContent>
        <Typography variant="h6" className="line-clamp-2">
          {novel.title}
        </Typography>
      </CardContent>
    </Card>
  );
}

// ❌ 避免：混亂的樣式方法
function NovelCard({ novel }) {
  return (
    <Card
      className="hover:shadow-lg"
      style={{ transition: 'all 0.3s' }}
      sx={{ '&:hover': { transform: 'scale(1.02)' } }}
    >
      {/* 太多樣式方法混用 */}
    </Card>
  );
}
```

## 🎯 實際應用場景

### 場景 1: 響應式小說卡片網格

```tsx
// 結合三種技術的最佳實踐
import { Card, CardContent, Typography, Skeleton } from '@mui/material';
import styled from 'styled-components';

const NovelGrid = styled.div`
  @media (min-width: ${props => props.theme.breakpoints.values.sm}px) {
    .novel-card {
      transition: transform 0.2s ease;
      &:hover {
        transform: translateY(-4px);
      }
    }
  }
`;

function NovelList({ novels, loading }) {
  return (
    <NovelGrid className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {loading
        ? Array(8)
            .fill(0)
            .map((_, i) => (
              <Skeleton key={i} variant="rectangular" height={300} className="novel-card" />
            ))
        : novels.map(novel => (
            <Card key={novel.id} className="novel-card">
              <CardContent>
                <Typography variant="h6" className="line-clamp-2">
                  {novel.title}
                </Typography>
              </CardContent>
            </Card>
          ))}
    </NovelGrid>
  );
}
```

### 場景 2: 主題切換的導航欄

```tsx
const ThemedAppBar = styled(AppBar)`
  backdrop-filter: blur(8px);
  background-color: ${props =>
    props.theme.palette.mode === 'dark' ? 'rgba(18, 18, 18, 0.9)' : 'rgba(255, 255, 255, 0.9)'};
`;

function Navigation() {
  return (
    <ThemedAppBar position="sticky" elevation={0}>
      <Toolbar className="container mx-auto px-4">
        <Typography variant="h6" className="flex-grow">
          NovelWebsite
        </Typography>
        <nav className="hidden md:flex space-x-4">{/* 導航項目 */}</nav>
      </Toolbar>
    </ThemedAppBar>
  );
}
```

## 📐 響應式斷點

統一使用 MUI 的斷點系統：

```javascript
const breakpoints = {
  xs: 0, // 手機
  sm: 600, // 平板豎屏
  md: 900, // 平板橫屏
  lg: 1200, // 桌面
  xl: 1536, // 大屏幕
};
```

在 Tailwind 中對應：

- `sm:` = 640px (略大於 MUI 的 sm)
- `md:` = 768px (略小於 MUI 的 md)
- `lg:` = 1024px (略小於 MUI 的 lg)
- `xl:` = 1280px (略小於 MUI 的 xl)

## 🎨 顏色系統

優先使用 MUI 主題顏色：

```tsx
// 在 styled-components 中使用
const StyledDiv = styled.div`
  color: ${props => props.theme.palette.primary.main};
  background: ${props => props.theme.palette.background.paper};
`;

// 在 sx prop 中使用
<Box sx={{
  color: 'primary.main',
  bgcolor: 'background.paper'
}} />

// Tailwind 僅用於標準顏色
<div className="text-gray-600 bg-gray-100">
  {/* 內容 */}
</div>
```

## 📦 組件開發流程

1. **基礎結構**: 使用 MUI 組件作為基礎
2. **響應式布局**: 使用 Tailwind 的網格系統
3. **交互樣式**: 使用 MUI 的 sx prop
4. **複雜動畫**: 使用 styled-components
5. **主題適配**: 確保支持暗色模式

## 🚀 性能最佳實踐

1. **避免運行時樣式計算**

   ```tsx
   // ❌ 避免
   <div style={{ margin: `${spacing * 8}px` }}>

   // ✅ 推薦
   <div className="m-8">
   ```

2. **使用 CSS-in-JS 的靜態樣式**

   ```tsx
   // ✅ 推薦：靜態樣式
   const StaticCard = styled(Card)`
     padding: 16px;
     border-radius: 8px;
   `;
   ```

3. **條件類名使用 clsx**

   ```tsx
   import clsx from 'clsx';

   <div className={clsx(
     'base-class',
     isActive && 'active-class',
     isMobile ? 'mobile-class' : 'desktop-class'
   )}>
   ```

## 📱 移動優先設計

1. **默認樣式針對移動設備**
2. **使用 `md:` 和 `lg:` 擴展到大屏幕**
3. **觸摸目標至少 44x44 像素**
4. **適當的字體大小和行高**

## 🔍 調試技巧

1. **MUI 樣式調試**

   ```javascript
   // 在瀏覽器控制台
   window.__MUI_STYLES__;
   ```

2. **Tailwind 類名檢查**

   - 使用 Tailwind CSS IntelliSense 擴展
   - 檢查編譯後的 CSS 文件

3. **styled-components 調試**
   - 使用 React DevTools
   - 啟用 displayName 選項

## 📝 總結

這個混合方案讓我們能夠：

- 快速構建符合 Material Design 的界面
- 靈活處理響應式設計
- 優雅地處理複雜交互
- 保持代碼的可維護性

記住：**選擇合適的工具解決合適的問題**。
