# Storybook 使用指南

## 🚀 快速開始

```bash
# 安裝依賴
pnpm install

# 啟動 Storybook
pnpm run storybook

# 建置靜態 Storybook
pnpm run build-storybook
```

訪問 http://localhost:6006 查看元件文檔。

## 📝 撰寫 Story

### 基本結構

```tsx
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'danger'],
    },
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: '按鈕',
  },
};
```

## 🎨 測試響應式設計

### 使用 Viewport 工具

```tsx
export const MobileView: Story = {
  args: {
    // ... props
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
};
```

### 測試深色模式

```tsx
export const DarkMode: Story = {
  args: {
    // ... props
  },
  globals: {
    theme: 'dark',
  },
};
```

## 🧩 整合樣式系統

Storybook 已配置支援：

1. **Tailwind CSS** - 所有工具類可直接使用
2. **MUI Theme** - 透過 decorator 提供主題
3. **styled-components** - 正常運作
4. **CSS Modules** - 自動處理

## 📦 元件組織建議

```
src/components/
├── Button/
│   ├── Button.tsx
│   ├── Button.stories.tsx
│   ├── Button.test.tsx
│   └── Button.module.css
├── NovelCard/
│   ├── NovelCard.tsx
│   ├── NovelCard.stories.tsx
│   └── NovelCard.test.tsx
└── ...
```

## 🔍 實用功能

### Controls 面板

- 即時調整元件 props
- 測試各種狀態組合

### Actions 面板

- 查看事件觸發
- 驗證回調函數

### Docs 頁面

- 自動生成的元件文檔
- Props 表格
- 使用範例

## 🎯 最佳實踐

1. **每個元件都要有 Story**
2. **涵蓋所有狀態** - 預設、載入中、錯誤、空白等
3. **測試邊界情況** - 長文字、缺少資料等
4. **響應式測試** - 至少包含手機和桌面版
5. **無障礙測試** - 使用 a11y addon 檢查

## 🛠️ 進階配置

如需修改 Storybook 配置：

- `.storybook/main.js` - 主要配置
- `.storybook/preview.js` - 全域設定
- `.storybook/preview-head.html` - HTML head 內容
