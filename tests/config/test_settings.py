import os


import importlib


def load_settings():
    module = importlib.import_module("backend.config.settings")
    module.get_settings.cache_clear()
    return module.get_settings


def test_env_layering(tmp_path, monkeypatch):
    (tmp_path / ".env").write_text("SECRET_KEY=env\n")
    (tmp_path / ".env.local").write_text("SECRET_KEY=local\n")
    (tmp_path / ".env.production").write_text("SECRET_KEY=prod\n")

    monkeypatch.chdir(tmp_path)
    monkeypatch.setenv("ENVIRONMENT", "production")
    monkeypatch.setenv("DB_PASSWORD", "x")
    monkeypatch.setenv("SECRET_KEY", "prod-key")

    get_settings = load_settings()
    settings = get_settings()
    assert settings.security.secret_key == "prod"


def test_cli_override(monkeypatch):
    apply_cli_config_overrides = importlib.import_module(
        "backend.manage"
    ).apply_cli_config_overrides
    argv = ["manage.py", "runserver", "--config", "SECRET_KEY=cli"]
    new_argv = apply_cli_config_overrides(argv)
    assert os.environ["SECRET_KEY"] == "cli"
    assert "--config" not in new_argv


def test_schema_generation():
    os.environ.setdefault("DB_PASSWORD", "x")
    os.environ.setdefault("SECRET_KEY", "test")
    get_settings = load_settings()
    schema = get_settings().model_json_schema()
    assert isinstance(schema, dict)
    assert "title" in schema
