# ==================================================
# 爬蟲系統配置環境變數範例文件
# 複製此文件為 .env 並填入實際值
# ==================================================

# ===== Redis 配置 =====
CRAWLER_REDIS_HOST=localhost
CRAWLER_REDIS_PORT=6379
CRAWLER_REDIS_DB=0
CRAWLER_REDIS_MAX_CONNECTIONS=20

# ===== PostgreSQL 配置 =====
CRAWLER_POSTGRES_HOST=localhost
CRAWLER_POSTGRES_PORT=5432
CRAWLER_POSTGRES_DB=novel_db
CRAWLER_POSTGRES_USER=postgres
CRAWLER_POSTGRES_PASSWORD=your_postgres_password_here
CRAWLER_POSTGRES_MIN_CONNECTIONS=5
CRAWLER_POSTGRES_MAX_CONNECTIONS=20

# ===== Scrapy 配置 =====
CRAWLER_SCRAPY_CONCURRENT_REQUESTS=16
CRAWLER_SCRAPY_DOWNLOAD_DELAY=0.5
CRAWLER_SCRAPY_USER_AGENT="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# ===== 爬蟲業務配置 =====
CRAWLER_MAX_WORKERS=3
CRAWLER_CHUNK_SIZE=10
CRAWLER_DELAY=1.0
CRAWLER_TIMEOUT=30
CRAWLER_RETRIES=3

# ===== 日誌配置 =====
CRAWLER_LOG_LEVEL=INFO
CRAWLER_LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
CRAWLER_LOG_FILE=crawler.log

# ===== 緩存配置 =====
CRAWLER_CACHE_ENABLED=true
CRAWLER_CACHE_EXPIRE=86400
CRAWLER_CACHE_PREFIX=novel_cache:

# ===== 錯誤處理配置 =====
CRAWLER_RETRYABLE_ERRORS="ConnectionError,TimeoutError,HTTPError"
CRAWLER_MAX_RETRIES=3
CRAWLER_RETRY_DELAY=5
