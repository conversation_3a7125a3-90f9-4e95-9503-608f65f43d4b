version: '3.8'

services:
  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - dev_network

  dev-container:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/workspace
      - /var/run/docker.sock:/var/run/docker.sock
    working_dir: /workspace
    environment:
      - PYTHONPATH=/workspace/backend
      - DJANGO_SETTINGS_MODULE=config.django_settings
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    networks:
      - dev_network
    depends_on:
      - redis
    stdin_open: true
    tty: true
    command: bash

  prometheus:
    image: prom/prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - dev_network
    depends_on:
      - dev-container

networks:
  dev_network:
    driver: bridge

volumes:
  redis_data:
