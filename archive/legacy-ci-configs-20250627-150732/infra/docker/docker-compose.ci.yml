# CI Deployment Simulation Docker Compose Configuration
# 防線三：部署模擬測試專用配置
# 最小化配置，專為 CI 環境設計

version: '3.8'

services:
  # PostgreSQL 測試資料庫
  postgres-ci:
    image: postgres:15-alpine
    container_name: novel-postgres-ci
    environment:
      POSTGRES_DB: test_novelwebsite_ci
      POSTGRES_USER: ci_user
      POSTGRES_PASSWORD: ci_password
      POSTGRES_INITDB_ARGS: --encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5433:5432"  # 使用不同端口避免衝突
    tmpfs:
      # 使用內存文件系統提高性能
      - /var/lib/postgresql/data
    command: >
      postgres
      -c log_statement=none
      -c log_destination=stderr
      -c logging_collector=off
      -c max_connections=20
      -c shared_buffers=32MB
      -c effective_cache_size=128MB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ci_user -d test_novelwebsite_ci"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 10s
    networks:
      - ci_network

  # Redis 快取服務
  redis-ci:
    image: redis:7-alpine
    container_name: novel-redis-ci
    ports:
      - "6380:6379"  # 使用不同端口避免衝突
    tmpfs:
      # 使用內存文件系統
      - /data
    command: >
      redis-server
      --maxmemory 64mb
      --maxmemory-policy allkeys-lru
      --save ""
      --appendonly no
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 5s
    networks:
      - ci_network

  # 後端應用服務
  backend-ci:
    build:
      context: ../..
      dockerfile: infra/docker/backend-ci.Dockerfile
    container_name: novel-backend-ci
    environment:
      # Django 設置
      - DJANGO_SETTINGS_MODULE=config.django_settings
      - PYTHONPATH=/workspace/backend
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1

      # 資料庫設置
      - DATABASE_URL=*************************************************/test_novelwebsite_ci
      - DB_HOST=postgres-ci
      - DB_PORT=5432
      - DB_NAME=test_novelwebsite_ci
      - DB_USER=ci_user
      - DB_PASSWORD=ci_password

      # Redis 設置
      - REDIS_URL=redis://redis-ci:6379/0
      - REDIS_HOST=redis-ci
      - REDIS_PORT=6379
      - REDIS_DB=0

      # CI 專用設置
      - DEBUG=False
      - SECRET_KEY=ci-test-secret-key-not-for-production
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend-ci
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend-ci:3000

      # 爬蟲設置（最小化）
      - CRAWLER_MAX_WORKERS=1
      - CRAWLER_CHUNK_SIZE=5
      - CRAWLER_DELAY=0.5
      - CRAWLER_TIMEOUT=10
      - CRAWLER_RETRIES=1
    ports:
      - "8001:8000"  # 使用不同端口避免衝突
    volumes:
      - ../..:/workspace
    working_dir: /workspace
    depends_on:
      postgres-ci:
        condition: service_healthy
      redis-ci:
        condition: service_healthy
    command: >
      bash -c "
        echo '🚀 Starting CI Backend Deployment Simulation...' &&
        cd backend &&
        echo '📋 Running Django checks...' &&
        python manage.py check --deploy &&
        echo '🗄️ Running database migrations...' &&
        python manage.py migrate --noinput &&
        echo '📦 Collecting static files...' &&
        python manage.py collectstatic --noinput --clear &&
        echo '🔥 Starting Gunicorn server...' &&
        gunicorn config.wsgi:application \
          --bind 0.0.0.0:8000 \
          --workers 2 \
          --worker-class sync \
          --timeout 30 \
          --keep-alive 2 \
          --max-requests 100 \
          --max-requests-jitter 10 \
          --access-logfile - \
          --error-logfile - \
          --log-level info
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/", "||", "exit", "1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - ci_network

  # 前端應用服務（可選，用於完整部署測試）
  frontend-ci:
    build:
      context: ../..
      dockerfile: infra/docker/frontend-ci.Dockerfile
    container_name: novel-frontend-ci
    environment:
      - NODE_ENV=production
      - CI=true
      - REACT_APP_API_URL=http://backend-ci:8000/api/v1
      - REACT_APP_MEDIA_URL=http://backend-ci:8000/media
      - REACT_APP_TITLE=瘟仙小說 (CI Test)
      - REACT_APP_DESCRIPTION=CI 部署測試環境
      - REACT_APP_ENABLE_ADS=false
      - REACT_APP_ENABLE_ANALYTICS=false
    ports:
      - "3001:3000"  # 使用不同端口避免衝突
    volumes:
      - ../..:/workspace
    working_dir: /workspace/frontend
    depends_on:
      backend-ci:
        condition: service_healthy
    command: >
      bash -c "
        echo '🚀 Starting CI Frontend Deployment Simulation...' &&
        echo '📦 Installing dependencies...' &&
        pnpm install --frozen-lockfile &&
        echo '🔨 Building application...' &&
        pnpm build &&
        echo '🌐 Starting production server...' &&
        pnpm preview --host 0.0.0.0 --port 3000
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/", "||", "exit", "1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    networks:
      - ci_network

  # Nginx 反向代理（模擬生產環境）
  nginx-ci:
    image: nginx:alpine
    container_name: novel-nginx-ci
    ports:
      - "8080:80"
    volumes:
      - ./nginx-ci.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      backend-ci:
        condition: service_healthy
      frontend-ci:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health", "||", "exit", "1"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - ci_network

# 網路配置
networks:
  ci_network:
    driver: bridge
    name: novel_ci_network

# 卷配置（全部使用臨時卷）
volumes:
  postgres_ci_data:
    driver: local
  redis_ci_data:
    driver: local
