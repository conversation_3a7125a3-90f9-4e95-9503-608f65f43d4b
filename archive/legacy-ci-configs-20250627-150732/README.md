# 舊版 CI 配置文件備份

## 備份時間
Fri Jun 27 15:07:32 CST 2025

## 備份原因
遷移到基於 Monorepo 架構的新版 CI 部署模擬系統 v2

## 新版配置文件位置
- `docker-compose.ci.yml` (根目錄)
- `infra/docker/backend-ci-v2.Dockerfile`
- `infra/docker/frontend-ci-v2.Dockerfile`
- `scripts/ci/deployment-simulation-test-v2.sh`

## 如何回滾（如果需要）
```bash
# 從備份恢復舊配置
cp -r archive/legacy-ci-configs-20250627-150732/infra/docker/* infra/docker/
cp -r archive/legacy-ci-configs-20250627-150732/scripts/ci/* scripts/ci/
```

## 新版優勢
1. 基於 Monorepo 架構設計
2. 智能 Docker 層緩存
3. 執行時間從 5 分鐘縮短到 30-60 秒
4. 更清晰的路徑配置
5. 更好的錯誤處理
