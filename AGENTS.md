## Overview

This file provides instructions and guidelines for AI agents (and human collaborators) working within this repository. It applies to all files and directories within the project root unless overridden by a more deeply nested `AGENTS.md`.

The project is a monorepo for a "小說app" (Novel Web App) consisting of:

- **Backend (Django)**
  - `backend/novel/`: Django project providing REST APIs, web crawler, and async processing.
  - `backend/apps/`: Core business logic modules (catalog, users, etc.)
- **Frontend (React + Tailwind CSS)**
  - `frontend/src/`: React application that consumes backend APIs.
- **Infrastructure**
  - `infra/`: AWS deployment configs, Docker files, GitHub Actions workflows.
  - Tier 2 CI/CD architecture with ECR and self-hosted runners.

Agents should use this document as a reference when modifying code, running tests, or committing changes.

---

## Table of Contents

1. [General Instructions](#general-instructions)
2. [Working with Git](#working-with-git)
3. [Coding Conventions](#coding-conventions)
4. [Running & Testing Backend](#running--testing-backend)
   - [Django Backend](#django-backend)
5. [Running & Testing Frontend](#running--testing-frontend)
6. [Crawler & Async Processing](#crawler--async-processing)
7. [CI/CD & Deployment](#cicd--deployment)
8. [Scope & Precedence of AGENTS.md](#scope--precedence-of-agentsmd)

---

## General Instructions

- **Read Before You Modify**
  Before touching any file, review any `AGENTS.md` in the directory tree above or within nested folders. Follow instructions in more deeply nested `AGENTS.md` when conflicts arise.

- **Respect Project Structure**

  - The Django project lives under `backend/novel/`.
  - Core business apps are under `backend/apps/` (catalog, users, etc.)
  - The React frontend lives under `frontend/src/`.
  - Infrastructure configs are under `infra/`.
  - Do not move files across top-level directories unless explicitly instructed.

- **Environment Variables & Secrets**

  - Do not commit any hard-coded credentials or secrets.
  - Both Django and Node.js configurations should read sensitive data (e.g., DB credentials, API keys) from environment variables or `.env` files that are excluded via `.gitignore`.

- **Code Ownership & Areas of Responsibility**
  - Python/Django developers focus on `backend/novel/` and `backend/apps/`.
  - Frontend/UI engineers focus on `frontend/src/`.
  - DevOps engineers focus on `infra/` and CI/CD workflows.
  - Crawler specialists work on `backend/novel/adapters/` and spider implementations.

---

## Working with Git

When completing tasks that involve creating or modifying files, follow these rules:

1. **Commit Discipline**

   - Do **not** create new branches: all work must be committed directly to the current branch (typically `main` or `develop`).
   - Stage only the files you intend to change.
   - Ensure your working tree is clean before pushing or merging.

2. **Commit Message Guidelines**

   - Use the [Conventional Commits](https://www.conventionalcommits.org/) format:

     ```
     <type>(<scope>): <short description>

     <optional detailed explanation>
     ```

   - Types include `feat`, `fix`, `docs`, `refactor`, `test`, etc.
   - Example:

     ```
     feat(api): add pagination to novel list endpoint

     Now the `/api/novels/` endpoint accepts `page` and `page_size` query params.
     ```

3. **Running Pre-commit Hooks**

   - Before any commit, run `pre-commit run --all-files`.
   - If hooks fail (e.g., lint errors, formatting issues), fix the reported issues and re-run.
   - Do not bypass or disable pre-commit hooks.

4. **Verifying a Clean State**
   - After successful commit, run `git status` and ensure it reports “working tree clean” before pushing.
   - Only committed changes will be evaluated by continuous integration (CI).

---

## Coding Conventions

### Python / Django (backend/novel)

- **Formatting & Linting**

  - Use **Black** for code formatting.
  - Run `flake8` for linting; target no errors/warnings.
  - Maintain line length ≤ 88 characters (Black’s default).

- **Imports**

  - Follow [PEP8 import order](https://www.python.org/dev/peps/pep-0008/#imports):
    1. Standard library
    2. Third-party packages
    3. Local application imports

- **Django App Structure**

  - Views, serializers, models, and tests each have their own directory or file under `backend/novel/`.
  - Do not import models or settings via relative paths; always use the registered app name.

- **DRF (Django REST Framework)**

  - Use ViewSets and Routers for API endpoints.
  - Include proper permissions (e.g., `IsAuthenticated` where needed).
  - Validate serializers strictly and write unit tests for all API endpoints.

- **Celery Tasks**
  - All Celery tasks live under `backend/novel/tasks.py` or `tasks/`.
  - Use `@shared_task` or app-specific Celery decorators.
  - Ensure idempotency where possible.

### TypeScript / Node.js (backend/src)

- **Formatting & Linting**

  - Use **Prettier** for formatting.
  - Use **ESLint** with TypeScript rules.
  - Aim for zero ESLint errors / warnings before committing.

- **Code Organization**

  - Routes are defined in `backend/src/routes/novel.routes.ts`.
  - Controllers live in `backend/src/controllers/`.
  - Database connection logic is in a shared module (e.g., `backend/src/db/`).
  - Do not hard-code database credentials; read from `process.env`.

- **Type Safety**

  - Always annotate function return types.
  - Define data interfaces for request payloads and responses.
  - Handle possible `null` or `undefined` states explicitly.

- **Error Handling**
  - Use Express middleware for centralized error handling.
  - Return JSON responses with consistent structure `{ success: boolean, data?: any, error?: string }`.

### React / Tailwind (frontend/src)

- **Formatting**

  - Use **Prettier** for JS/TSX formatting.
  - Use **ESLint** with React-specific and Tailwind CSS linting rules.

- **Component Structure**

  - Keep components small and focused (single responsibility).
  - Use functional components with Hooks; avoid class components.
  - Organize components by feature under `frontend/src/components/<FeatureName>/`.

- **Styling**

  - Use **Tailwind CSS** utility classes—no custom CSS unless necessary.
  - Follow the design tokens (colors, spacing) defined in `tailwind.config.js`.

- **State Management**

  - Prefer React Context or custom hooks for shared state.
  - Avoid introducing complex state libraries (e.g., Redux) unless explicitly justified.

- **API Integration**
  - All API calls go through a centralized service under `frontend/src/services/api.ts`.
  - Handle loading and error states gracefully (e.g., spinners, toast notifications).

---

## Running & Testing Backend

### Django (backend/novel)

1. **Setting Up**

   - Create a Python 3.10+ virtual environment.
   - Install dependencies:
     ```bash
     cd backend/novel/
     pip install -r requirements.txt
     ```
   - Copy `.env.example` to `.env` and fill in necessary variables (e.g., `DATABASE_URL`, `SECRET_KEY`, `REDIS_URL`).

2. **Database Migrations**
   ```bash
   python manage.py migrate
   ```

### Running Tests

```bash
cd backend/novel/
python manage.py test
```

### Development Tools

```bash
# Code formatting
black .
# Linting
flake8 .
# Type checking
mypy .
```
