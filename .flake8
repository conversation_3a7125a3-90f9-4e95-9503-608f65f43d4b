[flake8]
# 忽略第三方套件與建置輸出目錄
exclude =
    .git,
    __pycache__,
    */node_modules/*,
    sessionmanager-bundle,
    coverage,
    build,
    dist,
    venv,
    */venv/*

# 最大行寬設定
max-line-length = 88

# 全域忽略 E203（冒號前空格）與 W503（行尾斷行優先）
extend-ignore = E203, W503

# 針對 crawler/spiders 目錄額外忽略多項規則
# 遺留代碼暫時忽略部分規則，備份文件忽略長行問題
per-file-ignores =
    backend/novel/crawler/spiders/*: E203, F401, F403, E501
    backend/novel/crawler/config/settings_old.py: E501
    backend/novel/settings/test.py: F401, F403, F405, E402
    backend/novel/tests/*: F401, E402
    backend/novel/tests/test_crawler_integration.py: F401
    backend/test_cache_fix.py: E402
    backend/tests/unit/*: E402
    backend/tests/unit/novel_api_test.py: E402
    scripts/devcontext-init.py: F401, F541, E501
    scripts/validate-devcontext.py: F401
    scripts/local-ci.sh: E501
