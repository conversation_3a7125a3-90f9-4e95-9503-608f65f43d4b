[flake8]
# 忽略第三方套件與建置輸出目錄
exclude =
    .git,
    __pycache__,
    */node_modules/*,
    sessionmanager-bundle,
    coverage,
    build,
    dist,
    venv,
    */venv/*

# 最大行寬設定
max-line-length = 88

# 全域忽略 E203（冒號前空格）與 W503（行尾斷行優先）
extend-ignore = E203, W503

# 針對特定文件忽略規則
per-file-ignores =
    backend/test_cache_fix.py: E402
    backend/tests/unit/*: E402
    backend/tests/unit/novel_api_test.py: E402
    scripts/devcontext-init.py: F401, F541, E501
    scripts/validate-devcontext.py: F401
    scripts/local-ci.sh: E501
