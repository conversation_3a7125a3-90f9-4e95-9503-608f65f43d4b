# GitHub Actions Runner Golden AMI for Amazon Linux 2023
# Built with HashiCorp Packer

packer {
  required_version = ">= 1.8.0"
  required_plugins {
    amazon = {
      version = ">= 1.2.0"
      source  = "github.com/hashicorp/amazon"
    }
  }
}

# Variables
variable "region" {
  description = "AWS region"
  type        = string
  default     = "ap-northeast-1"
}

variable "instance_type" {
  description = "EC2 instance type for building"
  type        = string
  default     = "t3.medium"
}

variable "ami_name_prefix" {
  description = "Prefix for AMI name"
  type        = string
  default     = "novel-github-runner-al2023"
}

variable "github_runner_version" {
  description = "GitHub Actions Runner version"
  type        = string
  default     = "2.325.0"
}

variable "node_version" {
  description = "Node.js version to install"
  type        = string
  default     = "18"
}

# Local variables
locals {
  timestamp = regex_replace(timestamp(), "[- TZ:]", "")
  ami_name  = "${var.ami_name_prefix}-${local.timestamp}"
}

# Data sources
data "amazon-ami" "al2023" {
  filters = {
    name                = "al2023-ami-*-x86_64"
    root-device-type    = "ebs"
    virtualization-type = "hvm"
  }
  most_recent = true
  owners      = ["amazon"]
  region      = var.region
}

# Build configuration
source "amazon-ebs" "github-runner" {
  region          = var.region
  source_ami      = data.amazon-ami.al2023.id
  instance_type   = var.instance_type
  ssh_username    = "ec2-user"
  ami_name        = local.ami_name
  ami_description = "GitHub Actions Runner on Amazon Linux 2023 - Built on ${timestamp()}"

  # AMI configuration
  ami_groups = ["all"]  # Make AMI public (optional)

  # EBS configuration
  ebs_optimized = true

  # Security group for build
  temporary_security_group_source_cidrs = ["0.0.0.0/0"]

  # Tags
  tags = {
    Name            = local.ami_name
    Environment     = "production"
    Project         = "novel-website"
    Component       = "github-runner"
    BaseAMI         = "{{ .SourceAMI }}"
    BaseAMIName     = "{{ .SourceAMIName }}"
    BuildDate       = "{{ isotime }}"
    RunnerVersion   = var.github_runner_version
    NodeVersion     = var.node_version
    Automated       = "true"
  }
}

# Build steps
build {
  name = "github-runner-al2023"
  sources = [
    "source.amazon-ebs.github-runner"
  ]

  # Update system packages
  provisioner "shell" {
    inline = [
      "echo '=== Updating system packages ==='",
      "sudo dnf update -y",
      "sudo dnf install -y git docker tar gzip curl wget"
    ]
  }

  # Install GitHub Actions Runner dependencies
  provisioner "shell" {
    inline = [
      "echo '=== Installing GitHub Actions Runner dependencies ==='",
      "sudo dnf install -y icu libicu",
      "echo 'ICU libraries installed successfully'"
    ]
  }

  # Install Docker and configure
  provisioner "shell" {
    inline = [
      "echo '=== Configuring Docker ==='",
      "sudo systemctl enable docker",
      "sudo usermod -aG docker ec2-user",
      "echo 'Docker configured successfully'"
    ]
  }

  # Install Node.js (using NodeSource repository for latest versions)
  provisioner "shell" {
    inline = [
      "echo '=== Installing Node.js ${var.node_version} ==='",
      "curl -fsSL https://rpm.nodesource.com/setup_${var.node_version}.x | sudo bash -",
      "sudo dnf install -y nodejs",
      "node --version",
      "npm --version"
    ]
  }

  # Download and prepare GitHub Actions Runner
  provisioner "shell" {
    inline = [
      "echo '=== Downloading GitHub Actions Runner v${var.github_runner_version} ==='",
      "cd /home/<USER>",
      "mkdir -p actions-runner",
      "cd actions-runner",
      "curl -o actions-runner-linux-x64-${var.github_runner_version}.tar.gz -L https://github.com/actions/runner/releases/download/v${var.github_runner_version}/actions-runner-linux-x64-${var.github_runner_version}.tar.gz",
      "tar xzf ./actions-runner-linux-x64-${var.github_runner_version}.tar.gz",
      "sudo chown -R ec2-user:ec2-user /home/<USER>/actions-runner",
      "echo 'GitHub Actions Runner prepared successfully'"
    ]
  }

  # Install additional development tools
  provisioner "shell" {
    inline = [
      "echo '=== Installing additional development tools ==='",
      "sudo dnf groupinstall -y 'Development Tools'",
      "sudo dnf install -y python3 python3-pip",
      "pip3 install --user virtualenv",
      "echo 'Development tools installed successfully'"
    ]
  }

  # Create runner setup script
  provisioner "file" {
    source      = "scripts/setup-runner.sh"
    destination = "/home/<USER>/setup-runner.sh"
  }

  provisioner "shell" {
    inline = [
      "chmod +x /home/<USER>/setup-runner.sh",
      "sudo chown ec2-user:ec2-user /home/<USER>/setup-runner.sh"
    ]
  }

  # Install CloudWatch agent for monitoring
  provisioner "shell" {
    inline = [
      "echo '=== Installing CloudWatch agent ==='",
      "wget https://s3.amazonaws.com/amazoncloudwatch-agent/amazon_linux/amd64/latest/amazon-cloudwatch-agent.rpm",
      "sudo rpm -U ./amazon-cloudwatch-agent.rpm",
      "rm -f ./amazon-cloudwatch-agent.rpm",
      "echo 'CloudWatch agent installed successfully'"
    ]
  }

  # System cleanup and optimization
  provisioner "shell" {
    inline = [
      "echo '=== Performing system cleanup ==='",
      "sudo dnf clean all",
      "sudo rm -rf /tmp/*",
      "sudo rm -rf /var/tmp/*",
      "history -c",
      "echo 'System cleanup completed'"
    ]
  }

  # Final verification
  provisioner "shell" {
    inline = [
      "echo '=== Final verification ==='",
      "docker --version",
      "git --version",
      "node --version",
      "npm --version",
      "python3 --version",
      "ls -la /home/<USER>/actions-runner/",
      "echo 'AMI build completed successfully!'"
    ]
  }

  # Generate build manifest
  post-processor "manifest" {
    output = "manifest.json"
    strip_path = true
    custom_data = {
      build_time        = timestamp()
      runner_version    = var.github_runner_version
      node_version      = var.node_version
      base_ami          = "{{ .SourceAMI }}"
      base_ami_name     = "{{ .SourceAMIName }}"
    }
  }
}
