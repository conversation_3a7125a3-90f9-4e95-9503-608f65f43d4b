#!/bin/bash
# GitHub Actions Runner Setup Script
# This script is executed by User Data when EC2 instance starts

set -e

# Configuration
GITHUB_REPO_URL="https://github.com/MumuTW/novel-web"
RUNNER_NAME="runner-$(hostname)-$(date +%s)"
RUNNER_WORK_DIR="_work"
RUNNER_LABELS="self-hosted,Linux,X64,spot"

# Logging
exec > >(tee -a /var/log/github-runner-setup.log)
exec 2>&1

echo "=== GitHub Actions Runner Setup Started at $(date) ==="

# Function to get GitHub registration token
get_github_token() {
    local repo_owner="MumuTW"
    local repo_name="novel-web"

    # Try to get token from AWS Parameter Store or Secrets Manager
    # This is a placeholder - implement based on your secret management strategy

    echo "Getting GitHub registration token..."

    # Option 1: Using GitHub App (recommended for production)
    # TOKEN=$(aws secretsmanager get-secret-value --secret-id "github-app-token" --query SecretString --output text)

    # Option 2: Using PAT from Parameter Store
    # TOKEN=$(aws ssm get-parameter --name "/github/runner/token" --with-decryption --query Parameter.Value --output text)

    # Option 3: For development/testing - use pre-configured token (NOT recommended for production)
    # Replace with actual token management
    TOKEN="YOUR_GITHUB_TOKEN_HERE"  # gitleaks:allow

    echo "$TOKEN"
}

# Function to setup GitHub runner
setup_runner() {
    echo "Setting up GitHub Actions Runner..."

    cd /home/<USER>/actions-runner

    # Get registration token
    GITHUB_TOKEN=$(get_github_token)

    if [ -z "$GITHUB_TOKEN" ]; then
        echo "ERROR: Unable to get GitHub registration token"
        exit 1
    fi

    # Configure runner
    ./config.sh --url $GITHUB_REPO_URL \
                --token $GITHUB_TOKEN \
                --name $RUNNER_NAME \
                --work $RUNNER_WORK_DIR \
                --labels $RUNNER_LABELS \
                --unattended \
                --replace

    echo "Runner configured successfully"
}

# Function to install and start runner service
install_service() {
    echo "Installing and starting runner service..."

    cd /home/<USER>/actions-runner

    # Install as service
    sudo ./svc.sh install

    # Start service
    sudo ./svc.sh start

    echo "Runner service installed and started"
}

# Function to verify runner status
verify_runner() {
    echo "Verifying runner status..."

    cd /home/<USER>/actions-runner

    # Check service status
    sudo ./svc.sh status

    # Wait for runner to connect
    sleep 10

    # Check if runner is listening
    if journalctl -u actions.runner.MumuTW-novel-web.$(hostname).service --since "1 minute ago" | grep -q "Listening for Jobs"; then
        echo "✅ Runner is successfully listening for jobs"
    else
        echo "⚠️ Runner may not be fully ready yet"
    fi
}

# Function to setup CloudWatch monitoring
setup_monitoring() {
    echo "Setting up CloudWatch monitoring..."

    # Create CloudWatch config for runner monitoring
    cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << EOF
{
    "metrics": {
        "namespace": "GitHubRunner",
        "metrics_collected": {
            "cpu": {
                "measurement": ["cpu_usage_idle", "cpu_usage_iowait", "cpu_usage_user", "cpu_usage_system"],
                "metrics_collection_interval": 60
            },
            "disk": {
                "measurement": ["used_percent"],
                "metrics_collection_interval": 60,
                "resources": ["*"]
            },
            "mem": {
                "measurement": ["mem_used_percent"],
                "metrics_collection_interval": 60
            }
        }
    },
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [
                    {
                        "file_path": "/var/log/github-runner-setup.log",
                        "log_group_name": "/aws/ec2/github-runner",
                        "log_stream_name": "{instance_id}/setup",
                        "timezone": "UTC"
                    }
                ]
            }
        }
    }
}
EOF

    # Start CloudWatch agent
    sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
        -a fetch-config \
        -m ec2 \
        -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json \
        -s

    echo "CloudWatch monitoring configured"
}

# Function to perform health check
health_check() {
    echo "Performing health check..."

    # Check Docker
    if ! docker info > /dev/null 2>&1; then
        echo "⚠️ Docker is not running properly"
        sudo systemctl start docker
    fi

    # Check disk space
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 80 ]; then
        echo "⚠️ Disk usage is high: ${DISK_USAGE}%"
    fi

    # Check memory
    MEM_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
    if [ "$MEM_USAGE" -gt 80 ]; then
        echo "⚠️ Memory usage is high: ${MEM_USAGE}%"
    fi

    echo "Health check completed"
}

# Main execution
main() {
    echo "Starting GitHub Actions Runner setup..."

    # Update system
    sudo dnf update -y

    # Setup runner
    setup_runner

    # Install service
    install_service

    # Setup monitoring
    setup_monitoring

    # Verify setup
    verify_runner

    # Health check
    health_check

    echo "=== GitHub Actions Runner Setup Completed at $(date) ==="
    echo "Runner Name: $RUNNER_NAME"
    echo "Repository: $GITHUB_REPO_URL"
    echo "Labels: $RUNNER_LABELS"
}

# Execute main function
main "$@"
