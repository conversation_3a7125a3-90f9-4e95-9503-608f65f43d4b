# 🏗️ Infrastructure Automation

這個目錄包含了 NovelWebsite 專案的基礎設施自動化工具和配置。

## 📁 目錄結構

```
infra/
├── README.md                      # 本文檔
├── deployment/                    # 部署相關配置
├── docker/                        # Docker 相關配置
├── monitoring/                    # 監控配置
├── packer/                        # Packer AMI 構建配置
│   ├── github-runner-al2023.pkr.hcl  # 主要 Packer 模板
│   ├── variables.pkrvars.hcl          # 變數配置檔案
│   └── scripts/
│       └── setup-runner.sh           # Runner 自動設置腳本
└── terraform/                     # (未來) Terraform IaC 配置
```

## 🎯 核心目標

從「可變基礎設施」演進到「不可變基礎設施」：

- **當前問題**: 手動在空白 EC2 實例上配置 GitHub Actions Runner
- **解決方案**: 預先烘焙包含所有配置的「黃金 AMI」
- **目標結果**: 新 Spot Instance 在 1-2 分鐘內自動上線

## 🚀 快速開始

### 前置需求

1. **工具安裝**:

   ```bash
   # macOS
   brew install packer awscli jq

   # Linux (Ubuntu/Debian)
   sudo apt update && sudo apt install -y packer awscli jq
   ```

2. **AWS 認證配置**:
   ```bash
   aws configure
   # 或設置環境變數
   export AWS_ACCESS_KEY_ID="your-access-key"
   export AWS_SECRET_ACCESS_KEY="your-secret-key"
   export AWS_DEFAULT_REGION="ap-northeast-1"
   ```

### 構建黃金 AMI

```bash
# 1. 初始化 Packer 環境
make init-packer

# 2. 驗證配置
make validate-packer

# 3. 構建 AMI (需要 10-15 分鐘)
make build-golden-ami
```

## 📋 AMI 特性

### 預安裝軟體

- **作業系統**: Amazon Linux 2023.7
- **開發工具**: Git, Docker, Node.js 18, Python 3
- **GitHub Runner**: v2.325.0 (可配置)
- **監控**: CloudWatch Agent
- **依賴**: ICU 庫 (修復 .NET Core 問題)

### 系統配置

- **用戶**: `ec2-user` 具備 sudo 權限
- **服務**: Docker 開機自啟
- **安全**: 最小權限原則
- **監控**: 系統指標和日誌收集

## 🔧 自動化流程

### 1. AMI 構建階段 (一次性)

```mermaid
graph LR
    A[基礎 AL2023 AMI] --> B[安裝依賴]
    B --> C[配置 Docker]
    C --> D[下載 GitHub Runner]
    D --> E[安裝監控工具]
    E --> F[系統優化]
    F --> G[黃金 AMI]
```

### 2. 實例啟動階段 (每次)

```mermaid
graph LR
    A[啟動實例] --> B[執行 User Data]
    B --> C[取得 GitHub Token]
    C --> D[註冊 Runner]
    D --> E[啟動服務]
    E --> F[健康檢查]
    F --> G[開始監聽 Jobs]
```

## 🛠️ 配置說明

### Packer 變數

```hcl
# infra/packer/variables.pkrvars.hcl
region                = "ap-northeast-1"
instance_type        = "t3.medium"
ami_name_prefix      = "novel-github-runner-al2023"
github_runner_version = "2.325.0"
node_version         = "18"
```

### GitHub Token 管理

目前需要手動配置 GitHub Token。建議使用以下方式之一：

1. **AWS Secrets Manager** (推薦):

   ```bash
   aws secretsmanager create-secret \
     --name "github-runner-token" \
     --description "GitHub Actions Runner registration token" \
     --secret-string "your-github-token"
   ```

2. **AWS Systems Manager Parameter Store**:
   ```bash
   aws ssm put-parameter \
     --name "/github/runner/token" \
     --type "SecureString" \
     --value "your-github-token"
   ```

## 📊 監控與日誌

### CloudWatch 指標

- CPU 使用率
- 記憶體使用率
- 磁碟使用率
- 自定義 Runner 指標

### 日誌收集

- 系統日誌: `/var/log/messages`
- Runner 設置日誌: `/var/log/github-runner-setup.log`
- Runner 服務日誌: 透過 systemd journal

## 🔒 安全最佳實踐

### 認證管理

- ✅ 使用 IAM 角色而非長期憑證
- ✅ 實施最小權限原則
- ✅ 定期輪替 GitHub Token
- ✅ 啟用 CloudTrail 審計

### 網路安全

- ✅ 最小化安全群組規則
- ✅ 使用私有子網 (如適用)
- ✅ 啟用 VPC Flow Logs
- ✅ 實施網路 ACL

### 實例安全

- ✅ 定期更新 AMI
- ✅ 啟用實例元數據服務 v2 (IMDSv2)
- ✅ 實施磁碟加密
- ✅ 設置自動安全更新

## 🚨 故障排除

### 常見問題

1. **Packer 構建失敗**:

   ```bash
   # 檢查 AWS 認證
   aws sts get-caller-identity

   # 檢查權限
   aws iam get-user
   ```

2. **Runner 無法註冊**:

   ```bash
   # 檢查 GitHub Token
   curl -H "Authorization: token YOUR_TOKEN" \
        https://api.github.com/user

   # 檢查網路連通性
   curl -I https://github.com
   ```

3. **服務啟動失敗**:

   ```bash
   # 檢查服務狀態
   systemctl status actions.runner.*

   # 查看服務日誌
   journalctl -u actions.runner.* -f
   ```

### 除錯模式

```bash
# 啟用 Packer 除錯模式
PACKER_LOG=1 make build-golden-ami

# SSH 到構建實例進行除錯
packer build -debug infra/packer/github-runner-al2023.pkr.hcl
```

## 🔄 維護與更新

### 定期任務

- **每月**: 更新基礎 AMI 以獲取安全更新
- **按需**: 更新 GitHub Runner 版本
- **季度**: 檢視和優化成本

### 更新流程

```bash
# 1. 更新 Runner 版本
vim infra/packer/variables.pkrvars.hcl

# 2. 重新構建 AMI
make build-golden-ami

# 3. 更新 Auto Scaling Group 的 Launch Template
# (需要 Terraform 或手動操作)

# 4. 逐步替換運行中的實例
```

## 📈 未來改進

### 短期計畫

- [ ] 實施 Terraform IaC
- [ ] 自動化 GitHub Token 管理
- [ ] 設置 Auto Scaling Group
- [ ] 實施健康檢查和自動替換

### 中期計畫

- [ ] 多 AZ 部署
- [ ] 藍綠部署策略
- [ ] 成本優化和監控
- [ ] 安全加固和合規

### 長期願景

- [ ] 完全無伺服器 CI/CD
- [ ] AI 驅動的性能優化
- [ ] 自動故障檢測和恢復

## 🤝 貢獻指南

1. Fork 此專案
2. 創建功能分支: `git checkout -b feature/amazing-feature`
3. 提交變更: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 開啟 Pull Request

## 📞 支援

如有問題或建議，請：

1. 查看本文檔和故障排除部分
2. 檢查 [GitHub Issues](../../issues)
3. 創建新的 Issue 並提供詳細資訊

---

**維護者**: NovelWebsite 開發團隊
**最後更新**: 2025-06-22
