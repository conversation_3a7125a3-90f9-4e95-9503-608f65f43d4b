# 🧹 Self-hosted Runner 清理政策

## 🎯 清理策略概述

Spot Instance 隨時可能被回收，需要確保：
1. 重要快取保留 (降低後續 CI 時間)
2. 敏感資訊清除 (安全性)
3. 費用控制 (避免殘留資源累積)

## 💾 快取保留策略

### 1. pnpm 全域快取 (保留)
```bash
# 位置: /home/<USER>/.pnpm-store
# 大小: ~500MB-2GB
# 保留原因: 大幅加速 pnpm install
# 處理: 掛載 EBS 或 EFS 持久化
```

### 2. Docker Layer Cache (保留)
```bash
# 位置: /var/lib/docker
# 大小: ~2-5GB
# 保留原因: Frontend/Backend image 層級快取
# 處理:
#   - 方案A: EBS 快照恢復
#   - 方案B: ECR 作為 remote cache
```

### 3. Node.js 模組快取 (保留)
```bash
# 位置: /home/<USER>/.npm
# 大小: ~200MB-1GB
# 保留原因: npm/yarn fallback 加速
# 處理: 與 pnpm 快取一併處理
```

### 4. Turborepo 快取 (保留)
```bash
# 位置: /home/<USER>/.turbo
# 大小: ~100MB-500MB
# 保留原因: Monorepo 建構加速
# 處理: 可考慮 Remote Cache (Vercel)
```

## 🗑️ 必須清理項目

### 1. 敏感資訊 (強制清理)
```bash
# GitHub PAT Tokens
- /home/<USER>/.github-token
- 環境變數 GITHUB_TOKEN

# SSH 金鑰
- /home/<USER>/.ssh/id_*
- /home/<USER>/.ssh/known_hosts

# AWS 暫時憑證
- /home/<USER>/.aws/credentials
- EC2 Instance Profile 例外

# Git 配置
- /home/<USER>/.gitconfig (if contains sensitive info)
```

### 2. 暫存檔案 (強制清理)
```bash
# 建構產物
- apps/*/build/
- apps/*/.next/
- dist/
- node_modules/ (工作目錄內)

# 系統暫存
- /tmp/*
- /var/tmp/*
- /home/<USER>/Downloads/*

# 日誌檔案
- /var/log/messages*
- /home/<USER>/.npm/_logs/
- Runner 工作日誌
```

## 🔄 自動清理腳本

### 1. Pre-termination Hook
```bash
#!/bin/bash
# /opt/runner-cleanup.sh

echo "🧹 Runner termination cleanup started..."

# 清理敏感資訊
sudo rm -rf /home/<USER>/.github-token
sudo rm -rf /home/<USER>/.ssh/id_*
sudo rm -rf /home/<USER>/.gitconfig

# 清理暫存檔案
sudo rm -rf /tmp/*
sudo rm -rf /var/tmp/*
sudo rm -rf /home/<USER>/actions-runner/_work/*

# 清理 Docker 無用容器 (保留 images)
docker container prune -f
docker volume prune -f

# 備份快取到持久化儲存 (如果有設定)
if [ -d "/mnt/cache" ]; then
    rsync -av /home/<USER>/.pnpm-store/ /mnt/cache/pnpm-store/
    rsync -av /home/<USER>/.turbo/ /mnt/cache/turbo/
fi

echo "✅ Cleanup completed"
```

### 2. 開機時快取恢復
```bash
#!/bin/bash
# /opt/runner-restore-cache.sh

echo "📦 Restoring cache from persistent storage..."

# 恢復 pnpm 快取
if [ -d "/mnt/cache/pnpm-store" ]; then
    mkdir -p /home/<USER>/.pnpm-store
    rsync -av /mnt/cache/pnpm-store/ /home/<USER>/.pnpm-store/
    chown -R ec2-user:ec2-user /home/<USER>/.pnpm-store
fi

# 恢復 Turborepo 快取
if [ -d "/mnt/cache/turbo" ]; then
    mkdir -p /home/<USER>/.turbo
    rsync -av /mnt/cache/turbo/ /home/<USER>/.turbo/
    chown -R ec2-user:ec2-user /home/<USER>/.turbo
fi

echo "✅ Cache restoration completed"
```

## 💰 費用控制措施

### 1. EBS 卷管理
```bash
# 自動標籤清理
- 所有 EBS 卷標記 "AutoDelete": "7days"
- CloudWatch + Lambda 自動清理孤兒卷
- Snapshot 保留政策: 最近 3 個版本

# 大小限制
- 系統盤: 20GB (足夠 OS + 基礎工具)
- 快取盤: 50GB (pnpm + Docker cache)
```

### 2. ECR 映像清理
```bash
# 定期清理政策 (已在 main-ci.yml 實作)
- 保留: latest, stable, v* 標籤
- 清理: 7天前的 untagged 映像
- 清理: feature 分支映像 (PR 合併後)
```

### 3. S3 暫存清理
```bash
# CI 產物清理
- Lighthouse 報告: 30天自動過期
- 測試覆蓋率報告: 14天自動過期
- 建構日誌: 7天自動過期
```

## 🔧 Spot Instance 生命週期管理

### 1. Spot Interruption 偵測
```bash
# CloudWatch + EventBridge 監控
- Spot Instance interruption warning (2分鐘前)
- 自動觸發清理腳本
- 通知 Slack/Teams 運維團隊
```

### 2. Auto Scaling 替換策略
```bash
# ASG 配置
- Health Check Grace Period: 300s
- Termination Policy: OldestInstance
- 混合實例政策: 70% Spot + 30% On-Demand
```

### 3. 工作中斷處理
```bash
# GitHub Actions Runner 設定
- Runner idle timeout: 30分鐘
- 工作完成後自動 deregister
- 中斷時重新排程到新 Runner
```

## 📊 監控與告警

### 1. 清理執行監控
```bash
# CloudWatch Metrics
- Cleanup script execution time
- Cache restoration success rate
- Failed cleanup instances count

# 告警閾值
- 清理失敗率 > 5%
- 快取恢復失敗率 > 10%
- 孤兒資源增長 > 10 per day
```

### 2. 費用監控
```bash
# AWS Cost Explorer
- Runner 相關資源日費用
- EBS 卷費用趨勢
- ECR 儲存費用成長

# 預算告警
- 每日 Runner 費用 > $50
- 每月累計 > $1000
```

## 📋 檢查清單

### 每日自動檢查
- [ ] 孤兒 EBS 卷清理
- [ ] ECR untagged 映像清理
- [ ] S3 過期物件清理
- [ ] 失效 Runner 清理

### 週度手動檢查
- [ ] 快取效率分析
- [ ] 清理腳本執行日誌檢視
- [ ] 費用趨勢分析
- [ ] Spot Instance 回收率統計

### 月度深度檢查
- [ ] 清理政策有效性評估
- [ ] 費用優化機會識別
- [ ] 安全掃描清理盲點
- [ ] 災難恢復測試

---

**最後更新**: 2025-06-28
**維護者**: NovelWebsite DevOps Team
