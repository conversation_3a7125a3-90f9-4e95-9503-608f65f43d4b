# 🔒 Self-hosted Runner 安全註冊管理

## 🎯 安全性威脅分析

### 主要風險點
1. **外部 PR 惡意代碼執行** - Fork repo 提交惡意 workflow
2. **Token 洩漏** - GitHub PAT 或 Runner token 外洩
3. **權限擴散** - Runner 獲得過多 AWS/系統權限
4. **供應鏈攻擊** - 依賴套件被污染
5. **資料外洩** - 敏感環境變數或 secrets 洩漏

## 🛡️ GitHub Actions Runner 註冊安全

### 1. 註冊 Token 管理

#### 方案 A: GitHub App (推薦)
```bash
# 優點: 細粒度權限控制, 自動 token rotation
# 設定範圍: Repository 或 Organization level

# GitHub App 權限最小化配置:
- Actions: Read
- Administration: Write (僅用於 runner 註冊)
- Contents: Read
- Metadata: Read
- Pull requests: Read
```

#### 方案 B: Personal Access Token (Classic)
```bash
# 權限範圍限制:
- repo (如需私有倉庫存取)
- workflow (必須)
- admin:repo_hook (僅 runner 註冊用)

# 安全措施:
- 30天自動過期
- 僅特定 IP 存取 (如果 GitHub 支援)
- 定期 rotation 並更新 Runner
```

### 2. Runner 註冊流程

#### 安全註冊腳本
```bash
#!/bin/bash
# /opt/secure-runner-register.sh

set -euo pipefail

# 從 AWS Secrets Manager 取得 token
GITHUB_TOKEN=$(aws secretsmanager get-secret-value \
    --secret-id "github-runner-token" \
    --query 'SecretString' --output text)

# 驗證 token 有效性
curl -H "Authorization: token $GITHUB_TOKEN" \
    https://api.github.com/user || {
    echo "❌ GitHub token invalid"
    exit 1
}

# 取得註冊 token
RUNNER_TOKEN=$(curl -X POST \
    -H "Authorization: token $GITHUB_TOKEN" \
    -H "Accept: application/vnd.github.v3+json" \
    https://api.github.com/repos/MumuTW/novel-web/actions/runners/registration-token \
    | jq -r '.token')

# 配置 Runner
cd /opt/actions-runner
./config.sh \
    --url https://github.com/MumuTW/novel-web \
    --token $RUNNER_TOKEN \
    --name "spot-runner-$(hostname)" \
    --labels "self-hosted,linux,x64,spot" \
    --unattended \
    --replace

# 清理敏感變數
unset GITHUB_TOKEN RUNNER_TOKEN

echo "✅ Runner registered securely"
```

### 3. 權限最小化原則

#### IAM Role 配置 (EC2 Instance Profile)
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ecr:GetAuthorizationToken",
        "ecr:BatchCheckLayerAvailability",
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchGetImage"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject"
      ],
      "Resource": [
        "arn:aws:s3:::novel-web-ci-cache/*",
        "arn:aws:s3:::novel-web-lighthouse-reports/*"
      ]
    },
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": [
        "arn:aws:secretsmanager:ap-northeast-1:*:secret:github-runner-token-*"
      ]
    }
  ]
}
```

#### sudoers 限制配置
```bash
# /etc/sudoers.d/runner-limits
ec2-user ALL=(root) NOPASSWD: /usr/bin/systemctl restart docker
ec2-user ALL=(root) NOPASSWD: /usr/bin/docker system prune -f
ec2-user ALL=(root) NOPASSWD: /usr/bin/yum clean all
ec2-user ALL=(root) NOPASSWD: /usr/bin/dnf clean all

# 禁止其他 sudo 操作
ec2-user ALL=(ALL) !ALL
```

## 🚫 外部 PR 防護措施

### 1. Workflow 權限控制

#### 敏感 Workflow 限制
```yaml
# .github/workflows/main-ci.yml
name: Complete PR Quality Gates

on:
  pull_request_target:  # 使用 target 而非 pull_request
    branches: [ main ]
    types: [ opened, synchronize, reopened ]

permissions:
  contents: read
  pull-requests: read
  # 不給予 write 權限

jobs:
  security-check:
    if: github.event.pull_request.head.repo.full_name == github.repository
    # 僅允許同 repo 的 PR，拒絕 Fork

  external-pr-safe:
    if: github.event.pull_request.head.repo.full_name != github.repository
    runs-on: ubuntu-latest  # 使用 GitHub hosted runner
    # Fork PR 使用受限環境
```

### 2. Secrets 存取控制

#### 環境保護規則
```bash
# GitHub Repository Settings > Environments

Production Environment:
- Required reviewers: @devops-team
- Deployment branches: main only
- Environment secrets 僅此環境可存取

Staging Environment:
- Auto-approval for main branch
- Manual approval for PR
- 限制存取範圍
```

#### 敏感 Secrets 分離
```bash
# 高敏感度 (僅 Production)
- AWS_PROD_ACCESS_KEY
- DOPPLER_PROD_TOKEN
- DATABASE_PROD_PASSWORD

# 中等敏感度 (Staging + Production)
- ECR_REGISTRY_URL
- GITHUB_PACKAGE_TOKEN

# 低敏感度 (所有環境)
- NODE_VERSION
- BUILD_ENVIRONMENT
```

## 🔐 Token Rotation 自動化

### 1. 週期性 Token 更新

#### GitHub App Token 自動更新
```bash
#!/bin/bash
# /opt/rotate-runner-token.sh

# 每週執行 (cron: 0 2 * * 0)

echo "🔄 Starting token rotation..."

# 停止當前 Runner
sudo systemctl stop actions.runner.*

# 移除舊註冊
cd /opt/actions-runner
./config.sh remove --token $(get-current-token.sh)

# 重新註冊 (使用新 token)
./secure-runner-register.sh

# 啟動新 Runner
sudo systemctl start actions.runner.*

echo "✅ Token rotation completed"
```

### 2. 異常偵測與緊急撤銷

#### 監控指標
```bash
# CloudWatch Alarms
- 異常的 API 呼叫頻率
- 非預期的 AWS 資源建立
- 大量資料傳輸
- 長時間運行的 Job (>2小時)

# 自動響應
- 超過閾值自動 deregister runner
- 通知安全團隊
- 暫停相關 workflow
```

## 🔍 安全稽核與監控

### 1. 存取日誌記錄

#### CloudTrail 監控
```json
{
  "eventSource": "github.com",
  "userIdentity": {
    "type": "Runner",
    "principalId": "runner-spot-*"
  },
  "awsRegion": "ap-northeast-1",
  "eventName": [
    "AssumeRole",
    "GetSecretValue",
    "PutObject",
    "GetObject"
  ]
}
```

#### 應用層日誌
```bash
# GitHub Actions 執行日誌
- Workflow 開始/結束時間
- Runner 分配記錄
- 失敗 Job 詳細錯誤
- 異常行為模式 (長時間執行、大量網路請求)
```

### 2. 定期安全檢查

#### 週度檢查清單
- [ ] 檢視異常 Runner 活動
- [ ] 確認 Token 正常輪換
- [ ] 檢查權限範圍是否最小化
- [ ] 監控費用異常增長

#### 月度深度稽核
- [ ] Runner 存取日誌分析
- [ ] 外部 PR 安全事件回顧
- [ ] Secrets 使用情況稽核
- [ ] 權限升級請求檢視

## 🚨 事件響應程序

### 1. 安全事件分類

#### 高風險事件 (立即響應)
- Runner token 洩漏
- 惡意代碼執行跡象
- 異常的 AWS 資源建立
- 大量敏感資料外傳

#### 中風險事件 (24小時內響應)
- 異常 API 使用模式
- 長時間運行的可疑 Job
- 非預期的依賴套件變更

### 2. 緊急處置步驟

#### 立即隔離
```bash
# 1. 停用所有 Runner
aws ec2 terminate-instances --instance-ids $(aws ec2 describe-instances \
    --filters "Name=tag:Purpose,Values=github-runner" \
    --query 'Reservations[].Instances[].InstanceId' --output text)

# 2. 撤銷 GitHub Token
curl -X DELETE \
    -H "Authorization: token $ADMIN_TOKEN" \
    https://api.github.com/repos/MumuTW/novel-web/actions/runners/$RUNNER_ID

# 3. 禁用 Workflow
# (透過 GitHub UI 或 API 暫停 Actions)
```

#### 調查與恢復
1. 分析 CloudTrail 日誌識別影響範圍
2. 檢查是否有資料外洩或權限濫用
3. 更新所有相關 Secrets 和 Tokens
4. 重新建立乾淨的 Runner 環境
5. 逐步恢復 CI/CD 功能

---

**最後更新**: 2025-06-28
**維護者**: NovelWebsite Security & DevOps Team
