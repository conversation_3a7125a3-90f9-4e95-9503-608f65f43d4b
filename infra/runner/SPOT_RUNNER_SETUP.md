# 🚀 更新 Launch Template 添加 Chrome 預安裝

## 📋 問題解決

**之前**: CI 每次執行都要下載安裝 Chrome (30-60秒 + 失敗風險)
**現在**: Launch Template User Data 預安裝，Spot Runner 啟動即可用

## 🛠️ 更新步驟

### 1. 找到現有 Launch Template

```bash
# 列出所有 Launch Template
aws ec2 describe-launch-templates --query 'LaunchTemplates[].{Name:LaunchTemplateName,Id:LaunchTemplateId,Version:LatestVersionNumber}'

# 或根據標籤篩選
aws ec2 describe-launch-templates --filters "Name=tag:Purpose,Values=github-runner"
```

### 2. 更新 Launch Template

```bash
# 取得現有配置
aws ec2 describe-launch-template-versions \
  --launch-template-id lt-你的模板ID \
  --versions '$Latest'

# 創建新版本 (加入 User Data)
aws ec2 create-launch-template-version \
  --launch-template-id lt-你的模板ID \
  --source-version '$Latest' \
  --launch-template-data '{
    "UserData": "'$(base64 -w 0 infra/aws/launch-template-userdata.sh)'"
  }'

# 設定為預設版本
aws ec2 modify-launch-template \
  --launch-template-id lt-你的模板ID \
  --default-version '$Latest'
```

### 3. 驗證更新

```bash
# 檢查新版本
aws ec2 describe-launch-template-versions \
  --launch-template-id lt-你的模板ID \
  --versions '$Latest' \
  --query 'LaunchTemplateVersions[0].LaunchTemplateData.UserData' \
  --output text | base64 -d
```

## 🎯 Auto Scaling Group 更新

如果使用 ASG，確保它使用最新版本：

```bash
# 更新 ASG 使用最新 Launch Template 版本
aws autoscaling update-auto-scaling-group \
  --auto-scaling-group-name 你的ASG名稱 \
  --launch-template '{
    "LaunchTemplateId": "lt-你的模板ID",
    "Version": "$Latest"
  }'
```

## ✅ 預期效果

### Launch Template User Data 安裝內容:
- ✅ Google Chrome Stable
- ✅ Node.js 20 LTS
- ✅ pnpm 全域安裝
- ✅ Liberation 字體 (Lighthouse 需要)
- ✅ Xvfb (headless 顯示)
- ✅ Docker (如果 CI 需要)

### CI Workflow 改進:
- ❌ 移除: Chrome 下載安裝步驟
- ✅ 簡化: 直接驗證 Chrome 版本
- ⚡ 效能: 節省 30-60 秒 + 消除網路風險

## 🔄 測試新 Spot Instance

```bash
# 手動啟動測試實例
aws ec2 run-instances \
  --launch-template LaunchTemplateId=lt-你的模板ID,Version='$Latest' \
  --instance-type t3.medium \
  --instance-market-options 'MarketType=spot' \
  --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=test-runner-chrome}]'

# SSH 進去驗證
ssh -i ~/.ssh/novel-bastion-key-v2.pem ec2-user@新實例IP
google-chrome --version
pnpm --version
```

---

**注意**: 更新後，現有 Spot Runner 需要終止重啟才會套用新的 User Data。ASG 會自動創建具備 Chrome 的新實例。
