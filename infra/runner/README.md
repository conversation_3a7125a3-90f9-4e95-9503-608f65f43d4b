# 🏃‍♂️ Self-hosted GitHub Actions Runner 基礎設施

## 📂 目錄結構

```
infra/runner/
├── README.md                 # 本文件 - 總覽與快速開始
├── SPOT_RUNNER_SETUP.md      # 完整設置流程與 Launch Template 配置
├── USER_DATA_TEMPLATE.sh     # EC2 User Data 腳本 (可直接使用)
├── AMI_BUILD_CHECKLIST.md    # Golden AMI 構建檢查清單
├── CLEANUP_POLICY.md         # 資源清理與費用控制政策
└── SECURE_REGISTER.md        # 安全註冊與權限管理
```

## 🎯 當前架構狀態

### 運行模式
- **類型**: AWS EC2 Spot Instance
- **OS**: Amazon Linux 2023
- **配置方式**: Launch Template + User Data Script
- **管理**: Auto Scaling Group 自動替換

### 預安裝軟體
- ✅ Google Chrome 138.0.7204.49 (Lighthouse 需要)
- ✅ Node.js 20 LTS + pnpm 全域安裝
- ✅ Docker Engine + Compose v2
- ✅ AWS CLI v2
- ✅ Git + 基礎開發工具

## 🚀 快速操作指南

### 查看當前 Runner 狀態
```bash
# 找到運行中的 Runner 實例
./find-runner.sh

# SSH 連線檢查
ssh -i ~/.ssh/novel-bastion-key-v2.pem ec2-user@RUNNER_IP
google-chrome --version
pnpm --version
```

### 更新 Launch Template
```bash
# 1. 編輯 USER_DATA_TEMPLATE.sh
# 2. 更新 Launch Template
aws ec2 create-launch-template-version \
  --launch-template-id lt-你的模板ID \
  --source-version '$Latest' \
  --launch-template-data '{
    "UserData": "'$(base64 -w 0 infra/runner/USER_DATA_TEMPLATE.sh)'"
  }'

# 3. 設為預設版本
aws ec2 modify-launch-template \
  --launch-template-id lt-你的模板ID \
  --default-version '$Latest'
```

### 手動觸發 Runner 重建
```bash
# 終止當前實例 (ASG 會自動啟動新的)
aws ec2 terminate-instances --instance-ids i-當前runner實例ID
```

## 📊 監控與維護

### 每日檢查
- [ ] Runner 是否正常註冊到 GitHub
- [ ] CI/CD Pipeline 執行是否正常
- [ ] Spot Instance 是否被回收

### 週度維護
- [ ] 檢視 CloudWatch 日誌異常
- [ ] 確認安全更新已套用
- [ ] 費用趨勢分析

### 月度深度維護
- [ ] 安全掃描與權限稽核
- [ ] 效能基準測試
- [ ] 災難恢復演練

## 🔧 常見問題解決

### Problem: Chrome headless 失敗
```bash
# 檢查字體安裝
fc-list | grep liberation

# 檢查 Xvfb
ps aux | grep Xvfb

# 手動測試
google-chrome --headless --no-sandbox --disable-gpu --version
```

### Problem: pnpm 權限錯誤
```bash
# 檢查 pnpm 目錄權限
ls -la ~/.pnpm-store
ls -la ~/.local/share/pnpm

# 修復權限
sudo chown -R ec2-user:ec2-user ~/.pnpm-store
sudo chown -R ec2-user:ec2-user ~/.local/share/pnpm
```

### Problem: Docker daemon 無法啟動
```bash
# 檢查服務狀態
sudo systemctl status docker

# 重新啟動
sudo systemctl restart docker

# 檢查用戶群組
groups ec2-user
```

### Problem: GitHub Runner 註冊失敗
```bash
# 檢查網路連線
curl -I https://api.github.com

# 檢查 token (如果設定)
curl -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/user

# 手動重新註冊
cd /opt/actions-runner
./config.sh --help
```

## 🔒 安全注意事項

### 重要提醒
1. **絕不在 User Data 中硬編碼敏感資訊**
2. **使用 AWS Secrets Manager 管理 GitHub Token**
3. **定期輪換所有認證資訊**
4. **監控異常的 AWS API 呼叫**
5. **限制 Runner 的 sudo 權限範圍**

### 緊急聯絡
- **DevOps 團隊**: #devops-urgent
- **安全事件**: #security-incident
- **費用異常**: #billing-alerts

## 📚 進階閱讀

1. **[SPOT_RUNNER_SETUP.md](./SPOT_RUNNER_SETUP.md)** - 完整的設置與配置流程
2. **[SECURE_REGISTER.md](./SECURE_REGISTER.md)** - 安全最佳實踐與威脅防護
3. **[CLEANUP_POLICY.md](./CLEANUP_POLICY.md)** - 費用控制與資源管理
4. **[AMI_BUILD_CHECKLIST.md](./AMI_BUILD_CHECKLIST.md)** - Golden AMI 建構指南

---

**最後更新**: 2025-06-28
**維護者**: NovelWebsite DevOps Team
**緊急聯絡**: <EMAIL>
