#!/bin/bash

# 🚀 GitHub Actions Runner Launch Template User Data
# Amazon Linux 2023 自動設置 Chrome + Runner 依賴

set -euo pipefail

# 系統更新
dnf update -y

# 設置 Google Chrome YUM repository
tee /etc/yum.repos.d/google-chrome.repo <<'EOF'
[google-chrome]
name=google-chrome
baseurl=http://dl.google.com/linux/chrome/rpm/stable/x86_64
enabled=1
gpgcheck=1
gpgkey=https://dl.google.com/linux/linux_signing_key.pub
EOF

# 導入 GPG 金鑰
rpm --import https://dl.google.com/linux/linux_signing_key.pub

# 安裝 Chrome + Lighthouse 依賴
dnf install -y \
    google-chrome-stable \
    liberation-fonts \
    liberation-narrow-fonts \
    liberation-sans-fonts \
    liberation-serif-fonts \
    xorg-x11-server-Xvfb \
    git \
    curl \
    unzip

# 安裝 Node.js 20 LTS
curl -fsSL https://rpm.nodesource.com/setup_20.x | bash -
dnf install -y nodejs

# 安裝 pnpm
npm install -g pnpm

# 安裝 Docker (如果需要)
dnf install -y docker
systemctl enable docker
systemctl start docker
usermod -aG docker ec2-user

# 驗證安裝
echo "✅ Chrome: $(google-chrome --version)"
echo "✅ Node.js: $(node --version)"
echo "✅ pnpm: $(pnpm --version)"
echo "✅ Docker: $(docker --version)"

# 創建日誌
echo "$(date): Runner setup completed" >> /var/log/runner-setup.log
