# 🏗️ Self-hosted Runner AMI 構建檢查清單

## 📋 AMI 構建策略選擇

### 方案 A: Golden AMI (推薦生產環境)
- ✅ 預安裝所有依賴，啟動速度最快
- ✅ 版本控制，可回滾到穩定版本
- ❌ 需要定期更新維護

### 方案 B: User Data Script (當前使用)
- ✅ 彈性高，易於版本控制
- ✅ 每次啟動都是最新版本
- ❌ 啟動時間較長 (2-3分鐘)

## 🔧 Golden AMI 構建檢查清單

### 1. 基礎系統準備
```bash
# Amazon Linux 2023 基礎
- [ ] 系統更新: `dnf update -y`
- [ ] 基礎工具: git, curl, unzip, wget
- [ ] 時區設定: `timedatectl set-timezone Asia/Taipei`
- [ ] 安全更新自動化
```

### 2. 核心依賴安裝
```bash
# Node.js 生態系
- [ ] Node.js 20 LTS (via NodeSource)
- [ ] pnpm 全域安裝
- [ ] yarn 備用 (某些專案需要)

# Chrome & Lighthouse
- [ ] Google Chrome Stable (from official repo)
- [ ] Liberation Fonts 完整套件
- [ ] Xvfb (headless display)
- [ ] 驗證: google-chrome --headless --version
```

### 3. CI/CD 工具鏈
```bash
# Docker 生態系
- [ ] Docker Engine (latest stable)
- [ ] Docker Compose v2
- [ ] 設定 ec2-user 加入 docker 群組
- [ ] 驗證: docker run hello-world

# AWS 工具
- [ ] AWS CLI v2
- [ ] ECR 權限預設配置 (透過 IAM Role)
- [ ] S3 存取權限配置
```

### 4. GitHub Actions Runner
```bash
# Runner 安裝
- [ ] 下載最新版 actions-runner
- [ ] 設置在 /opt/actions-runner/
- [ ] 建立 runner 服務用戶
- [ ] 設定 systemd service 自動啟動

# 安全性配置
- [ ] 限制 runner 權限範圍
- [ ] 設定 sudoers 限制 (僅必要命令)
- [ ] SSH 金鑰管理
```

### 5. 效能優化
```bash
# 快取目錄預建
- [ ] /home/<USER>/.pnpm-store
- [ ] /home/<USER>/.npm
- [ ] /var/cache/docker (Docker layer cache)
- [ ] 正確的權限設定

# 系統調優
- [ ] 增加檔案描述符限制
- [ ] 記憶體 swap 配置
- [ ] 磁碟 I/O 調優
```

## 🔒 安全性檢查清單

### 1. 敏感資訊清理
```bash
# 確保 AMI 不包含
- [ ] 沒有 SSH 私鑰
- [ ] 沒有 GitHub PAT tokens
- [ ] 沒有 AWS credentials
- [ ] 清空 bash_history
- [ ] 清空系統日誌
```

### 2. 預設帳戶安全
```bash
- [ ] 停用不必要的服務
- [ ] 更新預設密碼原則
- [ ] SSH 配置強化
- [ ] iptables 基礎規則
```

## 🧪 驗證測試腳本

### 啟動後自動測試
```bash
#!/bin/bash
# /opt/runner-health-check.sh

echo "🔍 Runner Health Check..."

# 基礎工具
command -v git >/dev/null || { echo "❌ Git not found"; exit 1; }
command -v node >/dev/null || { echo "❌ Node.js not found"; exit 1; }
command -v pnpm >/dev/null || { echo "❌ pnpm not found"; exit 1; }

# Chrome & Lighthouse
google-chrome --version >/dev/null || { echo "❌ Chrome not working"; exit 1; }
google-chrome --headless --no-sandbox --version >/dev/null || { echo "❌ Chrome headless failed"; exit 1; }

# Docker
docker --version >/dev/null || { echo "❌ Docker not found"; exit 1; }
docker info >/dev/null || { echo "❌ Docker daemon not running"; exit 1; }

# AWS CLI
aws --version >/dev/null || { echo "❌ AWS CLI not found"; exit 1; }

echo "✅ All checks passed!"
```

## 📅 AMI 維護排程

### 月度更新 (第一個週末)
- [ ] 安全更新套件
- [ ] Node.js LTS 版本檢查
- [ ] Chrome 版本更新
- [ ] Docker 版本更新

### 季度審查 (每季末)
- [ ] 清理不需要的套件
- [ ] 效能基準測試
- [ ] 安全掃描 (Inspector/Security Hub)
- [ ] 費用優化分析

## 🏷️ AMI 標籤規範

```json
{
  "Name": "github-runner-v2025.01-chrome138-node20",
  "Environment": "production",
  "Purpose": "github-actions-runner",
  "BaseOS": "amazon-linux-2023",
  "Chrome": "138.0.7204.49",
  "NodeJS": "20.18.0",
  "BuildDate": "2025-01-28",
  "Team": "devops",
  "AutoDelete": "2026-01-28"
}
```

## 🔄 建立新 AMI 流程

### 1. 準備實例
```bash
# 啟動基礎 Amazon Linux 2023
aws ec2 run-instances \
  --image-id ami-0c02fb55956c7d316 \
  --instance-type t3.medium \
  --key-name novel-bastion-key-v2 \
  --security-group-ids sg-xxx \
  --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=runner-ami-build}]'
```

### 2. 執行建構腳本
```bash
# SSH 進入並執行完整安裝
ssh -i ~/.ssh/novel-bastion-key-v2.pem ec2-user@新實例IP
curl -sSL https://raw.githubusercontent.com/MumuTW/novel-web/main/infra/runner/USER_DATA_TEMPLATE.sh | bash
```

### 3. 清理與建立 AMI
```bash
# 清理敏感資訊
sudo rm -rf /home/<USER>/.bash_history
sudo rm -rf /var/log/messages*
sudo rm -rf /tmp/*

# 建立 AMI
aws ec2 create-image \
  --instance-id i-xxx \
  --name "github-runner-v$(date +%Y.%m)-chrome$(google-chrome --version | cut -d' ' -f3)-node$(node --version)" \
  --description "GitHub Actions Self-hosted Runner with Chrome, Node.js, Docker" \
  --tag-specifications 'ResourceType=image,Tags=[{Key=Purpose,Value=github-runner}]'
```

---

**最後更新**: 2025-06-28
**維護者**: NovelWebsite DevOps Team
