# 🎭 AWS IAM 角色設置指南 - 授權面具方案

## 概念說明：什麼是「授權面具」？

想像 GitHub Actions 是一位演員，需要進入 AWS 的舞台（ECR）來表演（推送 Docker 映像）。但守衛不認識這位演員，所以我們需要：

1. **創建一個面具（IAM Role）**：這是一個特殊的身份
2. **設定識別規則（Trust Policy）**：只有來自 `MumuTW/novel-web` 的 GitHub Actions 才能戴上這個面具
3. **賦予權限（Permissions）**：戴上面具後，可以操作 ECR

## 📋 手動設置步驟

### 步驟 1：登入 AWS Console
1. 前往 [AWS IAM Console](https://console.aws.amazon.com/iam/)
2. 在左側選單中點擊「Roles」（角色）

### 步驟 2：創建新角色
1. 點擊「Create role」（創建角色）
2. 選擇信任實體類型：
   - 選擇「Web identity」（Web 身份）
   - Identity provider：選擇 `token.actions.githubusercontent.com`
   - Audience：輸入 `sts.amazonaws.com`
   - GitHub organization：輸入 `MumuTW`
   - GitHub repository：輸入 `MumuTW/novel-web`
   - GitHub branch（可選）：留空（允許所有分支）

### 步驟 3：添加權限
1. 點擊「Create policy」創建新政策
2. 選擇 JSON 標籤，貼上以下內容：

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "ECRAuthentication",
            "Effect": "Allow",
            "Action": [
                "ecr:GetAuthorizationToken"
            ],
            "Resource": "*"
        },
        {
            "Sid": "ECRRepositoryAccess",
            "Effect": "Allow",
            "Action": [
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload",
                "ecr:PutImage",
                "ecr:ListImages",
                "ecr:DescribeImages",
                "ecr:DescribeRepositories"
            ],
            "Resource": [
                "arn:aws:ecr:ap-northeast-1:************:repository/novel-web-frontend",
                "arn:aws:ecr:ap-northeast-1:************:repository/novel-web-backend"
            ]
        }
    ]
}
```

3. 命名政策為：`NovelWeb-ECR-Access-Policy`
4. 返回角色創建頁面，選擇剛創建的政策

### 步驟 4：命名角色
1. 角色名稱：`GitHubActions-ECR-Role`
2. 描述：`GitHub Actions role for Novel Website ECR access with OIDC`
3. 添加標籤（可選）：
   - `Project`: `NovelWebsite`
   - `Purpose`: `CI/CD`
   - `Security`: `OIDC`

### 步驟 5：檢視並創建
1. 檢查信任關係應該類似：
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Federated": "arn:aws:iam::************:oidc-provider/token.actions.githubusercontent.com"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
                "StringEquals": {
                    "token.actions.githubusercontent.com:aud": "sts.amazonaws.com"
                },
                "StringLike": {
                    "token.actions.githubusercontent.com:sub": "repo:MumuTW/novel-web:*"
                }
            }
        }
    ]
}
```

2. 點擊「Create role」完成創建

### 步驟 6：記錄角色 ARN
創建成功後，複製角色 ARN，格式如下：
```
arn:aws:iam::************:role/GitHubActions-ECR-Role
```

## 🔧 在 GitHub 設置 Secrets

1. 前往 GitHub 倉庫：https://github.com/MumuTW/novel-web
2. Settings → Secrets and variables → Actions
3. 點擊「New repository secret」
4. 添加以下 secrets：

| Name | Value |
|------|-------|
| `AWS_ECR_ROLE_ARN` | `arn:aws:iam::************:role/GitHubActions-ECR-Role` |
| `AWS_ACCOUNT_ID` | `************` |
| `AWS_REGION` | `ap-northeast-1` |

## ✅ 驗證設置

### 測試 OIDC 連接
創建一個簡單的測試工作流 `.github/workflows/test-aws-oidc.yml`：

```yaml
name: Test AWS OIDC Connection

on:
  workflow_dispatch:

jobs:
  test-connection:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ECR_ROLE_ARN }}
          aws-region: ap-northeast-1

      - name: Test AWS connection
        run: |
          echo "Testing AWS connection..."
          aws sts get-caller-identity
          echo "✅ OIDC authentication successful!"
```

## 🎭 為什麼這是「授權面具」？

1. **臨時性**：每次 GitHub Actions 運行時，才會「戴上」這個面具
2. **安全性**：沒有永久的密鑰存儲在任何地方
3. **可追蹤**：每次使用都有 AWS CloudTrail 記錄
4. **限定範圍**：只有指定的倉庫可以使用這個面具
5. **自動過期**：臨時憑證會在短時間內自動失效

## 🚀 下一步

1. 按照上述步驟創建 IAM 角色
2. 在 GitHub 設置必要的 Secrets
3. 運行測試工作流驗證連接
4. 開始使用 `build-and-push-images.yml` 構建並推送映像到 ECR

這個「授權面具」方案讓我們的 CI/CD 流程既安全又優雅！
