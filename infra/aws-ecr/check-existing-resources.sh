#!/bin/bash

# 檢查現有 AWS 資源的腳本
# 用於在權限受限時了解當前狀態

set -euo pipefail

# 配置變數
AWS_REGION="${AWS_REGION:-ap-northeast-1}"
FRONTEND_REPO="novel-web-frontend"
BACKEND_REPO="novel-web-backend"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔍 檢查現有 AWS 資源"
echo "===================="

# 檢查 AWS 認證
log_info "檢查 AWS 認證..."
if aws sts get-caller-identity &> /dev/null; then
    CALLER_INFO=$(aws sts get-caller-identity)
    log_success "AWS 認證成功"
    echo "  用戶: $(echo $CALLER_INFO | jq -r .Arn)"
    echo "  帳號: $(echo $CALLER_INFO | jq -r .Account)"
else
    log_error "AWS 認證失敗"
    exit 1
fi

echo ""
log_info "檢查 ECR 倉庫..."

# 檢查 Frontend 倉庫
if aws ecr describe-repositories --repository-names "$FRONTEND_REPO" --region "$AWS_REGION" &> /dev/null; then
    log_success "✅ Frontend 倉庫已存在: $FRONTEND_REPO"
    FRONTEND_URI=$(aws ecr describe-repositories --repository-names "$FRONTEND_REPO" --region "$AWS_REGION" --query 'repositories[0].repositoryUri' --output text)
    echo "  URI: $FRONTEND_URI"
else
    log_warning "❌ Frontend 倉庫不存在: $FRONTEND_REPO"
fi

# 檢查 Backend 倉庫
if aws ecr describe-repositories --repository-names "$BACKEND_REPO" --region "$AWS_REGION" &> /dev/null; then
    log_success "✅ Backend 倉庫已存在: $BACKEND_REPO"
    BACKEND_URI=$(aws ecr describe-repositories --repository-names "$BACKEND_REPO" --region "$AWS_REGION" --query 'repositories[0].repositoryUri' --output text)
    echo "  URI: $BACKEND_URI"
else
    log_warning "❌ Backend 倉庫不存在: $BACKEND_REPO"
fi

echo ""
log_info "檢查 ECR 登入能力..."

# 嘗試獲取 ECR 登入令牌
if aws ecr get-login-password --region "$AWS_REGION" &> /dev/null; then
    log_success "✅ 可以獲取 ECR 登入令牌"
else
    log_error "❌ 無法獲取 ECR 登入令牌"
fi

echo ""
log_info "檢查 Docker 狀態..."

# 檢查 Docker
if command -v docker &> /dev/null && docker info &> /dev/null; then
    log_success "✅ Docker 正在運行"
    echo "  版本: $(docker --version)"
else
    log_error "❌ Docker 未運行或未安裝"
fi

echo ""
log_info "檢查 IAM OIDC Provider..."

# 檢查 OIDC Provider
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
OIDC_ARN="arn:aws:iam::${AWS_ACCOUNT_ID}:oidc-provider/token.actions.githubusercontent.com"

if aws iam get-open-id-connect-provider --open-id-connect-provider-arn "$OIDC_ARN" &> /dev/null; then
    log_success "✅ GitHub Actions OIDC Provider 已存在"
else
    log_warning "❌ GitHub Actions OIDC Provider 不存在"
fi

# 檢查 IAM 角色
ROLE_NAME="GitHubActions-ECR-Role"
if aws iam get-role --role-name "$ROLE_NAME" &> /dev/null; then
    log_success "✅ IAM 角色已存在: $ROLE_NAME"
    ROLE_ARN=$(aws iam get-role --role-name "$ROLE_NAME" --query 'Role.Arn' --output text)
    echo "  ARN: $ROLE_ARN"
else
    log_warning "❌ IAM 角色不存在: $ROLE_NAME"
fi

echo ""
echo "📊 資源狀態摘要"
echo "================"

# 顯示需要在 GitHub 設置的 Secrets
if [[ -n "${FRONTEND_URI:-}" ]] && [[ -n "${BACKEND_URI:-}" ]]; then
    echo ""
    log_info "GitHub Secrets 配置："
    echo "  AWS_REGION: $AWS_REGION"
    echo "  AWS_ACCOUNT_ID: $AWS_ACCOUNT_ID"
    if [[ -n "${ROLE_ARN:-}" ]]; then
        echo "  AWS_ECR_ROLE_ARN: $ROLE_ARN"
    fi
fi

echo ""
log_info "如果某些資源不存在，請參考 required-permissions.md 文件來獲取必要的權限。"
