# 🔐 GitHub Secrets 配置指南

## 獲得的角色信息
- **角色 ARN**: `arn:aws:iam::************:role/GitHubActions-ECR-Role`
- **AWS 帳號 ID**: `************`
- **區域**: `ap-northeast-1`

## 📋 設置 GitHub Secrets

### 步驟 1：前往 GitHub Secrets 設置
1. 開啟：https://github.com/MumuTW/novel-web/settings/secrets/actions
2. 點擊「New repository secret」

### 步驟 2：添加以下 Secrets

| Secret Name | Value |
|-------------|-------|
| `AWS_ECR_ROLE_ARN` | `arn:aws:iam::************:role/GitHubActions-ECR-Role` |
| `AWS_ACCOUNT_ID` | `************` |
| `AWS_REGION` | `ap-northeast-1` |

### 步驟 3：驗證設置
確保所有 3 個 secrets 都已正確添加，名稱完全匹配（區分大小寫）。

## 🧪 立即測試

設置完成後，立即執行以下測試：

### 1. OIDC 連接測試
- 前往：https://github.com/MumuTW/novel-web/actions/workflows/test-aws-oidc.yml
- 點擊「Run workflow」
- 預期結果：✅ 成功認證並顯示 AWS 身份信息

### 2. ECR 倉庫創建
- 前往：https://github.com/MumuTW/novel-web/actions/workflows/setup-ecr-repositories.yml
- 在 `confirm_creation` 欄位輸入：`CREATE`
- 點擊「Run workflow」
- 預期結果：✅ 創建 `novel-web-frontend` 和 `novel-web-backend` 倉庫

## ✨ 測試成功後的下一步

1. **驗證 ECR 倉庫**：檢查 AWS Console 確認倉庫已創建
2. **測試映像構建**：手動觸發 `build-and-push-images.yml`
3. **享受 Tier 2 性能**：體驗企業級 CI/CD 架構的威力！

## 🎭 授權面具狀態
- ✅ 面具已創建（IAM Role）
- ✅ 面具 ARN 已獲得
- 🔄 正在配置 GitHub Secrets
- ⏳ 等待測試驗證

立即開始測試吧！🚀
