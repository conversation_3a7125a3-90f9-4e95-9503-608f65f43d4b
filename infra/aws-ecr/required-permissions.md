# AWS ECR 設置所需權限

## 🚨 權限不足問題

當前用戶 `novel-admin` 缺少以下必要權限：

### 1. ECR 權限
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ecr:CreateRepository",
                "ecr:DeleteRepository",
                "ecr:DescribeRepositories",
                "ecr:PutLifecyclePolicy",
                "ecr:GetLifecyclePolicy",
                "ecr:TagResource",
                "ecr:GetAuthorizationToken",
                "ecr:GetLoginPassword"
            ],
            "Resource": "*"
        }
    ]
}
```

### 2. IAM 權限（用於 OIDC 設置）
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "iam:CreateOpenIDConnectProvider",
                "iam:GetOpenIDConnectProvider",
                "iam:CreateRole",
                "iam:GetRole",
                "iam:UpdateAssumeRolePolicy",
                "iam:CreatePolicy",
                "iam:GetPolicy",
                "iam:CreatePolicyVersion",
                "iam:AttachRolePolicy",
                "iam:TagRole",
                "iam:TagPolicy"
            ],
            "Resource": "*"
        }
    ]
}
```

## 🔧 解決方案

### 選項 A：請 AWS 管理員授予權限
1. 將上述權限政策提供給您的 AWS 管理員
2. 請求為 `novel-admin` 用戶附加這些權限
3. 或創建一個名為 `NovelWebsite-ECR-Setup-Policy` 的政策

### 選項 B：使用具有管理員權限的用戶
如果您有其他具有更高權限的 AWS 認證，可以：
```bash
# 暫時切換到管理員憑證
export AWS_PROFILE=your-admin-profile
# 或
aws configure set aws_access_key_id YOUR_ADMIN_KEY
aws configure set aws_secret_access_key YOUR_ADMIN_SECRET
```

### 選項 C：手動創建資源（臨時方案）
如果無法獲得權限，可以請管理員手動執行以下 AWS CLI 命令：

#### 創建 ECR 倉庫：
```bash
# Frontend 倉庫
aws ecr create-repository \
    --repository-name novel-web-frontend \
    --region ap-northeast-1 \
    --image-tag-mutability MUTABLE \
    --image-scanning-configuration scanOnPush=true

# Backend 倉庫
aws ecr create-repository \
    --repository-name novel-web-backend \
    --region ap-northeast-1 \
    --image-tag-mutability MUTABLE \
    --image-scanning-configuration scanOnPush=true
```

## 📝 後續步驟

1. 解決權限問題後，重新執行：
   ```bash
   ./setup-ecr-repositories.sh
   ```

2. 然後繼續執行 OIDC 設置：
   ```bash
   ./setup-iam-oidc.sh
   ```

3. 最後驗證所有設置：
   ```bash
   ./test-ecr-access.sh
   ```
