#!/bin/bash

# 本地 Docker 構建測試腳本
# 在無法訪問 ECR 時，先在本地測試 Tier 2 Dockerfile

set -euo pipefail

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🐳 本地 Docker 構建測試 (Tier 2)"
echo "================================="

# 檢查 Docker
log_info "檢查 Docker 狀態..."
if ! docker info &> /dev/null; then
    log_error "Docker 未運行！請啟動 Docker Desktop"
    echo "  macOS: 打開 Docker Desktop 應用程式"
    echo "  Linux: sudo systemctl start docker"
    exit 1
fi
log_success "Docker 正在運行"

# 移動到項目根目錄
cd ../..
PROJECT_ROOT=$(pwd)
log_info "項目根目錄: $PROJECT_ROOT"

# 測試構建 Frontend Tier 2
echo ""
log_info "構建 Frontend Tier 2 映像..."
if docker build \
    -f infra/docker/frontend-tier2.Dockerfile \
    -t novel-web-frontend:tier2-local \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    . ; then
    log_success "✅ Frontend Tier 2 映像構建成功"

    # 顯示映像大小
    SIZE=$(docker images novel-web-frontend:tier2-local --format "{{.Size}}")
    log_info "映像大小: $SIZE"
else
    log_error "❌ Frontend 構建失敗"
fi

# 測試構建 Backend Tier 2
echo ""
log_info "構建 Backend Tier 2 映像..."
if docker build \
    -f infra/docker/backend-tier2.Dockerfile \
    -t novel-web-backend:tier2-local \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    . ; then
    log_success "✅ Backend Tier 2 映像構建成功"

    # 顯示映像大小
    SIZE=$(docker images novel-web-backend:tier2-local --format "{{.Size}}")
    log_info "映像大小: $SIZE"
else
    log_error "❌ Backend 構建失敗"
fi

# 顯示所有構建的映像
echo ""
log_info "構建的映像列表："
docker images | grep -E "(REPOSITORY|novel-web-)" | grep -E "(tier2-local|REPOSITORY)"

echo ""
log_info "本地測試完成！"
echo ""
echo "📝 下一步："
echo "1. 獲取 AWS ECR 權限後，執行 setup-ecr-repositories.sh"
echo "2. 使用以下命令標記並推送映像到 ECR："
echo "   docker tag novel-web-frontend:tier2-local <ECR_URI>/novel-web-frontend:latest"
echo "   docker push <ECR_URI>/novel-web-frontend:latest"
