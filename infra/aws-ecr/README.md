# Tier 2 CI/CD 架構 - AWS ECR 企業級設置指南

## 🎯 目標：實現構建與測試分離的企業級 CI/CD

本目錄包含設置 **方案 B - 終極形態 CI/CD 架構** 的所有必要腳本和配置。

### 📋 架構概覽

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   建造者工作流        │───▶│    AWS ECR 倉庫     │◀───│   測試者工作流        │
│ build-and-push.yml  │    │  (中央映像倉庫)      │    │ lightning-ci.yml    │
│                     │    │                     │    │                     │
│ • 週期性構建         │    │ • Frontend Images   │    │ • 純 Pull 模式       │
│ • 依賴變更觸發       │    │ • Backend Images    │    │ • 極速測試 (<8s)     │
│ • Tier 2 多階段      │    │ • 版本管理          │    │ • 零構建時間         │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

### 🚀 快速開始

#### 1. 先決條件

```bash
# 確保已安裝必要工具
aws --version    # AWS CLI v2
docker --version # Docker Engine
git --version    # Git

# 配置 AWS 認證
aws configure
```

#### 2. 設置 ECR 倉庫

```bash
# 進入設置目錄
cd infra/aws-ecr

# 運行 ECR 倉庫設置
chmod +x *.sh
./setup-ecr-repositories.sh
```

#### 3. 配置 OIDC 認證（零信任安全）

```bash
# 設置 GitHub Actions OIDC 認證
./setup-iam-oidc.sh
```

#### 4. 測試設置

```bash
# 驗證所有配置
./test-ecr-access.sh
```

#### 5. 配置 GitHub Secrets

在 GitHub 倉庫設置中添加以下 Secrets：

```
AWS_REGION: ap-northeast-1
AWS_ECR_ROLE_ARN: arn:aws:iam::ACCOUNT_ID:role/GitHubActions-ECR-Role
AWS_ACCOUNT_ID: 你的AWS帳號ID
```

## 📁 文件說明

### 🔧 設置腳本

| 文件 | 功能 | 執行順序 |
|------|------|----------|
| `setup-ecr-repositories.sh` | 創建 ECR 私有倉庫 | 1 |
| `setup-iam-oidc.sh` | 配置 OIDC 認證和 IAM 角色 | 2 |
| `test-ecr-access.sh` | 測試所有配置是否正確 | 3 |

### 🐳 Tier 2 Dockerfile

| 文件 | 優化效果 | 目標性能 |
|------|----------|----------|
| `../docker/frontend-tier2.Dockerfile` | 多階段構建 + 依賴分離 | <8s 測試時間 |
| `../docker/backend-tier2.Dockerfile` | 2.28GB → <1.5GB (34% 減少) | <6s 測試時間 |

## 🏗️ 詳細設置流程

### 第一步：ECR 倉庫設置

```bash
./setup-ecr-repositories.sh
```

**功能**：
- 創建 `novel-web-frontend` 和 `novel-web-backend` ECR 倉庫
- 配置生命週期政策（保留最近 10 個版本）
- 啟用自動安全掃描
- 設置倉庫標籤和權限

**輸出示例**：
```
✅ 成功創建 ECR 倉庫: novel-web-frontend
✅ 成功創建 ECR 倉庫: novel-web-backend
📦 倉庫 URI: 123456789.dkr.ecr.ap-northeast-1.amazonaws.com/novel-web-frontend
```

### 第二步：OIDC 認證設置

```bash
./setup-iam-oidc.sh
```

**功能**：
- 創建 GitHub Actions OIDC Identity Provider
- 創建 `GitHubActions-ECR-Role` IAM 角色
- 配置零信任安全政策（只允許特定倉庫訪問）
- 附加 ECR 訪問權限

**安全特性**：
```json
{
  "StringLike": {
    "token.actions.githubusercontent.com:sub": [
      "repo:MumuTW/novel-web:*"
    ]
  }
}
```

### 第三步：配置驗證

```bash
./test-ecr-access.sh
```

**測試項目**：
- ✅ ECR 倉庫存在性
- ✅ ECR 認證功能
- ✅ Docker 映像推送/拉取
- ✅ IAM 角色配置
- ✅ 生命週期政策

**成功輸出**：
```
📊 測試結果摘要
=================
  總測試數: 12
  通過: 12
  失敗: 0
🎉 所有測試通過！ECR 設置正確。
```

## 🔄 下一步：實施工作流

### 建造者工作流 (`build-and-push-images.yml`)

**觸發條件**：
- 每週日凌晨 2:00 UTC 自動構建
- `package*.json` 或 `requirements*.txt` 變更
- 手動觸發

**核心特性**：
- 智能變更檢測
- 遠端快取加速 (`--cache-from`)
- 多階段構建優化
- 自動映像清理

### 測試者工作流 (`lightning-ci.yml`)

**性能目標**：
- Frontend: 125s → **<8s** (93.6% 提升)
- Backend: 38s → **<6s** (84.2% 提升)
- 總 CI 時間: **<15s**

## 🎯 預期效益

### 📈 性能提升

| 指標 | Tier 1.5 | Tier 2 目標 | 提升幅度 |
|------|----------|-------------|----------|
| Frontend 測試 | 11秒 | **<8秒** | **27%** |
| Backend 測試 | 11秒 | **<6秒** | **45%** |
| 映像大小 (Backend) | 2.28GB | **<1.5GB** | **34%** |
| CI 啟動時間 | <1秒 | **<0.5秒** | **50%** |

### 🏆 企業級特性

- ✅ **關注點分離**：構建與測試完全分離
- ✅ **中央化管理**：所有映像統一存儲在 ECR
- ✅ **零信任安全**：OIDC + IAM 精確權限控制
- ✅ **可重複性**：任何環境都可拉取相同映像
- ✅ **可擴展性**：支援多 Runner、多環境
- ✅ **成本最優**：避免重複構建，智能快取

## 🔧 故障排除

### 常見問題

**Q: ECR 登入失敗**
```bash
# 檢查認證
aws sts get-caller-identity
aws ecr get-login-password --region ap-northeast-1
```

**Q: IAM 權限錯誤**
```bash
# 檢查角色是否存在
aws iam get-role --role-name GitHubActions-ECR-Role
```

**Q: Docker 推送失敗**
```bash
# 重新登入 ECR
aws ecr get-login-password --region ap-northeast-1 | \
  docker login --username AWS --password-stdin ACCOUNT_ID.dkr.ecr.ap-northeast-1.amazonaws.com
```

### 重置設置

如果需要重新設置：

```bash
# 刪除 ECR 倉庫（⚠️ 會刪除所有映像）
aws ecr delete-repository --repository-name novel-web-frontend --force
aws ecr delete-repository --repository-name novel-web-backend --force

# 刪除 IAM 角色
aws iam detach-role-policy --role-name GitHubActions-ECR-Role --policy-arn ...
aws iam delete-role --role-name GitHubActions-ECR-Role

# 重新運行設置
./setup-ecr-repositories.sh
./setup-iam-oidc.sh
```

## 🚀 立即行動

```bash
# 克隆倉庫並開始設置
git clone https://github.com/MumuTW/novel-web.git
cd novel-web/infra/aws-ecr

# 運行完整設置流程
chmod +x *.sh
./setup-ecr-repositories.sh
./setup-iam-oidc.sh
./test-ecr-access.sh

# 🎉 準備實施 Tier 2 CI/CD 架構！
```

---

**🎯 這是通往企業級 CI/CD 架構的第一步，讓我們開始這個激動人心的旅程！**
