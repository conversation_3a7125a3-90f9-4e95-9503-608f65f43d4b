#!/bin/bash

# Tier 2 CI/CD 架構 - ECR 訪問權限測試腳本
# 目標：驗證 ECR 倉庫和認證配置是否正確

set -euo pipefail

# 配置變數
AWS_REGION="${AWS_REGION:-ap-northeast-1}"
AWS_ACCOUNT_ID="${AWS_ACCOUNT_ID:-}"
FRONTEND_REPO="novel-web-frontend"
BACKEND_REPO="novel-web-backend"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 測試計數器
tests_total=0
tests_passed=0
tests_failed=0

# 執行測試
run_test() {
    local test_name="$1"
    local test_command="$2"

    ((tests_total++))
    log_info "測試 $tests_total: $test_name"

    if eval "$test_command" &> /dev/null; then
        log_success "✅ $test_name"
        ((tests_passed++))
        return 0
    else
        log_error "❌ $test_name"
        ((tests_failed++))
        return 1
    fi
}

# 詳細測試（顯示輸出）
run_detailed_test() {
    local test_name="$1"
    local test_command="$2"

    ((tests_total++))
    log_info "詳細測試 $tests_total: $test_name"

    if eval "$test_command"; then
        log_success "✅ $test_name"
        ((tests_passed++))
        return 0
    else
        log_error "❌ $test_name"
        ((tests_failed++))
        return 1
    fi
}

# 檢查先決條件
check_prerequisites() {
    log_info "檢查先決條件..."

    # 檢查 AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI 未安裝"
        exit 1
    fi

    # 檢查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝"
        exit 1
    fi

    # 檢查 AWS 認證
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS 認證失敗"
        exit 1
    fi

    # 獲取 AWS Account ID
    if [[ -z "$AWS_ACCOUNT_ID" ]]; then
        AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    fi

    log_success "先決條件檢查通過"
    echo "  AWS Account ID: $AWS_ACCOUNT_ID"
    echo "  AWS Region: $AWS_REGION"
}

# 測試 ECR 倉庫存在性
test_ecr_repositories() {
    log_info "測試 ECR 倉庫存在性..."

    run_test "Frontend 倉庫存在" \
        "aws ecr describe-repositories --repository-names $FRONTEND_REPO --region $AWS_REGION"

    run_test "Backend 倉庫存在" \
        "aws ecr describe-repositories --repository-names $BACKEND_REPO --region $AWS_REGION"
}

# 測試 ECR 認證
test_ecr_authentication() {
    log_info "測試 ECR 認證..."

    run_test "ECR 登入測試" \
        "aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com"
}

# 測試 Docker 映像操作
test_docker_operations() {
    log_info "測試 Docker 映像操作..."

    local test_image="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$FRONTEND_REPO:test-$(date +%s)"

    # 創建測試映像
    log_info "創建測試映像..."
    cat > /tmp/Dockerfile.test << EOF
FROM alpine:latest
LABEL test=true
LABEL tier=2.0-test
RUN echo "Tier 2 ECR Test Image" > /test.txt
CMD ["cat", "/test.txt"]
EOF

    run_test "構建測試映像" \
        "docker build -f /tmp/Dockerfile.test -t $test_image /tmp"

    run_test "推送測試映像到 ECR" \
        "docker push $test_image"

    run_test "從 ECR 拉取測試映像" \
        "docker pull $test_image"

    run_test "運行測試映像" \
        "docker run --rm $test_image"

    # 清理測試映像
    log_info "清理測試映像..."
    docker rmi "$test_image" &> /dev/null || true

    # 從 ECR 刪除測試映像
    local image_digest=$(aws ecr list-images \
        --repository-name "$FRONTEND_REPO" \
        --region "$AWS_REGION" \
        --filter tagStatus=TAGGED \
        --query "imageIds[?imageTag=='test-*'].imageDigest" \
        --output text | head -1)

    if [[ -n "$image_digest" ]]; then
        aws ecr batch-delete-image \
            --repository-name "$FRONTEND_REPO" \
            --region "$AWS_REGION" \
            --image-ids imageDigest="$image_digest" &> /dev/null || true
    fi

    rm -f /tmp/Dockerfile.test
}

# 測試 IAM 角色（如果存在）
test_iam_role() {
    log_info "測試 IAM 角色配置..."

    local role_name="GitHubActions-ECR-Role"

    if aws iam get-role --role-name "$role_name" &> /dev/null; then
        run_test "IAM 角色存在" \
            "aws iam get-role --role-name $role_name"

        run_test "IAM 角色有 ECR 政策" \
            "aws iam list-attached-role-policies --role-name $role_name | grep -q ECR"
    else
        log_warning "IAM 角色 $role_name 不存在，跳過測試"
        log_info "請運行 setup-iam-oidc.sh 創建 OIDC 認證"
    fi
}

# 測試倉庫配置
test_repository_configuration() {
    log_info "測試倉庫配置..."

    # 測試生命週期政策
    run_test "Frontend 倉庫生命週期政策" \
        "aws ecr get-lifecycle-policy --repository-name $FRONTEND_REPO --region $AWS_REGION"

    run_test "Backend 倉庫生命週期政策" \
        "aws ecr get-lifecycle-policy --repository-name $BACKEND_REPO --region $AWS_REGION"

    # 測試映像掃描配置
    run_detailed_test "映像掃描配置檢查" \
        "aws ecr describe-repositories --repository-names $FRONTEND_REPO --region $AWS_REGION --query 'repositories[0].imageScanningConfiguration.scanOnPush'"
}

# 顯示詳細倉庫信息
show_repository_details() {
    log_info "倉庫詳細信息..."

    echo ""
    echo "📦 Frontend 倉庫："
    aws ecr describe-repositories \
        --repository-names "$FRONTEND_REPO" \
        --region "$AWS_REGION" \
        --query 'repositories[0].{URI:repositoryUri,CreatedAt:createdAt,Size:repositorySizeInBytes}' \
        --output table

    echo ""
    echo "📦 Backend 倉庫："
    aws ecr describe-repositories \
        --repository-names "$BACKEND_REPO" \
        --region "$AWS_REGION" \
        --query 'repositories[0].{URI:repositoryUri,CreatedAt:createdAt,Size:repositorySizeInBytes}' \
        --output table
}

# 顯示測試結果
show_test_results() {
    echo ""
    echo "📊 測試結果摘要"
    echo "================="
    echo "  總測試數: $tests_total"
    echo "  通過: $tests_passed"
    echo "  失敗: $tests_failed"

    if [[ $tests_failed -eq 0 ]]; then
        log_success "🎉 所有測試通過！ECR 設置正確。"
        echo ""
        log_info "下一步："
        echo "  ✅ ECR 倉庫已就緒"
        echo "  🔄 可以開始實施 build-and-push-images.yml"
        echo "  🚀 準備開始 Tier 2 Docker 映像構建"
        return 0
    else
        log_error "❌ 有 $tests_failed 個測試失敗"
        echo ""
        log_info "故障排除："
        echo "  1. 檢查 AWS 認證配置"
        echo "  2. 確認 ECR 倉庫已創建"
        echo "  3. 驗證 IAM 權限配置"
        echo "  4. 運行 setup-ecr-repositories.sh 重新設置"
        return 1
    fi
}

# 主函數
main() {
    echo "🧪 Tier 2 CI/CD 架構 - ECR 訪問權限測試"
    echo "========================================="

    check_prerequisites

    echo ""
    log_info "開始 ECR 測試..."

    test_ecr_repositories
    test_ecr_authentication
    test_docker_operations
    test_iam_role
    test_repository_configuration

    echo ""
    show_repository_details
    show_test_results
}

# 如果腳本被直接執行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
