#!/bin/bash

# Tier 2 CI/CD 架構 - GitHub Actions OIDC 認證設置
# 目標：實現零信任安全模型，只允許特定 GitHub 倉庫訪問 ECR

set -euo pipefail

# 配置變數
AWS_REGION="${AWS_REGION:-ap-northeast-1}"
GITHUB_ORG="MumuTW"
GITHUB_REPO="novel-web"
IAM_ROLE_NAME="GitHubActions-ECR-Role"
OIDC_PROVIDER_URL="https://token.actions.githubusercontent.com"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 檢查先決條件
check_prerequisites() {
    log_info "檢查先決條件..."

    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI 未安裝"
        exit 1
    fi

    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS 認證失敗"
        exit 1
    fi

    local aws_account_id=$(aws sts get-caller-identity --query Account --output text)
    log_info "AWS Account ID: $aws_account_id"

    log_success "先決條件檢查通過"
}

# 創建或更新 OIDC Identity Provider
setup_oidc_provider() {
    log_info "設置 GitHub Actions OIDC Identity Provider..."

    # 檢查 OIDC Provider 是否已存在
    if aws iam get-open-id-connect-provider --open-id-connect-provider-arn "arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):oidc-provider/token.actions.githubusercontent.com" &> /dev/null; then
        log_warning "OIDC Provider 已存在，跳過創建"
        return 0
    fi

    # 獲取 GitHub Actions OIDC 的指紋
    local thumbprint="6938fd4d98bab03faadb97b34396831e3780aea1"

    # 創建 OIDC Identity Provider
    aws iam create-open-id-connect-provider \
        --url "$OIDC_PROVIDER_URL" \
        --client-id-list sts.amazonaws.com \
        --thumbprint-list "$thumbprint" \
        --tags Key=Project,Value=NovelWebsite \
               Key=Purpose,Value=GitHub-Actions-OIDC \
        > /dev/null

    log_success "OIDC Identity Provider 創建成功"
}

# 創建信任政策文檔
create_trust_policy() {
    log_info "創建 IAM 角色信任政策..."

    local aws_account_id=$(aws sts get-caller-identity --query Account --output text)

    cat > /tmp/github-oidc-trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Federated": "arn:aws:iam::${aws_account_id}:oidc-provider/token.actions.githubusercontent.com"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
                "StringEquals": {
                    "token.actions.githubusercontent.com:aud": "sts.amazonaws.com"
                },
                "StringLike": {
                    "token.actions.githubusercontent.com:sub": [
                        "repo:${GITHUB_ORG}/${GITHUB_REPO}:*"
                    ]
                }
            }
        }
    ]
}
EOF

    log_success "信任政策文檔創建完成"
}

# 創建權限政策文檔
create_permissions_policy() {
    log_info "創建 ECR 訪問權限政策..."

    local aws_account_id=$(aws sts get-caller-identity --query Account --output text)

    cat > /tmp/ecr-access-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "ECRAuthentication",
            "Effect": "Allow",
            "Action": [
                "ecr:GetAuthorizationToken"
            ],
            "Resource": "*"
        },
        {
            "Sid": "ECRRepositoryAccess",
            "Effect": "Allow",
            "Action": [
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload",
                "ecr:PutImage",
                "ecr:ListImages",
                "ecr:DescribeImages",
                "ecr:DescribeRepositories"
            ],
            "Resource": [
                "arn:aws:ecr:${AWS_REGION}:${aws_account_id}:repository/novel-web-frontend",
                "arn:aws:ecr:${AWS_REGION}:${aws_account_id}:repository/novel-web-backend"
            ]
        },
        {
            "Sid": "ECRImageManagement",
            "Effect": "Allow",
            "Action": [
                "ecr:BatchDeleteImage"
            ],
            "Resource": [
                "arn:aws:ecr:${AWS_REGION}:${aws_account_id}:repository/novel-web-frontend",
                "arn:aws:ecr:${AWS_REGION}:${aws_account_id}:repository/novel-web-backend"
            ],
            "Condition": {
                "StringLike": {
                    "ecr:ImageTag": [
                        "dev-*",
                        "pr-*",
                        "temp-*"
                    ]
                }
            }
        }
    ]
}
EOF

    log_success "權限政策文檔創建完成"
}

# 創建 IAM 角色
create_iam_role() {
    log_info "創建 IAM 角色: $IAM_ROLE_NAME"

    # 檢查角色是否已存在
    if aws iam get-role --role-name "$IAM_ROLE_NAME" &> /dev/null; then
        log_warning "IAM 角色已存在，更新政策..."

        # 更新信任政策
        aws iam update-assume-role-policy \
            --role-name "$IAM_ROLE_NAME" \
            --policy-document file:///tmp/github-oidc-trust-policy.json

        log_success "信任政策更新完成"
    else
        # 創建新角色
        aws iam create-role \
            --role-name "$IAM_ROLE_NAME" \
            --assume-role-policy-document file:///tmp/github-oidc-trust-policy.json \
            --description "GitHub Actions role for Novel Website ECR access with OIDC" \
            --tags Key=Project,Value=NovelWebsite \
                   Key=Purpose,Value=GitHub-Actions-ECR \
                   Key=Security,Value=OIDC-ZeroTrust \
            > /dev/null

        log_success "IAM 角色創建成功"
    fi
}

# 附加權限政策
attach_permissions_policy() {
    log_info "附加權限政策到 IAM 角色..."

    local policy_name="NovelWebsite-ECR-Access-Policy"
    local aws_account_id=$(aws sts get-caller-identity --query Account --output text)
    local policy_arn="arn:aws:iam::${aws_account_id}:policy/${policy_name}"

    # 檢查政策是否已存在
    if aws iam get-policy --policy-arn "$policy_arn" &> /dev/null; then
        log_warning "政策已存在，更新政策版本..."

        # 創建新的政策版本
        aws iam create-policy-version \
            --policy-arn "$policy_arn" \
            --policy-document file:///tmp/ecr-access-policy.json \
            --set-as-default > /dev/null

        log_success "政策版本更新完成"
    else
        # 創建新政策
        aws iam create-policy \
            --policy-name "$policy_name" \
            --policy-document file:///tmp/ecr-access-policy.json \
            --description "ECR access policy for Novel Website GitHub Actions" \
            --tags Key=Project,Value=NovelWebsite \
                   Key=Purpose,Value=ECR-Access \
            > /dev/null

        log_success "權限政策創建成功"
    fi

    # 附加政策到角色
    aws iam attach-role-policy \
        --role-name "$IAM_ROLE_NAME" \
        --policy-arn "$policy_arn"

    log_success "權限政策附加完成"
}

# 顯示設置結果
show_setup_results() {
    local aws_account_id=$(aws sts get-caller-identity --query Account --output text)
    local role_arn="arn:aws:iam::${aws_account_id}:role/${IAM_ROLE_NAME}"

    echo ""
    log_success "🎉 OIDC 認證設置完成！"
    echo ""

    echo "📋 設置摘要："
    echo "  🔐 OIDC Provider: $OIDC_PROVIDER_URL"
    echo "  🎭 IAM 角色: $IAM_ROLE_NAME"
    echo "  🏷️  角色 ARN: $role_arn"
    echo "  📂 授權倉庫: $GITHUB_ORG/$GITHUB_REPO"
    echo "  🌍 AWS 區域: $AWS_REGION"

    echo ""
    log_info "GitHub Actions 環境變數配置："
    echo "  AWS_REGION: $AWS_REGION"
    echo "  AWS_ECR_ROLE_ARN: $role_arn"
    echo "  AWS_ACCOUNT_ID: $aws_account_id"

    echo ""
    log_info "下一步："
    echo "  1. 在 GitHub 倉庫中配置上述環境變數作為 secrets"
    echo "  2. 運行 test-oidc-authentication.sh 測試認證"
    echo "  3. 開始實施 build-and-push-images.yml 工作流"
}

# 清理臨時文件
cleanup() {
    rm -f /tmp/github-oidc-trust-policy.json
    rm -f /tmp/ecr-access-policy.json
    log_info "臨時文件清理完成"
}

# 主函數
main() {
    echo "🔐 Tier 2 CI/CD 架構 - GitHub Actions OIDC 認證設置"
    echo "================================================="

    check_prerequisites

    echo ""
    log_info "開始設置 OIDC 認證..."

    setup_oidc_provider
    create_trust_policy
    create_permissions_policy
    create_iam_role
    attach_permissions_policy

    show_setup_results
    cleanup
}

# 錯誤處理
trap cleanup EXIT

# 如果腳本被直接執行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
