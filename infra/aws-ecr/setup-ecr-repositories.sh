#!/bin/bash

# Tier 2 CI/CD 架構 - AWS ECR 倉庫設置腳本
# 目標：創建企業級中央映像倉庫，實現構建與測試分離

set -euo pipefail

# 配置變數
AWS_REGION="${AWS_REGION:-ap-northeast-1}"
AWS_ACCOUNT_ID="${AWS_ACCOUNT_ID:-}"
FRONTEND_REPO_NAME="novel-web-frontend"
BACKEND_REPO_NAME="novel-web-backend"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查先決條件
check_prerequisites() {
    log_info "檢查先決條件..."

    # 檢查 AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI 未安裝。請先安裝 AWS CLI。"
        exit 1
    fi

    # 檢查 AWS 認證
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS 認證失敗。請先配置 AWS credentials。"
        exit 1
    fi

    # 獲取 AWS Account ID
    if [[ -z "$AWS_ACCOUNT_ID" ]]; then
        AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        log_info "檢測到 AWS Account ID: $AWS_ACCOUNT_ID"
    fi

    log_success "先決條件檢查通過"
}

# 創建 ECR 倉庫
create_ecr_repository() {
    local repo_name=$1
    local description=$2

    log_info "創建 ECR 倉庫: $repo_name"

    # 檢查倉庫是否已存在
    if aws ecr describe-repositories --repository-names "$repo_name" --region "$AWS_REGION" &> /dev/null; then
        log_warning "倉庫 $repo_name 已存在，跳過創建"
        return 0
    fi

    # 創建倉庫
    aws ecr create-repository \
        --repository-name "$repo_name" \
        --region "$AWS_REGION" \
        --image-tag-mutability MUTABLE \
        --image-scanning-configuration scanOnPush=true \
        --tags Key=Project,Value=NovelWebsite \
               Key=Environment,Value=Production \
               Key=ManagedBy,Value=Tier2-CICD \
               Key=Description,Value="$description" \
        > /dev/null

    log_success "成功創建 ECR 倉庫: $repo_name"

    # 設置生命週期政策
    set_lifecycle_policy "$repo_name"
}

# 設置生命週期政策
set_lifecycle_policy() {
    local repo_name=$1

    log_info "為 $repo_name 設置生命週期政策..."

    # 創建生命週期政策 JSON
    cat > /tmp/lifecycle-policy.json << EOF
{
    "rules": [
        {
            "rulePriority": 1,
            "description": "Keep last 10 tagged images",
            "selection": {
                "tagStatus": "tagged",
                "countType": "imageCountMoreThan",
                "countNumber": 10
            },
            "action": {
                "type": "expire"
            }
        },
        {
            "rulePriority": 2,
            "description": "Delete untagged images older than 1 day",
            "selection": {
                "tagStatus": "untagged",
                "countType": "sinceImagePushed",
                "countUnit": "days",
                "countNumber": 1
            },
            "action": {
                "type": "expire"
            }
        }
    ]
}
EOF

    aws ecr put-lifecycle-policy \
        --repository-name "$repo_name" \
        --region "$AWS_REGION" \
        --lifecycle-policy-text file:///tmp/lifecycle-policy.json > /dev/null

    log_success "生命週期政策設置完成"
    rm -f /tmp/lifecycle-policy.json
}

# 配置倉庫權限
configure_repository_permissions() {
    local repo_name=$1

    log_info "配置 $repo_name 的倉庫權限..."

    # 創建倉庫政策 JSON
    cat > /tmp/repository-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "AllowPull",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::${AWS_ACCOUNT_ID}:root"
            },
            "Action": [
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:BatchCheckLayerAvailability"
            ]
        },
        {
            "Sid": "AllowPushPull",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/GitHubActions-ECR-Role"
            },
            "Action": [
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:BatchCheckLayerAvailability",
                "ecr:PutImage",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload"
            ]
        }
    ]
}
EOF

    # 設置倉庫政策（如果角色存在）
    if aws iam get-role --role-name GitHubActions-ECR-Role &> /dev/null; then
        aws ecr set-repository-policy \
            --repository-name "$repo_name" \
            --region "$AWS_REGION" \
            --policy-text file:///tmp/repository-policy.json > /dev/null
        log_success "倉庫權限配置完成"
    else
        log_warning "GitHubActions-ECR-Role 角色不存在，跳過倉庫權限配置"
        log_info "請先運行 setup-iam-oidc.sh 創建 IAM 角色"
    fi

    rm -f /tmp/repository-policy.json
}

# 顯示倉庫信息
show_repository_info() {
    local repo_name=$1

    log_info "倉庫信息: $repo_name"

    local repo_uri=$(aws ecr describe-repositories \
        --repository-names "$repo_name" \
        --region "$AWS_REGION" \
        --query 'repositories[0].repositoryUri' \
        --output text)

    echo "  📦 倉庫 URI: $repo_uri"
    echo "  🏷️  標籤策略: latest, v2.x.x-YYYYMMDD, sha-xxxxxxx"
    echo "  🧹 生命週期: 保留最近 10 個標籤版本"
    echo "  🔍 安全掃描: 推送時自動掃描"
}

# 主函數
main() {
    echo "🏗️  Tier 2 CI/CD 架構 - AWS ECR 倉庫設置"
    echo "========================================"

    check_prerequisites

    echo ""
    log_info "開始創建 ECR 倉庫..."

    # 創建前端倉庫
    create_ecr_repository "$FRONTEND_REPO_NAME" "Novel Website Frontend - Tier 2 Optimized Images"
    configure_repository_permissions "$FRONTEND_REPO_NAME"

    # 創建後端倉庫
    create_ecr_repository "$BACKEND_REPO_NAME" "Novel Website Backend - Tier 2 Multi-stage Optimized Images"
    configure_repository_permissions "$BACKEND_REPO_NAME"

    echo ""
    log_success "🎉 ECR 倉庫設置完成！"
    echo ""

    # 顯示倉庫信息
    show_repository_info "$FRONTEND_REPO_NAME"
    echo ""
    show_repository_info "$BACKEND_REPO_NAME"

    echo ""
    log_info "下一步："
    echo "  1. 運行 setup-iam-oidc.sh 創建 OIDC 認證"
    echo "  2. 運行 test-ecr-access.sh 測試訪問權限"
    echo "  3. 開始實施 build-and-push-images.yml 工作流"

    echo ""
    log_info "倉庫 URL："
    echo "  Frontend: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${FRONTEND_REPO_NAME}"
    echo "  Backend:  ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${BACKEND_REPO_NAME}"
}

# 如果腳本被直接執行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
