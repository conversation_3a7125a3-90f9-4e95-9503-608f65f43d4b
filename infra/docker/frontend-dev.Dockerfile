# Frontend Development Dockerfile
# 🎯 目標：本地開發專用，與本機架構一致
# 🚀 策略：官方 Node.js + 完整 Monorepo + volume 掛載

FROM node:20-alpine

LABEL stage="frontend-development"
LABEL tier="local-dev-simple"
LABEL description="Simple dev image with ARM64 support"
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="4.0-local-dev"
LABEL architecture="arm64-native"

# 設置工作目錄
WORKDIR /app

# 安裝 pnpm（全域）
RUN corepack enable && corepack prepare pnpm@9.4.0 --activate

# 複製整個 Monorepo（開發環境）
COPY . .

# 安裝依賴（可選，通常在 volume 掛載下會直接讀本機 node_modules）
RUN pnpm install

# 設置開發環境變數
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV WATCHPACK_POLLING=true

# 暴露開發伺服器端口
EXPOSE 3000

# 開發標籤
LABEL dev-ports="3000:next-dev"
LABEL hot-reload="enabled"
LABEL volume-support="enabled"
LABEL usage="docker-compose-up,local-dev"

# 預設命令：啟動 Next.js 開發伺服器
CMD ["pnpm", "dev"]
