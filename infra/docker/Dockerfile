FROM python:3.9-slim

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 安裝Python依賴
RUN pip install --no-cache-dir \
    django \
    djangorestframework \
    django-cors-headers \
    psycopg2-binary \
    scrapy \
    requests \
    python-dotenv

# 創建必要的目錄
RUN mkdir -p /app/backend /app/data

# 設置工作目錄
WORKDIR /app/backend

# 設置環境變量
ENV PYTHONPATH=/app/backend
ENV DJANGO_SETTINGS_MODULE=config.django_settings

# 創建Django項目結構
RUN django-admin startproject novel . && \
    python manage.py startapp crawler

# 複製爬蟲代碼
COPY backend/novel/crawler /app/backend/novel/crawler/
COPY backend/novel/management /app/backend/novel/management/

# 設置權限
RUN chmod -R 755 /app/backend
