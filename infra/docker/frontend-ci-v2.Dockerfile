# Monorepo Frontend CI Dockerfile v2 - 統一依賴層重用版
# 全新設計：使用統一 node_modules 層，真正消除重複安裝
# 優化目標：零 pnpm install 時間，最大化快取命中率

# 🔑 全局 ARG 聲明（在所有 FROM 之前）
ARG ECR_REGISTRY=509399605447.dkr.ecr.ap-northeast-1.amazonaws.com

FROM node:20-alpine AS base

# 安裝 pnpm 和系統依賴
RUN apk add --no-cache \
    git \
    curl \
    bash \
    && corepack enable \
    && corepack prepare pnpm@9.4.0 --activate

# 🔑 統一工作目錄為 /workspace（與 deps image 保持一致）
WORKDIR /workspace

# ========================================
# Stage 2: 依賴層（使用 Frontend Base Image）
# ========================================

# 🚀 使用統一依賴基礎映像
FROM ${ECR_REGISTRY}/novel-web-frontend:base AS dependencies

# 📝 ARG 在 FROM 之後重新聲明以在此階段使用
ARG ECR_REGISTRY=509399605447.dkr.ecr.ap-northeast-1.amazonaws.com

# ✅ 驗證映像正確性（簡化版本）
RUN echo "✅ 繼承自 Base Image: ${ECR_REGISTRY}/novel-web-frontend:base"

LABEL stage="dependencies-from-base-image"
LABEL optimization="cache-from-ecr-base"
LABEL base-image="novel-web-frontend:base"

# 🚀 直接繼承 Base Image，無需任何 COPY 操作
# Base Image 已包含完整的依賴和配置

RUN echo "✅ CI 依賴層從 Base Image 繼承 - 零時間消耗"

# ========================================
# Stage 3: Application Build
# ========================================
FROM dependencies AS builder

# 複製 Next.js 15 源代碼（完整目錄結構）
COPY apps/web-next/ ./apps/web-next/

# 設置工作目錄到 Next.js 15 應用
WORKDIR /workspace/apps/web-next

# 構建應用
RUN echo "🏗️ 開始建置 Next.js 15 應用..." && \
    echo "📊 確認 node_modules 存在: $(ls -la ../../node_modules/.bin | wc -l) 個可執行檔" && \
    pnpm build && \
    echo "✅ Next.js 15 建置完成"

# ========================================
# Stage 4: Production Runtime (統一依賴層)
# ========================================
FROM node:20-alpine AS production

# 安裝 pnpm 和運行時依賴
RUN apk add --no-cache curl bash \
    && corepack enable \
    && corepack prepare pnpm@9.4.0 --activate

# 設置工作目錄
WORKDIR /workspace

# 🚀 關鍵：從依賴階段複製，避免重複安裝
COPY --from=dependencies /workspace/node_modules ./node_modules
COPY --from=dependencies /workspace/package.json ./package.json

# 從構建階段複製 Next.js 15 構建產物
COPY --from=builder /workspace/apps/web-next/.next ./apps/web-next/.next
COPY --from=builder /workspace/apps/web-next/public ./apps/web-next/public
COPY --from=builder /workspace/apps/web-next/package.json ./apps/web-next/package.json
COPY --from=builder /workspace/apps/web-next/next.config.js ./apps/web-next/next.config.js

# 設置用戶權限
RUN addgroup -g 1001 -S ci-user && \
    adduser -S ci-user -u 1001 && \
    chown -R ci-user:ci-user /workspace

USER ci-user

# 設置工作目錄到 Next.js 15 應用
WORKDIR /workspace/apps/web-next

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# 標籤
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="2.1-nextjs15-ci"
LABEL description="Next.js 15 App Router CI image with intelligent caching"
LABEL tier="2.0-monorepo-ci"
LABEL optimization="Monorepo-aware with pnpm intelligent caching"

# 預設命令 - Next.js 15 生產模式
CMD ["pnpm", "start"]
