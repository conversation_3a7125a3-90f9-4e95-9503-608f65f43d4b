# 🐳 Docker Socket + act 專用環境
# 基於Python環境，集成Docker CLI和act

FROM python:3.11-slim AS base

# 📦 系統依賴和Docker CLI安裝
RUN apt-get update && apt-get install -y \
    # 基礎工具
    curl \
    git \
    wget \
    unzip \
    jq \
    # 編譯工具 (Python包需要)
    gcc \
    g++ \
    python3-dev \
    libpq-dev \
    # Docker CLI依賴
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    # 網絡工具
    iputils-ping \
    telnet \
    # 清理
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 🐳 安裝Docker CLI (不包含daemon)
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli docker-compose-plugin \
    && rm -rf /var/lib/apt/lists/*

# 🎭 安裝act (GitHub Actions本地執行器)
RUN curl -s https://raw.githubusercontent.com/nektos/act/master/install.sh | bash \
    && mv bin/act /usr/local/bin/ \
    && rm -rf bin

# 📋 安裝基礎Python依賴
RUN pip install --no-cache-dir \
    django==4.2.23 \
    scrapy==2.13.2 \
    requests==2.32.4 \
    pytest \
    pytest-django \
    pytest-asyncio

# 🔧 創建非root用戶 (安全最佳實踐)
ARG USER_ID=1000
ARG GROUP_ID=1000
RUN groupadd -g ${GROUP_ID} developer \
    && useradd -u ${USER_ID} -g developer -m -s /bin/bash developer \
    && usermod -aG docker developer

# 📁 設置工作目錄
WORKDIR /workspace

# 🎨 複製act配置文件
COPY .actrc ./.actrc

# 📝 複製啟動腳本
COPY scripts/docker-act-init.sh /usr/local/bin/docker-act-init.sh
RUN chmod +x /usr/local/bin/docker-act-init.sh

# 🔧 環境變數
ENV PYTHONPATH=/workspace/backend
ENV DJANGO_SETTINGS_MODULE=config.django_settings
ENV ACT_LOG_LEVEL=info

# 👤 切換到非root用戶
USER developer

# 🚀 默認啟動命令
CMD ["/usr/local/bin/docker-act-init.sh"]

# 🏷️ 標籤
LABEL maintainer="Claude Code <<EMAIL>>"
LABEL description="NovelWebsite Docker Socket + act 開發環境"
LABEL version="1.0"
