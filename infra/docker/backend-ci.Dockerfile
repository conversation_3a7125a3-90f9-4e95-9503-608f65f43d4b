# Monorepo Backend CI Dockerfile
# 適配新 Monorepo 架構的後端 CI 映像
FROM python:3.11-slim

LABEL maintainer="NovelWebsite Team"
LABEL description="Monorepo Backend CI Environment"
LABEL version="v2.0-monorepo"

# 設置工作目錄為 Monorepo 根目錄
WORKDIR /workspace

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    libpq-dev \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 複製 Monorepo 根目錄的 requirements
COPY requirements.txt ./
COPY backend/requirements*.txt ./backend/

# 安裝 Python 依賴 (根目錄 + backend)
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r backend/requirements.txt

# 複製整個 Monorepo 結構
COPY . .

# 設置 Python 路徑包含 Monorepo 根目錄
ENV PYTHONPATH=/workspace

# 設置 Django 配置
ENV DJANGO_SETTINGS_MODULE=config.django_settings

# 創建必要的目錄
RUN mkdir -p backend/logs

# 設置權限
RUN chmod +x scripts/ci/*.sh

# 工作目錄切換到 backend 進行 Django 操作
WORKDIR /workspace/backend

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python manage.py check --settings=config.django_settings || exit 1

# 默認命令
CMD ["python", "manage.py", "check", "--settings=config.django_settings"]
