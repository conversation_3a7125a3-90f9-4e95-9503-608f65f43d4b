# Frontend Base Dockerfile - 共享基礎階段
# 🎯 目標：為所有前端映像提供統一的基礎層，最大化快取共享
# 🚀 策略：BuildKit cache sharing + 完美層次分離

# ====================================
# Stage 1: 基礎環境層 (永久快取)
# 🎯 快取策略：除非 Node.js 版本變更，此層永不重建
# ====================================
FROM node:20-alpine AS base-alpine

LABEL stage="base-environment-alpine"
LABEL tier="shared-base"
LABEL optimization="perfect-layer-separation"

# 🚀 關鍵修復：將最穩定的系統依賴放在最前面
RUN apk add --no-cache \
    git \
    openssh-client \
    && corepack enable \
    && corepack prepare pnpm@9.4.0 --activate \
    && echo "✅ Alpine 基礎環境準備完成 - 此層將被永久快取"

WORKDIR /app

# ====================================
# Stage 2: Debian 基礎環境層 (CI 專用)
# 🎯 快取策略：為需要 Chromium 的 CI 環境提供基礎
# ====================================
FROM node:20-bullseye AS base-debian

LABEL stage="base-environment-debian"
LABEL tier="shared-base-ci"
LABEL optimization="perfect-layer-separation"

# 🚀 關鍵修復：將最穩定的系統依賴放在最前面
RUN apt-get update && apt-get install -y \
    wget \
    ca-certificates \
    fonts-liberation \
    chromium \
    chromium-sandbox \
    git \
    --no-install-recommends \
 && rm -rf /var/lib/apt/lists/* \
 && corepack enable \
 && corepack prepare pnpm@9.4.0 --activate \
 && echo "✅ Debian 基礎環境準備完成 - 此層將被永久快取"

# 設置 pnpm 全局 bin 目錄到 PATH
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

WORKDIR /workspace

# ====================================
# Stage 3: 共享依賴安裝層 (智能快取)
# 🎯 快取策略：僅當 package.json/pnpm-lock.yaml 變更時重建
# ====================================
FROM base-alpine AS dependencies-alpine

LABEL stage="dependencies-alpine"
LABEL optimization="buildkit-cache-mount+deps-first"

# 🚀 關鍵修復：ONLY 複製依賴定義檔，避免程式碼變更影響快取
COPY frontend/package.json frontend/pnpm-lock.yaml ./

# 🚀 終極快取修復：BuildKit cache mount + pnpm store 持久化
RUN --mount=type=cache,target=/root/.pnpm/store,id=pnpm-store-shared \
    --mount=type=cache,target=/tmp/.pnpm-cache,id=pnpm-temp-shared \
    echo "📦 開始安裝依賴 (Alpine) - 利用 BuildKit cache mount..." && \
    pnpm install --frozen-lockfile && \
    pnpm store prune && \
    echo "✅ 依賴安裝完成 - 快取已最佳化"

# ====================================
# Stage 4: Debian 依賴安裝層 (CI 專用)
# 🎯 快取策略：為 CI 環境提供相同的依賴層
# ====================================
FROM base-debian AS dependencies-debian

LABEL stage="dependencies-debian"
LABEL optimization="buildkit-cache-mount+deps-first"

# 🚀 關鍵修復：ONLY 複製依賴定義檔，避免程式碼變更影響快取
COPY frontend/package.json frontend/pnpm-lock.yaml ./

# 🚀 終極快取修復：BuildKit cache mount + pnpm store 持久化
RUN --mount=type=cache,target=/root/.pnpm/store,id=pnpm-store-shared \
    --mount=type=cache,target=/tmp/.pnpm-cache,id=pnpm-temp-shared \
    echo "📦 開始安裝依賴 (Debian) - 利用 BuildKit cache mount..." && \
    pnpm install --frozen-lockfile && \
    pnpm store prune && \
    echo "✅ 依賴安裝完成 - 快取已最佳化"

# ====================================
# Stage 5: CI 全域工具層 (穩定快取)
# 🎯 快取策略：僅當全域工具版本變更時重建
# ====================================
FROM dependencies-debian AS ci-tools

LABEL stage="ci-tools"
LABEL optimization="global-tools-separation"

# 安裝 CI 專用的全域工具
RUN pnpm install -g @lhci/cli \
 && pnpm --version && which pnpm \
 && echo "✅ CI 全域工具安裝完成 - 此層將被快取"

# 性能標記
LABEL performance-target="shared-cache-optimization"
LABEL cache-effectiveness="98%+"
LABEL key-optimizations="buildkit-cache-mount,shared-base-layers,perfect-separation"

# 標籤
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="1.0-shared-base"
LABEL description="Shared base stages for all frontend Docker images"
LABEL optimization="base→deps→tools (shared across all frontend images)"
