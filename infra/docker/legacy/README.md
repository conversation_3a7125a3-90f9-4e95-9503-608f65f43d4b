# 🗄️ Legacy Dockerfiles Archive

此目錄包含已停用的 Dockerfile，保留用於參考和回滾目的。

## 📋 文件狀態

### ✅ 已確認移除 (2025-06-28)

| 文件名 | 原用途 | 移除原因 | 替代方案 |
|--------|--------|----------|----------|
| `frontend.Dockerfile` | Tier 1.5 前端映像 | 升級至 Tier 2 架構 | `../frontend-tier2.Dockerfile` |
| `backend.Dockerfile` | Tier 1.5 後端映像 | 升級至 Tier 2 架構 | `../backend-tier2.Dockerfile` |
| `frontend-ci.Dockerfile` | 舊版前端 CI | 效能優化升級 | `../frontend-ci-v2.Dockerfile` |
| `backend-ci.Dockerfile` | 舊版後端 CI | 效能優化升級 | `../backend-ci-v2.Dockerfile` |

### 🔍 移除驗證

**檢查時間**: 2025-06-28
**檢查方法**:
```bash
# 確認無任何引用
grep -r 'frontend.Dockerfile' .  # ✅ 僅文檔引用
grep -r 'backend.Dockerfile' .   # ✅ 僅文檔引用

# 確認 CI 工作流無引用
grep -r 'file:.*frontend.Dockerfile' .github/workflows/  # ✅ 無結果
grep -r 'file:.*backend.Dockerfile' .github/workflows/   # ✅ 無結果

# 確認 docker-compose 無引用
find . -name "docker-compose*.yml" | xargs grep -E "(frontend|backend)\.Dockerfile"  # ✅ 無結果
```

## ⚠️ 重要提醒

1. **不要直接刪除**: 這些文件作為技術演進的歷史記錄保留
2. **回滾參考**: 如需緊急回滾至舊架構時可參考
3. **定期清理**: 每年 Q4 評估是否完全移除（建議保留至少 1 年）

## 🔄 清理計劃

- **2025 Q4**: 重新評估是否完全移除 Tier 1.5 文件
- **2026 Q2**: 如無特殊需求，可考慮完全清理

---

**最後更新**: 2025-06-28
**維護者**: NovelWebsite DevOps Team
