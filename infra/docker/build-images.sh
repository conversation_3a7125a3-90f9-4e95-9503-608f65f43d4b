#!/bin/bash
# Tier 1.5 Docker 映像構建腳本
# 構建預安裝依賴的 CI/CD 映像

set -e

# 配置
REGISTRY="novel-web"
VERSION="ci-1.0"
FRONTEND_IMAGE="${REGISTRY}-frontend:${VERSION}"
BACKEND_IMAGE="${REGISTRY}-backend:${VERSION}"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查 Docker 是否運行
check_docker() {
    log_info "檢查 Docker 狀態..."
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未運行或無法連接"
        exit 1
    fi
    log_success "Docker 運行正常"
}

# 構建前端映像
build_frontend() {
    log_info "構建前端映像: ${FRONTEND_IMAGE}"

    # 檢查是否存在 frontend/package.json
    if [ ! -f "frontend/package.json" ]; then
        log_error "找不到 frontend/package.json"
        exit 1
    fi

    # 構建映像
    docker build \
        -f infra/docker/frontend-tier2.Dockerfile \
        -t "${FRONTEND_IMAGE}" \
        --progress=plain \
        .

    if [ $? -eq 0 ]; then
        log_success "前端映像構建完成: ${FRONTEND_IMAGE}"
    else
        log_error "前端映像構建失敗"
        exit 1
    fi
}

# 構建後端映像
build_backend() {
    log_info "構建後端映像: ${BACKEND_IMAGE}"

    # 檢查是否存在 backend/requirements.txt
    if [ ! -f "backend/requirements.txt" ]; then
        log_error "找不到 backend/requirements.txt"
        exit 1
    fi

    # 構建映像
    docker build \
        -f infra/docker/backend-tier2.Dockerfile \
        -t "${BACKEND_IMAGE}" \
        --progress=plain \
        .

    if [ $? -eq 0 ]; then
        log_success "後端映像構建完成: ${BACKEND_IMAGE}"
    else
        log_error "後端映像構建失敗"
        exit 1
    fi
}

# 驗證映像
verify_images() {
    log_info "驗證映像..."

    # 測試前端映像
    log_info "測試前端映像..."
    docker run --rm "${FRONTEND_IMAGE}" node --version
    docker run --rm "${FRONTEND_IMAGE}" npm --version

    # 測試後端映像
    log_info "測試後端映像..."
    docker run --rm "${BACKEND_IMAGE}" python --version
    docker run --rm "${BACKEND_IMAGE}" pip --version

    log_success "映像驗證完成"
}

# 顯示映像資訊
show_images() {
    log_info "映像資訊:"
    echo ""
    docker images | grep -E "(${REGISTRY}|REPOSITORY)" | head -3
    echo ""

    log_info "映像大小:"
    docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep "${REGISTRY}"
}

# 清理舊映像 (可選)
cleanup_old_images() {
    log_info "清理舊映像..."

    # 詢問用戶是否要清理
    read -p "是否要清理舊的映像? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker image prune -f
        log_success "舊映像已清理"
    else
        log_info "跳過清理"
    fi
}

# 推送映像到遠端倉庫 (可選)
push_images() {
    log_warning "推送映像功能尚未實現"
    log_info "如需推送到遠端倉庫，請手動執行:"
    echo "  docker tag ${FRONTEND_IMAGE} your-registry/${FRONTEND_IMAGE}"
    echo "  docker push your-registry/${FRONTEND_IMAGE}"
    echo "  docker tag ${BACKEND_IMAGE} your-registry/${BACKEND_IMAGE}"
    echo "  docker push your-registry/${BACKEND_IMAGE}"
}

# 主函數
main() {
    log_info "=== Tier 1.5 Docker 映像構建開始 ==="

    # 檢查工作目錄
    if [ ! -f "CLAUDE.md" ]; then
        log_error "請在專案根目錄執行此腳本"
        exit 1
    fi

    # 執行構建流程
    check_docker
    build_frontend
    build_backend
    verify_images
    show_images
    cleanup_old_images
    push_images

    log_success "=== 映像構建完成 ==="
    log_info "你現在可以在 GitHub Actions 中使用這些映像:"
    echo "  - 前端: ${FRONTEND_IMAGE}"
    echo "  - 後端: ${BACKEND_IMAGE}"
}

# 執行主函數
main "$@"
