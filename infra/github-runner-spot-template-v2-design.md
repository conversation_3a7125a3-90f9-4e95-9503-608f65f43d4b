# GitHub Runner Spot Template v2 設計文檔

## 📋 當前 v1 版本分析

### 實例配置
- **Instance ID**: `i-0d605ea28987f7361`
- **Instance Type**: `t3.medium`
- **AMI ID**: `ami-0bb2c57f7cfafb1cb` (標準 Amazon Linux 2023)
- **Availability Zone**: `ap-northeast-1a`
- **Security Group**: `ec2-rds-1`
- **IAM Role**: 無 (404 錯誤)

### v1 User Data 腳本問題分析

```bash
#!/bin/bash
# Current v1 User Data (問題很多)

# ❌ 問題 1: 大量重複安裝
sudo dnf update -y
sudo dnf install -y git docker python3.11 python3.11-pip nodejs npm postgresql15

# ❌ 問題 2: 硬編碼且可能過期的 Token
RUNNER_TOKEN="AKGWK3SQZB5KUNZRLUYCRLTIK2HWY"  # gitleaks:allow

# ❌ 問題 3: 複雜的動態下載和解壓
LATEST_VERSION=$(curl -s https://api.github.com/repos/actions/runner/releases/latest...)
curl -o "actions-runner-linux-x64-${LATEST_VERSION}.tar.gz"...
tar xzf ./*.tar.gz

# ❌ 問題 4: 長期運行的 Runner (非 ephemeral)
./config.sh --url ... --replace  # 缺少 --ephemeral
```

### v1 版本的主要痛點
1. **啟動時間長**: 每次都需要下載、安裝、配置 (5-10 分鐘)
2. **不安全**: 硬編碼 Token，無 IAM 角色
3. **不可靠**: 網路問題可能導致下載失敗
4. **不經濟**: 非 ephemeral runner 造成資源浪費
5. **難維護**: User Data 腳本過於複雜

## 🎯 v2 版本設計目標 (修正版)

### 核心改進
- **啟動時間**: 從 5-10 分鐘縮短到 1-2 分鐘 (實例預熱)
- **任務隔離**: Long-lived Runner + Docker 容器化 (近 stateless)
- **安全性**: JIT Token + IAM 角色 + 容器隔離
- **可靠性**: 預安裝所有依賴，Docker 緩存層
- **經濟性**: Spot Instance + 長時間存活 + 資源複用
- **可維護性**: 簡化 User Data，Docker 標準化

## 🏗️ v2 Launch Template 設計

### 1. AMI 配置
```json
{
  "ImageId": "ami-xxxxxxxx",  // 我們的黃金 AMI ID (待構建)
  "Description": "GitHub Runner Golden AMI with pre-installed dependencies"
}
```

### 2. 實例配置
```json
{
  "InstanceType": "t3.medium",  // 可根據 CI 負載調整
  "KeyName": "novel-bastion-key-v2",
  "SecurityGroupIds": ["sg-xxxxxxxx"],  // 最小權限 SG
  "IamInstanceProfile": {
    "Name": "GitHubRunnerRole"  // 新的 IAM 角色
  }
}
```

### 3. Spot Instance 配置
```json
{
  "InstanceMarketOptions": {
    "MarketType": "spot",
    "SpotOptions": {
      "MaxPrice": "0.05",  // 設定最高價格
      "SpotInstanceType": "one-time",
      "InstanceInterruptionBehavior": "terminate"
    }
  }
}
```

### 4. 標籤配置
```json
{
  "TagSpecifications": [
    {
      "ResourceType": "instance",
      "Tags": [
        {"Key": "Name", "Value": "github-runner-spot"},
        {"Key": "Project", "Value": "novel-website"},
        {"Key": "Component", "Value": "ci-cd"},
        {"Key": "Environment", "Value": "production"},
        {"Key": "ManagedBy", "Value": "auto-scaling"},
        {"Key": "Version", "Value": "v2"}
      ]
    }
  ]
}
```

## 🔐 IAM 角色設計

### GitHubRunnerRole 權限需求

#### 1. GitHub API 存取
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "arn:aws:secretsmanager:ap-northeast-1:*:secret:github-runner-*"
    }
  ]
}
```

#### 2. CloudWatch 日誌
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:ap-northeast-1:*:log-group:/aws/ec2/github-runner*"
    }
  ]
}
```

#### 3. 實例自我管理
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ec2:DescribeInstances",
        "ec2:TerminateInstances"
      ],
      "Resource": "*",
      "Condition": {
        "StringEquals": {
          "ec2:ResourceTag/Component": "ci-cd"
        }
      }
    }
  ]
}
```

## 📜 v2 User Data 腳本設計

### 簡化後的 User Data
```bash
#!/bin/bash
# GitHub Runner Spot Template v2 - Minimal User Data
# All dependencies pre-installed in Golden AMI

exec > >(tee /var/log/github-runner-startup.log) 2>&1
echo "=== GitHub Runner v2 Startup: $(date) ==="

# 配置變數
REPO_URL="https://github.com/MumuTW/novel-web"
RUNNER_DIR="/home/<USER>/actions-runner"
INSTANCE_ID=$(curl -s http://169.254.169.254/latest/meta-data/instance-id)
RUNNER_NAME="spot-runner-${INSTANCE_ID}"

# 1. 從 AWS Secrets Manager 獲取 GitHub App 配置
echo "Retrieving GitHub App credentials..."
GITHUB_APP_ID=$(aws secretsmanager get-secret-value \
  --secret-id "github-runner/app-id" \
  --query SecretString --output text)
GITHUB_APP_KEY=$(aws secretsmanager get-secret-value \
  --secret-id "github-runner/private-key" \
  --query SecretString --output text)

# 2. 生成 JWT 並獲取 Installation Token
echo "Generating GitHub App JWT..."
JWT=$(generate_github_app_jwt "$GITHUB_APP_ID" "$GITHUB_APP_KEY")
INSTALLATION_TOKEN=$(get_github_installation_token "$JWT")

# 3. 獲取 Registration Token
echo "Getting repository registration token..."
REGISTRATION_TOKEN=$(curl -X POST \
  -H "Authorization: token $INSTALLATION_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  "https://api.github.com/repos/MumuTW/novel-web/actions/runners/registration-token" \
  | jq -r '.token')

# 4. 配置 Runner (關鍵: --ephemeral)
echo "Configuring GitHub Runner..."
cd $RUNNER_DIR
sudo -u ec2-user ./config.sh \
  --url $REPO_URL \
  --token $REGISTRATION_TOKEN \
  --name $RUNNER_NAME \
  --labels "spot,linux,x64,docker,python,node,v2" \
  --work "_work" \
  --unattended \
  --ephemeral \
  --replace

# 5. 啟動服務
echo "Starting GitHub Runner service..."
sudo ./svc.sh install ec2-user
sudo ./svc.sh start

# 6. 設置自動清理
echo "Setting up auto-cleanup on job completion..."
cat > /home/<USER>/cleanup-on-completion.sh << 'EOF'
#!/bin/bash
# Monitor for job completion and terminate instance
while true; do
  if ! pgrep -f "Runner.Worker" > /dev/null; then
    echo "No active jobs, terminating instance..."
    aws ec2 terminate-instances --instance-ids $(curl -s http://169.254.169.254/latest/meta-data/instance-id)
    break
  fi
  sleep 30
done
EOF

chmod +x /home/<USER>/cleanup-on-completion.sh
nohup /home/<USER>/cleanup-on-completion.sh &

echo "=== GitHub Runner v2 Setup Complete: $(date) ==="
```

## 🔄 Auto Scaling Group 配置

### ASG 設定建議
```json
{
  "AutoScalingGroupName": "github-runner-spot-v2",
  "LaunchTemplate": {
    "LaunchTemplateName": "github-runner-spot-template-v2",
    "Version": "$Latest"
  },
  "MinSize": 0,
  "MaxSize": 5,
  "DesiredCapacity": 1,
  "AvailabilityZones": [
    "ap-northeast-1a",
    "ap-northeast-1c"
  ],
  "HealthCheckType": "EC2",
  "HealthCheckGracePeriod": 300,
  "Tags": [
    {
      "Key": "Project",
      "Value": "novel-website",
      "PropagateAtLaunch": true
    }
  ]
}
```

## 📊 成本與效能對比

### v1 vs v2 對比表
| 項目 | v1 (當前) | v2 (目標) | 改進 |
|------|-----------|-----------|------|
| 啟動時間 | 5-10 分鐘 | 1-2 分鐘 | 75% ↓ |
| 成本 (每月) | ~$50 | ~$20 | 60% ↓ |
| 安全性 | 低 (硬編碼 Token) | 高 (JIT + IAM) | 🔐 |
| 可靠性 | 中 (網路依賴) | 高 (預安裝) | ⬆️ |
| 維護難度 | 高 (複雜腳本) | 低 (標準化) | ⬇️ |

## 🚀 實施計畫

### Phase 1: 基礎建設 (本週)
1. ✅ 創建 Packer 配置
2. ⏳ 構建黃金 AMI
3. ⏳ 設計 IAM 角色

### Phase 2: Security & Token Management (下週)
1. 設置 GitHub App
2. 配置 AWS Secrets Manager
3. 實現 JWT 生成邏輯

### Phase 3: Template & Testing (第三週)
1. 創建 v2 Launch Template
2. 更新 Auto Scaling Group
3. 端到端測試

### Phase 4: 部署與監控 (第四週)
1. 生產環境部署
2. 監控和告警設置
3. 成本優化調整

## ⚠️ 風險與緩解

### 主要風險
1. **Spot Instance 中斷**: 使用多 AZ 和 Mixed Instance Policy
2. **Token 管理複雜性**: 完善的錯誤處理和重試機制
3. **冷啟動問題**: AMI 預熱和健康檢查

### 緩解策略
1. 實施優雅的中斷處理
2. 備用 On-Demand 實例
3. 完整的監控和告警

---

**建立日期**: 2025-06-22
**版本**: v2.0-draft
**狀態**: 設計階段
