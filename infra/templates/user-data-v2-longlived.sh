#!/bin/bash
# GitHub Runner Spot Template v2 - Long-lived Runner with Docker Isolation
# Optimized for "Super-Job" pattern: warm instance + containerized tasks
#
# Prerequisites: Golden AMI with all dependencies pre-installed
# Architecture: Long-lived EC2 + Short-lived Docker containers

set -euo pipefail  # Exit on any error

# Comprehensive logging
exec > >(tee -a /var/log/github-runner-startup.log) 2>&1
echo "=== GitHub Runner v2 Long-lived Startup: $(date) ==="

# Configuration
readonly REPO_URL="https://github.com/MumuTW/novel-web"
readonly RUNNER_DIR="/home/<USER>/actions-runner"
readonly LOG_GROUP="/aws/ec2/github-runner"

# Instance metadata
readonly TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600" 2>/dev/null)
readonly INSTANCE_ID=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/instance-id 2>/dev/null)
readonly AVAILABILITY_ZONE=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/placement/availability-zone 2>/dev/null)
readonly RUNNER_NAME="longlived-runner-${INSTANCE_ID}"

# Error handling
trap 'handle_error $? $LINENO' ERR

handle_error() {
    local exit_code=$1
    local line_number=$2
    echo "❌ ERROR: Script failed with exit code $exit_code at line $line_number"

    # Send failure notification to CloudWatch
    aws logs put-log-events \
        --log-group-name "$LOG_GROUP" \
        --log-stream-name "${INSTANCE_ID}/startup-errors" \
        --log-events timestamp=$(date +%s000),message="GitHub Runner startup failed: exit_code=$exit_code, line=$line_number" \
        2>/dev/null || true

    # For long-lived runners, we don't terminate on startup failure
    # Instead, we retry after a delay
    echo "Retrying startup in 60 seconds..."
    sleep 60
    exec "$0" "$@"  # Retry the script
}

# Utility functions
log_info() {
    echo "ℹ️ [$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

log_error() {
    echo "❌ [$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1" >&2
}

log_success() {
    echo "✅ [$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to generate GitHub App JWT (same as before)
generate_github_app_jwt() {
    local app_id=$1
    local private_key=$2  # gitleaks:allow
    local now=$(date +%s)
    local exp=$((now + 600))  # 10 minutes expiry

    # Create JWT header
    local header='{"alg":"RS256","typ":"JWT"}'
    local header_b64=$(echo -n "$header" | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

    # Create JWT payload
    local payload="{\"iat\":$now,\"exp\":$exp,\"iss\":\"$app_id\"}"
    local payload_b64=$(echo -n "$payload" | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

    # Create signature
    local signature_input="${header_b64}.${payload_b64}"

    # Save private key to temporary file
    local key_file=$(mktemp)
    echo "$private_key" > "$key_file"  # gitleaks:allow

    # Generate signature
    local signature=$(echo -n "$signature_input" | openssl dgst -sha256 -sign "$key_file" | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

    # Clean up
    rm -f "$key_file"

    echo "${signature_input}.${signature}"
}

# Function to get GitHub installation token
get_github_installation_token() {
    local jwt=$1
    local installation_id

    # Get installation ID for the repository
    installation_id=$(curl -s \
        -H "Authorization: Bearer $jwt" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/app/installations" | \
        jq -r '.[] | select(.account.login=="MumuTW") | .id')

    if [[ -z "$installation_id" || "$installation_id" == "null" ]]; then
        log_error "Failed to get installation ID"
        return 1
    fi

    # Get installation access token
    curl -s \
        -X POST \
        -H "Authorization: Bearer $jwt" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/app/installations/${installation_id}/access_tokens" | \
        jq -r '.token'
}

# Function to get repository registration token
get_registration_token() {
    local installation_token=$1

    curl -s \
        -X POST \
        -H "Authorization: token $installation_token" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/repos/MumuTW/novel-web/actions/runners/registration-token" | \
        jq -r '.token'
}

# Docker optimization function
optimize_docker_for_ci() {
    log_info "Optimizing Docker for CI workloads..."

    # Configure Docker for better performance
    cat > /etc/docker/daemon.json << 'EOF'
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.override_kernel_check=true"
  ],
  "max-concurrent-downloads": 10,
  "max-concurrent-uploads": 5,
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 65536,
      "Soft": 65536
    }
  }
}
EOF

    # Restart Docker with new configuration
    sudo systemctl restart docker

    # Pre-pull common images for faster container startup
    log_info "Pre-pulling common CI images..."
    docker pull node:18-alpine &
    docker pull python:3.11-slim &
    docker pull postgres:15-alpine &
    wait

    # Create a custom network for isolation
    docker network create ci-network --driver bridge || true

    log_success "Docker optimization completed"
}

# Health check function
health_check() {
    log_info "Performing health checks..."

    # Check if runner directory exists
    if [[ ! -d "$RUNNER_DIR" ]]; then
        log_error "Runner directory not found: $RUNNER_DIR"
        return 1
    fi

    # Check if config.sh exists
    if [[ ! -f "$RUNNER_DIR/config.sh" ]]; then
        log_error "Runner config script not found"
        return 1
    fi

    # Check Docker service
    if ! systemctl is-active --quiet docker; then
        log_info "Starting Docker service..."
        sudo systemctl start docker
        sudo systemctl enable docker
    fi

    # Check if ec2-user is in docker group
    if ! groups ec2-user | grep -q docker; then
        log_info "Adding ec2-user to docker group..."
        sudo usermod -aG docker ec2-user
        # Note: Group membership will be effective after next login/restart
    fi

    # Check disk space (should have at least 5GB free for long-lived runner)
    local available_gb=$(df / | awk 'NR==2 {print int($4/1024/1024)}')
    if [[ $available_gb -lt 5 ]]; then
        log_error "Insufficient disk space: ${available_gb}GB available"
        return 1
    fi

    log_success "Health checks passed"
}

# Container cleanup function
setup_container_cleanup() {
    log_info "Setting up container cleanup routines..."

    # Create cleanup script for finished containers
    cat > /home/<USER>/cleanup-containers.sh << 'EOF'
#!/bin/bash
# Container cleanup script for long-lived runners
# Removes stopped containers and unused images to free up space

echo "$(date): Starting container cleanup..."

# Remove stopped containers older than 1 hour
docker container prune -f --filter "until=1h"

# Remove unused images (but keep frequently used ones)
docker image prune -f --filter "until=24h"

# Remove unused volumes
docker volume prune -f

# Remove unused networks (except our CI network)
docker network prune -f --filter "name!=ci-network"

# Log disk usage
echo "$(date): Disk usage after cleanup:"
df -h /

echo "$(date): Container cleanup completed"
EOF

    chmod +x /home/<USER>/cleanup-containers.sh
    chown ec2-user:ec2-user /home/<USER>/cleanup-containers.sh

    # Set up periodic cleanup (every 4 hours)
    (crontab -u ec2-user -l 2>/dev/null || echo "") | grep -v cleanup-containers.sh | (cat; echo "0 */4 * * * /home/<USER>/cleanup-containers.sh >> /var/log/container-cleanup.log 2>&1") | crontab -u ec2-user -

    log_success "Container cleanup routines configured"
}

# Monitoring and metrics setup
setup_monitoring() {
    log_info "Setting up monitoring for long-lived runner..."

    # Create monitoring script
    cat > /home/<USER>/runner-monitor.sh << 'EOF'
#!/bin/bash
# Monitor runner health and send metrics to CloudWatch

INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)

# Check if runner service is running
if systemctl is-active --quiet actions.runner.* ; then
    aws cloudwatch put-metric-data \
        --namespace "GitHubRunner" \
        --metric-data MetricName=RunnerHealth,Value=1,Unit=Count,Dimensions=InstanceId=$INSTANCE_ID
else
    aws cloudwatch put-metric-data \
        --namespace "GitHubRunner" \
        --metric-data MetricName=RunnerHealth,Value=0,Unit=Count,Dimensions=InstanceId=$INSTANCE_ID
fi

# Monitor Docker daemon
if systemctl is-active --quiet docker ; then
    aws cloudwatch put-metric-data \
        --namespace "GitHubRunner" \
        --metric-data MetricName=DockerHealth,Value=1,Unit=Count,Dimensions=InstanceId=$INSTANCE_ID
else
    aws cloudwatch put-metric-data \
        --namespace "GitHubRunner" \
        --metric-data MetricName=DockerHealth,Value=0,Unit=Count,Dimensions=InstanceId=$INSTANCE_ID
fi

# Monitor disk usage
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
aws cloudwatch put-metric-data \
    --namespace "GitHubRunner" \
    --metric-data MetricName=DiskUsage,Value=$DISK_USAGE,Unit=Percent,Dimensions=InstanceId=$INSTANCE_ID

# Monitor running containers
CONTAINER_COUNT=$(docker ps --format "table {{.ID}}" | wc -l | xargs)
aws cloudwatch put-metric-data \
    --namespace "GitHubRunner" \
    --metric-data MetricName=RunningContainers,Value=$CONTAINER_COUNT,Unit=Count,Dimensions=InstanceId=$INSTANCE_ID
EOF

    chmod +x /home/<USER>/runner-monitor.sh
    chown ec2-user:ec2-user /home/<USER>/runner-monitor.sh

    # Set up monitoring (every 5 minutes)
    (crontab -u ec2-user -l 2>/dev/null || echo "") | grep -v runner-monitor.sh | (cat; echo "*/5 * * * * /home/<USER>/runner-monitor.sh") | crontab -u ec2-user -

    log_success "Monitoring setup completed"
}

# Main execution
main() {
    log_info "=== Starting GitHub Runner v2 Long-lived Configuration ==="
    log_info "Instance ID: $INSTANCE_ID"
    log_info "Availability Zone: $AVAILABILITY_ZONE"
    log_info "Runner Name: $RUNNER_NAME"
    log_info "Architecture: Long-lived Runner + Docker Containers"

    # Health checks
    health_check

    # Optimize Docker for CI workloads
    optimize_docker_for_ci

    # 1. Retrieve GitHub App credentials from AWS Secrets Manager
    log_info "Retrieving GitHub App credentials from Secrets Manager..."

    local github_app_id
    local github_app_private_key  # gitleaks:allow

    github_app_id=$(aws secretsmanager get-secret-value \
        --secret-id "github-runner/app-id" \
        --query SecretString \
        --output text 2>/dev/null) || {
        log_error "Failed to retrieve GitHub App ID from Secrets Manager"
        return 1
    }

    github_app_private_key=$(aws secretsmanager get-secret-value \  # gitleaks:allow
        --secret-id "github-runner/private-key" \
        --query SecretString \
        --output text 2>/dev/null) || {  # gitleaks:allow
        log_error "Failed to retrieve GitHub App private key from Secrets Manager"
        return 1
    }

    log_success "GitHub App credentials retrieved"

    # 2. Generate JWT and get installation token
    log_info "Generating GitHub App JWT..."
    local jwt
    jwt=$(generate_github_app_jwt "$github_app_id" "$github_app_private_key") || {  # gitleaks:allow
        log_error "Failed to generate GitHub App JWT"
        return 1
    }

    log_info "Getting GitHub installation token..."
    local installation_token
    installation_token=$(get_github_installation_token "$jwt") || {
        log_error "Failed to get GitHub installation token"
        return 1
    }

    log_success "GitHub installation token obtained"

    # 3. Get repository registration token
    log_info "Getting repository registration token..."
    local registration_token
    registration_token=$(get_registration_token "$installation_token") || {
        log_error "Failed to get repository registration token"
        return 1
    }

    log_success "Repository registration token obtained"

    # 4. Configure GitHub Runner for long-lived operation (NOT ephemeral)
    log_info "Configuring GitHub Runner (long-lived mode)..."
    cd "$RUNNER_DIR"

    sudo -u ec2-user ./config.sh \
        --url "$REPO_URL" \
        --token "$registration_token" \
        --name "$RUNNER_NAME" \
        --labels "longlived,linux,x64,docker,python,node,v2,${AVAILABILITY_ZONE}" \
        --work "_work" \
        --unattended \
        --replace || {
        log_error "Failed to configure GitHub Runner"
        return 1
    }

    log_success "GitHub Runner configured successfully (long-lived mode)"

    # 5. Install and start the runner service
    log_info "Installing and starting runner service..."
    sudo ./svc.sh install ec2-user || {
        log_error "Failed to install runner service"
        return 1
    }

    sudo ./svc.sh start || {
        log_error "Failed to start runner service"
        return 1
    }

    log_success "Runner service started successfully"

    # 6. Set up container cleanup and monitoring
    setup_container_cleanup
    setup_monitoring

    # 7. Final verification
    log_info "Performing final verification..."
    sleep 10

    if sudo ./svc.sh status | grep -q "active (running)"; then
        log_success "Runner service is active and running"
    else
        log_error "Runner service verification failed"
        return 1
    fi

    # 8. Send success notification
    aws logs put-log-events \
        --log-group-name "$LOG_GROUP" \
        --log-stream-name "${INSTANCE_ID}/startup-success" \
        --log-events timestamp=$(date +%s000),message="GitHub Runner v2 long-lived startup completed successfully. Runner: $RUNNER_NAME" \
        2>/dev/null || true

    log_success "=== GitHub Runner v2 Long-lived Setup Complete: $(date) ==="
    log_info "Runner Name: $RUNNER_NAME"
    log_info "Mode: Long-lived (will handle multiple jobs with Docker isolation)"
    log_info "Labels: longlived,linux,x64,docker,python,node,v2,$AVAILABILITY_ZONE"
    log_info "Repository: $REPO_URL"
    log_info "Container Network: ci-network"
    log_info "Cleanup Schedule: Every 4 hours"
    log_info "Monitoring: Every 5 minutes → CloudWatch"
}

# Execute main function
main "$@"
