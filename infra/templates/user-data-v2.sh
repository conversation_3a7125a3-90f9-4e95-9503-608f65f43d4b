#!/bin/bash
# GitHub Runner Spot Template v2 - Production User Data Script
# Minimal, secure, and efficient startup for pre-configured Golden AMI
#
# Prerequisites: Golden AMI with all dependencies pre-installed
# Security: Uses GitHub App + AWS Secrets Manager for token management

set -euo pipefail  # Exit on any error

# Comprehensive logging
exec > >(tee -a /var/log/github-runner-startup.log) 2>&1
echo "=== GitHub Runner v2 Startup Started: $(date) ==="

# Configuration
readonly REPO_URL="https://github.com/MumuTW/novel-web"
readonly RUNNER_DIR="/home/<USER>/actions-runner"
readonly LOG_GROUP="/aws/ec2/github-runner"

# Instance metadata
readonly TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600" 2>/dev/null)
readonly INSTANCE_ID=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/instance-id 2>/dev/null)
readonly AVAILABILITY_ZONE=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/placement/availability-zone 2>/dev/null)
readonly RUNNER_NAME="spot-runner-${INSTANCE_ID}"

# Error handling
trap 'handle_error $? $LINENO' ERR

handle_error() {
    local exit_code=$1
    local line_number=$2
    echo "❌ ERROR: Script failed with exit code $exit_code at line $line_number"

    # Send failure notification to CloudWatch
    aws logs put-log-events \
        --log-group-name "$LOG_GROUP" \
        --log-stream-name "${INSTANCE_ID}/startup-errors" \
        --log-events timestamp=$(date +%s000),message="GitHub Runner startup failed: exit_code=$exit_code, line=$line_number" \
        2>/dev/null || true

    # Terminate failed instance to avoid charges
    echo "Terminating failed instance..."
    aws ec2 terminate-instances --instance-ids "$INSTANCE_ID" 2>/dev/null || true
    exit $exit_code
}

# Utility functions
log_info() {
    echo "ℹ️ [$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

log_error() {
    echo "❌ [$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1" >&2
}

log_success() {
    echo "✅ [$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to generate GitHub App JWT
generate_github_app_jwt() {
    local app_id=$1
    local private_key=$2  # gitleaks:allow
    local now=$(date +%s)
    local exp=$((now + 600))  # 10 minutes expiry

    # Create JWT header
    local header='{"alg":"RS256","typ":"JWT"}'
    local header_b64=$(echo -n "$header" | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

    # Create JWT payload
    local payload="{\"iat\":$now,\"exp\":$exp,\"iss\":\"$app_id\"}"
    local payload_b64=$(echo -n "$payload" | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

    # Create signature
    local signature_input="${header_b64}.${payload_b64}"

    # Save private key to temporary file
    local key_file=$(mktemp)
    echo "$private_key" > "$key_file"  # gitleaks:allow

    # Generate signature
    local signature=$(echo -n "$signature_input" | openssl dgst -sha256 -sign "$key_file" | base64 | tr -d '=' | tr '/+' '_-' | tr -d '\n')

    # Clean up
    rm -f "$key_file"

    echo "${signature_input}.${signature}"
}

# Function to get GitHub installation token
get_github_installation_token() {
    local jwt=$1
    local installation_id

    # Get installation ID for the repository
    installation_id=$(curl -s \
        -H "Authorization: Bearer $jwt" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/app/installations" | \
        jq -r '.[] | select(.account.login=="MumuTW") | .id')

    if [[ -z "$installation_id" || "$installation_id" == "null" ]]; then
        log_error "Failed to get installation ID"
        return 1
    fi

    # Get installation access token
    curl -s \
        -X POST \
        -H "Authorization: Bearer $jwt" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/app/installations/${installation_id}/access_tokens" | \
        jq -r '.token'
}

# Function to get repository registration token
get_registration_token() {
    local installation_token=$1

    curl -s \
        -X POST \
        -H "Authorization: token $installation_token" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/repos/MumuTW/novel-web/actions/runners/registration-token" | \
        jq -r '.token'
}

# Health check function
health_check() {
    log_info "Performing health checks..."

    # Check if runner directory exists
    if [[ ! -d "$RUNNER_DIR" ]]; then
        log_error "Runner directory not found: $RUNNER_DIR"
        return 1
    fi

    # Check if config.sh exists
    if [[ ! -f "$RUNNER_DIR/config.sh" ]]; then
        log_error "Runner config script not found"
        return 1
    fi

    # Check Docker service
    if ! systemctl is-active --quiet docker; then
        log_info "Starting Docker service..."
        sudo systemctl start docker
    fi

    # Check disk space (should have at least 2GB free)
    local available_gb=$(df / | awk 'NR==2 {print int($4/1024/1024)}')
    if [[ $available_gb -lt 2 ]]; then
        log_error "Insufficient disk space: ${available_gb}GB available"
        return 1
    fi

    log_success "Health checks passed"
}

# Main execution
main() {
    log_info "=== Starting GitHub Runner v2 Configuration ==="
    log_info "Instance ID: $INSTANCE_ID"
    log_info "Availability Zone: $AVAILABILITY_ZONE"
    log_info "Runner Name: $RUNNER_NAME"

    # Health checks
    health_check

    # 1. Retrieve GitHub App credentials from AWS Secrets Manager
    log_info "Retrieving GitHub App credentials from Secrets Manager..."

    local github_app_id
    local github_app_private_key  # gitleaks:allow

    github_app_id=$(aws secretsmanager get-secret-value \
        --secret-id "github-runner/app-id" \
        --query SecretString \
        --output text 2>/dev/null) || {
        log_error "Failed to retrieve GitHub App ID from Secrets Manager"
        return 1
    }

    github_app_private_key=$(aws secretsmanager get-secret-value \  # gitleaks:allow
        --secret-id "github-runner/private-key" \
        --query SecretString \
        --output text 2>/dev/null) || {
        log_error "Failed to retrieve GitHub App private key from Secrets Manager"
        return 1
    }

    log_success "GitHub App credentials retrieved"

    # 2. Generate JWT and get installation token
    log_info "Generating GitHub App JWT..."
    local jwt
    jwt=$(generate_github_app_jwt "$github_app_id" "$github_app_private_key") || {  # gitleaks:allow
        log_error "Failed to generate GitHub App JWT"
        return 1
    }

    log_info "Getting GitHub installation token..."
    local installation_token
    installation_token=$(get_github_installation_token "$jwt") || {
        log_error "Failed to get GitHub installation token"
        return 1
    }

    log_success "GitHub installation token obtained"

    # 3. Get repository registration token
    log_info "Getting repository registration token..."
    local registration_token
    registration_token=$(get_registration_token "$installation_token") || {
        log_error "Failed to get repository registration token"
        return 1
    }

    log_success "Repository registration token obtained"

    # 4. Configure GitHub Runner with ephemeral mode
    log_info "Configuring GitHub Runner (ephemeral mode)..."
    cd "$RUNNER_DIR"

    sudo -u ec2-user ./config.sh \
        --url "$REPO_URL" \
        --token "$registration_token" \
        --name "$RUNNER_NAME" \
        --labels "spot,linux,x64,docker,python,node,v2,${AVAILABILITY_ZONE}" \
        --work "_work" \
        --unattended \
        --ephemeral \
        --replace || {
        log_error "Failed to configure GitHub Runner"
        return 1
    }

    log_success "GitHub Runner configured successfully"

    # 5. Install and start the runner service
    log_info "Installing and starting runner service..."
    sudo ./svc.sh install ec2-user || {
        log_error "Failed to install runner service"
        return 1
    }

    sudo ./svc.sh start || {
        log_error "Failed to start runner service"
        return 1
    }

    log_success "Runner service started successfully"

    # 6. Set up auto-cleanup mechanism
    log_info "Setting up auto-cleanup on job completion..."
    cat > /home/<USER>/cleanup-on-completion.sh << 'EOF'
#!/bin/bash
# Monitor for job completion and terminate instance
# This ensures cost optimization for ephemeral runners

LOG_FILE="/var/log/github-runner-cleanup.log"
exec > >(tee -a "$LOG_FILE") 2>&1

echo "=== Auto-cleanup monitor started: $(date) ==="

# Wait for runner to be fully ready
sleep 60

# Monitor for active jobs
while true; do
    # Check if Runner.Worker process is running (indicates active job)
    if ! pgrep -f "Runner.Worker" > /dev/null; then
        echo "No active jobs detected, checking again in 30 seconds..."
        sleep 30

        # Double-check to avoid false positives
        if ! pgrep -f "Runner.Worker" > /dev/null; then
            echo "Confirmed: No active jobs. Terminating instance for cost optimization..."

            # Send termination notification to CloudWatch
            aws logs put-log-events \
                --log-group-name "/aws/ec2/github-runner" \
                --log-stream-name "$(curl -s http://***************/latest/meta-data/instance-id)/auto-cleanup" \
                --log-events timestamp=$(date +%s000),message="Auto-terminating idle ephemeral runner" \
                2>/dev/null || true

            # Graceful termination
            sudo /home/<USER>/actions-runner/svc.sh stop 2>/dev/null || true

            # Terminate instance
            aws ec2 terminate-instances \
                --instance-ids "$(curl -s http://***************/latest/meta-data/instance-id)" \
                2>/dev/null || true

            break
        fi
    else
        echo "Active job detected, continuing to monitor..."
        sleep 60
    fi
done
EOF

    chmod +x /home/<USER>/cleanup-on-completion.sh
    nohup /home/<USER>/cleanup-on-completion.sh > /var/log/cleanup-monitor.log 2>&1 &

    log_success "Auto-cleanup mechanism configured"

    # 7. Final verification
    log_info "Performing final verification..."
    sleep 10

    if sudo ./svc.sh status | grep -q "active (running)"; then
        log_success "Runner service is active and running"
    else
        log_error "Runner service verification failed"
        return 1
    fi

    # 8. Send success notification
    aws logs put-log-events \
        --log-group-name "$LOG_GROUP" \
        --log-stream-name "${INSTANCE_ID}/startup-success" \
        --log-events timestamp=$(date +%s000),message="GitHub Runner v2 startup completed successfully. Runner: $RUNNER_NAME" \
        2>/dev/null || true

    log_success "=== GitHub Runner v2 Setup Complete: $(date) ==="
    log_info "Runner Name: $RUNNER_NAME"
    log_info "Mode: Ephemeral (will auto-terminate after job completion)"
    log_info "Labels: spot,linux,x64,docker,python,node,v2,$AVAILABILITY_ZONE"
    log_info "Repository: $REPO_URL"
}

# Execute main function
main "$@"
