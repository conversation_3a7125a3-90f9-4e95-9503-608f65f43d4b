# GitHub Runner Spot Template v2 實施指南

## 📋 實施檢查清單

### Phase 1: 前置準備 ✅
- [x] 分析當前 v1 配置
- [x] 設計 v2 架構
- [x] 創建 User Data 腳本
- [x] 定義 IAM 權限

### Phase 2: GitHub App 設置 🔄
- [ ] 創建 GitHub App
- [ ] 生成 Private Key
- [ ] 設置 Webhook (可選)
- [ ] 配置權限範圍

### Phase 3: AWS 資源配置 🔄
- [ ] 構建黃金 AMI
- [ ] 創建 IAM 角色
- [ ] 設置 Secrets Manager
- [ ] 創建 Security Group
- [ ] 建立 Launch Template v2

### Phase 4: 測試與驗證 ⏳
- [ ] 單實例測試
- [ ] Auto Scaling 測試
- [ ] 故障恢復測試
- [ ] 成本驗證

## 🛠️ 詳細實施步驟

### 1. 構建黃金 AMI

```bash
# 使用 Packer 構建
make build-golden-ami

# 記錄 AMI ID
export GOLDEN_AMI_ID="ami-xxxxxxxxx"
```

### 2. 創建 GitHub App

#### 2.1 在 GitHub 中創建 App
1. 訪問 GitHub Settings > Developer settings > GitHub Apps
2. 點擊 "New GitHub App"
3. 配置基本信息：
   ```
   GitHub App name: NovelWebsite-Runner-App
   Homepage URL: https://github.com/MumuTW/novel-web
   Webhook URL: (可留空)
   ```

#### 2.2 設置權限
```json
{
  "Repository permissions": {
    "Actions": "write",
    "Administration": "read",
    "Metadata": "read"
  },
  "Organization permissions": {
    "Self-hosted runners": "write"
  }
}
```

#### 2.3 安裝到 Repository
- 選擇 "Install App"
- 選擇 "MumuTW/novel-web" repository

### 3. 配置 AWS Secrets Manager

#### 3.1 存儲 GitHub App ID
```bash
aws secretsmanager create-secret \
  --name "github-runner/app-id" \
  --description "GitHub App ID for runner registration" \
  --secret-string "123456" \
  --tags Key=Project,Value=novel-website Key=Component,Value=github-runner
```

#### 3.2 存儲 GitHub App Private Key
```bash
aws secretsmanager create-secret \
  --name "github-runner/private-key" \
  --description "GitHub App private key for JWT generation" \
  --secret-string "$(cat /path/to/private-key.pem)" \
  --tags Key=Project,Value=novel-website Key=Component,Value=github-runner
```

### 4. 創建 IAM 角色

#### 4.1 創建信任政策
```bash
aws iam create-role \
  --role-name GitHubRunnerRole \
  --assume-role-policy-document file://infra/templates/iam-trust-policy.json \
  --description "IAM role for GitHub Actions Runner EC2 instances"
```

#### 4.2 附加權限政策
```bash
aws iam put-role-policy \
  --role-name GitHubRunnerRole \
  --policy-name GitHubRunnerPolicy \
  --policy-document file://infra/templates/iam-role-policy.json
```

#### 4.3 創建實例配置檔案
```bash
aws iam create-instance-profile \
  --instance-profile-name GitHubRunnerInstanceProfile

aws iam add-role-to-instance-profile \
  --instance-profile-name GitHubRunnerInstanceProfile \
  --role-name GitHubRunnerRole
```

### 5. 創建 Security Group

```bash
aws ec2 create-security-group \
  --group-name github-runner-v2-sg \
  --description "Security group for GitHub Runner v2" \
  --vpc-id vpc-xxxxxxxxx

# 記錄 Security Group ID
export SECURITY_GROUP_ID="sg-xxxxxxxxx"

# 允許 SSH 訪問 (僅用於調試)
aws ec2 authorize-security-group-ingress \
  --group-id $SECURITY_GROUP_ID \
  --protocol tcp \
  --port 22 \
  --cidr 0.0.0.0/0

# 允許 HTTPS 出站流量
aws ec2 authorize-security-group-egress \
  --group-id $SECURITY_GROUP_ID \
  --protocol tcp \
  --port 443 \
  --cidr 0.0.0.0/0
```

### 6. 準備 User Data 腳本

```bash
# Base64 編碼 User Data 腳本
base64 -i infra/templates/user-data-v2.sh -o user-data-v2-encoded.txt

# 複製到 Launch Template JSON
# 將編碼內容貼到 launch-template-v2.json 的 UserData 欄位
```

### 7. 創建 Launch Template v2

#### 7.1 更新 Launch Template JSON
```bash
# 替換 placeholder 值
sed -i '' "s/ami-PLACEHOLDER-GOLDEN-AMI/$GOLDEN_AMI_ID/g" infra/templates/launch-template-v2.json
sed -i '' "s/sg-PLACEHOLDER-SECURITY-GROUP/$SECURITY_GROUP_ID/g" infra/templates/launch-template-v2.json
```

#### 7.2 創建 Launch Template
```bash
aws ec2 create-launch-template \
  --cli-input-json file://infra/templates/launch-template-v2.json
```

### 8. 測試新配置

#### 8.1 手動啟動測試實例
```bash
aws ec2 run-instances \
  --launch-template LaunchTemplateName=github-runner-spot-template-v2,Version=1 \
  --min-count 1 \
  --max-count 1
```

#### 8.2 監控啟動過程
```bash
# 查看啟動日誌
aws logs get-log-events \
  --log-group-name "/aws/ec2/github-runner" \
  --log-stream-name "i-xxxxxxxxx/startup-success"
```

### 9. 更新 Auto Scaling Group

```bash
aws autoscaling update-auto-scaling-group \
  --auto-scaling-group-name github-runner-spot-v2 \
  --launch-template LaunchTemplateName=github-runner-spot-template-v2,Version='$Latest'
```

## 🔍 驗證測試

### 測試案例 1: 基本啟動
```bash
# 預期結果: 實例在 2 分鐘內完成註冊
# 檢查: GitHub Repository Settings > Actions > Runners
```

### 測試案例 2: Job 執行
```bash
# 觸發 CI/CD pipeline
# 預期結果: Job 成功執行並完成
```

### 測試案例 3: 自動清理
```bash
# 預期結果: Job 完成後實例自動終止
# 檢查: EC2 Console 中實例狀態
```

### 測試案例 4: 故障處理
```bash
# 模擬配置錯誤
# 預期結果: 實例檢測到錯誤並自動終止
```

## 📊 監控指標

### CloudWatch 指標
- `GitHubRunner.StartupTime` - 啟動時間
- `GitHubRunner.JobDuration` - Job 執行時間
- `GitHubRunner.FailureRate` - 失敗率

### 成本監控
- 每月 Spot Instance 費用
- 相比 v1 的成本節省
- 平均實例生命週期

## 🚨 故障排除

### 常見問題

#### 1. GitHub App 認證失敗
```bash
# 檢查 App ID 和 Private Key
aws secretsmanager get-secret-value --secret-id "github-runner/app-id"

# 驗證 JWT 生成
curl -H "Authorization: Bearer $JWT" https://api.github.com/app
```

#### 2. IAM 權限不足
```bash
# 檢查 IAM 角色
aws iam get-role --role-name GitHubRunnerRole

# 測試權限
aws sts assume-role --role-arn arn:aws:iam::ACCOUNT:role/GitHubRunnerRole --role-session-name test
```

#### 3. Runner 註冊失敗
```bash
# 檢查實例日誌
ssh -i ~/.ssh/novel-bastion-key-v2.pem ec2-user@INSTANCE_IP
tail -f /var/log/github-runner-startup.log
```

### 回退計畫
如果 v2 出現問題，可以快速回退到 v1：

```bash
# 1. 停止 v2 Auto Scaling Group
aws autoscaling update-auto-scaling-group \
  --auto-scaling-group-name github-runner-spot-v2 \
  --desired-capacity 0

# 2. 啟動 v1 配置
aws autoscaling update-auto-scaling-group \
  --auto-scaling-group-name github-runner-spot-v1 \
  --desired-capacity 1
```

## ✅ 完成檢查

部署完成後，確認以下項目：

- [ ] GitHub Repository 中可以看到新的 v2 runner
- [ ] Runner 標籤包含 "v2", "spot", "ephemeral"
- [ ] CI/CD pipeline 可以成功執行
- [ ] Job 完成後實例自動終止
- [ ] CloudWatch 中可以看到相關日誌
- [ ] 成本監控顯示預期的節省

---

**實施負責人**: DevOps Team
**預計完成時間**: 2-3 個工作日
**風險等級**: 中 (有回退方案)
**成功標準**: 啟動時間 < 2分鐘，成本節省 > 50%
