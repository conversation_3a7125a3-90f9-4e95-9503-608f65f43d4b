# TypeScript Any型別修復計劃

## 修復目標
修復6個檔案中的12個any型別使用，提升程式碼品質和型別安全性。

## 修復策略

### 1. API相關型別定義強化

#### apps/web-next/lib/api.ts (主要目標)
- **APIError.data**: `any` → `Record<string, unknown> | string | null`
- **RequestConfig.body**: `any` → `BodyInit | Record<string, unknown> | null`
- **泛型默認值**: `T = any` → `T = unknown`
- **params**: `Record<string, any>` → `Record<string, string | number | boolean>`

#### apps/web-sunset/src/services/api.ts
- **axios config**: `any` → 使用適當的axios型別

#### frontend/src/services/api.ts
- **axios config**: `any` → 使用適當的axios型別

### 2. 組件型別定義

#### ChapterDetail.tsx (兩個位置)
- **事件處理**: `any` → 適當的事件型別
- **錯誤處理**: `any` → `Error | unknown`

### 3. 測試和API頁面
- **api-test頁面**: `any` → 具體的API響應型別
- **proxy route**: `any` → NextRequest/NextResponse型別

## 實作順序
1. 建立統一的型別定義檔案
2. 修復核心API客戶端 (apps/web-next/lib/api.ts)
3. 修復其他API服務層
4. 修復組件層級
5. 修復測試和工具頁面
6. 執行完整測試驗證

## 驗證方式
- TypeScript編譯檢查: `npx tsc --noEmit`
- ESLint檢查: `npx eslint . --fix`
- 單元測試: `npm test`
- 建立測試: `npm run build`

---

## 🛡️ 型別安全守護機制 (已實施 - Issue #189)

### 三層防護體系

#### 第一層：Pre-commit Hook 自動檢查
- **位置**: `.pre-commit-config.yaml`
- **觸發**: 每次 `git commit` 前
- **指令**: `pnpm turbo run type-check`
- **功能**: 阻止任何包含型別錯誤或 `any` 的提交

#### 第二層：CI/CD 最後防線
- **位置**: `.github/workflows/main-ci.yml`
- **觸發**: 每次 PR 和 main 分支更新
- **指令**: `pnpm --filter @novelwebsite/web-next type-check`
- **功能**: 確保即使本地檢查被跳過，CI 也會攔截

#### 第三層：開發者工具整合
- **指令**: `pnpm typecheck` 或 `pnpm type-check`
- **功能**: 開發者可隨時手動執行型別檢查
- **IDE整合**: VSCode TypeScript 即時檢查

### 守護規範

#### 嚴格模式配置
```json
// tsconfig.json 必須包含
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noEmit": true
  }
}
```

#### 禁用項目
- ❌ 禁止使用 `any` 型別
- ❌ 禁止使用 `@ts-ignore` 註解
- ❌ 禁止停用 strict 模式
- ❌ 禁止跳過 pre-commit hooks

#### 例外處理
如果必須使用動態型別，請使用：
- ✅ `unknown` 型別 + 型別守衛
- ✅ 聯合型別 (Union Types)
- ✅ 泛型約束 (Generic Constraints)
- ✅ 型別斷言 (Type Assertions) 配合註解說明

### 維護和監控

#### 定期檢查清單
- [ ] 每月檢查 `tsconfig.json` 配置完整性
- [ ] 確認所有新增檔案都通過型別檢查
- [ ] 檢查 pre-commit hooks 是否正常運作
- [ ] 驗證 CI 型別檢查是否有效執行

#### 升級策略
- TypeScript 版本升級前先本地測試
- ESLint TypeScript 規則定期更新
- 型別定義庫 (@types/*) 保持最新

**最後更新**: 2025-06-29 (基於 Issue #189 實施)
