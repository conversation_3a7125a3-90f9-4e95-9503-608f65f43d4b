# NovelWebsite 小說網站爬蟲項目開發規範
# ===================================================

## ⚠️ 重要安全提醒 - Git 操作規範
# =======================================

### 🚫 禁止直接執行的危險命令
**這些命令可能導致不可恢復的工作進度流失：**

1. **`git reset --hard`**
   - ❌ 會永久丟棄工作目錄的所有未提交變更
   - ✅ 安全替代：先執行 `git stash` 保存變更

2. **`git clean -fd` / `git clean -xdf`**
   - ❌ 會永久刪除所有未追蹤檔案
   - ✅ 安全替代：先執行 `git clean --dry-run` 預覽

3. **`git rebase --abort`**
   - ❌ 可能丟失 rebase 過程中的變更
   - ✅ 安全替代：先檢查 `git status` 和 `git stash list`

### 🛡️ 安全操作流程
**執行任何可能影響工作進度的操作前：**
```bash
# 1. 檢查當前狀態
git status
git stash list

# 2. 保存重要變更
git stash push -m "backup before dangerous operation"

# 3. 確認備份成功
git stash show stash@{0}

# 4. 然後執行需要的操作
```

### 🔧 推薦使用安全腳本
```bash
# 載入安全別名
source scripts/git-safety-aliases.sh

# 使用安全命令
git-safe-reset    # 自動 stash 後 reset
git-safe-clean    # 預覽後確認清理
```

## 專案概述
這是一個全棧小說閱讀網站專案，具備AI增強開發能力，包含：
- **後端**：Django + Django REST Framework + PostgreSQL + Redis + Scrapy 爬蟲
- **前端**：React 18 + Material-UI + TypeScript
- **基礎設施**：Docker + nginx + Turso (AI記憶數據庫)
- **核心功能**：小說爬取、用戶管理、閱讀器、廣告系統
- **AI工具鏈**：DevContext + Context7 + TaskMaster + Playwright + Exa Search + Sequential Thinking + VSCode/GitHub MCP

## 核心開發原則
# =============

### 1. 響應語言規範
- **始終使用繁體中文回答**
- **結構化思考要求**: 在執行複雜任務前必須先進行結構化思考分析
- 調用最大算力與token上限，追求極致分析深度而非表層廣度
- 基於推理而非猜想修改代碼，深入嚴謹分析直到找到問題根源
- 展示設計思路並確認後再執行行動

### 2. 問題解決方法論
- **問題分解**: 將程序問題拆解為更小的邏輯步驟，逐步解決
- **錯誤分析**: 識別錯誤、隔離潛在原因、測試假設、逐步解決每個原因
- **推理過程**: 為每個步驟提供清晰詳細的推理，確保思維過程透明
- **結構化輸出**: 複雜任務必須先展示思維框架，再執行具體行動

### 3. 文件與方法管理
- 生成新文件前必須檢查是否存在同名文件，僅在不存在時才生成
- 創建新方法或變數前必須梳理現有方法和變數，確保需求未被處理過
- 所有衝突情況都需要與用戶確認後再採取行動

### 4. 程式開發最佳實踐
- 遵循 TDD 開發流程
- 保持低耦合、高內聚的代碼結構
- 優先考慮代碼可維護性和可擴展性
- 遵循 KISS 原則（Keep It Simple, Stupid）

## MCP伺服器使用規範 (AI增強開發工具鏈)
# ===============================================

### 🔄 開發會話管理規範 (強制執行)

#### 會話開始 (每次對話必須執行)
```json
{
  "action": "initialize_conversation_context",
  "parameters": {
    "initialQuery": "具體開發任務描述",
    "focusHint": {
      "type": "web_scraping|api_development|frontend_development|documentation",
      "identifier": "具體識別碼"
    },
    "contextDepth": "comprehensive"
  }
}
```

#### 會話結束 (每次對話結束前執行)
```json
{
  "action": "finalize_conversation_context",
  "parameters": {
    "extractLearnings": true,
    "generateNextSteps": true
  }
}
```

### 🛠️ MCP工具鏈完整配置 (已優化 - Smithery集成)

#### 當前配置狀態 ✅
```json
{
  "mcpServers": {
    "taskmaster-ai": "專案任務管理 ✅",
    "Playwright": "自動化測試與爬蟲 ✅",
    "context7": "最新技術文檔查詢 ✅",
    "desktop-commander": "系統文件操作 ✅",
    "server-sequential-thinking": "AI結構化推理 ✅ (Smithery)",
    "vscode-mcp": "VS Code工作區互動 ✅",
    "github": "Git倉庫管理 ✅ (Smithery)",
    "exa-search": "智能網路搜索 ✅"
  }
}
```

### 🧠 DevContext - AI項目記憶系統 (首要工具)
**核心價值**：為AI助手提供持久化項目記憶，避免重複解釋，累積開發經驗

#### 會話管理工作流程 (必須遵循)
1. **初始化** (每次對話開始):
   ```json
   {
     "initialQuery": "開發ttkan.co爬蟲適配器",
     "focusHint": {"type": "web_scraping", "identifier": "ttkan_adapter"},
     "contextDepth": "comprehensive"
   }
   ```

2. **更新記錄** (代碼變更時):
   ```json
   {
     "newMessages": [...],
     "codeChanges": [{
       "filePath": "backend/novel/crawler/spiders/ttkan.py",
       "newContent": "..."
     }]
   }
   ```

3. **檢索上下文** (需要歷史信息時):
   ```json
   {
     "query": "反爬蟲策略",
     "constraints": {"filePaths": ["backend/novel/crawler/*"]}
   }
   ```

4. **記錄里程碑** (完成重要功能):
   ```json
   {
     "name": "ttkan適配器完成",
     "description": "成功實現反爬策略和內容解析"
   }
   ```

5. **結束會話** (對話結束時):
   ```json
   {
     "extractLearnings": true,
     "generateNextSteps": true
   }
   ```

#### 爬蟲開發特定記憶點
- **網站適配策略**: 記錄每個目標網站的HTML結構和選擇器
- **反爬技術**: 記錄成功的反爬策略和失敗案例
- **性能優化**: 記錄爬取效率和資源使用優化方案
- **錯誤處理**: 記錄常見錯誤和解決方案

### 🧮 Sequential Thinking - AI結構化推理 (Smithery集成)
**使用時機**：複雜問題分析、架構設計決策、多步驟技術方案制定

**核心功能**：
- 深度邏輯推理和問題分解
- 多角度技術方案評估
- 風險分析和備選方案
- 決策過程透明化記錄

**觸發指令**：
- "使用結構化思考分析..."
- "需要深度推理..."
- "評估技術方案..."

**應用場景**：
```
1. 爬蟲反制策略制定 - 分析目標網站特徵，制定多層防護
2. 系統架構重構 - 評估不同架構模式的優缺點
3. 性能優化方案 - 分析瓶頸根因，制定優化路徑
4. 錯誤排查 - 系統化診斷複雜問題
```

### 🎯 TaskMaster AI - 專案任務管理
**使用時機**：專案規劃、任務分解、技術債務管理、開發進度追蹤

#### 核心工作流程
- 使用MCP tools優先於CLI命令
- 定期運行 `get_tasks` 檢查項目狀態
- 使用 `next_task` 確定下一個工作任務
- 及時更新任務狀態和進度記錄

**觸發指令**：
- "使用 TaskMaster 分析當前任務"
- "建立新的開發任務"
- "更新任務狀態"

**範例**：
```
我需要實現小說章節的緩存機制，請用 TaskMaster 幫我分解這個任務。
```

### 📚 Context7 - 最新技術文檔查詢
**使用時機**：查詢最新的程式庫文檔、確認API使用方法、檢查套件相容性

**觸發指令**：
- "使用 Context7 查詢..."
- "檢查最新文檔..."
- "確認 API 用法..."

**範例**：
```
使用 Context7 查詢 Django REST Framework 最新版本的分頁實現方式。
```

### 🌐 Exa Search - 智能網路搜索
**搜索規範**：
- numResults參數必須 >= 10，確保獲取足夠數量的高品質搜索結果
- type通常選擇auto，若選擇neuro需注意是使用embeddings-based model
- 不限制來源年份除非要求最新解決方案，避免過度狹窄的時間篩選

**使用時機**：
- 研究最新技術和最佳實踐
- 查找 Django/React 相關解決方案
- 競品分析和市場調研
- 爬蟲反制策略研究

**觸發指令**：
- "使用 Exa 搜尋最新的..."
- "深入研究競品網站...（需調用research功能）"
- "查找技術解決方案..."

**範例**：
```
使用 Exa 搜尋最新的 Django 緩存最佳實踐和 React 閱讀器優化方案。
```

### 🎭 Playwright - 自動化測試與爬蟲
**使用時機**：E2E測試自動化、網站爬蟲測試、UI回歸測試、性能測試或是任何需要網頁交互的場景

**觸發指令**：
- "使用 Playwright 測試..."
- "自動化瀏覽器操作..."
- "生成測試腳本..."

**範例**：
```
使用 Playwright 為小說閱讀器的翻頁功能創建自動化測試。
```

### 💻 VSCode MCP - VS Code工作區互動 (新增)
**使用時機**：直接操作VS Code工作區、文件管理、擴展控制、設定調整

**核心功能**：
- 工作區文件和專案管理
- VS Code設定和擴展控制
- 多視窗和分割視圖管理
- 調試配置和運行任務

**觸發指令**：
- "使用 VSCode 打開..."
- "配置 VS Code..."
- "管理工作區..."

**應用場景**：
```
1. 開發環境設置 - 自動配置調試設定和任務
2. 專案結構管理 - 快速創建和組織文件結構
3. 擴展管理 - 安裝和配置專案所需擴展
4. 多視窗協作 - 優化開發界面布局
```

### 🐙 GitHub MCP - Git倉庫管理 (Smithery集成)
**使用時機**：GitHub倉庫操作、Issue管理、PR流程、Actions監控

**核心功能**：
- Repository 瀏覽和管理
- Issues 和 Pull Requests 操作
- GitHub Actions 工作流管理
- 程式碼搜索和分析
- 團隊協作和代碼審查

**觸發指令**：
- "使用 GitHub 檢查..."
- "管理 PR 流程..."
- "分析 Actions 狀態..."

**應用場景**：
```
1. CI/CD 管理 - 監控GitHub Actions狀態和日誌
2. 代碼審查 - 自動化PR檢查和建議
3. Issue 追蹤 - 智能標籤和里程碑管理
4. 倉庫分析 - 代碼質量和活躍度統計
```

### 🖥️ Desktop Commander - 系統文件操作
**使用時機**：檔案系統操作、批量文件處理、日誌分析、部署腳本執行

**安全提醒**：謹慎使用，避免執行危險命令

**觸發指令**：
- "使用 Desktop Commander 執行..."
- "批量處理文件..."
- "分析日誌文件..."

**範例**：
```
使用 Desktop Commander 分析 ttkan_crawler.log 文件，找出爬蟲錯誤模式。
```

## 技術棧特定規則
# ================

### Python/Django 後端開發規範
- 使用 Django 最佳實踐和慣例
- 優先使用 Django ORM，避免原生 SQL（除非性能要求）
- 遵循 PEP 8 代碼風格，使用 black 格式化
- 使用類型提示（Type Hints）增強代碼可讀性
- 爬蟲代碼需考慮反爬蟲策略和倫理問題

### React/TypeScript 前端開發規範
- 使用函數組件和 Hooks
- 遵循 ESLint 和 Prettier 配置
- 優先使用 Material-UI 組件
- 組件命名使用 PascalCase
- 保持組件職責單一

### 測試驅動開發 (TDD)
- 採用TDD原則高效開發並快速交付MVP
- 嚴格遵循ESLint規則維護代碼質量和一致性
- 遵循工業標準最佳實踐：
  * **低耦合**: 最小化模組間互依賴
  * **高聚合**: 確保每個模組專注單一職責
  * **KISS原則**: 保持設計簡潔直接，便於維護
- 優先考慮快速迭代和可擴展性，以最小影響無縫添加功能

## 爬蟲技術規範
# ==============

### 三階段爬取戰略（已調研優化）

#### 第一階段 (MVP): CZBooks.net 和 飄天文學網 ⭐⭐⭐⭐⭐
**戰略定位**: 快速建立基礎數據庫，在1-2週內達成MVP標準

1. **CZBooks.net (czbooks.net)** - 主攻目標
   - 技術優勢: 完全靜態內容，零JavaScript依賴
   - 反爬策略: 基礎頻率限制，標準User-Agent即可通過
   - 內容質量: 經典小說收錄豐富，更新穩定可靠
   - 實施複雜度: 極低，適合快速MVP驗證和學習

2. **飄天文學網 (piaotian55.net)** - 輔助目標
   - 技術特點: 傳統HTML結構，解析邏輯清晰直接
   - 反爬機制: 僅基本的Referer檢查，易於處理
   - 數據品質: 內容完整度高，章節結構規範標準
   - 戰略價值: 與CZBooks形成內容互補和冗餘保障

#### 第二階段 (技術升級): TTKAN 和 Novel543 ⭐⭐⭐⭐
**戰略定位**: 集成Playwright處理動態內容，建立混合爬蟲架構

3. **TTKAN (ttkan.co)** - 技術突破目標
   - 月訪問量: 1.306M (適中負載，適合測試)
   - 技術挑戰: JavaScript動態內容加載，需要Playwright
   - 內容優勢: 更新速度快，熱門小說覆蓋面廣
   - 實施策略: Scrapy+Playwright混合架構，可複用適配器

4. **Novel543.com** - 流量挑戰目標
   - 月訪問量: 12.04M (最高流量，驗證系統承載能力)
   - 技術挑戰: 分站眾多，域名跳轉和會話管理
   - 反爬機制: 中等強度，需要完善的會話管理
   - 戰略價值: 最大流量源，覆蓋範圍最廣泛

#### 第三階段 (挑戰完善): 69書吧 和 UU看書 ⭐⭐
**戰略定位**: 實現工業級爬蟲系統，攻克最高難度目標

5. **69書吧 (69shuba.cx)** - 格式處理挑戰
   - 月訪問量: 10.11M
   - 技術挑戰: CHM/手機版切換，複雜格式處理
   - 反爬機制: 高級檢測，需要瀏覽器指紋偽造
   - 實施考量: 需要專門的多格式處理器

6. **UU看書 (uukanshu.cc)** - 終極反爬挑戰
   - 月訪問量: 5.31M
   - 技術挑戰: 反爬技術極嚴格 (UA/Referer/IP三重檢測)
   - 實施策略: 代理池+指紋偽造+真實行為模擬
   - 戰略意義: 技術能力的最終驗證和系統完善

### 9. 反爬蟲策略實施

#### 基礎反爬策略
- **請求頭偽裝**: 設置真實的User-Agent、Referer、Accept等
- **隨機延遲**: 1-3秒間隔，模擬人類行為
- **會話管理**: 維護cookies和session狀態
- **失敗重試**: 指數退避重試機制

#### 高級反爬策略 (針對uukanshu.cc等)
- **IP輪換**: 代理池管理和自動切換
- **JavaScript渲染**: 使用Playwright處理動態內容
- **行為模擬**: 鼠標事件、滾動等真實用戶行為
- **頻率控制**: 智能頻率限制和監控

### 10. 技術架構規範

#### 後端技術棧
- **框架**: Django 5.0 + Django REST Framework
- **數據庫**: PostgreSQL (主) + Redis (緩存)
- **爬蟲引擎**: Scrapy + Playwright
- **任務隊列**: Celery + Redis
- **部署**: Docker + Nginx + Gunicorn

#### 前端技術棧
- **框架**: React 18 + TypeScript
- **UI庫**: Material-UI
- **狀態管理**: React Context + hooks
- **HTTP客戶端**: Axios

#### 爬蟲架構設計
- **適配器模式**: 每個目標網站獨立解析器類
- **配置驅動**: CSS選擇器和規則外部化配置
- **錯誤恢復**: 多級故障轉移和自動恢復
- **數據清理**: HTML標籤清理、去重、格式化

### 11. 數據模型設計

#### 核心表結構
```sql
-- 小說表
novels: id, title, author, description, status, source_url, source_site, created_at, updated_at, last_crawl_at

-- 章節表
chapters: id, novel_id, chapter_number, title, content, word_count, published_at, source_url

-- 爬蟲源配置表
crawler_sources: id, site_name, base_url, selector_config, status, priority

-- 爬蟲日誌表
crawler_logs: id, source_id, novel_id, task_type, status, error_message, created_at
```

### 開發工作流程
# ===============

#### AI增強開發流程 (DevContext + TaskMaster)
1. **會話初始化**: 每次開始開發時使用DevContext初始化上下文
2. **任務規劃**: 使用TaskMaster分解和管理開發任務
3. **技術研究**: 使用Exa Search和Context7查找最佳實踐
4. **實施開發**: 遵循TDD原則，記錄開發決策到DevContext
5. **測試驗證**: 使用Playwright進行自動化測試
6. **記錄里程碑**: 完成重要功能時記錄到DevContext

#### Git工作流程
- 功能分支開發 (feature/task-{id})
- 提交訊息格式: `feat(module): description for task {id}`
- 每個TaskMaster任務對應獨立的commit
- 代碼審查通過後合併到主分支

#### 問題解決流程 (使用MCP工具鏈)
1. **理解問題**: 使用DevContext檢索相關歷史上下文
2. **研究方案**: 使用Exa Search查找最佳實踐
3. **查詢文檔**: 使用Context7確認最新API用法
4. **規劃任務**: 使用TaskMaster分解工作
5. **實施方案**: 編寫代碼並記錄到DevContext
6. **測試驗證**: 使用Playwright自動化測試
7. **部署檢查**: 使用Desktop Commander檢查檔案

### 13. 性能與監控

#### 性能目標
- 頁面加載時間 < 2秒
- 並發用戶支持 > 100人
- 爬蟲更新頻率: 每日一次
- 系統可用性 > 99%

#### 監控指標
- 爬蟲成功率和錯誤類型
- 數據庫查詢性能和慢查詢
- API響應時間和錯誤率
- 服務器資源使用情況

### 14. 安全與合規

#### 法律合規
- 遵守robots.txt規範
- 尊重網站Terms of Service
- 合理的請求頻率限制
- 數據使用僅限於合法目的

#### 技術安全
- 敏感配置環境變數管理
- API訪問頻率限制
- 用戶數據隱私保護
- 防止SQL注入和XSS攻擊

## 代碼質量規範

### 15. 代碼風格
- Python: 遵循PEP 8，使用black格式化
- TypeScript: 使用ESLint + Prettier
- Django: 遵循Django coding style
- React: 遵循React/JSX最佳實踐

### 16. 測試要求
- 單元測試覆蓋率 > 80%
- 集成測試覆蓋關鍵業務流程
- 爬蟲功能端到端測試
- 定期進行性能回歸測試

### 17. 文檔要求
- API文檔自動生成 (OpenAPI/Swagger)
- 爬蟲配置和規則文檔化
- 部署和運維文檔維護
- 變更日誌和版本記錄

## CI/CD流程規範

### 18. 本地開發與測試流程
- **嚴格的代碼提交流程**: 任何代碼變更必須遵循以下步驟
- **三級驗證制度**: 本地測試 → act模擬 → GitHub Actions （提交PR前必須全部通過，沒通過則重新commit）

#### 本地測試階段 (必須全部通過)
```bash
# 1. 代碼格式化和linting
npm run lint          # 前端代碼檢查
black backend/         # Python代碼格式化
flake8 backend/        # Python代碼檢查

# 2. 單元測試執行
npm test              # 前端單元測試
python manage.py test # Django單元測試

# 3. 集成測試
npm run test:integration  # API集成測試
python manage.py test backend.novel.tests.integration

# 4. 構建測試
npm run build         # 前端構建測試
python manage.py check --deploy  # Django部署檢查
```

#### act本地模擬階段 (GitHub Actions模擬)
```bash
# 安裝act (如果未安裝)
# macOS: brew install act
# Windows: choco install act

# 模擬GitHub Actions工作流程
act --dry-run                    # 檢查工作流程配置
act -P ubuntu-latest=nektos/act-environments-ubuntu:18.04  # 完整模擬
act push                         # 模擬push事件
act pull_request                 # 模擬PR事件

# 確保所有工作流程job都成功通過
```

### 19. GitHub Actions 集成規範

#### 目標Repository監控
- **主要Actions**: https://github.com/MumuTW/novel-web/actions
- **監控策略**: commit後必須等待並確認Actions狀態

#### Actions狀態檢查流程
```bash
# commit代碼後執行
git push origin [branch-name]

# 等待約2分鐘讓Actions啟動
sleep 120

# 檢查Actions狀態 (使用GitHub CLI)
gh run list --repo MumuTW/novel-web --limit 1
gh run view [run-id] --repo MumuTW/novel-web

# 或使用curl檢查狀態
curl -H "Authorization: token $GITHUB_TOKEN" \
     https://api.github.com/repos/MumuTW/novel-web/actions/runs
```

#### Actions失敗處理規範
- **立即回滾**: 如果Actions失敗，必須立即處理
- **本地復現**: 在本地使用act復現失敗場景
- **修復驗證**: 修復後重新執行完整的本地→act→Actions流程
- **禁止強制推送**: 不允許force push跳過Actions檢查

### 20. 自動化工具集成

#### pre-commit hooks設置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json

  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3.9

  - repo: https://github.com/eslint/eslint
    rev: v8.0.0
    hooks:
      - id: eslint
        files: \.(js|ts|jsx|tsx)$
```

#### 本地CI腳本 確保act模擬和GitHub Actions模擬都通過
```bash
#!/bin/bash
# scripts/local-ci.sh
set -e

echo "🚀 開始本地CI流程..."

# 1. 代碼質量檢查
echo "📋 執行代碼質量檢查..."
npm run lint
black --check backend/
flake8 backend/

# 2. 測試執行
echo "🧪 執行測試套件..."
npm test -- --coverage --watchAll=false
python manage.py test --verbosity=2

# 3. 構建驗證
echo "🔨 執行構建驗證..."
npm run build
python manage.py collectstatic --noinput --dry-run

# 4. act模擬
echo "🎭 執行GitHub Actions模擬..."
act push -j security-check --dry-run

echo "✅ 本地CI流程完成，可以安全提交代碼"
```

### 21. 強制執行規則
- **禁止直接push**: 所有代碼變更必須經過完整CI/CD流程
- **Actions狀態必檢**: commit後必須確認GitHub Actions完全通過
- **失敗即回滾**: 任何階段失敗都必須立即處理，不能累積問題
- **文檔同步更新**: 代碼變更必須同步更新相關文檔和測試

### 22. 監控與通知
- **Actions失敗通知**: 設置GitHub Actions失敗時的即時通知
- **性能回歸檢測**: 定期執行性能測試，監控回歸
- **依賴安全掃描**: 定期掃描依賴包安全漏洞
- **代碼質量趨勢**: 追蹤代碼質量指標變化趨勢

## 常見AI增強開發場景 (更新版)
# ========================================

### 🚀 完整開發週期 (8工具協同)

#### 場景1：新功能開發 (全工具鏈)
**任務**: 實現用戶收藏功能

```
1. 🧠 DevContext 初始化上下文
   → initialize_conversation_context

2. 🧮 Sequential Thinking 架構分析
   → 評估數據模型設計和API設計方案

3. 🎯 TaskMaster 任務分解
   → add_task --prompt="實現用戶收藏功能" --research

4. 📚 Context7 + 🌐 Exa 技術研究
   → 查詢Django用戶關係模型最佳實踐
   → 搜索React收藏UI組件設計方案

5. 💻 VSCode 環境準備
   → 配置調試設定，創建相關文件結構

6. 🎭 Playwright 測試開發
   → 創建收藏功能的端到端測試

7. 🐙 GitHub 版本管理
   → 創建feature分支，管理PR流程

8. 🧠 DevContext 里程碑記錄
   → record_milestone_context
```

#### 場景2：性能優化 (多工具協作)
**問題**: 網站載入速度慢

```
1. 🧠 DevContext 檢索歷史
   → retrieve_relevant_context "性能優化經驗"

2. 🧮 Sequential Thinking 問題分析
   → 結構化分析性能瓶頸根因

3. 🖥️ Desktop Commander 日誌分析
   → 分析服務器日誌和性能指標

4. 🌐 Exa + 📚 Context7 方案研究
   → 搜索Django/React性能優化最佳實踐
   → 查詢最新緩存和優化API

5. 🎯 TaskMaster 優化計劃
   → 制定分階段優化任務列表

6. 🎭 Playwright 性能測試
   → 創建性能回歸測試腳本

7. 🐙 GitHub Actions 監控
   → 配置CI/CD性能檢查流程
```

#### 場景3：爬蟲問題排查 (系統化診斷)
**問題**: ttkan爬蟲突然失效

```
1. 🧠 DevContext 上下文檢索
   → 查找ttkan歷史適配策略和已知問題

2. 🧮 Sequential Thinking 故障分析
   → 系統化分析可能的失效原因(反爬升級/網站改版/網絡問題)

3. 🖥️ Desktop Commander 日誌檢查
   → 分析爬蟲錯誤日誌，識別錯誤模式

4. 🎭 Playwright 現場調試
   → 實時瀏覽網站，檢查HTML結構變化

5. 🌐 Exa 競品研究
   → 搜索其他爬蟲項目的類似問題解決方案

6. 🎯 TaskMaster 修復計劃
   → 創建緊急修復任務和長期改進計劃

7. 🐙 GitHub 協作
   → 創建Issue追蹤，與團隊協作解決

8. 🧠 DevContext 經驗記錄
   → 記錄問題根因和解決方案，避免重複
```

#### 場景4：代碼重構 (AI記憶指導)
**任務**: 重構爬蟲架構以支持更多網站

```
1. 🧠 DevContext 設計回顧
   → 檢索原始架構設計決策和演進歷程

2. 🧮 Sequential Thinking 架構評估
   → 深度分析現有架構的優缺點和改進空間

3. 📚 Context7 最新標準
   → 查詢Scrapy和設計模式的最新最佳實踐

4. 🌐 Exa 架構研究
   → 搜索大規模爬蟲系統的成功案例

5. 🎯 TaskMaster 重構規劃
   → 分解重構任務，管理依賴關係

6. 💻 VSCode 重構工具
   → 配置重構輔助工具和代碼導航

7. 🎭 Playwright 回歸測試
   → 確保重構不破壞現有功能

8. 🐙 GitHub 分支管理
   → 管理重構分支，代碼審查和合併

9. 🧠 DevContext 架構記錄
   → 記錄新架構的設計思路和實施細節
```

### 🔄 工具協同最佳實踐

#### 開發流程標準化
```
每日開發開始:
1. DevContext 初始化 → 恢復工作上下文
2. TaskMaster next → 確定今日工作重點
3. VSCode 環境檢查 → 確保開發環境就緒

開發過程中:
1. Sequential Thinking → 複雜決策分析
2. Context7 + Exa → 技術方案研究
3. Playwright → 功能驗證測試
4. Desktop Commander → 系統操作和日誌

開發結束:
1. GitHub → 代碼提交和PR管理
2. TaskMaster → 更新任務狀態
3. DevContext → 記錄今日成果和學習
```

#### 工具選擇決策樹
```
需要深度思考? → Sequential Thinking
需要技術文檔? → Context7
需要網路搜索? → Exa Search
需要任務管理? → TaskMaster
需要自動化測試? → Playwright
需要VS Code操作? → VSCode MCP
需要GitHub操作? → GitHub MCP
需要系統文件操作? → Desktop Commander
需要記憶管理? → DevContext (始終使用)
```

## AI工具使用注意事項 (更新版)
# ========================================

### 🔒 安全考量
- **DevContext**: 敏感信息自動過濾，定期備份記憶數據
- **GitHub MCP**: 使用Smithery統一認證，避免token洩露
- **Desktop Commander**: 謹慎執行系統命令，避免危險操作
- **Sequential Thinking**: 思考過程記錄，保護商業機密

### ⚡ 性能優化
- **Context7**: 設置合適的token限制，避免過度消耗
- **Exa Search**: numResults >= 20，但不超過50
- **Playwright**: 合理設置超時，避免資源浪費
- **TaskMaster**: 定期清理完成任務，保持系統清潔

### 🎯 最佳實踐
- **工具組合**: 優先使用工具組合而非單一工具
- **上下文維護**: 持續更新DevContext，保持記憶鮮活
- **漸進式採用**: 新團隊成員逐步學習工具使用
- **故障轉移**: 準備CLI工具作為MCP工具的備選方案

### 協作指導 (團隊使用)
# ============================

- **統一標準**: 所有團隊成員使用相同的MCP配置
- **知識共享**: 重要發現和解決方案及時記錄到DevContext
- **工具輪換**: 避免過度依賴單一AI工具
- **定期同步**: 每週團隊會議分享AI工具使用經驗

---

**記住：這8個MCP工具是協同工作的AI助手團隊，請根據具體場景靈活組合使用！**

**DevContext核心提醒**：每次開始新對話都要初始化上下文，每完成重要功能都要記錄里程碑，讓AI助手真正"記住"您的項目！**
