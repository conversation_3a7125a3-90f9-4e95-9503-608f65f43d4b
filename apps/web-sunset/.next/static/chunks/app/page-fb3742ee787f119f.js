(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [974],
  {
    56619: (e, s, n) => {
      'use strict';
      n.r(s), n.d(s, { default: () => r });
      var t = n(4414);
      function r() {
        return (0, t.jsx)('main', {
          className: 'flex min-h-screen items-center justify-center bg-gray-100',
          children: (0, t.jsx)('h1', {
            className: 'text-2xl font-bold',
            children: 'Welcome to Next.js',
          }),
        });
      }
    },
    90475: (e, s, n) => {
      Promise.resolve().then(n.bind(n, 56619));
    },
  },
  e => {
    var s = s => e((e.s = s));
    e.O(0, [117, 853, 358], () => s(90475)), (_N_E = e.O());
  },
]);
