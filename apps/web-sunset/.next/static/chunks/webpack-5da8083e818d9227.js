(() => {
  'use strict';
  var e = {},
    t = {};
  function r(o) {
    var n = t[o];
    if (void 0 !== n) return n.exports;
    var a = (t[o] = { id: o, loaded: !1, exports: {} }),
      i = !0;
    try {
      e[o](a, a.exports, r), (i = !1);
    } finally {
      i && delete t[o];
    }
    return (a.loaded = !0), a.exports;
  }
  (r.m = e),
    (() => {
      var e = [];
      r.O = (t, o, n, a) => {
        if (o) {
          a = a || 0;
          for (var i = e.length; i > 0 && e[i - 1][2] > a; i--) e[i] = e[i - 1];
          e[i] = [o, n, a];
          return;
        }
        for (var l = 1 / 0, i = 0; i < e.length; i++) {
          for (var [o, n, a] = e[i], d = !0, u = 0; u < o.length; u++)
            (!1 & a || l >= a) && Object.keys(r.O).every(e => r.O[e](o[u]))
              ? o.splice(u--, 1)
              : ((d = !1), a < l && (l = a));
          if (d) {
            e.splice(i--, 1);
            var s = n();
            void 0 !== s && (t = s);
          }
        }
        return t;
      };
    })(),
    (r.n = e => {
      var t = e && e.__esModule ? () => e.default : () => e;
      return r.d(t, { a: t }), t;
    }),
    (() => {
      var e,
        t = Object.getPrototypeOf ? e => Object.getPrototypeOf(e) : e => e.__proto__;
      r.t = function (o, n) {
        if (
          (1 & n && (o = this(o)),
          8 & n ||
            ('object' == typeof o &&
              o &&
              ((4 & n && o.__esModule) || (16 & n && 'function' == typeof o.then))))
        )
          return o;
        var a = Object.create(null);
        r.r(a);
        var i = {};
        e = e || [null, t({}), t([]), t(t)];
        for (var l = 2 & n && o; 'object' == typeof l && !~e.indexOf(l); l = t(l))
          Object.getOwnPropertyNames(l).forEach(e => (i[e] = () => o[e]));
        return (i.default = () => o), r.d(a, i), a;
      };
    })(),
    (r.d = (e, t) => {
      for (var o in t)
        r.o(t, o) && !r.o(e, o) && Object.defineProperty(e, o, { enumerable: !0, get: t[o] });
    }),
    (r.f = {}),
    (r.e = e => Promise.all(Object.keys(r.f).reduce((t, o) => (r.f[o](e, t), t), []))),
    (r.u = e => {}),
    (r.miniCssF = e => {}),
    (r.g = (function () {
      if ('object' == typeof globalThis) return globalThis;
      try {
        return this || Function('return this')();
      } catch (e) {
        if ('object' == typeof window) return window;
      }
    })()),
    (r.hmd = e => (
      (e = Object.create(e)).children || (e.children = []),
      Object.defineProperty(e, 'exports', {
        enumerable: !0,
        set: () => {
          throw Error(
            'ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: ' +
              e.id
          );
        },
      }),
      e
    )),
    (r.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t)),
    (() => {
      var e = {},
        t = '_N_E:';
      r.l = (o, n, a, i) => {
        if (e[o]) return void e[o].push(n);
        if (void 0 !== a)
          for (var l, d, u = document.getElementsByTagName('script'), s = 0; s < u.length; s++) {
            var c = u[s];
            if (c.getAttribute('src') == o || c.getAttribute('data-webpack') == t + a) {
              l = c;
              break;
            }
          }
        l ||
          ((d = !0),
          ((l = document.createElement('script')).charset = 'utf-8'),
          (l.timeout = 120),
          r.nc && l.setAttribute('nonce', r.nc),
          l.setAttribute('data-webpack', t + a),
          (l.src = r.tu(o))),
          (e[o] = [n]);
        var f = (t, r) => {
            (l.onerror = l.onload = null), clearTimeout(p);
            var n = e[o];
            if (
              (delete e[o],
              l.parentNode && l.parentNode.removeChild(l),
              n && n.forEach(e => e(r)),
              t)
            )
              return t(r);
          },
          p = setTimeout(f.bind(null, void 0, { type: 'timeout', target: l }), 12e4);
        (l.onerror = f.bind(null, l.onerror)),
          (l.onload = f.bind(null, l.onload)),
          d && document.head.appendChild(l);
      };
    })(),
    (r.r = e => {
      'undefined' != typeof Symbol &&
        Symbol.toStringTag &&
        Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
        Object.defineProperty(e, '__esModule', { value: !0 });
    }),
    (r.nmd = e => ((e.paths = []), e.children || (e.children = []), e)),
    (() => {
      var e;
      r.tt = () => (
        void 0 === e &&
          ((e = { createScriptURL: e => e }),
          'undefined' != typeof trustedTypes &&
            trustedTypes.createPolicy &&
            (e = trustedTypes.createPolicy('nextjs#bundler', e))),
        e
      );
    })(),
    (r.tu = e => r.tt().createScriptURL(e)),
    (r.p = '/_next/'),
    (() => {
      var e = { 68: 0, 545: 0 };
      (r.f.j = (t, o) => {
        var n = r.o(e, t) ? e[t] : void 0;
        if (0 !== n)
          if (n) o.push(n[2]);
          else if (/^(545|68)$/.test(t)) e[t] = 0;
          else {
            var a = new Promise((r, o) => (n = e[t] = [r, o]));
            o.push((n[2] = a));
            var i = r.p + r.u(t),
              l = Error();
            r.l(
              i,
              o => {
                if (r.o(e, t) && (0 !== (n = e[t]) && (e[t] = void 0), n)) {
                  var a = o && ('load' === o.type ? 'missing' : o.type),
                    i = o && o.target && o.target.src;
                  (l.message = 'Loading chunk ' + t + ' failed.\n(' + a + ': ' + i + ')'),
                    (l.name = 'ChunkLoadError'),
                    (l.type = a),
                    (l.request = i),
                    n[1](l);
                }
              },
              'chunk-' + t,
              t
            );
          }
      }),
        (r.O.j = t => 0 === e[t]);
      var t = (t, o) => {
          var n,
            a,
            [i, l, d] = o,
            u = 0;
          if (i.some(t => 0 !== e[t])) {
            for (n in l) r.o(l, n) && (r.m[n] = l[n]);
            if (d) var s = d(r);
          }
          for (t && t(o); u < i.length; u++) (a = i[u]), r.o(e, a) && e[a] && e[a][0](), (e[a] = 0);
          return r.O(s);
        },
        o = (self.webpackChunk_N_E = self.webpackChunk_N_E || []);
      o.forEach(t.bind(null, 0)), (o.push = t.bind(null, o.push.bind(o)));
    })();
})();
