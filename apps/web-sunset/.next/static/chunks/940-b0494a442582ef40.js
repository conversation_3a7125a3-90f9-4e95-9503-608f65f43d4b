(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [940],
  {
    1254: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-valuemax': null, 'aria-valuemin': null, 'aria-valuenow': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    1543: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: { constraints: ['scoped to the body element'], name: 'footer' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    2002: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-colindex': null,
            'aria-expanded': null,
            'aria-level': null,
            'aria-posinset': null,
            'aria-rowindex': null,
            'aria-selected': null,
            'aria-setsize': null,
          },
          relatedConcepts: [{ concept: { name: 'tr' }, module: 'HTML' }],
          requireContextRole: ['grid', 'rowgroup', 'table', 'treegrid'],
          requiredContextRole: ['grid', 'rowgroup', 'table', 'treegrid'],
          requiredOwnedElements: [['cell'], ['columnheader'], ['gridcell'], ['rowheader']],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'group'],
            ['roletype', 'widget'],
          ],
        });
    },
    2085: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-checked': null, 'aria-posinset': null, 'aria-setsize': null },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'radio' }], name: 'input' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    2417: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-orientation': 'horizontal',
            'aria-valuemax': '100',
            'aria-valuemin': '0',
            'aria-valuenow': null,
            'aria-valuetext': null,
          },
          relatedConcepts: [{ concept: { name: 'hr' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    3031: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isVisible = function (e) {
          let t = (0, n.getWindowFromNode)(e);
          for (let n = e; null != (r = n) && r.ownerDocument; n = n.parentElement) {
            var r;
            if ('none' === t.getComputedStyle(n).display) return !1;
          }
          return !0;
        });
      var n = r(18666);
    },
    3071: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-errormessage': null, 'aria-invalid': null },
          relatedConcepts: [{ concept: { name: 'referrer [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command', 'link']],
        });
    },
    3492: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.keydownBehavior = void 0);
      var n = r(22154),
        o = r(76696);
      t.keydownBehavior = [
        {
          matches: (e, t) =>
            ('Home' === e.key || 'End' === e.key) &&
            ((0, n.isElementType)(t, ['input', 'textarea']) || (0, n.isContentEditable)(t)),
          handle: (e, t) => {
            if ('Home' === e.key) (0, n.setSelectionRange)(t, 0, 0);
            else {
              var r, o;
              let e = null != (r = null == (o = (0, n.getValue)(t)) ? void 0 : o.length) ? r : 0;
              (0, n.setSelectionRange)(t, e, e);
            }
          },
        },
        {
          matches: (e, t) =>
            ('PageUp' === e.key || 'PageDown' === e.key) && (0, n.isElementType)(t, ['input']),
          handle: (e, t) => {
            if ('PageUp' === e.key) (0, n.setSelectionRange)(t, 0, 0);
            else {
              var r, o;
              let e = null != (r = null == (o = (0, n.getValue)(t)) ? void 0 : o.length) ? r : 0;
              (0, n.setSelectionRange)(t, e, e);
            }
          },
        },
        {
          matches: (e, t) => 'Delete' === e.key && (0, n.isEditable)(t) && !(0, n.isCursorAtEnd)(t),
          handle: (e, t, r, a) => {
            let { newValue: l, newSelectionStart: i } = (0, n.calculateNewValue)(
              '',
              t,
              a.carryValue,
              void 0,
              'forward'
            );
            (0, o.fireInputEvent)(t, {
              newValue: l,
              newSelectionStart: i,
              eventOverrides: { inputType: 'deleteContentForward' },
            }),
              (0, o.carryValue)(t, a, l);
          },
        },
      ];
    },
    3548: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.keydownBehavior = void 0);
      var n = r(22154);
      t.keydownBehavior = [
        {
          matches: (e, t) =>
            ('ArrowLeft' === e.key || 'ArrowRight' === e.key) &&
            (0, n.isElementType)(t, ['input', 'textarea']),
          handle: (e, t) => {
            var r;
            let { selectionStart: o, selectionEnd: a } = (0, n.getSelectionRange)(t),
              l = 'ArrowLeft' === e.key ? -1 : 1,
              i = null != (r = o === a ? (null != o ? o : 0) + l : l < 0 ? o : a) ? r : 0;
            (0, n.setSelectionRange)(t, i, i);
          },
        },
      ];
    },
    3554: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.keyboard = function (e, t) {
          var r;
          let { promise: o, state: a } = i(e, t);
          return (null != (r = null == t ? void 0 : t.delay) ? r : 0) > 0
            ? (0, n.getConfig)().asyncWrapper(() => o.then(() => a))
            : (o.catch(console.error), a);
        }),
        (t.keyboardImplementationWrapper = i),
        Object.defineProperty(t, 'specialCharMap', {
          enumerable: !0,
          get: function () {
            return l.specialCharMap;
          },
        });
      var n = r(50817),
        o = r(30221),
        a = r(55853),
        l = r(80065);
      function i(e, t = {}) {
        let {
            keyboardState: r = {
              activeElement: null,
              pressed: [],
              carryChar: '',
              modifiers: { alt: !1, caps: !1, ctrl: !1, meta: !1, shift: !1 },
            },
            delay: n = 0,
            document: l = document,
            autoModify: u = !1,
            keyboardMap: s = a.defaultKeyMap,
          } = t,
          d = { delay: n, document: l, autoModify: u, keyboardMap: s };
        return {
          promise: (0, o.keyboardImplementation)(e, d, r),
          state: r,
          releaseAllKeys: () => (0, o.releaseAllKeys)(d, r),
        };
      }
    },
    3579: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'dd' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    3737: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = r(63239),
        o = l(r(91232)),
        a = l(r(38310));
      function l(e) {
        return e && e.__esModule ? e : { default: e };
      }
      function i(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                l = !0,
                i = !1;
              try {
                for (
                  o = o.call(e);
                  !(l = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  l = !0
                );
              } catch (e) {
                (i = !0), (n = e);
              } finally {
                try {
                  l || null == o.return || o.return();
                } finally {
                  if (i) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          u(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function u(e, t) {
        if (e) {
          if ('string' == typeof e) return s(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return s(e, t);
        }
      }
      function s(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      for (var d = [], c = a.default.keys(), p = 0; p < c.length; p++) {
        var m = c[p],
          f = a.default.get(m);
        if (f)
          for (var b = [].concat(f.baseConcepts, f.relatedConcepts), y = 0; y < b.length; y++) {
            var v = b[y];
            'HTML' === v.module &&
              (function () {
                var e = v.concept;
                if (e) {
                  var t,
                    r = d.find(function (t) {
                      return (0, n.dequal)(t, e);
                    });
                  t = r ? r[1] : [];
                  for (var o = !0, a = 0; a < t.length; a++)
                    if (t[a] === m) {
                      o = !1;
                      break;
                    }
                  o && t.push(m), d.push([e, t]);
                }
              })();
          }
      }
      var h = {
        entries: function () {
          return d;
        },
        forEach: function (e) {
          var t,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
            n = (function (e, t) {
              var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
              if (!r) {
                if (Array.isArray(e) || (r = u(e))) {
                  r && (e = r);
                  var n = 0,
                    o = function () {};
                  return {
                    s: o,
                    n: function () {
                      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
                    },
                    e: function (e) {
                      throw e;
                    },
                    f: o,
                  };
                }
                throw TypeError(
                  'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
                );
              }
              var a,
                l = !0,
                i = !1;
              return {
                s: function () {
                  r = r.call(e);
                },
                n: function () {
                  var e = r.next();
                  return (l = e.done), e;
                },
                e: function (e) {
                  (i = !0), (a = e);
                },
                f: function () {
                  try {
                    l || null == r.return || r.return();
                  } finally {
                    if (i) throw a;
                  }
                },
              };
            })(d);
          try {
            for (n.s(); !(t = n.n()).done; ) {
              var o = i(t.value, 2),
                a = o[0],
                l = o[1];
              e.call(r, l, a, d);
            }
          } catch (e) {
            n.e(e);
          } finally {
            n.f();
          }
        },
        get: function (e) {
          var t = d.find(function (t) {
            return e.name === t[0].name && (0, n.dequal)(e.attributes, t[0].attributes);
          });
          return t && t[1];
        },
        has: function (e) {
          return !!h.get(e);
        },
        keys: function () {
          return d.map(function (e) {
            return i(e, 1)[0];
          });
        },
        values: function () {
          return d.map(function (e) {
            return i(e, 2)[1];
          });
        },
      };
      t.default = (0, o.default)(h, h.entries());
    },
    4215: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'code' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    4596: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'math' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    5012: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'epilogue [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    5185: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = (function (e) {
        return e && e.__esModule ? e : { default: e };
      })(r(91232));
      function o(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                l = !0,
                i = !1;
              try {
                for (
                  o = o.call(e);
                  !(l = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  l = !0
                );
              } catch (e) {
                (i = !0), (n = e);
              } finally {
                try {
                  l || null == o.return || o.return();
                } finally {
                  if (i) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          a(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function a(e, t) {
        if (e) {
          if ('string' == typeof e) return l(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return l(e, t);
        }
      }
      function l(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      var i = [
          ['a', { reserved: !1 }],
          ['abbr', { reserved: !1 }],
          ['acronym', { reserved: !1 }],
          ['address', { reserved: !1 }],
          ['applet', { reserved: !1 }],
          ['area', { reserved: !1 }],
          ['article', { reserved: !1 }],
          ['aside', { reserved: !1 }],
          ['audio', { reserved: !1 }],
          ['b', { reserved: !1 }],
          ['base', { reserved: !0 }],
          ['bdi', { reserved: !1 }],
          ['bdo', { reserved: !1 }],
          ['big', { reserved: !1 }],
          ['blink', { reserved: !1 }],
          ['blockquote', { reserved: !1 }],
          ['body', { reserved: !1 }],
          ['br', { reserved: !1 }],
          ['button', { reserved: !1 }],
          ['canvas', { reserved: !1 }],
          ['caption', { reserved: !1 }],
          ['center', { reserved: !1 }],
          ['cite', { reserved: !1 }],
          ['code', { reserved: !1 }],
          ['col', { reserved: !0 }],
          ['colgroup', { reserved: !0 }],
          ['content', { reserved: !1 }],
          ['data', { reserved: !1 }],
          ['datalist', { reserved: !1 }],
          ['dd', { reserved: !1 }],
          ['del', { reserved: !1 }],
          ['details', { reserved: !1 }],
          ['dfn', { reserved: !1 }],
          ['dialog', { reserved: !1 }],
          ['dir', { reserved: !1 }],
          ['div', { reserved: !1 }],
          ['dl', { reserved: !1 }],
          ['dt', { reserved: !1 }],
          ['em', { reserved: !1 }],
          ['embed', { reserved: !1 }],
          ['fieldset', { reserved: !1 }],
          ['figcaption', { reserved: !1 }],
          ['figure', { reserved: !1 }],
          ['font', { reserved: !1 }],
          ['footer', { reserved: !1 }],
          ['form', { reserved: !1 }],
          ['frame', { reserved: !1 }],
          ['frameset', { reserved: !1 }],
          ['h1', { reserved: !1 }],
          ['h2', { reserved: !1 }],
          ['h3', { reserved: !1 }],
          ['h4', { reserved: !1 }],
          ['h5', { reserved: !1 }],
          ['h6', { reserved: !1 }],
          ['head', { reserved: !0 }],
          ['header', { reserved: !1 }],
          ['hgroup', { reserved: !1 }],
          ['hr', { reserved: !1 }],
          ['html', { reserved: !0 }],
          ['i', { reserved: !1 }],
          ['iframe', { reserved: !1 }],
          ['img', { reserved: !1 }],
          ['input', { reserved: !1 }],
          ['ins', { reserved: !1 }],
          ['kbd', { reserved: !1 }],
          ['keygen', { reserved: !1 }],
          ['label', { reserved: !1 }],
          ['legend', { reserved: !1 }],
          ['li', { reserved: !1 }],
          ['link', { reserved: !0 }],
          ['main', { reserved: !1 }],
          ['map', { reserved: !1 }],
          ['mark', { reserved: !1 }],
          ['marquee', { reserved: !1 }],
          ['menu', { reserved: !1 }],
          ['menuitem', { reserved: !1 }],
          ['meta', { reserved: !0 }],
          ['meter', { reserved: !1 }],
          ['nav', { reserved: !1 }],
          ['noembed', { reserved: !0 }],
          ['noscript', { reserved: !0 }],
          ['object', { reserved: !1 }],
          ['ol', { reserved: !1 }],
          ['optgroup', { reserved: !1 }],
          ['option', { reserved: !1 }],
          ['output', { reserved: !1 }],
          ['p', { reserved: !1 }],
          ['param', { reserved: !0 }],
          ['picture', { reserved: !0 }],
          ['pre', { reserved: !1 }],
          ['progress', { reserved: !1 }],
          ['q', { reserved: !1 }],
          ['rp', { reserved: !1 }],
          ['rt', { reserved: !1 }],
          ['rtc', { reserved: !1 }],
          ['ruby', { reserved: !1 }],
          ['s', { reserved: !1 }],
          ['samp', { reserved: !1 }],
          ['script', { reserved: !0 }],
          ['section', { reserved: !1 }],
          ['select', { reserved: !1 }],
          ['small', { reserved: !1 }],
          ['source', { reserved: !0 }],
          ['spacer', { reserved: !1 }],
          ['span', { reserved: !1 }],
          ['strike', { reserved: !1 }],
          ['strong', { reserved: !1 }],
          ['style', { reserved: !0 }],
          ['sub', { reserved: !1 }],
          ['summary', { reserved: !1 }],
          ['sup', { reserved: !1 }],
          ['table', { reserved: !1 }],
          ['tbody', { reserved: !1 }],
          ['td', { reserved: !1 }],
          ['textarea', { reserved: !1 }],
          ['tfoot', { reserved: !1 }],
          ['th', { reserved: !1 }],
          ['thead', { reserved: !1 }],
          ['time', { reserved: !1 }],
          ['title', { reserved: !0 }],
          ['tr', { reserved: !1 }],
          ['track', { reserved: !0 }],
          ['tt', { reserved: !1 }],
          ['u', { reserved: !1 }],
          ['ul', { reserved: !1 }],
          ['var', { reserved: !1 }],
          ['video', { reserved: !1 }],
          ['wbr', { reserved: !1 }],
          ['xmp', { reserved: !1 }],
        ],
        u = {
          entries: function () {
            return i;
          },
          forEach: function (e) {
            var t,
              r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
              n = (function (e, t) {
                var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
                if (!r) {
                  if (Array.isArray(e) || (r = a(e))) {
                    r && (e = r);
                    var n = 0,
                      o = function () {};
                    return {
                      s: o,
                      n: function () {
                        return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
                      },
                      e: function (e) {
                        throw e;
                      },
                      f: o,
                    };
                  }
                  throw TypeError(
                    'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
                  );
                }
                var l,
                  i = !0,
                  u = !1;
                return {
                  s: function () {
                    r = r.call(e);
                  },
                  n: function () {
                    var e = r.next();
                    return (i = e.done), e;
                  },
                  e: function (e) {
                    (u = !0), (l = e);
                  },
                  f: function () {
                    try {
                      i || null == r.return || r.return();
                    } finally {
                      if (u) throw l;
                    }
                  },
                };
              })(i);
            try {
              for (n.s(); !(t = n.n()).done; ) {
                var l = o(t.value, 2),
                  u = l[0],
                  s = l[1];
                e.call(r, s, u, i);
              }
            } catch (e) {
              n.e(e);
            } finally {
              n.f();
            }
          },
          get: function (e) {
            var t = i.find(function (t) {
              return t[0] === e;
            });
            return t && t[1];
          },
          has: function (e) {
            return !!u.get(e);
          },
          keys: function () {
            return i.map(function (e) {
              return o(e, 1)[0];
            });
          },
          values: function () {
            return i.map(function (e) {
              return o(e, 2)[1];
            });
          },
        };
      t.default = (0, n.default)(u, u.entries());
    },
    5393: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'rearnote [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: ['doc-endnotes'],
          requiredContextRole: ['doc-endnotes'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'listitem']],
        });
    },
    5726: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    6018: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.hover = function (e, t, { skipPointerEventsCheck: r = !1 } = {}) {
          if (!r && !(0, o.hasPointerEvents)(e))
            throw Error(
              'unable to hover element as it has or inherits pointer-events set to "none".'
            );
          if ((0, o.isLabelWithInternallyDisabledControl)(e)) return;
          let l = a(e).reverse();
          for (let r of (n.fireEvent.pointerOver(e, t), l)) n.fireEvent.pointerEnter(r, t);
          if (!(0, o.isDisabled)(e))
            for (let r of (n.fireEvent.mouseOver(e, (0, o.getMouseEventOptions)('mouseover', t)),
            l))
              n.fireEvent.mouseEnter(r, (0, o.getMouseEventOptions)('mouseenter', t));
          n.fireEvent.pointerMove(e, t),
            (0, o.isDisabled)(e) ||
              n.fireEvent.mouseMove(e, (0, o.getMouseEventOptions)('mousemove', t));
        }),
        (t.unhover = function (e, t, { skipPointerEventsCheck: r = !1 } = {}) {
          if (!r && !(0, o.hasPointerEvents)(e))
            throw Error(
              'unable to unhover element as it has or inherits pointer-events set to "none".'
            );
          if ((0, o.isLabelWithInternallyDisabledControl)(e)) return;
          let l = a(e);
          for (let r of (n.fireEvent.pointerMove(e, t),
          (0, o.isDisabled)(e) ||
            n.fireEvent.mouseMove(e, (0, o.getMouseEventOptions)('mousemove', t)),
          n.fireEvent.pointerOut(e, t),
          l))
            n.fireEvent.pointerLeave(r, t);
          if (!(0, o.isDisabled)(e))
            for (let r of (n.fireEvent.mouseOut(e, (0, o.getMouseEventOptions)('mouseout', t)), l))
              n.fireEvent.mouseLeave(r, (0, o.getMouseEventOptions)('mouseleave', t));
        });
      var n = r(50817),
        o = r(22154);
      function a(e) {
        let t = [e],
          r = e;
        for (; null != (r = r.parentElement); ) t.push(r);
        return t;
      }
    },
    6636: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'introduction [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    7313: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'EPUB biblioentry [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: ['doc-bibliography'],
          requiredContextRole: ['doc-bibliography'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'listitem']],
        });
    },
    7558: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.calculateNewValue = function (
          e,
          t,
          r = null != (n = (0, a.getValue)(t)) ? n : '',
          u = (0, o.getSelectionRange)(t),
          s
        ) {
          let d = null === u.selectionStart ? r.length : u.selectionStart,
            c = null === u.selectionEnd ? r.length : u.selectionEnd,
            p = Math.max(0, d === c && 'backward' === s ? d - 1 : d),
            m = r.substring(0, p),
            f = Math.min(r.length, d === c && 'forward' === s ? c + 1 : c),
            b = r.substring(f, r.length),
            y = `${m}${e}${b}`,
            v = p + e.length;
          return (
            'date' !== t.type || (0, l.isValidDateValue)(t, y) || (y = r),
            'time' !== t.type ||
              (0, i.isValidInputTimeValue)(t, y) ||
              (y = (0, i.isValidInputTimeValue)(t, e) ? e : r),
            { newValue: y, newSelectionStart: v }
          );
        });
      var n,
        o = r(97676),
        a = r(20894),
        l = r(28180),
        i = r(82745);
    },
    7581: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-orientation': 'horizontal' },
          relatedConcepts: [{ concept: { name: 'menubar' }, module: 'ARIA' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'group']],
        });
    },
    8042: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'button' }, module: 'ARIA' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [['roletype', 'widget', 'input', 'checkbox']],
        });
    },
    8993: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.replaceBehavior =
          t.preKeyupBehavior =
          t.preKeydownBehavior =
          t.postKeyupBehavior =
          t.keyupBehavior =
          t.keypressBehavior =
          t.keydownBehavior =
            void 0);
      var n = r(22154),
        o = s(r(3548)),
        a = s(r(3492)),
        l = s(r(90862)),
        i = s(r(36138));
      function u(e) {
        if ('function' != typeof WeakMap) return null;
        var t = new WeakMap(),
          r = new WeakMap();
        return (u = function (e) {
          return e ? r : t;
        })(e);
      }
      function s(e, t) {
        if (!t && e && e.__esModule) return e;
        if (null === e || ('object' != typeof e && 'function' != typeof e)) return { default: e };
        var r = u(t);
        if (r && r.has(e)) return r.get(e);
        var n = {},
          o = Object.defineProperty && Object.getOwnPropertyDescriptor;
        for (var a in e)
          if ('default' !== a && Object.prototype.hasOwnProperty.call(e, a)) {
            var l = o ? Object.getOwnPropertyDescriptor(e, a) : null;
            l && (l.get || l.set) ? Object.defineProperty(n, a, l) : (n[a] = e[a]);
          }
        return (n.default = e), r && r.set(e, n), n;
      }
      (t.replaceBehavior = [
        {
          matches: (e, t) =>
            'selectall' === e.key && (0, n.isElementType)(t, ['input', 'textarea']),
          handle: (e, t, r, o) => {
            var a;
            (0, n.setSelectionRange)(t, 0, (null != (a = o.carryValue) ? a : t.value).length);
          },
        },
      ]),
        (t.preKeydownBehavior = [...i.preKeydownBehavior]),
        (t.keydownBehavior = [...o.keydownBehavior, ...a.keydownBehavior, ...i.keydownBehavior]),
        (t.keypressBehavior = [...i.keypressBehavior, ...l.keypressBehavior]),
        (t.preKeyupBehavior = [...i.preKeyupBehavior]),
        (t.keyupBehavior = [...i.keyupBehavior]),
        (t.postKeyupBehavior = [...i.postKeyupBehavior]);
    },
    10078: (e, t) => {
      'use strict';
      function r(e) {
        return 'mousedown' === e || 'mouseup' === e || 'click' === e || 'dblclick' === e;
      }
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.getMouseEventOptions = function (e, t, r = 0) {
          var n;
          return {
            ...(t = null != (n = t) ? n : {}),
            detail: 'mousedown' === e || 'mouseup' === e || 'click' === e ? 1 + r : r,
            buttons: l(e, t, 'buttons'),
            button: l(e, t, 'button'),
          };
        });
      let n = { none: 0, primary: 1, secondary: 2, auxiliary: 4 },
        o = { primary: 0, auxiliary: 1, secondary: 2 };
      function a(e, t) {
        var r;
        let [a, l] = 'button' === t ? [o, n] : [n, o],
          i = null == (r = Object.entries(a).find(([, t]) => t === e)) ? void 0 : r[0];
        return i && Object.prototype.hasOwnProperty.call(l, i) ? l[i] : 0;
      }
      function l(e, t, n) {
        return r(e)
          ? 'number' == typeof t[n]
            ? t[n]
            : 'button' === n && 'number' == typeof t.buttons
              ? a(t.buttons, 'buttons')
              : 'buttons' === n && 'number' == typeof t.button
                ? a(t.button, 'button')
                : 'button' != n && r(e)
                  ? 1
                  : 0
          : 0;
      }
    },
    10102: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = (function (e) {
        return e && e.__esModule ? e : { default: e };
      })(r(91232));
      function o(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                l = !0,
                i = !1;
              try {
                for (
                  o = o.call(e);
                  !(l = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  l = !0
                );
              } catch (e) {
                (i = !0), (n = e);
              } finally {
                try {
                  l || null == o.return || o.return();
                } finally {
                  if (i) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          a(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function a(e, t) {
        if (e) {
          if ('string' == typeof e) return l(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return l(e, t);
        }
      }
      function l(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      var i = [
          ['aria-activedescendant', { type: 'id' }],
          ['aria-atomic', { type: 'boolean' }],
          ['aria-autocomplete', { type: 'token', values: ['inline', 'list', 'both', 'none'] }],
          ['aria-braillelabel', { type: 'string' }],
          ['aria-brailleroledescription', { type: 'string' }],
          ['aria-busy', { type: 'boolean' }],
          ['aria-checked', { type: 'tristate' }],
          ['aria-colcount', { type: 'integer' }],
          ['aria-colindex', { type: 'integer' }],
          ['aria-colspan', { type: 'integer' }],
          ['aria-controls', { type: 'idlist' }],
          [
            'aria-current',
            { type: 'token', values: ['page', 'step', 'location', 'date', 'time', !0, !1] },
          ],
          ['aria-describedby', { type: 'idlist' }],
          ['aria-description', { type: 'string' }],
          ['aria-details', { type: 'id' }],
          ['aria-disabled', { type: 'boolean' }],
          [
            'aria-dropeffect',
            { type: 'tokenlist', values: ['copy', 'execute', 'link', 'move', 'none', 'popup'] },
          ],
          ['aria-errormessage', { type: 'id' }],
          ['aria-expanded', { type: 'boolean', allowundefined: !0 }],
          ['aria-flowto', { type: 'idlist' }],
          ['aria-grabbed', { type: 'boolean', allowundefined: !0 }],
          [
            'aria-haspopup',
            { type: 'token', values: [!1, !0, 'menu', 'listbox', 'tree', 'grid', 'dialog'] },
          ],
          ['aria-hidden', { type: 'boolean', allowundefined: !0 }],
          ['aria-invalid', { type: 'token', values: ['grammar', !1, 'spelling', !0] }],
          ['aria-keyshortcuts', { type: 'string' }],
          ['aria-label', { type: 'string' }],
          ['aria-labelledby', { type: 'idlist' }],
          ['aria-level', { type: 'integer' }],
          ['aria-live', { type: 'token', values: ['assertive', 'off', 'polite'] }],
          ['aria-modal', { type: 'boolean' }],
          ['aria-multiline', { type: 'boolean' }],
          ['aria-multiselectable', { type: 'boolean' }],
          ['aria-orientation', { type: 'token', values: ['vertical', 'undefined', 'horizontal'] }],
          ['aria-owns', { type: 'idlist' }],
          ['aria-placeholder', { type: 'string' }],
          ['aria-posinset', { type: 'integer' }],
          ['aria-pressed', { type: 'tristate' }],
          ['aria-readonly', { type: 'boolean' }],
          [
            'aria-relevant',
            { type: 'tokenlist', values: ['additions', 'all', 'removals', 'text'] },
          ],
          ['aria-required', { type: 'boolean' }],
          ['aria-roledescription', { type: 'string' }],
          ['aria-rowcount', { type: 'integer' }],
          ['aria-rowindex', { type: 'integer' }],
          ['aria-rowspan', { type: 'integer' }],
          ['aria-selected', { type: 'boolean', allowundefined: !0 }],
          ['aria-setsize', { type: 'integer' }],
          ['aria-sort', { type: 'token', values: ['ascending', 'descending', 'none', 'other'] }],
          ['aria-valuemax', { type: 'number' }],
          ['aria-valuemin', { type: 'number' }],
          ['aria-valuenow', { type: 'number' }],
          ['aria-valuetext', { type: 'string' }],
        ],
        u = {
          entries: function () {
            return i;
          },
          forEach: function (e) {
            var t,
              r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
              n = (function (e, t) {
                var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
                if (!r) {
                  if (Array.isArray(e) || (r = a(e))) {
                    r && (e = r);
                    var n = 0,
                      o = function () {};
                    return {
                      s: o,
                      n: function () {
                        return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
                      },
                      e: function (e) {
                        throw e;
                      },
                      f: o,
                    };
                  }
                  throw TypeError(
                    'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
                  );
                }
                var l,
                  i = !0,
                  u = !1;
                return {
                  s: function () {
                    r = r.call(e);
                  },
                  n: function () {
                    var e = r.next();
                    return (i = e.done), e;
                  },
                  e: function (e) {
                    (u = !0), (l = e);
                  },
                  f: function () {
                    try {
                      i || null == r.return || r.return();
                    } finally {
                      if (u) throw l;
                    }
                  },
                };
              })(i);
            try {
              for (n.s(); !(t = n.n()).done; ) {
                var l = o(t.value, 2),
                  u = l[0],
                  s = l[1];
                e.call(r, s, u, i);
              }
            } catch (e) {
              n.e(e);
            } finally {
              n.f();
            }
          },
          get: function (e) {
            var t = i.find(function (t) {
              return t[0] === e;
            });
            return t && t[1];
          },
          has: function (e) {
            return !!u.get(e);
          },
          keys: function () {
            return i.map(function (e) {
              return o(e, 1)[0];
            });
          },
          values: function () {
            return i.map(function (e) {
              return o(e, 2)[1];
            });
          },
        };
      t.default = (0, n.default)(u, u.entries());
    },
    10330: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = b(r(32750)),
        o = b(r(81972)),
        a = b(r(52083)),
        l = b(r(30273)),
        i = b(r(1254)),
        u = b(r(36753)),
        s = b(r(80340)),
        d = b(r(5726)),
        c = b(r(26461)),
        p = b(r(30438)),
        m = b(r(85229)),
        f = b(r(93589));
      function b(e) {
        return e && e.__esModule ? e : { default: e };
      }
      t.default = [
        ['command', n.default],
        ['composite', o.default],
        ['input', a.default],
        ['landmark', l.default],
        ['range', i.default],
        ['roletype', u.default],
        ['section', s.default],
        ['sectionhead', d.default],
        ['select', c.default],
        ['structure', p.default],
        ['widget', m.default],
        ['window', f.default],
      ];
    },
    10465: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-expanded': null, 'aria-haspopup': null },
          relatedConcepts: [],
          requireContextRole: ['group', 'tree'],
          requiredContextRole: ['group', 'tree'],
          requiredOwnedElements: [],
          requiredProps: { 'aria-selected': null },
          superClass: [
            ['roletype', 'structure', 'section', 'listitem'],
            ['roletype', 'widget', 'input', 'option'],
          ],
        });
    },
    11263: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.fireChangeForInputTimeIfValid = function (e, t, r) {
          (0, o.isValidInputTimeValue)(e, r) &&
            t !== r &&
            n.fireEvent.change(e, { target: { value: r } });
        });
      var n = r(50817),
        o = r(22154);
    },
    11546: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['article']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'list']],
        });
    },
    11909: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'menuitem' }, module: 'ARIA' }],
          requireContextRole: ['group', 'menu', 'menubar'],
          requiredContextRole: ['group', 'menu', 'menubar'],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [
            ['roletype', 'widget', 'input', 'checkbox', 'menuitemcheckbox'],
            ['roletype', 'widget', 'command', 'menuitem', 'menuitemcheckbox'],
            ['roletype', 'widget', 'input', 'radio'],
          ],
        });
    },
    11968: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.typeImplementation = l);
      var n = r(22154),
        o = r(41384),
        a = r(3554);
      async function l(
        e,
        t,
        {
          delay: r,
          skipClick: l = !1,
          skipAutoClose: i = !1,
          initialSelectionStart: u,
          initialSelectionEnd: s,
        }
      ) {
        if (e.disabled) return;
        l || (0, o.click)(e);
        let d = () => (0, n.getActiveElement)(e.ownerDocument),
          c = (0, n.getValue)(d()),
          { selectionStart: p, selectionEnd: m } = (0, n.getSelectionRange)(e);
        null != c &&
          (null === p || 0 === p) &&
          (null === m || 0 === m) &&
          (0, n.setSelectionRange)(d(), null != u ? u : c.length, null != s ? s : c.length);
        let { promise: f, releaseAllKeys: b } = (0, a.keyboardImplementationWrapper)(t, {
          delay: r,
          document: e.ownerDocument,
        });
        return r > 0 && (await f), i || b(), f;
      }
    },
    12447: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [
            { concept: { name: 'a' }, module: 'HTML' },
            { concept: { name: 'area' }, module: 'HTML' },
            { concept: { name: 'aside' }, module: 'HTML' },
            { concept: { name: 'b' }, module: 'HTML' },
            { concept: { name: 'bdo' }, module: 'HTML' },
            { concept: { name: 'body' }, module: 'HTML' },
            { concept: { name: 'data' }, module: 'HTML' },
            { concept: { name: 'div' }, module: 'HTML' },
            {
              concept: {
                constraints: [
                  'scoped to the main element',
                  'scoped to a sectioning content element',
                  'scoped to a sectioning root element other than body',
                ],
                name: 'footer',
              },
              module: 'HTML',
            },
            {
              concept: {
                constraints: [
                  'scoped to the main element',
                  'scoped to a sectioning content element',
                  'scoped to a sectioning root element other than body',
                ],
                name: 'header',
              },
              module: 'HTML',
            },
            { concept: { name: 'hgroup' }, module: 'HTML' },
            { concept: { name: 'i' }, module: 'HTML' },
            { concept: { name: 'pre' }, module: 'HTML' },
            { concept: { name: 'q' }, module: 'HTML' },
            { concept: { name: 'samp' }, module: 'HTML' },
            { concept: { name: 'section' }, module: 'HTML' },
            { concept: { name: 'small' }, module: 'HTML' },
            { concept: { name: 'span' }, module: 'HTML' },
            { concept: { name: 'u' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    12516: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-activedescendant': null,
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'Device Independence Delivery Unit' } }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    12865: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'tbody' }, module: 'HTML' },
            { concept: { name: 'tfoot' }, module: 'HTML' },
            { concept: { name: 'thead' }, module: 'HTML' },
          ],
          requireContextRole: ['grid', 'table', 'treegrid'],
          requiredContextRole: ['grid', 'table', 'treegrid'],
          requiredOwnedElements: [['row']],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    14042: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.FOCUSABLE_SELECTOR = void 0),
        (t.FOCUSABLE_SELECTOR =
          'input:not([type=hidden]):not([disabled]), button:not([disabled]), select:not([disabled]), textarea:not([disabled]), [contenteditable=""], [contenteditable="true"], a[href], [tabindex]:not([disabled])');
    },
    14658: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'dedication [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    14780: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'footnote [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    14805: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-activedescendant': null, 'aria-disabled': null },
          relatedConcepts: [
            { concept: { name: 'details' }, module: 'HTML' },
            { concept: { name: 'fieldset' }, module: 'HTML' },
            { concept: { name: 'optgroup' }, module: 'HTML' },
            { concept: { name: 'address' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    15111: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'search' },
                ],
                constraints: ['the list attribute is not set'],
                name: 'input',
              },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'input', 'textbox']],
        });
    },
    15171: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.type = function (e, t, { delay: r = 0, ...a } = {}) {
          return r > 0
            ? (0, n.getConfig)().asyncWrapper(() =>
                (0, o.typeImplementation)(e, t, { delay: r, ...a })
              )
            : void (0, o.typeImplementation)(e, t, { delay: r, ...a }).catch(console.error);
        });
      var n = r(50817),
        o = r(11968);
    },
    15398: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-multiselectable': null, 'aria-readonly': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['row'], ['row', 'rowgroup']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite'],
            ['roletype', 'structure', 'section', 'table'],
          ],
        });
    },
    15692: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'glossary [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['definition'], ['term']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    16163: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'cover [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'img']],
        });
    },
    17778: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-colcount': null, 'aria-rowcount': null },
          relatedConcepts: [{ concept: { name: 'table' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['row'], ['row', 'rowgroup']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    18270: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.hasUnreliableEmptyValue = function (e) {
          return (0, o.isElementType)(e, 'input') && !!n[e.type];
        });
      var n,
        o = r(26581);
      (n || (n = {})).number = 'number';
    },
    18409: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.blur = function (e) {
          (0, n.isFocusable)(e) &&
            (0, n.getActiveElement)(e.ownerDocument) === e &&
            (0, n.eventWrapper)(() => e.blur());
        });
      var n = r(22154);
    },
    18666: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.TEXT_NODE = void 0),
        (t.checkContainerType = function (e) {
          if (
            !e ||
            'function' != typeof e.querySelector ||
            'function' != typeof e.querySelectorAll
          ) {
            var t;
            throw TypeError(
              `Expected container to be an Element, a Document or a DocumentFragment but got ${'object' == typeof (t = e) ? (null === t ? 'null' : t.constructor.name) : typeof t}.`
            );
          }
        }),
        (t.getDocument = function () {
          if ('undefined' == typeof window) throw Error('Could not find default container');
          return window.document;
        }),
        (t.getWindowFromNode = function (e) {
          if (e.defaultView) return e.defaultView;
          if (e.ownerDocument && e.ownerDocument.defaultView) return e.ownerDocument.defaultView;
          if (e.window) return e.window;
          if (e.ownerDocument && null === e.ownerDocument.defaultView)
            throw Error('It looks like the window object is not available for the provided node.');
          if (e.then instanceof Function)
            throw Error(
              'It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?'
            );
          else if (Array.isArray(e))
            throw Error(
              'It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?'
            );
          else if ('function' == typeof e.debug && 'function' == typeof e.logTestingPlaygroundURL)
            throw Error(
              'It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?'
            );
          else throw Error(`The given node is not an Element, the node type is: ${typeof e}.`);
        }),
        (t.jestFakeTimersAreEnabled = function () {
          return (
            'undefined' != typeof jest &&
            null !== jest &&
            (!0 === setTimeout._isMockFunction ||
              Object.prototype.hasOwnProperty.call(setTimeout, 'clock'))
          );
        }),
        (t.TEXT_NODE = 3);
    },
    19654: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'figure' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    19837: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'blockquote' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    20894: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.getValue = function (e) {
          return e ? ((0, n.isContentEditable)(e) ? e.textContent : e.value) : null;
        });
      var n = r(83684);
    },
    21410: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-pressed': null,
          },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'button' }], name: 'input' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'type', value: 'image' }], name: 'input' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'type', value: 'reset' }], name: 'input' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'type', value: 'submit' }], name: 'input' },
              module: 'HTML',
            },
            { concept: { name: 'button' }, module: 'HTML' },
            { concept: { name: 'trigger' }, module: 'XForms' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command']],
        });
    },
    21872: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'nav' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    22154: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 });
      var n = r(10078);
      Object.keys(n).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === n[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return n[e];
              },
            }));
      });
      var o = r(29583);
      Object.keys(o).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === o[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return o[e];
              },
            }));
      });
      var a = r(34149);
      Object.keys(a).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === a[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return a[e];
              },
            }));
      });
      var l = r(7558);
      Object.keys(l).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === l[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return l[e];
              },
            }));
      });
      var i = r(55654);
      Object.keys(i).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === i[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return i[e];
              },
            }));
      });
      var u = r(20894);
      Object.keys(u).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === u[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return u[e];
              },
            }));
      });
      var s = r(18270);
      Object.keys(s).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === s[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return s[e];
              },
            }));
      });
      var d = r(83684);
      Object.keys(d).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === d[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return d[e];
              },
            }));
      });
      var c = r(54347);
      Object.keys(c).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === c[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return c[e];
              },
            }));
      });
      var p = r(28180);
      Object.keys(p).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === p[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return p[e];
              },
            }));
      });
      var m = r(82745);
      Object.keys(m).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === m[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return m[e];
              },
            }));
      });
      var f = r(59307);
      Object.keys(f).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === f[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return f[e];
              },
            }));
      });
      var b = r(97676);
      Object.keys(b).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === b[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return b[e];
              },
            }));
      });
      var y = r(70929);
      Object.keys(y).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === y[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return y[e];
              },
            }));
      });
      var v = r(66549);
      Object.keys(v).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === v[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return v[e];
              },
            }));
      });
      var h = r(14042);
      Object.keys(h).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === h[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return h[e];
              },
            }));
      });
      var g = r(38380);
      Object.keys(g).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === g[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return g[e];
              },
            }));
      });
      var E = r(26581);
      Object.keys(E).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === E[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return E[e];
              },
            }));
      });
      var C = r(97698);
      Object.keys(C).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === C[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return C[e];
              },
            }));
      });
      var P = r(3031);
      Object.keys(P).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === P[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return P[e];
              },
            }));
      });
      var q = r(43819);
      Object.keys(q).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === q[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return q[e];
              },
            }));
      });
      var w = r(23762);
      Object.keys(w).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === w[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return w[e];
              },
            }));
      });
      var O = r(77070);
      Object.keys(O).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === O[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return O[e];
              },
            }));
      });
      var x = r(22287);
      Object.keys(x).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === x[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return x[e];
              },
            }));
      });
      var _ = r(23167);
      Object.keys(_).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === _[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return _[e];
              },
            }));
      });
    },
    22287: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.hasPointerEvents = function (e) {
          let t = (0, n.getWindowFromNode)(e);
          for (let n = e; null != (r = n) && r.ownerDocument; n = n.parentElement) {
            var r;
            let e = t.getComputedStyle(n).pointerEvents;
            if (e && !['inherit', 'unset'].includes(e)) return 'none' !== e;
          }
          return !0;
        });
      var n = r(18666);
    },
    23167: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.hasFormSubmit = void 0),
        (t.hasFormSubmit = e =>
          !!(
            e &&
            (e.querySelector('input[type="submit"]') || e.querySelector('button[type="submit"]'))
          ));
    },
    23617: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'prologue [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    23762: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isDocument = function (e) {
          return e.nodeType === e.DOCUMENT_NODE;
        });
    },
    24427: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-checked': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
          },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'checkbox' }], name: 'input' },
              module: 'HTML',
            },
            { concept: { name: 'option' }, module: 'ARIA' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    26461: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-orientation': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite'],
            ['roletype', 'structure', 'section', 'group'],
          ],
        });
    },
    26581: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isElementType = function (e, t, r) {
          return (
            (!e.namespaceURI || 'http://www.w3.org/1999/xhtml' === e.namespaceURI) &&
            !!(t = Array.isArray(t) ? t : [t]).includes(e.tagName.toLowerCase()) &&
            (!r || Object.entries(r).every(([t, r]) => e[t] === r))
          );
        });
    },
    27520: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [],
        });
    },
    28180: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isValidDateValue = function (e, t) {
          let r = e.cloneNode();
          return (r.value = t), r.value === t;
        });
    },
    28264: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-posinset': null, 'aria-setsize': null },
          relatedConcepts: [{ concept: { name: 'article' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'document']],
        });
    },
    28407: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-errormessage': null, 'aria-invalid': null },
          relatedConcepts: [{ concept: { name: 'glossref [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command', 'link']],
        });
    },
    28429: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.upload = function (e, t, r, { applyAccept: u = !1 } = {}) {
          var s;
          let d = (0, i.isElementType)(e, 'label') ? e.control : e;
          if (!d || !(0, i.isElementType)(d, 'input', { type: 'file' }))
            throw TypeError(
              `The ${d === e ? 'given' : 'associated'} ${null == d ? void 0 : d.tagName} element does not accept file uploads`
            );
          if ((0, i.isDisabled)(e)) return;
          (0, o.click)(e, null == r ? void 0 : r.clickInit);
          let c = (Array.isArray(t) ? t : [t])
            .filter(
              e =>
                !u ||
                (function (e, t) {
                  if (!t) return !0;
                  let r = ['audio/*', 'image/*', 'video/*'];
                  return t
                    .split(',')
                    .some(t =>
                      t.startsWith('.')
                        ? e.name.endsWith(t)
                        : r.includes(t)
                          ? e.type.startsWith(t.substr(0, t.length - 1))
                          : e.type === t
                    );
                })(e, d.accept)
            )
            .slice(0, d.multiple ? void 0 : 1);
          if (
            ((0, a.blur)(e),
            (0, l.focus)(e),
            c.length === (null == (s = d.files) ? void 0 : s.length) &&
              c.every((e, t) => {
                var r;
                return e === (null == (r = d.files) ? void 0 : r.item(t));
              }))
          )
            return;
          let p = {
            ...c,
            length: c.length,
            item: e => c[e],
            [Symbol.iterator]() {
              let e = 0;
              return { next: () => ({ done: e >= c.length, value: c[e++] }) };
            },
          };
          (0, n.fireEvent)(
            d,
            (0, n.createEvent)('input', d, {
              target: { files: p },
              bubbles: !0,
              cancelable: !1,
              composed: !0,
            })
          ),
            n.fireEvent.change(d, { target: { files: p }, ...(null == r ? void 0 : r.changeInit) });
        });
      var n = r(50817),
        o = r(41384),
        a = r(18409),
        l = r(73164),
        i = r(22154);
    },
    29583: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isClickableInput = function (e) {
          return (
            (0, n.isElementType)(e, 'button') ||
            ((0, n.isElementType)(e, 'input') && o.includes(e.type))
          );
        });
      var n = r(26581);
      let o = ['button', 'color', 'file', 'image', 'reset', 'submit', 'checkbox', 'radio'];
    },
    29716: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-level': '2' },
          relatedConcepts: [
            { concept: { name: 'h1' }, module: 'HTML' },
            { concept: { name: 'h2' }, module: 'HTML' },
            { concept: { name: 'h3' }, module: 'HTML' },
            { concept: { name: 'h4' }, module: 'HTML' },
            { concept: { name: 'h5' }, module: 'HTML' },
            { concept: { name: 'h6' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-level': '2' },
          superClass: [['roletype', 'structure', 'sectionhead']],
        });
    },
    29949: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    30221: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.keyboardImplementation = s),
        (t.releaseAllKeys = function (e, t) {
          let r = () => d(e.document);
          for (let n of t.pressed) c(n.keyDef, r, e, t, n.unpreventedDefault);
        });
      var n = r(50817),
        o = r(22154),
        a = r(33457),
        l = (function (e, t) {
          if (e && e.__esModule) return e;
          if (null === e || ('object' != typeof e && 'function' != typeof e)) return { default: e };
          var r = u(t);
          if (r && r.has(e)) return r.get(e);
          var n = {},
            o = Object.defineProperty && Object.getOwnPropertyDescriptor;
          for (var a in e)
            if ('default' !== a && Object.prototype.hasOwnProperty.call(e, a)) {
              var l = o ? Object.getOwnPropertyDescriptor(e, a) : null;
              l && (l.get || l.set) ? Object.defineProperty(n, a, l) : (n[a] = e[a]);
            }
          return (n.default = e), r && r.set(e, n), n;
        })(r(8993)),
        i = r(80920);
      function u(e) {
        if ('function' != typeof WeakMap) return null;
        var t = new WeakMap(),
          r = new WeakMap();
        return (u = function (e) {
          return e ? r : t;
        })(e);
      }
      async function s(e, t, r) {
        var u, m, f, b;
        let { document: y } = t,
          v = () => d(y),
          {
            keyDef: h,
            consumedLength: g,
            releasePrevious: E,
            releaseSelf: C,
            repeat: P,
          } = null != (u = r.repeatKey) ? u : (0, a.getNextKeyDef)(e, t);
        if (!p(l.replaceBehavior, h, v(), t, r)) {
          let e = r.pressed.find(e => e.keyDef === h);
          if ((e && !r.repeatKey && c(h, v, t, r, e.unpreventedDefault), !E)) {
            let e = (function (e, t, r, o) {
              let a = t();
              a !== o.activeElement && ((o.carryValue = void 0), (o.carryChar = '')),
                (o.activeElement = a),
                p(l.preKeydownBehavior, e, a, r, o);
              let u = n.fireEvent.keyDown(a, (0, i.getKeyEventProps)(e, o));
              return (
                o.pressed.push({ keyDef: e, unpreventedDefault: u }),
                u && p(l.keydownBehavior, e, t(), r, o),
                u
              );
            })(h, v, t, r);
            !e ||
              ((m = h),
              (f = r),
              ((null == (b = m.key) ? void 0 : b.length) !== 1 && 'Enter' !== m.key) ||
                f.modifiers.ctrl ||
                f.modifiers.alt) ||
              (function (e, t, r, o) {
                let a = t();
                n.fireEvent.keyPress(a, (0, i.getKeyEventProps)(e, o)) &&
                  p(l.keypressBehavior, e, t(), r, o);
              })(h, v, t, r),
              C && P <= 1 && c(h, v, t, r, e);
          }
        }
        if (
          (P > 1
            ? (r.repeatKey = {
                consumedLength: 0,
                keyDef: h,
                releasePrevious: E,
                releaseSelf: C,
                repeat: P - 1,
              })
            : delete r.repeatKey,
          e.length > g || P > 1)
        )
          return t.delay > 0 && (await (0, o.wait)(t.delay)), s(e.slice(g), t, r);
      }
      function d(e) {
        var t;
        return null != (t = (0, o.getActiveElement)(e)) ? t : e.body;
      }
      function c(e, t, r, o, a) {
        let u = t();
        p(l.preKeyupBehavior, e, u, r, o);
        let s = n.fireEvent.keyUp(u, (0, i.getKeyEventProps)(e, o));
        a && s && p(l.keyupBehavior, e, t(), r, o),
          (o.pressed = o.pressed.filter(t => t.keyDef !== e)),
          p(l.postKeyupBehavior, e, u, r, o);
      }
      function p(e, t, r, n, o) {
        let a = e.find(e => e.matches(t, r, n, o));
        return a && a.handle(t, r, n, o), !!a;
      }
    },
    30273: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    30438: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype']],
        });
    },
    32543: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'main' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    32750: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget']],
        });
    },
    32957: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'help [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'note']],
        });
    },
    33136: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-live': 'polite' },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    33457: (e, t) => {
      'use strict';
      var r, n, o;
      function a(e, t, r) {
        if (!e) throw Error(l('key descriptor', t[r], t));
      }
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.getNextKeyDef = function (e, t) {
          var i;
          let {
            type: u,
            descriptor: s,
            consumedLength: d,
            releasePrevious: c,
            releaseSelf: p,
            repeat: m,
          } = (function (e) {
            let t = 0,
              o = e[0] in r ? e[t] : '';
            t += o.length;
            let i = o ? e.match(RegExp(`^\\${o}+`))[0].length : 0,
              u = 2 === i || ('{' === o && i > 3) ? '' : o;
            return {
              type: u,
              ...('' === u
                ? (function (e, t) {
                    let r = e[t];
                    return (
                      a(r, e, t),
                      {
                        consumedLength: (t += r.length),
                        descriptor: r,
                        releasePrevious: !1,
                        releaseSelf: !0,
                        repeat: 1,
                      }
                    );
                  })(e, t)
                : (function (e, t, o) {
                    var i, u, s, d, c, p, m;
                    let f = '/' === e[t] ? '/' : '';
                    t += f.length;
                    let b = null == (i = e.slice(t).match(/^\w+/)) ? void 0 : i[0];
                    a(b, e, t), (t += b.length);
                    let y =
                        null != (u = null == (s = e.slice(t).match(/^>\d+/)) ? void 0 : s[0])
                          ? u
                          : '',
                      v = '/' !== e[(t += y.length)] && (y || '>' !== e[t]) ? '' : e[t];
                    t += v.length;
                    let h = r[o],
                      g = e[t] === h ? h : '';
                    if (!g)
                      throw Error(
                        l(
                          [!y && 'repeat modifier', !v && 'release modifier', `"${h}"`]
                            .filter(Boolean)
                            .join(' or '),
                          e[t],
                          e
                        )
                      );
                    return {
                      consumedLength: (t += g.length),
                      descriptor: b,
                      releasePrevious: !!f,
                      repeat: y ? Math.max(Number(y.substr(1)), 1) : 1,
                      releaseSelf:
                        ((d = o),
                        (c = b),
                        (p = v),
                        (m = y),
                        p ? '/' === p : !m && ('{' !== d || !n[c.toLowerCase()])),
                    };
                  })(e, t, u)),
            };
          })(e);
          return {
            keyDef:
              null !=
              (i = t.keyboardMap.find(e => {
                var t, r, n, a;
                if ('[' === u)
                  return (null == (t = e.code) ? void 0 : t.toLowerCase()) === s.toLowerCase();
                if ('{' === u) {
                  let t = null != (a = o[(n = s)]) ? a : n;
                  return (null == (r = e.key) ? void 0 : r.toLowerCase()) === t.toLowerCase();
                }
                return e.key === s;
              }))
                ? i
                : { key: 'Unknown', code: 'Unknown', ['[' === u ? 'code' : 'key']: s },
            consumedLength: d,
            releasePrevious: c,
            releaseSelf: p,
            repeat: m,
          };
        }),
        (function (e) {
          (e['{'] = '}'), (e['['] = ']');
        })(r || (r = {})),
        (function (e) {
          (e.alt = 'alt'), (e.ctrl = 'ctrl'), (e.meta = 'meta'), (e.shift = 'shift');
        })(n || (n = {})),
        (function (e) {
          (e.ctrl = 'Control'), (e.del = 'Delete'), (e.esc = 'Escape'), (e.space = ' ');
        })(o || (o = {}));
      function l(e, t, r) {
        return `Expected ${e} but found "${null != t ? t : ''}" in "${r}"
    See https://github.com/testing-library/user-event/blob/main/README.md#keyboardtext-options
    for more information about how userEvent parses your input.`;
      }
    },
    33473: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'credit [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    33976: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'preface [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    34132: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'alert' }, module: 'XForms' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'alert'],
            ['roletype', 'window', 'dialog'],
          ],
        });
    },
    34149: (e, t) => {
      'use strict';
      function r(e, t) {
        let r = Math.min(parseInt(e.slice(0, t), 10), 23),
          n = Math.min(parseInt(e.slice(t), 10), 59);
        return `${r.toString().padStart(2, '0')}:${n.toString().padStart(2, '0')}`;
      }
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.buildTimeValue = function (e) {
          let t = e.replace(/\D/g, '');
          if (t.length < 2) return e;
          let n = parseInt(t[0], 10),
            o = parseInt(t[1], 10);
          if (n >= 3 || (2 === n && o >= 4)) return r(t, n >= 3 ? 1 : 2);
          return 2 === e.length ? e : r(t, 2);
        });
    },
    34459: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.selectOptions = t.deselectOptions = void 0);
      var n = r(50817),
        o = r(22154),
        a = r(41384),
        l = r(73164),
        i = r(6018);
      function u(e, t, r, s, { skipPointerEventsCheck: d = !1 } = {}) {
        if (!e && !t.multiple)
          throw (0, n.getConfig)().getElementError(
            'Unable to deselect an option in a non-multiple select. Use selectOptions to change the selection instead.',
            t
          );
        let c = Array.isArray(r) ? r : [r],
          p = Array.from(t.querySelectorAll('option, [role="option"]')),
          m = c
            .map(e => {
              if ('string' != typeof e && p.includes(e)) return e;
              {
                let r = p.find(t => t.value === e || t.innerHTML === e);
                if (r) return r;
                throw (0, n.getConfig)().getElementError(
                  `Value "${String(e)}" not found in options`,
                  t
                );
              }
            })
            .filter(e => !(0, o.isDisabled)(e));
        if (!(0, o.isDisabled)(t) && m.length)
          if ((0, o.isElementType)(t, 'select'))
            if (t.multiple)
              for (let e of m) {
                let r = !!d || (0, o.hasPointerEvents)(e);
                r &&
                  (n.fireEvent.pointerOver(e, s),
                  n.fireEvent.pointerEnter(t, s),
                  n.fireEvent.mouseOver(e),
                  n.fireEvent.mouseEnter(t),
                  n.fireEvent.pointerMove(e, s),
                  n.fireEvent.mouseMove(e, s),
                  n.fireEvent.pointerDown(e, s),
                  n.fireEvent.mouseDown(e, s)),
                  (0, l.focus)(t),
                  r && (n.fireEvent.pointerUp(e, s), n.fireEvent.mouseUp(e, s)),
                  f(e),
                  r && n.fireEvent.click(e, s);
              }
            else if (1 === m.length) {
              let e = !!d || (0, o.hasPointerEvents)(t);
              e ? (0, a.click)(t, s, { skipPointerEventsCheck: d }) : (0, l.focus)(t),
                f(m[0]),
                e &&
                  (n.fireEvent.pointerOver(t, s),
                  n.fireEvent.pointerEnter(t, s),
                  n.fireEvent.mouseOver(t),
                  n.fireEvent.mouseEnter(t),
                  n.fireEvent.pointerUp(t, s),
                  n.fireEvent.mouseUp(t, s),
                  n.fireEvent.click(t, s));
            } else
              throw (0, n.getConfig)().getElementError(
                'Cannot select multiple options on a non-multiple select',
                t
              );
          else if ('listbox' === t.getAttribute('role'))
            m.forEach(e => {
              (0, i.hover)(e, s, { skipPointerEventsCheck: d }),
                (0, a.click)(e, s, { skipPointerEventsCheck: d }),
                (0, i.unhover)(e, s, { skipPointerEventsCheck: d });
            });
          else
            throw (0, n.getConfig)().getElementError(
              'Cannot select options on elements that are neither select nor listbox elements',
              t
            );
        function f(r) {
          (r.selected = e),
            (0, n.fireEvent)(
              t,
              (0, n.createEvent)('input', t, { bubbles: !0, cancelable: !1, composed: !0, ...s })
            ),
            n.fireEvent.change(t, s);
        }
      }
      (t.selectOptions = u.bind(null, !0)), (t.deselectOptions = u.bind(null, !1));
    },
    34753: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.clear = function (e) {
          var t, r;
          if (!(0, n.isElementType)(e, ['input', 'textarea']))
            throw Error('clear currently only supports input and textarea elements.');
          if ((0, n.isDisabled)(e)) return;
          let a = e.type;
          'textarea' !== a && (e.type = 'text'),
            (0, o.type)(e, '{selectall}{del}', {
              delay: 0,
              initialSelectionStart: null != (t = e.selectionStart) ? t : void 0,
              initialSelectionEnd: null != (r = e.selectionEnd) ? r : void 0,
            }),
            'textarea' !== a && (e.type = a);
        });
      var n = r(22154),
        o = r(15171);
    },
    35546: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'credits [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    35547: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: [],
          props: {
            'aria-braillelabel': null,
            'aria-brailleroledescription': null,
            'aria-description': null,
          },
          relatedConcepts: [{ concept: { name: 'mark' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    35615: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'page-list [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']],
        });
    },
    36138: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.preKeyupBehavior =
          t.preKeydownBehavior =
          t.postKeyupBehavior =
          t.keyupBehavior =
          t.keypressBehavior =
          t.keydownBehavior =
            void 0);
      var n = r(50817),
        o = r(22154),
        a = r(80920),
        l = r(76696);
      let i = { Alt: 'alt', Control: 'ctrl', Shift: 'shift', Meta: 'meta' };
      (t.preKeydownBehavior = [
        ...Object.entries(i).map(([e, t]) => ({
          matches: t => t.key === e,
          handle: (e, r, n, o) => {
            o.modifiers[t] = !0;
          },
        })),
        {
          matches: e => 'AltGraph' === e.key,
          handle: (e, t, r, o) => {
            var l;
            let i =
              null != (l = r.keyboardMap.find(e => 'Control' === e.key))
                ? l
                : { key: 'Control', code: 'Control' };
            n.fireEvent.keyDown(t, (0, a.getKeyEventProps)(i, o));
          },
        },
      ]),
        (t.keydownBehavior = [
          {
            matches: e => 'CapsLock' === e.key,
            handle: (e, t, r, n) => {
              n.modifiers.caps = !n.modifiers.caps;
            },
          },
          {
            matches: (e, t) =>
              'Backspace' === e.key && (0, o.isEditable)(t) && !(0, o.isCursorAtStart)(t),
            handle: (e, t, r, n) => {
              let { newValue: a, newSelectionStart: i } = (0, o.calculateNewValue)(
                '',
                t,
                n.carryValue,
                void 0,
                'backward'
              );
              (0, l.fireInputEvent)(t, {
                newValue: a,
                newSelectionStart: i,
                eventOverrides: { inputType: 'deleteContentBackward' },
              }),
                (0, l.carryValue)(t, n, a);
            },
          },
        ]),
        (t.keypressBehavior = [
          {
            matches: (e, t) =>
              'Enter' === e.key &&
              (0, o.isElementType)(t, 'input') &&
              ['checkbox', 'radio'].includes(t.type),
            handle: (e, t) => {
              let r = t.form;
              (0, o.hasFormSubmit)(r) && n.fireEvent.submit(r);
            },
          },
          {
            matches: (e, t) =>
              'Enter' === e.key &&
              ((0, o.isClickableInput)(t) || ((0, o.isElementType)(t, 'a') && !!t.href)),
            handle: (e, t, r, o) => {
              n.fireEvent.click(t, (0, a.getMouseEventProps)(o));
            },
          },
          {
            matches: (e, t) => 'Enter' === e.key && (0, o.isElementType)(t, 'input'),
            handle: (e, t) => {
              let r = t.form;
              r &&
                (1 === r.querySelectorAll('input').length || (0, o.hasFormSubmit)(r)) &&
                n.fireEvent.submit(r);
            },
          },
        ]),
        (t.preKeyupBehavior = [
          ...Object.entries(i).map(([e, t]) => ({
            matches: t => t.key === e,
            handle: (e, r, n, o) => {
              o.modifiers[t] = !1;
            },
          })),
        ]),
        (t.keyupBehavior = [
          {
            matches: (e, t) => ' ' === e.key && (0, o.isClickableInput)(t),
            handle: (e, t, r, o) => {
              n.fireEvent.click(t, (0, a.getMouseEventProps)(o));
            },
          },
        ]),
        (t.postKeyupBehavior = [
          {
            matches: e => 'AltGraph' === e.key,
            handle: (e, t, r, o) => {
              var l;
              let i =
                null != (l = r.keyboardMap.find(e => 'Control' === e.key))
                  ? l
                  : { key: 'Control', code: 'Control' };
              n.fireEvent.keyUp(t, (0, a.getKeyEventProps)(i, o));
            },
          },
        ]);
    },
    36753: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {
            'aria-atomic': null,
            'aria-busy': null,
            'aria-controls': null,
            'aria-current': null,
            'aria-describedby': null,
            'aria-details': null,
            'aria-dropeffect': null,
            'aria-flowto': null,
            'aria-grabbed': null,
            'aria-hidden': null,
            'aria-keyshortcuts': null,
            'aria-label': null,
            'aria-labelledby': null,
            'aria-live': null,
            'aria-owns': null,
            'aria-relevant': null,
            'aria-roledescription': null,
          },
          relatedConcepts: [
            { concept: { name: 'role' }, module: 'XHTML' },
            { concept: { name: 'type' }, module: 'Dublin Core' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [],
        });
    },
    37231: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'ins' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    37993: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-posinset': null,
            'aria-setsize': null,
            'aria-selected': 'false',
          },
          relatedConcepts: [],
          requireContextRole: ['tablist'],
          requiredContextRole: ['tablist'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'sectionhead'],
            ['roletype', 'widget'],
          ],
        });
    },
    38080: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'caption' }, module: 'HTML' }],
          requireContextRole: ['figure', 'grid', 'table'],
          requiredContextRole: ['figure', 'grid', 'table'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    38310: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = u(r(10330)),
        o = u(r(42659)),
        a = u(r(76537)),
        l = u(r(62503)),
        i = u(r(91232));
      function u(e) {
        return e && e.__esModule ? e : { default: e };
      }
      function s(e, t) {
        var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
        if (!r) {
          if (Array.isArray(e) || (r = c(e)) || (t && e && 'number' == typeof e.length)) {
            r && (e = r);
            var n = 0,
              o = function () {};
            return {
              s: o,
              n: function () {
                return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
              },
              e: function (e) {
                throw e;
              },
              f: o,
            };
          }
          throw TypeError(
            'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
          );
        }
        var a,
          l = !0,
          i = !1;
        return {
          s: function () {
            r = r.call(e);
          },
          n: function () {
            var e = r.next();
            return (l = e.done), e;
          },
          e: function (e) {
            (i = !0), (a = e);
          },
          f: function () {
            try {
              l || null == r.return || r.return();
            } finally {
              if (i) throw a;
            }
          },
        };
      }
      function d(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                l = !0,
                i = !1;
              try {
                for (
                  o = o.call(e);
                  !(l = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  l = !0
                );
              } catch (e) {
                (i = !0), (n = e);
              } finally {
                try {
                  l || null == o.return || o.return();
                } finally {
                  if (i) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          c(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function c(e, t) {
        if (e) {
          if ('string' == typeof e) return p(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return p(e, t);
        }
      }
      function p(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      var m = [].concat(n.default, o.default, a.default, l.default);
      m.forEach(function (e) {
        var t,
          r = d(e, 2)[1],
          n = s(r.superClass);
        try {
          for (n.s(); !(t = n.n()).done; ) {
            var o,
              a = t.value,
              l = s(a);
            try {
              for (l.s(); !(o = l.n()).done; )
                !(function () {
                  var e = o.value,
                    t = m.find(function (t) {
                      return d(t, 1)[0] === e;
                    });
                  if (t)
                    for (var n = t[1], a = 0, l = Object.keys(n.props); a < l.length; a++) {
                      var i,
                        u,
                        s = l[a];
                      Object.prototype.hasOwnProperty.call(r.props, s) ||
                        Object.assign(
                          r.props,
                          ((i = {}),
                          (u = n.props[s]),
                          s in i
                            ? Object.defineProperty(i, s, {
                                value: u,
                                enumerable: !0,
                                configurable: !0,
                                writable: !0,
                              })
                            : (i[s] = u),
                          i)
                        );
                    }
                })();
            } catch (e) {
              l.e(e);
            } finally {
              l.f();
            }
          }
        } catch (e) {
          n.e(e);
        } finally {
          n.f();
        }
      });
      var f = {
        entries: function () {
          return m;
        },
        forEach: function (e) {
          var t,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
            n = s(m);
          try {
            for (n.s(); !(t = n.n()).done; ) {
              var o = d(t.value, 2),
                a = o[0],
                l = o[1];
              e.call(r, l, a, m);
            }
          } catch (e) {
            n.e(e);
          } finally {
            n.f();
          }
        },
        get: function (e) {
          var t = m.find(function (t) {
            return t[0] === e;
          });
          return t && t[1];
        },
        has: function (e) {
          return !!f.get(e);
        },
        keys: function () {
          return m.map(function (e) {
            return d(e, 1)[0];
          });
        },
        values: function () {
          return m.map(function (e) {
            return d(e, 2)[1];
          });
        },
      };
      t.default = (0, i.default)(f, f.entries());
    },
    38380: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.eventWrapper = function (e) {
          let t;
          return (
            (0, n.getConfig)().eventWrapper(() => {
              t = e();
            }),
            t
          );
        });
      var n = r(50817);
    },
    39960: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'del' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    40098: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: {
                attributes: [{ constraints: ['set'], name: 'aria-label' }],
                name: 'section',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [{ constraints: ['set'], name: 'aria-labelledby' }],
                name: 'section',
              },
              module: 'HTML',
            },
            { concept: { name: 'Device Independence Glossart perceivable unit' } },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    40501: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-sort': null },
          relatedConcepts: [
            { concept: { name: 'th' }, module: 'HTML' },
            {
              concept: { attributes: [{ name: 'scope', value: 'col' }], name: 'th' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'scope', value: 'colgroup' }], name: 'th' },
              module: 'HTML',
            },
          ],
          requireContextRole: ['row'],
          requiredContextRole: ['row'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'cell'],
            ['roletype', 'structure', 'section', 'cell', 'gridcell'],
            ['roletype', 'widget', 'gridcell'],
            ['roletype', 'structure', 'sectionhead'],
          ],
        });
    },
    41384: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.click = u),
        (t.dblClick = function (e, t, { skipPointerEventsCheck: r = !1 } = {}) {
          if (!r && !(0, o.hasPointerEvents)(e))
            throw Error(
              'unable to double-click element as it has or inherits pointer-events set to "none".'
            );
          (0, a.hover)(e, t, { skipPointerEventsCheck: r }),
            u(e, t, { skipHover: !0, clickCount: 0, skipPointerEventsCheck: r }),
            u(e, t, { skipHover: !0, clickCount: 1, skipPointerEventsCheck: r }),
            n.fireEvent.dblClick(e, (0, o.getMouseEventOptions)('dblclick', t, 2));
        });
      var n = r(50817),
        o = r(22154),
        a = r(6018),
        l = r(18409),
        i = r(73164);
      function u(
        e,
        t,
        { skipHover: r = !1, clickCount: d = 0, skipPointerEventsCheck: c = !1 } = {}
      ) {
        if (!c && !(0, o.hasPointerEvents)(e))
          throw Error(
            'unable to click element as it has or inherits pointer-events set to "none".'
          );
        r || (0, a.hover)(e, t, { skipPointerEventsCheck: !0 }),
          (0, o.isElementType)(e, 'label')
            ? (function (e, t, { clickCount: r }) {
                !(0, o.isLabelWithInternallyDisabledControl)(e) &&
                  (n.fireEvent.pointerDown(e, t),
                  n.fireEvent.mouseDown(e, (0, o.getMouseEventOptions)('mousedown', t, r)),
                  n.fireEvent.pointerUp(e, t),
                  n.fireEvent.mouseUp(e, (0, o.getMouseEventOptions)('mouseup', t, r)),
                  s(e, (0, o.getMouseEventOptions)('click', t, r)),
                  e.control && (0, i.focus)(e.control));
              })(e, t, { clickCount: d })
            : (0, o.isElementType)(e, 'input') && ('checkbox' === e.type || 'radio' === e.type)
              ? (function (e, t, { clickCount: r }) {
                  n.fireEvent.pointerDown(e, t),
                    e.disabled ||
                      n.fireEvent.mouseDown(e, (0, o.getMouseEventOptions)('mousedown', t, r)),
                    (0, i.focus)(e),
                    n.fireEvent.pointerUp(e, t),
                    e.disabled ||
                      (n.fireEvent.mouseUp(e, (0, o.getMouseEventOptions)('mouseup', t, r)),
                      s(e, (0, o.getMouseEventOptions)('click', t, r)));
                })(e, t, { clickCount: d })
              : (function (e, t, { clickCount: r }) {
                  let a = (function (e) {
                    let t = e.ownerDocument.activeElement;
                    return t && t !== e.ownerDocument.body && t !== e ? t : null;
                  })(e);
                  if (
                    (n.fireEvent.pointerDown(e, t),
                    !(0, o.isDisabled)(e) &&
                      n.fireEvent.mouseDown(e, (0, o.getMouseEventOptions)('mousedown', t, r)))
                  ) {
                    let t = (function (e, t) {
                      let r = e;
                      do {
                        if (t(r)) return r;
                        r = r.parentElement;
                      } while (r && r !== e.ownerDocument.body);
                    })(e, o.isFocusable);
                    a && !t ? (0, l.blur)(a) : t && (0, i.focus)(t);
                  }
                  if ((n.fireEvent.pointerUp(e, t), !(0, o.isDisabled)(e))) {
                    n.fireEvent.mouseUp(e, (0, o.getMouseEventOptions)('mouseup', t, r)),
                      s(e, (0, o.getMouseEventOptions)('click', t, r));
                    let a = e.closest('label');
                    null != a && a.control && (0, i.focus)(a.control);
                  }
                })(e, t, { clickCount: d });
      }
      function s(e, t) {
        2 === t.button ? n.fireEvent.contextMenu(e, t) : n.fireEvent.click(e, t);
      }
    },
    41712: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'epigraph [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    42659: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = ej(r(48340)),
        o = ej(r(34132)),
        a = ej(r(12516)),
        l = ej(r(28264)),
        i = ej(r(88372)),
        u = ej(r(19837)),
        s = ej(r(21410)),
        d = ej(r(38080)),
        c = ej(r(74026)),
        p = ej(r(24427)),
        m = ej(r(4215)),
        f = ej(r(40501)),
        b = ej(r(78453)),
        y = ej(r(93172)),
        v = ej(r(1543)),
        h = ej(r(3579)),
        g = ej(r(39960)),
        E = ej(r(69638)),
        C = ej(r(94133)),
        P = ej(r(59203)),
        q = ej(r(97732)),
        w = ej(r(11546)),
        O = ej(r(19654)),
        x = ej(r(77516)),
        _ = ej(r(12447)),
        R = ej(r(15398)),
        T = ej(r(49450)),
        M = ej(r(14805)),
        k = ej(r(29716)),
        j = ej(r(89721)),
        A = ej(r(37231)),
        S = ej(r(75286)),
        B = ej(r(53502)),
        I = ej(r(77483)),
        L = ej(r(71713)),
        N = ej(r(33136)),
        F = ej(r(32543)),
        D = ej(r(35547)),
        U = ej(r(42934)),
        H = ej(r(4596)),
        V = ej(r(74033)),
        K = ej(r(96350)),
        W = ej(r(82662)),
        $ = ej(r(98027)),
        z = ej(r(11909)),
        G = ej(r(59243)),
        Y = ej(r(21872)),
        Q = ej(r(27520)),
        X = ej(r(86906)),
        J = ej(r(99651)),
        Z = ej(r(62148)),
        ee = ej(r(46278)),
        et = ej(r(53482)),
        er = ej(r(2085)),
        en = ej(r(69752)),
        eo = ej(r(40098)),
        ea = ej(r(2002)),
        el = ej(r(12865)),
        ei = ej(r(48331)),
        eu = ej(r(81460)),
        es = ej(r(74442)),
        ed = ej(r(15111)),
        ec = ej(r(2417)),
        ep = ej(r(55031)),
        em = ej(r(93370)),
        ef = ej(r(42816)),
        eb = ej(r(53569)),
        ey = ej(r(73747)),
        ev = ej(r(66154)),
        eh = ej(r(8042)),
        eg = ej(r(37993)),
        eE = ej(r(17778)),
        eC = ej(r(69121)),
        eP = ej(r(67665)),
        eq = ej(r(74304)),
        ew = ej(r(84510)),
        eO = ej(r(90851)),
        ex = ej(r(93339)),
        e_ = ej(r(7581)),
        eR = ej(r(29949)),
        eT = ej(r(93758)),
        eM = ej(r(81518)),
        ek = ej(r(10465));
      function ej(e) {
        return e && e.__esModule ? e : { default: e };
      }
      t.default = [
        ['alert', n.default],
        ['alertdialog', o.default],
        ['application', a.default],
        ['article', l.default],
        ['banner', i.default],
        ['blockquote', u.default],
        ['button', s.default],
        ['caption', d.default],
        ['cell', c.default],
        ['checkbox', p.default],
        ['code', m.default],
        ['columnheader', f.default],
        ['combobox', b.default],
        ['complementary', y.default],
        ['contentinfo', v.default],
        ['definition', h.default],
        ['deletion', g.default],
        ['dialog', E.default],
        ['directory', C.default],
        ['document', P.default],
        ['emphasis', q.default],
        ['feed', w.default],
        ['figure', O.default],
        ['form', x.default],
        ['generic', _.default],
        ['grid', R.default],
        ['gridcell', T.default],
        ['group', M.default],
        ['heading', k.default],
        ['img', j.default],
        ['insertion', A.default],
        ['link', S.default],
        ['list', B.default],
        ['listbox', I.default],
        ['listitem', L.default],
        ['log', N.default],
        ['main', F.default],
        ['mark', D.default],
        ['marquee', U.default],
        ['math', H.default],
        ['menu', V.default],
        ['menubar', K.default],
        ['menuitem', W.default],
        ['menuitemcheckbox', $.default],
        ['menuitemradio', z.default],
        ['meter', G.default],
        ['navigation', Y.default],
        ['none', Q.default],
        ['note', X.default],
        ['option', J.default],
        ['paragraph', Z.default],
        ['presentation', ee.default],
        ['progressbar', et.default],
        ['radio', er.default],
        ['radiogroup', en.default],
        ['region', eo.default],
        ['row', ea.default],
        ['rowgroup', el.default],
        ['rowheader', ei.default],
        ['scrollbar', eu.default],
        ['search', es.default],
        ['searchbox', ed.default],
        ['separator', ec.default],
        ['slider', ep.default],
        ['spinbutton', em.default],
        ['status', ef.default],
        ['strong', eb.default],
        ['subscript', ey.default],
        ['superscript', ev.default],
        ['switch', eh.default],
        ['tab', eg.default],
        ['table', eE.default],
        ['tablist', eC.default],
        ['tabpanel', eP.default],
        ['term', eq.default],
        ['textbox', ew.default],
        ['time', eO.default],
        ['timer', ex.default],
        ['toolbar', e_.default],
        ['tooltip', eR.default],
        ['tree', eT.default],
        ['treegrid', eM.default],
        ['treeitem', ek.default],
      ];
    },
    42816: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-atomic': 'true', 'aria-live': 'polite' },
          relatedConcepts: [{ concept: { name: 'output' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    42934: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    43185: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-errormessage': null, 'aria-invalid': null },
          relatedConcepts: [{ concept: { name: 'noteref [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command', 'link']],
        });
    },
    43819: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isDisabled = function (e) {
          return !!(e && e.disabled);
        });
    },
    46278: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [
            { concept: { attributes: [{ name: 'alt', value: '' }], name: 'img' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    48331: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-sort': null },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'scope', value: 'row' }], name: 'th' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ name: 'scope', value: 'rowgroup' }], name: 'th' },
              module: 'HTML',
            },
          ],
          requireContextRole: ['row', 'rowgroup'],
          requiredContextRole: ['row', 'rowgroup'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'cell'],
            ['roletype', 'structure', 'section', 'cell', 'gridcell'],
            ['roletype', 'widget', 'gridcell'],
            ['roletype', 'structure', 'sectionhead'],
          ],
        });
    },
    48340: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-atomic': 'true', 'aria-live': 'assertive' },
          relatedConcepts: [{ concept: { name: 'alert' }, module: 'XForms' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    48940: (e, t, r) => {
      'use strict';
      t.Ay = void 0;
      var n = r(41384),
        o = r(15171),
        a = r(34753),
        l = r(54519),
        i = r(6018),
        u = r(28429),
        s = r(34459),
        d = r(99131),
        c = r(3554);
      t.Ay = {
        click: n.click,
        dblClick: n.dblClick,
        type: o.type,
        clear: a.clear,
        tab: l.tab,
        hover: i.hover,
        unhover: i.unhover,
        upload: u.upload,
        selectOptions: s.selectOptions,
        deselectOptions: s.deselectOptions,
        paste: d.paste,
        keyboard: c.keyboard,
      };
    },
    49450: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
            'aria-selected': null,
          },
          relatedConcepts: [
            {
              concept: {
                constraints: [
                  'ancestor table element has grid role',
                  'ancestor table element has treegrid role',
                ],
                name: 'td',
              },
              module: 'HTML',
            },
          ],
          requireContextRole: ['row'],
          requiredContextRole: ['row'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'section', 'cell'],
            ['roletype', 'widget'],
          ],
        });
    },
    50292: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-errormessage': null, 'aria-invalid': null },
          relatedConcepts: [{ concept: { name: 'biblioref [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command', 'link']],
        });
    },
    50498: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'qna [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    50817: (e, t, r) => {
      'use strict';
      r.r(t),
        r.d(t, {
          buildQueries: () => ev,
          configure: () => A,
          createEvent: () => t_,
          findAllByAltText: () => e2,
          findAllByDisplayValue: () => eY,
          findAllByLabelText: () => eO,
          findAllByPlaceholderText: () => eB,
          findAllByRole: () => ts,
          findAllByTestId: () => tv,
          findAllByText: () => eH,
          findAllByTitle: () => tt,
          findByAltText: () => e5,
          findByDisplayValue: () => eQ,
          findByLabelText: () => ex,
          findByPlaceholderText: () => eI,
          findByRole: () => td,
          findByTestId: () => th,
          findByText: () => eV,
          findByTitle: () => tr,
          fireEvent: () => tx,
          getAllByAltText: () => e1,
          getAllByDisplayValue: () => ez,
          getAllByLabelText: () => e_,
          getAllByPlaceholderText: () => eA,
          getAllByRole: () => ti,
          getAllByTestId: () => tb,
          getAllByText: () => eD,
          getAllByTitle: () => e9,
          getByAltText: () => e3,
          getByDisplayValue: () => eG,
          getByLabelText: () => eR,
          getByPlaceholderText: () => eS,
          getByRole: () => tu,
          getByTestId: () => ty,
          getByText: () => eU,
          getByTitle: () => te,
          getConfig: () => S,
          getDefaultNormalizer: () => H,
          getElementError: () => ei,
          getMultipleElementsFoundError: () => eu,
          getNodeText: () => W,
          getQueriesForElement: () => tE,
          getRoles: () => Q,
          getSuggestedQuery: () => eo,
          isInaccessible: () => G,
          logDOM: () => k,
          logRoles: () => J,
          makeFindQuery: () => ef,
          makeGetAllQuery: () => em,
          makeSingleQuery: () => ec,
          prettyDOM: () => M,
          prettyFormat: () => n,
          queries: () => tg,
          queryAllByAltText: () => eZ,
          queryAllByAttribute: () => es,
          queryAllByDisplayValue: () => eW,
          queryAllByLabelText: () => eT,
          queryAllByPlaceholderText: () => ek,
          queryAllByRole: () => ta,
          queryAllByTestId: () => tm,
          queryAllByText: () => eN,
          queryAllByTitle: () => e6,
          queryByAltText: () => e0,
          queryByAttribute: () => ed,
          queryByDisplayValue: () => e$,
          queryByLabelText: () => eq,
          queryByPlaceholderText: () => ej,
          queryByRole: () => tl,
          queryByTestId: () => tf,
          queryByText: () => eF,
          queryByTitle: () => e7,
          queryHelpers: () => eh,
          screen: () => tT,
          waitFor: () => el,
          waitForElementToBeRemoved: () => tq,
          within: () => tE,
          wrapAllByQueryWithSuggestion: () => ey,
          wrapSingleQueryWithSuggestion: () => eb,
        });
      var n = r(31777),
        o = r(14945),
        a = r(58465),
        l = r(95490),
        i = r.n(l);
      e = r.hmd(e);
      var u = r(77051);
      function s(e) {
        return e.replace(/</g, '&lt;').replace(/>/g, '&gt;');
      }
      let d = (e, t, r, n, o, a, l) => {
          let i = n + r.indent,
            u = r.colors;
          return e
            .map(e => {
              let s = t[e],
                d = l(s, r, i, o, a);
              return (
                'string' != typeof s &&
                  (-1 !== d.indexOf('\n') && (d = r.spacingOuter + i + d + r.spacingOuter + n),
                  (d = '{' + d + '}')),
                r.spacingInner +
                  n +
                  u.prop.open +
                  e +
                  u.prop.close +
                  '=' +
                  u.value.open +
                  d +
                  u.value.close
              );
            })
            .join('');
        },
        c = (e, t, r, n, o, a) =>
          e
            .map(e => {
              let l = 'string' == typeof e ? p(e, t) : a(e, t, r, n, o);
              return '' === l && 'object' == typeof e && null !== e && 3 !== e.nodeType
                ? ''
                : t.spacingOuter + r + l;
            })
            .join(''),
        p = (e, t) => {
          let r = t.colors.content;
          return r.open + s(e) + r.close;
        },
        m = (e, t) => {
          let r = t.colors.comment;
          return r.open + '\x3c!--' + s(e) + '--\x3e' + r.close;
        },
        f = (e, t, r, n, o) => {
          let a = n.colors.tag;
          return (
            a.open +
            '<' +
            e +
            (t && a.close + t + n.spacingOuter + o + a.open) +
            (r
              ? '>' + a.close + r + n.spacingOuter + o + a.open + '</' + e
              : (t && !n.min ? '' : ' ') + '/') +
            '>' +
            a.close
          );
        },
        b = (e, t) => {
          let r = t.colors.tag;
          return r.open + '<' + e + r.close + ' …' + r.open + ' />' + r.close;
        },
        y = /^((HTML|SVG)\w*)?Element$/,
        v = e => {
          let { tagName: t } = e;
          return !!(
            ('string' == typeof t && t.includes('-')) ||
            ('function' == typeof e.hasAttribute && e.hasAttribute('is'))
          );
        },
        h = e => {
          let t = e.constructor.name,
            { nodeType: r } = e;
          return (
            (1 === r && (y.test(t) || v(e))) ||
            (3 === r && 'Text' === t) ||
            (8 === r && 'Comment' === t) ||
            (11 === r && 'DocumentFragment' === t)
          );
        };
      function g(e) {
        return 11 === e.nodeType;
      }
      let E = null,
        C = null,
        P = null;
      try {
        let t = e && e.require;
        (C = t.call(e, 'fs').readFileSync),
          (P = t.call(e, '@babel/code-frame').codeFrameColumns),
          (E = t.call(e, 'chalk'));
      } catch {}
      function q() {
        return (
          'undefined' != typeof jest &&
          null !== jest &&
          (!0 === setTimeout._isMockFunction ||
            Object.prototype.hasOwnProperty.call(setTimeout, 'clock'))
        );
      }
      function w() {
        if ('undefined' == typeof window) throw Error('Could not find default container');
        return window.document;
      }
      function O(e) {
        if (e.defaultView) return e.defaultView;
        if (e.ownerDocument && e.ownerDocument.defaultView) return e.ownerDocument.defaultView;
        if (e.window) return e.window;
        if (e.ownerDocument && null === e.ownerDocument.defaultView)
          throw Error('It looks like the window object is not available for the provided node.');
        if (e.then instanceof Function)
          throw Error(
            'It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?'
          );
        else if (Array.isArray(e))
          throw Error(
            'It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?'
          );
        else if ('function' == typeof e.debug && 'function' == typeof e.logTestingPlaygroundURL)
          throw Error(
            'It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?'
          );
        else throw Error('The given node is not an Element, the node type is: ' + typeof e + '.');
      }
      function x(e) {
        if (!e || 'function' != typeof e.querySelector || 'function' != typeof e.querySelectorAll) {
          var t;
          throw TypeError(
            'Expected container to be an Element, a Document or a DocumentFragment but got ' +
              ('object' == typeof (t = e) ? (null === t ? 'null' : t.constructor.name) : typeof t) +
              '.'
          );
        }
      }
      let _ = () => {
          let e;
          if (void 0 === u) return !1;
          try {
            var t;
            let r = null == (t = u.env) ? void 0 : t.COLORS;
            r && (e = JSON.parse(r));
          } catch {}
          return 'boolean' == typeof e ? e : void 0 !== u.versions && void 0 !== u.versions.node;
        },
        { DOMCollection: R } = n.plugins;
      function T(e) {
        return 8 !== e.nodeType && (1 !== e.nodeType || !e.matches(j.defaultIgnore));
      }
      function M(e, t, r) {
        if (
          (void 0 === r && (r = {}),
          e || (e = w().body),
          'number' != typeof t &&
            (t = (void 0 !== u && void 0 !== u.env && u.env.DEBUG_PRINT_LIMIT) || 7e3),
          0 === t)
        )
          return '';
        e.documentElement && (e = e.documentElement);
        let o = typeof e;
        if (('object' === o ? (o = e.constructor.name) : (e = {}), !('outerHTML' in e)))
          throw TypeError('Expected an element or document but got ' + o);
        let { filterNode: a = T, ...l } = r,
          i = n.format(e, {
            plugins: [
              {
                test: e => {
                  var t;
                  return (
                    ((null == e || null == (t = e.constructor) ? void 0 : t.name) || v(e)) && h(e)
                  );
                },
                serialize: (e, t, r, n, o, l) => {
                  if (3 === e.nodeType) return p(e.data, t);
                  if (8 === e.nodeType) return m(e.data, t);
                  let i = g(e) ? 'DocumentFragment' : e.tagName.toLowerCase();
                  return ++n > t.maxDepth
                    ? b(i, t)
                    : f(
                        i,
                        d(
                          g(e)
                            ? []
                            : Array.from(e.attributes)
                                .map(e => e.name)
                                .sort(),
                          g(e)
                            ? {}
                            : Array.from(e.attributes).reduce(
                                (e, t) => ((e[t.name] = t.value), e),
                                {}
                              ),
                          t,
                          r + t.indent,
                          n,
                          o,
                          l
                        ),
                        c(
                          Array.prototype.slice.call(e.childNodes || e.children).filter(a),
                          t,
                          r + t.indent,
                          n,
                          o,
                          l
                        ),
                        t,
                        r
                      );
                },
              },
              R,
            ],
            printFunctionName: !1,
            highlight: _(),
            ...l,
          });
        return void 0 !== t && e.outerHTML.length > t ? i.slice(0, t) + '...' : i;
      }
      let k = function () {
          let e =
            C && P
              ? (function (e) {
                  let t = e.indexOf('(') + 1,
                    r = e.indexOf(')'),
                    n = e.slice(t, r),
                    o = n.split(':'),
                    [a, l, i] = [o[0], parseInt(o[1], 10), parseInt(o[2], 10)],
                    u = '';
                  try {
                    u = C(a, 'utf-8');
                  } catch {
                    return '';
                  }
                  let s = P(
                    u,
                    { start: { line: l, column: i } },
                    { highlightCode: !0, linesBelow: 0 }
                  );
                  return E.dim(n) + '\n' + s + '\n';
                })(
                  Error()
                    .stack.split('\n')
                    .slice(1)
                    .find(e => !e.includes('node_modules/'))
                )
              : '';
          e ? console.log(M(...arguments) + '\n\n' + e) : console.log(M(...arguments));
        },
        j = {
          testIdAttribute: 'data-testid',
          asyncUtilTimeout: 1e3,
          asyncWrapper: e => e(),
          unstable_advanceTimersWrapper: e => e(),
          eventWrapper: e => e(),
          defaultHidden: !1,
          defaultIgnore: 'script, style',
          showOriginalStackTrace: !1,
          throwSuggestions: !1,
          getElementError(e, t) {
            let r = M(t),
              n = Error(
                [e, 'Ignored nodes: comments, ' + j.defaultIgnore + '\n' + r]
                  .filter(Boolean)
                  .join('\n\n')
              );
            return (n.name = 'TestingLibraryElementError'), n;
          },
          _disableExpensiveErrorDiagnostics: !1,
          computedStyleSupportsPseudoElements: !1,
        };
      function A(e) {
        'function' == typeof e && (e = e(j)), (j = { ...j, ...e });
      }
      function S() {
        return j;
      }
      let B = ['button', 'meter', 'output', 'progress', 'select', 'textarea', 'input'];
      function I(e) {
        let t;
        return 'label' === e.tagName.toLowerCase()
          ? (function e(t) {
              return B.includes(t.nodeName.toLowerCase())
                ? ''
                : 3 === t.nodeType
                  ? t.textContent
                  : Array.from(t.childNodes)
                      .map(t => e(t))
                      .join('');
            })(e)
          : e.value || e.textContent;
      }
      function L(e) {
        var t, r;
        if (void 0 !== e.labels) {
          return null != (t = e.labels) ? t : [];
        }
        return ((r = e),
        /BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(r.tagName) ||
          ('INPUT' === r.tagName && 'hidden' !== r.getAttribute('type')))
          ? Array.from(e.ownerDocument.querySelectorAll('label')).filter(t => t.control === e)
          : [];
      }
      function N(e, t, r) {
        let { selector: n = '*' } = void 0 === r ? {} : r,
          o = t.getAttribute('aria-labelledby'),
          a = o ? o.split(' ') : [];
        return a.length
          ? a.map(t => {
              let r = e.querySelector('[id="' + t + '"]');
              return r ? { content: I(r), formControl: null } : { content: '', formControl: null };
            })
          : Array.from(L(t)).map(e => ({
              content: I(e),
              formControl: Array.from(
                e.querySelectorAll('button, input, meter, output, progress, select, textarea')
              ).filter(e => e.matches(n))[0],
            }));
      }
      function F(e) {
        if (null == e)
          throw Error(
            'It looks like ' +
              e +
              ' was passed instead of a matcher. Did you do something like getByText(' +
              e +
              ')?'
          );
      }
      function D(e, t, r, n) {
        if ('string' != typeof e) return !1;
        F(r);
        let o = n(e);
        return 'string' == typeof r || 'number' == typeof r
          ? o.toLowerCase().includes(r.toString().toLowerCase())
          : 'function' == typeof r
            ? r(o, t)
            : K(r, o);
      }
      function U(e, t, r, n) {
        if ('string' != typeof e) return !1;
        F(r);
        let o = n(e);
        return r instanceof Function ? r(o, t) : r instanceof RegExp ? K(r, o) : o === String(r);
      }
      function H(e) {
        let { trim: t = !0, collapseWhitespace: r = !0 } = void 0 === e ? {} : e;
        return e => {
          let n = e;
          return (n = t ? n.trim() : n), (n = r ? n.replace(/\s+/g, ' ') : n);
        };
      }
      function V(e) {
        let { trim: t, collapseWhitespace: r, normalizer: n } = e;
        if (!n) return H({ trim: t, collapseWhitespace: r });
        if (void 0 !== t || void 0 !== r)
          throw Error(
            'trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer'
          );
        return n;
      }
      function K(e, t) {
        let r = e.test(t);
        return (
          e.global &&
            0 !== e.lastIndex &&
            (console.warn(
              'To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp.'
            ),
            (e.lastIndex = 0)),
          r
        );
      }
      function W(e) {
        return e.matches('input[type=submit], input[type=button], input[type=reset]')
          ? e.value
          : Array.from(e.childNodes)
              .filter(e => 3 === e.nodeType && !!e.textContent)
              .map(e => e.textContent)
              .join('');
      }
      let $ = (function (e) {
        let t = [];
        for (let [r, n] of e.entries())
          t = [
            ...t,
            {
              match: (function (e) {
                let { attributes: t = [] } = e,
                  r = t.findIndex(e => e.value && 'type' === e.name && 'text' === e.value);
                r >= 0 && (t = [...t.slice(0, r), ...t.slice(r + 1)]);
                let n = (function (e) {
                  let { name: t, attributes: r } = e;
                  return (
                    '' +
                    t +
                    r
                      .map(e => {
                        let { name: t, value: r, constraints: n = [] } = e,
                          o = -1 !== n.indexOf('undefined'),
                          a = -1 !== n.indexOf('set');
                        return void 0 !== r
                          ? '[' + t + '="' + r + '"]'
                          : o
                            ? ':not([' + t + '])'
                            : a
                              ? '[' + t + ']:not([' + t + '=""])'
                              : '[' + t + ']';
                      })
                      .join('')
                  );
                })({ ...e, attributes: t });
                return e => (!(r >= 0) || 'text' === e.type) && e.matches(n);
              })(r),
              roles: Array.from(n),
              specificity: (function (e) {
                let { attributes: t = [] } = e;
                return t.length;
              })(r),
            },
          ];
        return t.sort(function (e, t) {
          let { specificity: r } = e,
            { specificity: n } = t;
          return n - r;
        });
      })(a._s);
      function z(e) {
        return (
          !0 === e.hidden ||
          'true' === e.getAttribute('aria-hidden') ||
          'none' === e.ownerDocument.defaultView.getComputedStyle(e).display
        );
      }
      function G(e, t) {
        void 0 === t && (t = {});
        let { isSubtreeInaccessible: r = z } = t;
        if ('hidden' === e.ownerDocument.defaultView.getComputedStyle(e).visibility) return !0;
        let n = e;
        for (; n; ) {
          if (r(n)) return !0;
          n = n.parentElement;
        }
        return !1;
      }
      function Y(e) {
        for (let { match: t, roles: r } of $) if (t(e)) return [...r];
        return [];
      }
      function Q(e, t) {
        let { hidden: r = !1 } = void 0 === t ? {} : t;
        return (function e(t) {
          return [t, ...Array.from(t.children).reduce((t, r) => [...t, ...e(r)], [])];
        })(e)
          .filter(e => !1 !== r || !1 === G(e))
          .reduce((e, t) => {
            let r = [];
            return (
              t.hasAttribute('role') ? t.getAttribute('role').split(' ').slice(0, 1) : Y(t)
            ).reduce(
              (e, r) => (Array.isArray(e[r]) ? { ...e, [r]: [...e[r], t] } : { ...e, [r]: [t] }),
              e
            );
          }, {});
      }
      function X(e, t) {
        let { hidden: r, includeDescription: n } = t;
        return Object.entries(Q(e, { hidden: r }))
          .filter(e => {
            let [t] = e;
            return 'generic' !== t;
          })
          .map(e => {
            let [t, r] = e,
              a = '-'.repeat(50);
            return (
              t +
              ':\n\n' +
              r
                .map(e => {
                  let t =
                      'Name "' +
                      (0, o.D0)(e, {
                        computedStyleSupportsPseudoElements: j.computedStyleSupportsPseudoElements,
                      }) +
                      '":\n',
                    r = M(e.cloneNode(!1));
                  return n
                    ? '' +
                        t +
                        ('Description "' +
                          (0, o._1)(e, {
                            computedStyleSupportsPseudoElements:
                              j.computedStyleSupportsPseudoElements,
                          })) +
                        '":\n' +
                        r
                    : '' + t + r;
                })
                .join('\n\n') +
              '\n\n' +
              a
            );
          })
          .join('\n');
      }
      let J = function (e, t) {
        let { hidden: r = !1 } = void 0 === t ? {} : t;
        return console.log(X(e, { hidden: r }));
      };
      function Z(e, t) {
        let r = e.getAttribute(t);
        return 'true' === r || ('false' !== r && void 0);
      }
      let ee = H();
      function et(e) {
        return RegExp(e.toLowerCase().replace(/[.*+\-?^${}()|[\]\\]/g, '\\$&'), 'i');
      }
      function er(e, t, r, n) {
        let { variant: o, name: a } = n,
          l = '',
          i = {},
          u = [['Role', 'TestId'].includes(e) ? r : et(r)];
        a && (i.name = et(a)),
          'Role' === e &&
            G(t) &&
            ((i.hidden = !0),
            (l =
              'Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    ')),
          Object.keys(i).length > 0 && u.push(i);
        let s = o + 'By' + e;
        return {
          queryName: e,
          queryMethod: s,
          queryArgs: u,
          variant: o,
          warning: l,
          toString() {
            l && console.warn(l);
            let [e, t] = u;
            return (
              s +
              '(' +
              (e = 'string' == typeof e ? "'" + e + "'" : e) +
              (t = t
                ? ', { ' +
                  Object.entries(t)
                    .map(e => {
                      let [t, r] = e;
                      return t + ': ' + r;
                    })
                    .join(', ') +
                  ' }'
                : '') +
              ')'
            );
          },
        };
      }
      function en(e, t, r) {
        return r && (!t || t.toLowerCase() === e.toLowerCase());
      }
      function eo(e, t, r) {
        var n, a;
        if ((void 0 === t && (t = 'get'), e.matches(j.defaultIgnore))) return;
        let l = null != (n = e.getAttribute('role')) ? n : null == (a = Y(e)) ? void 0 : a[0];
        if ('generic' !== l && en('Role', r, l))
          return er('Role', e, l, {
            variant: t,
            name: (0, o.D0)(e, {
              computedStyleSupportsPseudoElements: j.computedStyleSupportsPseudoElements,
            }),
          });
        let i = N(document, e)
          .map(e => e.content)
          .join(' ');
        if (en('LabelText', r, i)) return er('LabelText', e, i, { variant: t });
        let u = e.getAttribute('placeholder');
        if (en('PlaceholderText', r, u)) return er('PlaceholderText', e, u, { variant: t });
        let s = ee(W(e));
        if (en('Text', r, s)) return er('Text', e, s, { variant: t });
        if (en('DisplayValue', r, e.value))
          return er('DisplayValue', e, ee(e.value), { variant: t });
        let d = e.getAttribute('alt');
        if (en('AltText', r, d)) return er('AltText', e, d, { variant: t });
        let c = e.getAttribute('title');
        if (en('Title', r, c)) return er('Title', e, c, { variant: t });
        let p = e.getAttribute(j.testIdAttribute);
        if (en('TestId', r, p)) return er('TestId', e, p, { variant: t });
      }
      function ea(e, t) {
        e.stack = t.stack.replace(t.message, e.message);
      }
      function el(e, t) {
        let r = Error('STACK_TRACE_MESSAGE');
        return j.asyncWrapper(() =>
          (function (e, t) {
            let {
              container: r = w(),
              timeout: n = j.asyncUtilTimeout,
              showOriginalStackTrace: o = j.showOriginalStackTrace,
              stackTraceError: a,
              interval: l = 50,
              onTimeout: i = e => (
                Object.defineProperty(e, 'message', {
                  value: j.getElementError(e.message, r).message,
                }),
                e
              ),
              mutationObserverOptions: u = {
                subtree: !0,
                childList: !0,
                attributes: !0,
                characterData: !0,
              },
            } = t;
            if ('function' != typeof e)
              throw TypeError('Received `callback` arg must be a function');
            return new Promise(async (t, s) => {
              let d,
                c,
                p,
                m = !1,
                f = 'idle',
                b = setTimeout(function () {
                  let e;
                  d
                    ? ((e = d), o || 'TestingLibraryElementError' !== e.name || ea(e, a))
                    : ((e = Error('Timed out in waitFor.')), o || ea(e, a)),
                    v(i(e), null);
                }, n),
                y = q();
              if (y) {
                let { unstable_advanceTimersWrapper: e } = j;
                for (g(); !m; ) {
                  if (!q()) {
                    let e = Error(
                      "Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830"
                    );
                    o || ea(e, a), s(e);
                    return;
                  }
                  if (
                    (await e(async () => {
                      jest.advanceTimersByTime(l);
                    }),
                    m)
                  )
                    break;
                  g();
                }
              } else {
                try {
                  x(r);
                } catch (e) {
                  s(e);
                  return;
                }
                c = setInterval(h, l);
                let { MutationObserver: e } = O(r);
                (p = new e(h)).observe(r, u), g();
              }
              function v(e, r) {
                (m = !0), clearTimeout(b), y || (clearInterval(c), p.disconnect()), e ? s(e) : t(r);
              }
              function h() {
                if (!q()) return g();
                {
                  let e = Error(
                    "Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830"
                  );
                  return o || ea(e, a), s(e);
                }
              }
              function g() {
                if ('pending' !== f)
                  try {
                    let t = (function (e) {
                      try {
                        return (j._disableExpensiveErrorDiagnostics = !0), e();
                      } finally {
                        j._disableExpensiveErrorDiagnostics = !1;
                      }
                    })(e);
                    'function' == typeof (null == t ? void 0 : t.then)
                      ? ((f = 'pending'),
                        t.then(
                          e => {
                            (f = 'resolved'), v(null, e);
                          },
                          e => {
                            (f = 'rejected'), (d = e);
                          }
                        ))
                      : v(null, t);
                  } catch (e) {
                    d = e;
                  }
              }
            });
          })(e, { stackTraceError: r, ...t })
        );
      }
      function ei(e, t) {
        return j.getElementError(e, t);
      }
      function eu(e, t) {
        return ei(
          e +
            '\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).',
          t
        );
      }
      function es(e, t, r, n) {
        let {
            exact: o = !0,
            collapseWhitespace: a,
            trim: l,
            normalizer: i,
          } = void 0 === n ? {} : n,
          u = o ? U : D,
          s = V({ collapseWhitespace: a, trim: l, normalizer: i });
        return Array.from(t.querySelectorAll('[' + e + ']')).filter(t =>
          u(t.getAttribute(e), t, r, s)
        );
      }
      function ed(e, t, r, n) {
        let o = es(e, t, r, n);
        if (o.length > 1) throw eu('Found multiple elements by [' + e + '=' + r + ']', t);
        return o[0] || null;
      }
      function ec(e, t) {
        return function (r) {
          for (var n = arguments.length, o = Array(n > 1 ? n - 1 : 0), a = 1; a < n; a++)
            o[a - 1] = arguments[a];
          let l = e(r, ...o);
          if (l.length > 1) {
            let e = l.map(e => ei(null, e).message).join('\n\n');
            throw eu(t(r, ...o) + '\n\nHere are the matching elements:\n\n' + e, r);
          }
          return l[0] || null;
        };
      }
      function ep(e, t) {
        return j.getElementError(
          'A better query is available, try this:\n' + e.toString() + '\n',
          t
        );
      }
      function em(e, t) {
        return function (r) {
          for (var n = arguments.length, o = Array(n > 1 ? n - 1 : 0), a = 1; a < n; a++)
            o[a - 1] = arguments[a];
          let l = e(r, ...o);
          if (!l.length) throw j.getElementError(t(r, ...o), r);
          return l;
        };
      }
      function ef(e) {
        return (t, r, n, o) => el(() => e(t, r, n), { container: t, ...o });
      }
      let eb = (e, t, r) =>
          function (n) {
            for (var o = arguments.length, a = Array(o > 1 ? o - 1 : 0), l = 1; l < o; l++)
              a[l - 1] = arguments[l];
            let i = e(n, ...a),
              [{ suggest: u = j.throwSuggestions } = {}] = a.slice(-1);
            if (i && u) {
              let e = eo(i, r);
              if (e && !t.endsWith(e.queryName)) throw ep(e.toString(), n);
            }
            return i;
          },
        ey = (e, t, r) =>
          function (n) {
            for (var o = arguments.length, a = Array(o > 1 ? o - 1 : 0), l = 1; l < o; l++)
              a[l - 1] = arguments[l];
            let i = e(n, ...a),
              [{ suggest: u = j.throwSuggestions } = {}] = a.slice(-1);
            if (i.length && u) {
              let e = [
                ...new Set(
                  i.map(e => {
                    var t;
                    return null == (t = eo(e, r)) ? void 0 : t.toString();
                  })
                ),
              ];
              if (1 === e.length && !t.endsWith(eo(i[0], r).queryName)) throw ep(e[0], n);
            }
            return i;
          };
      function ev(e, t, r) {
        let n = eb(ec(e, t), e.name, 'query'),
          o = em(e, r),
          a = ec(o, t),
          l = eb(a, e.name, 'get'),
          i = ey(o, e.name.replace('query', 'get'), 'getAll');
        return [n, i, l, ef(ey(o, e.name, 'findAll')), ef(eb(a, e.name, 'find'))];
      }
      var eh = Object.freeze({
        __proto__: null,
        getElementError: ei,
        wrapAllByQueryWithSuggestion: ey,
        wrapSingleQueryWithSuggestion: eb,
        getMultipleElementsFoundError: eu,
        queryAllByAttribute: es,
        queryByAttribute: ed,
        makeSingleQuery: ec,
        makeGetAllQuery: em,
        makeFindQuery: ef,
        buildQueries: ev,
      });
      let eg = function (e, t, r) {
          let {
              exact: n = !0,
              trim: o,
              collapseWhitespace: a,
              normalizer: l,
            } = void 0 === r ? {} : r,
            i = n ? U : D,
            u = V({ collapseWhitespace: a, trim: o, normalizer: l });
          return Array.from(e.querySelectorAll('label,input'))
            .map(e => ({ node: e, textToMatch: I(e) }))
            .filter(e => {
              let { textToMatch: t } = e;
              return null !== t;
            })
            .filter(e => {
              let { node: r, textToMatch: n } = e;
              return i(n, r, t, u);
            })
            .map(e => {
              let { node: t } = e;
              return t;
            });
        },
        eE = function (e, t, r) {
          let {
            selector: n = '*',
            exact: o = !0,
            collapseWhitespace: a,
            trim: l,
            normalizer: i,
          } = void 0 === r ? {} : r;
          x(e);
          let u = o ? U : D,
            s = V({ collapseWhitespace: a, trim: l, normalizer: i });
          return Array.from(
            new Set(
              Array.from(e.querySelectorAll('*'))
                .filter(e => L(e).length || e.hasAttribute('aria-labelledby'))
                .reduce((r, o) => {
                  let a = N(e, o, { selector: n });
                  a.filter(e => !!e.formControl).forEach(e => {
                    u(e.content, e.formControl, t, s) && e.formControl && r.push(e.formControl);
                  });
                  let l = a.filter(e => !!e.content).map(e => e.content);
                  return (
                    u(l.join(' '), o, t, s) && r.push(o),
                    l.length > 1 &&
                      l.forEach((e, n) => {
                        u(e, o, t, s) && r.push(o);
                        let a = [...l];
                        a.splice(n, 1), a.length > 1 && u(a.join(' '), o, t, s) && r.push(o);
                      }),
                    r
                  );
                }, [])
                .concat(es('aria-label', e, t, { exact: o, normalizer: s }))
            )
          ).filter(e => e.matches(n));
        },
        eC = function (e, t) {
          for (var r = arguments.length, n = Array(r > 2 ? r - 2 : 0), o = 2; o < r; o++)
            n[o - 2] = arguments[o];
          let a = eE(e, t, ...n);
          if (!a.length) {
            let r = eg(e, t, ...n);
            if (r.length) {
              let n = r
                .map(t =>
                  (function (e, t) {
                    let r = t.getAttribute('for');
                    if (!r) return null;
                    let n = e.querySelector('[id="' + r + '"]');
                    return n ? n.tagName.toLowerCase() : null;
                  })(e, t)
                )
                .filter(e => !!e);
              if (n.length)
                throw j.getElementError(
                  n
                    .map(
                      e =>
                        'Found a label with the text of: ' +
                        t +
                        ', however the element associated with this label (<' +
                        e +
                        ' />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <' +
                        e +
                        ' />, you can use aria-label or aria-labelledby instead.'
                    )
                    .join('\n\n'),
                  e
                );
              throw j.getElementError(
                'Found a label with the text of: ' +
                  t +
                  ', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',
                e
              );
            }
            throw j.getElementError('Unable to find a label with the text of: ' + t, e);
          }
          return a;
        },
        eP = (e, t) => 'Found multiple elements with the text of: ' + t,
        eq = eb(ec(eE, eP), eE.name, 'query'),
        ew = ec(eC, eP),
        eO = ef(ey(eC, eC.name, 'findAll')),
        ex = ef(eb(ew, eC.name, 'find')),
        e_ = ey(eC, eC.name, 'getAll'),
        eR = eb(ew, eC.name, 'get'),
        eT = ey(eE, eE.name, 'queryAll'),
        eM = function () {
          for (var e = arguments.length, t = Array(e), r = 0; r < e; r++) t[r] = arguments[r];
          return x(t[0]), es('placeholder', ...t);
        },
        ek = ey(eM, eM.name, 'queryAll'),
        [ej, eA, eS, eB, eI] = ev(
          eM,
          (e, t) => 'Found multiple elements with the placeholder text of: ' + t,
          (e, t) => 'Unable to find an element with the placeholder text of: ' + t
        ),
        eL = function (e, t, r) {
          let {
            selector: n = '*',
            exact: o = !0,
            collapseWhitespace: a,
            trim: l,
            ignore: i = j.defaultIgnore,
            normalizer: u,
          } = void 0 === r ? {} : r;
          x(e);
          let s = o ? U : D,
            d = V({ collapseWhitespace: a, trim: l, normalizer: u }),
            c = [];
          return (
            'function' == typeof e.matches && e.matches(n) && (c = [e]),
            [...c, ...Array.from(e.querySelectorAll(n))]
              .filter(e => !i || !e.matches(i))
              .filter(e => s(W(e), e, t, d))
          );
        },
        eN = ey(eL, eL.name, 'queryAll'),
        [eF, eD, eU, eH, eV] = ev(
          eL,
          (e, t) => 'Found multiple elements with the text: ' + t,
          function (e, t, r) {
            void 0 === r && (r = {});
            let { collapseWhitespace: n, trim: o, normalizer: a, selector: l } = r,
              i = V({ collapseWhitespace: n, trim: o, normalizer: a })(t.toString());
            return (
              'Unable to find an element with the text: ' +
              (i !== t.toString() ? i + " (normalized from '" + t + "')" : t) +
              ((null != l ? l : '*') !== '*' ? ", which matches selector '" + l + "'" : '') +
              '. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.'
            );
          }
        ),
        eK = function (e, t, r) {
          let {
            exact: n = !0,
            collapseWhitespace: o,
            trim: a,
            normalizer: l,
          } = void 0 === r ? {} : r;
          x(e);
          let i = n ? U : D,
            u = V({ collapseWhitespace: o, trim: a, normalizer: l });
          return Array.from(e.querySelectorAll('input,textarea,select')).filter(e =>
            'SELECT' === e.tagName
              ? Array.from(e.options)
                  .filter(e => e.selected)
                  .some(e => i(W(e), e, t, u))
              : i(e.value, e, t, u)
          );
        },
        eW = ey(eK, eK.name, 'queryAll'),
        [e$, ez, eG, eY, eQ] = ev(
          eK,
          (e, t) => 'Found multiple elements with the display value: ' + t + '.',
          (e, t) => 'Unable to find an element with the display value: ' + t + '.'
        ),
        eX = /^(img|input|area|.+-.+)$/i,
        eJ = function (e, t, r) {
          return void 0 === r && (r = {}), x(e), es('alt', e, t, r).filter(e => eX.test(e.tagName));
        },
        eZ = ey(eJ, eJ.name, 'queryAll'),
        [e0, e1, e3, e2, e5] = ev(
          eJ,
          (e, t) => 'Found multiple elements with the alt text: ' + t,
          (e, t) => 'Unable to find an element with the alt text: ' + t
        ),
        e8 = e => {
          var t;
          return (
            'title' === e.tagName.toLowerCase() &&
            (null == (t = e.parentElement) ? void 0 : t.tagName.toLowerCase()) === 'svg'
          );
        },
        e4 = function (e, t, r) {
          let {
            exact: n = !0,
            collapseWhitespace: o,
            trim: a,
            normalizer: l,
          } = void 0 === r ? {} : r;
          x(e);
          let i = n ? U : D,
            u = V({ collapseWhitespace: o, trim: a, normalizer: l });
          return Array.from(e.querySelectorAll('[title], svg > title')).filter(
            e => i(e.getAttribute('title'), e, t, u) || (e8(e) && i(W(e), e, t, u))
          );
        },
        e6 = ey(e4, e4.name, 'queryAll'),
        [e7, e9, te, tt, tr] = ev(
          e4,
          (e, t) => 'Found multiple elements with the title: ' + t + '.',
          (e, t) => 'Unable to find an element with the title: ' + t + '.'
        ),
        tn = function (e, t, r) {
          var n, l, i, u, s, d, c, p, m, f, b, y;
          let {
            hidden: v = j.defaultHidden,
            name: h,
            description: g,
            queryFallbacks: E = !1,
            selected: C,
            busy: P,
            checked: q,
            pressed: w,
            current: O,
            level: _,
            expanded: R,
            value: { now: T, min: M, max: k, text: A } = {},
          } = void 0 === r ? {} : r;
          if (
            (x(e),
            void 0 !== C &&
              (null == (n = a.Ot.get(t)) ? void 0 : n.props['aria-selected']) === void 0)
          )
            throw Error('"aria-selected" is not supported on role "' + t + '".');
          if (
            void 0 !== P &&
            (null == (l = a.Ot.get(t)) ? void 0 : l.props['aria-busy']) === void 0
          )
            throw Error('"aria-busy" is not supported on role "' + t + '".');
          if (
            void 0 !== q &&
            (null == (i = a.Ot.get(t)) ? void 0 : i.props['aria-checked']) === void 0
          )
            throw Error('"aria-checked" is not supported on role "' + t + '".');
          if (
            void 0 !== w &&
            (null == (u = a.Ot.get(t)) ? void 0 : u.props['aria-pressed']) === void 0
          )
            throw Error('"aria-pressed" is not supported on role "' + t + '".');
          if (
            void 0 !== O &&
            (null == (s = a.Ot.get(t)) ? void 0 : s.props['aria-current']) === void 0
          )
            throw Error('"aria-current" is not supported on role "' + t + '".');
          if (void 0 !== _ && 'heading' !== t)
            throw Error('Role "' + t + '" cannot have "level" property.');
          if (
            void 0 !== T &&
            (null == (d = a.Ot.get(t)) ? void 0 : d.props['aria-valuenow']) === void 0
          )
            throw Error('"aria-valuenow" is not supported on role "' + t + '".');
          if (
            void 0 !== k &&
            (null == (c = a.Ot.get(t)) ? void 0 : c.props['aria-valuemax']) === void 0
          )
            throw Error('"aria-valuemax" is not supported on role "' + t + '".');
          if (
            void 0 !== M &&
            (null == (p = a.Ot.get(t)) ? void 0 : p.props['aria-valuemin']) === void 0
          )
            throw Error('"aria-valuemin" is not supported on role "' + t + '".');
          if (
            void 0 !== A &&
            (null == (m = a.Ot.get(t)) ? void 0 : m.props['aria-valuetext']) === void 0
          )
            throw Error('"aria-valuetext" is not supported on role "' + t + '".');
          if (
            void 0 !== R &&
            (null == (f = a.Ot.get(t)) ? void 0 : f.props['aria-expanded']) === void 0
          )
            throw Error('"aria-expanded" is not supported on role "' + t + '".');
          let S = new WeakMap();
          function B(e) {
            return S.has(e) || S.set(e, z(e)), S.get(e);
          }
          return Array.from(
            e.querySelectorAll(
              ['*[role~="' + (b = t) + '"]']
                .concat(
                  Array.from(
                    new Set(
                      Array.from(null != (y = a.wZ.get(b)) ? y : new Set()).map(e => {
                        let { name: t } = e;
                        return t;
                      })
                    )
                  )
                )
                .join(',')
            )
          )
            .filter(e => {
              if (e.hasAttribute('role')) {
                let r = e.getAttribute('role');
                if (E)
                  return r
                    .split(' ')
                    .filter(Boolean)
                    .some(e => e === t);
                let [n] = r.split(' ');
                return n === t;
              }
              return Y(e).some(e => e === t);
            })
            .filter(e => {
              var t, r, n;
              if (void 0 !== C)
                return C === ('OPTION' === e.tagName ? e.selected : Z(e, 'aria-selected'));
              if (void 0 !== P) return P === ('true' === e.getAttribute('aria-busy'));
              if (void 0 !== q)
                return (
                  q ===
                  (function (e) {
                    if (!('indeterminate' in e) || !e.indeterminate)
                      return 'checked' in e ? e.checked : Z(e, 'aria-checked');
                  })(e)
                );
              if (void 0 !== w) return w === Z(e, 'aria-pressed');
              if (void 0 !== O)
                return (
                  O ===
                  (null !=
                    (t = null != (r = Z(e, 'aria-current')) ? r : e.getAttribute('aria-current')) &&
                    t)
                );
              if (void 0 !== R) return R === Z(e, 'aria-expanded');
              if (void 0 !== _)
                return (
                  _ ===
                  ((e.getAttribute('aria-level') && Number(e.getAttribute('aria-level'))) ||
                    { H1: 1, H2: 2, H3: 3, H4: 4, H5: 5, H6: 6 }[e.tagName])
                );
              if (void 0 !== T || void 0 !== k || void 0 !== M || void 0 !== A) {
                let t = !0;
                return (
                  void 0 !== T &&
                    t &&
                    (t =
                      T ===
                      (function (e) {
                        let t = e.getAttribute('aria-valuenow');
                        return null === t ? void 0 : +t;
                      })(e)),
                  void 0 !== k &&
                    t &&
                    (t =
                      k ===
                      (function (e) {
                        let t = e.getAttribute('aria-valuemax');
                        return null === t ? void 0 : +t;
                      })(e)),
                  void 0 !== M &&
                    t &&
                    (t =
                      M ===
                      (function (e) {
                        let t = e.getAttribute('aria-valuemin');
                        return null === t ? void 0 : +t;
                      })(e)),
                  void 0 !== A &&
                    t &&
                    (t = U(
                      null !=
                        (n = (function (e) {
                          let t = e.getAttribute('aria-valuetext');
                          return null === t ? void 0 : t;
                        })(e))
                        ? n
                        : null,
                      e,
                      A,
                      e => e
                    )),
                  t
                );
              }
              return !0;
            })
            .filter(
              e =>
                void 0 === h ||
                U(
                  (0, o.D0)(e, {
                    computedStyleSupportsPseudoElements: j.computedStyleSupportsPseudoElements,
                  }),
                  e,
                  h,
                  e => e
                )
            )
            .filter(
              e =>
                void 0 === g ||
                U(
                  (0, o._1)(e, {
                    computedStyleSupportsPseudoElements: j.computedStyleSupportsPseudoElements,
                  }),
                  e,
                  g,
                  e => e
                )
            )
            .filter(e => !1 !== v || !1 === G(e, { isSubtreeInaccessible: B }));
        },
        to = e => {
          let t = '';
          return void 0 === e
            ? ''
            : 'string' == typeof e
              ? ' and name "' + e + '"'
              : ' and name `' + e + '`';
        },
        ta = ey(tn, tn.name, 'queryAll'),
        [tl, ti, tu, ts, td] = ev(
          tn,
          function (e, t, r) {
            let { name: n } = void 0 === r ? {} : r;
            return 'Found multiple elements with the role "' + t + '"' + to(n);
          },
          function (e, t, r) {
            let n,
              { hidden: o = j.defaultHidden, name: a, description: l } = void 0 === r ? {} : r;
            if (j._disableExpensiveErrorDiagnostics)
              return 'Unable to find role="' + t + '"' + to(a);
            let i = '';
            Array.from(e.children).forEach(e => {
              i += X(e, { hidden: o, includeDescription: void 0 !== l });
            }),
              (n =
                0 === i.length
                  ? !1 === o
                    ? 'There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole'
                    : 'There are no available roles.'
                  : (
                      '\nHere are the ' +
                      (!1 === o ? 'accessible' : 'available') +
                      ' roles:\n\n  ' +
                      i.replace(/\n/g, '\n  ').replace(/\n\s\s\n/g, '\n\n') +
                      '\n'
                    ).trim());
            let u = '',
              s = '';
            return (
              '\nUnable to find an ' +
              (!1 === o ? 'accessible ' : '') +
              'element with the role "' +
              t +
              '"' +
              (void 0 === a
                ? ''
                : 'string' == typeof a
                  ? ' and name "' + a + '"'
                  : ' and name `' + a + '`') +
              (void 0 === l
                ? ''
                : 'string' == typeof l
                  ? ' and description "' + l + '"'
                  : ' and description `' + l + '`') +
              '\n\n' +
              n
            ).trim();
          }
        ),
        tc = () => j.testIdAttribute,
        tp = function () {
          for (var e = arguments.length, t = Array(e), r = 0; r < e; r++) t[r] = arguments[r];
          return x(t[0]), es(tc(), ...t);
        },
        tm = ey(tp, tp.name, 'queryAll'),
        [tf, tb, ty, tv, th] = ev(
          tp,
          (e, t) => 'Found multiple elements by: [' + tc() + '="' + t + '"]',
          (e, t) => 'Unable to find an element by: [' + tc() + '="' + t + '"]'
        );
      var tg = Object.freeze({
        __proto__: null,
        queryAllByLabelText: eT,
        queryByLabelText: eq,
        getAllByLabelText: e_,
        getByLabelText: eR,
        findAllByLabelText: eO,
        findByLabelText: ex,
        queryByPlaceholderText: ej,
        queryAllByPlaceholderText: ek,
        getByPlaceholderText: eS,
        getAllByPlaceholderText: eA,
        findAllByPlaceholderText: eB,
        findByPlaceholderText: eI,
        queryByText: eF,
        queryAllByText: eN,
        getByText: eU,
        getAllByText: eD,
        findAllByText: eH,
        findByText: eV,
        queryByDisplayValue: e$,
        queryAllByDisplayValue: eW,
        getByDisplayValue: eG,
        getAllByDisplayValue: ez,
        findAllByDisplayValue: eY,
        findByDisplayValue: eQ,
        queryByAltText: e0,
        queryAllByAltText: eZ,
        getByAltText: e3,
        getAllByAltText: e1,
        findAllByAltText: e2,
        findByAltText: e5,
        queryByTitle: e7,
        queryAllByTitle: e6,
        getByTitle: te,
        getAllByTitle: e9,
        findAllByTitle: tt,
        findByTitle: tr,
        queryByRole: tl,
        queryAllByRole: ta,
        getAllByRole: ti,
        getByRole: tu,
        findAllByRole: ts,
        findByRole: td,
        queryByTestId: tf,
        queryAllByTestId: tm,
        getByTestId: ty,
        getAllByTestId: tb,
        findAllByTestId: tv,
        findByTestId: th,
      });
      function tE(e, t, r) {
        return (
          void 0 === t && (t = tg),
          void 0 === r && (r = {}),
          Object.keys(t).reduce((r, n) => {
            let o = t[n];
            return (r[n] = o.bind(null, e)), r;
          }, r)
        );
      }
      let tC = e => !e || (Array.isArray(e) && !e.length);
      function tP(e) {
        if (tC(e))
          throw Error(
            'The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.'
          );
      }
      async function tq(e, t) {
        let r = Error('Timed out in waitForElementToBeRemoved.');
        if ('function' != typeof e) {
          tP(e);
          let t = (Array.isArray(e) ? e : [e]).map(e => {
            let t = e.parentElement;
            if (null === t) return () => null;
            for (; t.parentElement; ) t = t.parentElement;
            return () => (t.contains(e) ? e : null);
          });
          e = () => t.map(e => e()).filter(Boolean);
        }
        return (
          tP(e()),
          el(() => {
            let t;
            try {
              t = e();
            } catch (e) {
              if ('TestingLibraryElementError' === e.name) return;
              throw e;
            }
            if (!tC(t)) throw r;
          }, t)
        );
      }
      let tw = {
          copy: {
            EventType: 'ClipboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          cut: {
            EventType: 'ClipboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          paste: {
            EventType: 'ClipboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          compositionEnd: {
            EventType: 'CompositionEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          compositionStart: {
            EventType: 'CompositionEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          compositionUpdate: {
            EventType: 'CompositionEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          keyDown: {
            EventType: 'KeyboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, charCode: 0, composed: !0 },
          },
          keyPress: {
            EventType: 'KeyboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, charCode: 0, composed: !0 },
          },
          keyUp: {
            EventType: 'KeyboardEvent',
            defaultInit: { bubbles: !0, cancelable: !0, charCode: 0, composed: !0 },
          },
          focus: {
            EventType: 'FocusEvent',
            defaultInit: { bubbles: !1, cancelable: !1, composed: !0 },
          },
          blur: {
            EventType: 'FocusEvent',
            defaultInit: { bubbles: !1, cancelable: !1, composed: !0 },
          },
          focusIn: {
            EventType: 'FocusEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          focusOut: {
            EventType: 'FocusEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          change: { EventType: 'Event', defaultInit: { bubbles: !0, cancelable: !1 } },
          input: {
            EventType: 'InputEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          invalid: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !0 } },
          submit: { EventType: 'Event', defaultInit: { bubbles: !0, cancelable: !0 } },
          reset: { EventType: 'Event', defaultInit: { bubbles: !0, cancelable: !0 } },
          click: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, button: 0, composed: !0 },
          },
          contextMenu: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          dblClick: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          drag: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          dragEnd: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          dragEnter: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          dragExit: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          dragLeave: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          dragOver: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          dragStart: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          drop: {
            EventType: 'DragEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseDown: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseEnter: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !1, cancelable: !1, composed: !0 },
          },
          mouseLeave: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !1, cancelable: !1, composed: !0 },
          },
          mouseMove: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseOut: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseOver: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          mouseUp: {
            EventType: 'MouseEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          select: { EventType: 'Event', defaultInit: { bubbles: !0, cancelable: !1 } },
          touchCancel: {
            EventType: 'TouchEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          touchEnd: {
            EventType: 'TouchEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          touchMove: {
            EventType: 'TouchEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          touchStart: {
            EventType: 'TouchEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          resize: { EventType: 'UIEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          scroll: { EventType: 'UIEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          wheel: {
            EventType: 'WheelEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          abort: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          canPlay: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          canPlayThrough: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          durationChange: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          emptied: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          encrypted: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          ended: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          loadedData: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          loadedMetadata: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          loadStart: { EventType: 'ProgressEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          pause: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          play: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          playing: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          progress: { EventType: 'ProgressEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          rateChange: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          seeked: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          seeking: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          stalled: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          suspend: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          timeUpdate: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          volumeChange: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          waiting: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          load: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          error: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          animationStart: {
            EventType: 'AnimationEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          animationEnd: {
            EventType: 'AnimationEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          animationIteration: {
            EventType: 'AnimationEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          transitionCancel: {
            EventType: 'TransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          transitionEnd: {
            EventType: 'TransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !0 },
          },
          transitionRun: {
            EventType: 'TransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          transitionStart: {
            EventType: 'TransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !1 },
          },
          pointerOver: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerEnter: { EventType: 'PointerEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          pointerDown: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerMove: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerUp: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerCancel: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          pointerOut: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !0, composed: !0 },
          },
          pointerLeave: { EventType: 'PointerEvent', defaultInit: { bubbles: !1, cancelable: !1 } },
          gotPointerCapture: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          lostPointerCapture: {
            EventType: 'PointerEvent',
            defaultInit: { bubbles: !0, cancelable: !1, composed: !0 },
          },
          popState: { EventType: 'PopStateEvent', defaultInit: { bubbles: !0, cancelable: !1 } },
          offline: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          online: { EventType: 'Event', defaultInit: { bubbles: !1, cancelable: !1 } },
          pageHide: {
            EventType: 'PageTransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !0 },
          },
          pageShow: {
            EventType: 'PageTransitionEvent',
            defaultInit: { bubbles: !0, cancelable: !0 },
          },
        },
        tO = { doubleClick: 'dblClick' };
      function tx(e, t) {
        return j.eventWrapper(() => {
          if (!t) throw Error('Unable to fire an event - please provide an event object.');
          if (!e)
            throw Error('Unable to fire a "' + t.type + '" event - please provide a DOM element.');
          return e.dispatchEvent(t);
        });
      }
      function t_(e, t, r, n) {
        let o,
          { EventType: a = 'Event', defaultInit: l = {} } = void 0 === n ? {} : n;
        if (!t) throw Error('Unable to fire a "' + e + '" event - please provide a DOM element.');
        let i = { ...l, ...r },
          { target: { value: u, files: s, ...d } = {} } = i;
        void 0 !== u &&
          (function (e, t) {
            let { set: r } = Object.getOwnPropertyDescriptor(e, 'value') || {},
              { set: n } = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(e), 'value') || {};
            if (n && r !== n) n.call(e, t);
            else if (r) r.call(e, t);
            else throw Error('The given element does not have a value setter');
          })(t, u),
          void 0 !== s &&
            Object.defineProperty(t, 'files', {
              configurable: !0,
              enumerable: !0,
              writable: !0,
              value: s,
            }),
          Object.assign(t, d);
        let c = O(t),
          p = c[a] || c.Event;
        if ('function' == typeof p) o = new p(e, i);
        else {
          o = c.document.createEvent(a);
          let { bubbles: t, cancelable: r, detail: n, ...l } = i;
          o.initEvent(e, t, r, n),
            Object.keys(l).forEach(e => {
              o[e] = l[e];
            });
        }
        return (
          ['dataTransfer', 'clipboardData'].forEach(e => {
            let t = i[e];
            'object' == typeof t &&
              ('function' == typeof c.DataTransfer
                ? Object.defineProperty(o, e, {
                    value: Object.getOwnPropertyNames(t).reduce(
                      (e, r) => (Object.defineProperty(e, r, { value: t[r] }), e),
                      new c.DataTransfer()
                    ),
                  })
                : Object.defineProperty(o, e, { value: t }));
          }),
          o
        );
      }
      Object.keys(tw).forEach(e => {
        let { EventType: t, defaultInit: r } = tw[e],
          n = e.toLowerCase();
        (t_[e] = (e, o) => t_(n, e, o, { EventType: t, defaultInit: r })),
          (tx[e] = (t, r) => tx(t, t_[e](t, r)));
      }),
        Object.keys(tO).forEach(e => {
          let t = tO[e];
          tx[e] = function () {
            return tx[t](...arguments);
          };
        });
      let tR = {
          debug: (e, t, r) => (Array.isArray(e) ? e.forEach(e => k(e, t, r)) : k(e, t, r)),
          logTestingPlaygroundURL: function (e) {
            var t;
            if ((void 0 === e && (e = w().body), !e || !('innerHTML' in e)))
              return void console.log("The element you're providing isn't a valid DOM element.");
            if (!e.innerHTML)
              return void console.log("The provided element doesn't have any children.");
            let r =
              'https://testing-playground.com/#markup=' +
              ((t = e.innerHTML),
              i().compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g, '\n')));
            return console.log('Open this URL in your browser\n\n' + r), r;
          },
        },
        tT =
          'undefined' != typeof document && document.body
            ? tE(document.body, tg, tR)
            : Object.keys(tg).reduce(
                (e, t) => (
                  (e[t] = () => {
                    throw TypeError(
                      'For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error'
                    );
                  }),
                  e
                ),
                tR
              );
    },
    51110: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'afterword [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    52083: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-disabled': null },
          relatedConcepts: [{ concept: { name: 'input' }, module: 'XForms' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget']],
        });
    },
    52264: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.fireInputEvent = function (e, { newValue: t, newSelectionStart: r, eventOverrides: a }) {
          var l, u;
          if ((0, o.isContentEditable)(e)) i(e, 'textContent', t);
          else if ((0, o.isElementType)(e, ['input', 'textarea'])) i(e, 'value', t);
          else throw Error('Invalid Element');
          (l = e),
            (u = r),
            (0, o.setSelectionRange)(l, u, u),
            n.fireEvent.input(e, { ...a }),
            (function (e, t, r) {
              let n = (0, o.getValue)(e);
              if (!('' === n && (0, o.hasUnreliableEmptyValue)(e)) && n === t) {
                let { selectionStart: t } = (0, o.getSelectionRange)(e);
                t === n.length && (0, o.setSelectionRange)(e, r, r);
              }
            })(e, t, r);
        });
      var n = r(50817),
        o = r(22154);
      let a = Symbol('initial input value/textContent'),
        l = Symbol('onBlur');
      function i(e, t, r) {
        let o = Object.getOwnPropertyDescriptor(e, t),
          i = Object.getOwnPropertyDescriptor(e.constructor.prototype, t);
        if (
          (o && i && Object.defineProperty(e, t, i),
          void 0 === e[a] && (e[a] = String(e[t])),
          (e[t] = r),
          !e[l])
        ) {
          var u;
          null == (u = e.ownerDocument.defaultView) ||
            u.addEventListener(
              'blur',
              (e[l] = () => {
                let r = e[a];
                delete e[l], delete e[a], String(e[t]) !== r && n.fireEvent.change(e);
              }),
              { capture: !0, once: !0 }
            );
        }
        o && Object.defineProperty(e, t, o);
      }
    },
    53482: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-valuetext': null },
          relatedConcepts: [
            { concept: { name: 'progress' }, module: 'HTML' },
            { concept: { name: 'status' }, module: 'ARIA' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'structure', 'range'],
            ['roletype', 'widget'],
          ],
        });
    },
    53502: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'menu' }, module: 'HTML' },
            { concept: { name: 'ol' }, module: 'HTML' },
            { concept: { name: 'ul' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['listitem']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    53569: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'strong' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    54347: (e, t, r) => {
      'use strict';
      let n;
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.editableInputTypes = void 0),
        (t.isEditable = function (e) {
          return (
            l(e) ||
            (0, o.isElementType)(e, 'textarea', { readOnly: !1 }) ||
            (0, a.isContentEditable)(e)
          );
        }),
        (t.isEditableInput = l);
      var o = r(26581),
        a = r(83684);
      function l(e) {
        return (0, o.isElementType)(e, 'input', { readOnly: !1 }) && !!n[e.type];
      }
      (t.editableInputTypes = n),
        (function (e) {
          (e.text = 'text'),
            (e.date = 'date'),
            (e['datetime-local'] = 'datetime-local'),
            (e.email = 'email'),
            (e.month = 'month'),
            (e.number = 'number'),
            (e.password = 'password'),
            (e.search = 'search'),
            (e.tel = 'tel'),
            (e.time = 'time'),
            (e.url = 'url'),
            (e.week = 'week');
        })(n || (t.editableInputTypes = n = {}));
    },
    54519: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.tab = function ({ shift: e = !1, focusTrap: t } = {}) {
          var r, i;
          let u = null != (r = null == (i = t) ? void 0 : i.ownerDocument) ? r : document,
            s = (0, o.getActiveElement)(u);
          t || (t = u);
          let d = Array.from(t.querySelectorAll(o.FOCUSABLE_SELECTOR)).filter(
            e =>
              e === s ||
              ('-1' !== e.getAttribute('tabindex') && !(0, o.isDisabled)(e) && (0, o.isVisible)(e))
          );
          if (0 === d.length) return;
          let c = d
              .map((e, t) => ({ el: e, idx: t }))
              .sort((e, t) => {
                if (s && '-1' === s.getAttribute('tabindex')) return e.idx - t.idx;
                let r =
                  Number(e.el.getAttribute('tabindex')) - Number(t.el.getAttribute('tabindex'));
                return 0 === r ? e.idx - t.idx : r;
              })
              .map(({ el: e }) => e),
            p = {},
            m = [];
          c.forEach(e => {
            if ('radio' === e.type && e.name) {
              if (s && s.type === e.type && s.name === e.name) {
                e === s && m.push(e);
                return;
              }
              if (e.checked) {
                (m = m.filter(t => t.type !== e.type || t.name !== e.name)).push(e),
                  (p[e.name] = e);
                return;
              }
              if (void 0 !== p[e.name]) return;
            }
            m.push(e);
          });
          let f = (function (e, t, r, n) {
              if ((0, o.isDocument)(n) && ((0 === e && t) || (e === r.length - 1 && !t)))
                return n.body;
              let a = t ? r.length - 1 : 0;
              return r[t ? e - 1 : e + 1] || r[a];
            })(
              m.findIndex(e => e === s),
              e,
              m,
              t
            ),
            b = { key: 'Shift', keyCode: 16, shiftKey: !0 },
            y = { key: 'Tab', keyCode: 9, shiftKey: e },
            v = !0;
          s && (e && n.fireEvent.keyDown(s, { ...b }), (v = n.fireEvent.keyDown(s, { ...y })));
          let h = !v && s ? s : f;
          v && (f === u.body ? s && (0, l.blur)(s) : (0, a.focus)(f)),
            n.fireEvent.keyUp(h, { ...y }),
            e && n.fireEvent.keyUp(h, { ...b, shiftKey: !1 });
        });
      var n = r(50817),
        o = r(22154),
        a = r(73164),
        l = r(18409);
    },
    54613: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'chapter [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    55031: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-haspopup': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-valuetext': null,
            'aria-orientation': 'horizontal',
            'aria-valuemax': '100',
            'aria-valuemin': '0',
          },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'range' }], name: 'input' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-valuenow': null },
          superClass: [
            ['roletype', 'widget', 'input'],
            ['roletype', 'structure', 'range'],
          ],
        });
    },
    55654: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isCursorAtEnd = function (e) {
          var t;
          let { selectionStart: r, selectionEnd: a } = (0, n.getSelectionRange)(e);
          return (
            r === a && (null != r ? r : 0) === (null != (t = (0, o.getValue)(e)) ? t : '').length
          );
        }),
        (t.isCursorAtStart = function (e) {
          let { selectionStart: t, selectionEnd: r } = (0, n.getSelectionRange)(e);
          return t === r && (null != t ? t : 0) === 0;
        });
      var n = r(97676),
        o = r(20894);
    },
    55841: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = a(r(91232)),
        o = a(r(38310));
      function a(e) {
        return e && e.__esModule ? e : { default: e };
      }
      function l(e, t) {
        return (
          (function (e) {
            if (Array.isArray(e)) return e;
          })(e) ||
          (function (e, t) {
            var r,
              n,
              o =
                null == e
                  ? null
                  : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
            if (null != o) {
              var a = [],
                l = !0,
                i = !1;
              try {
                for (
                  o = o.call(e);
                  !(l = (r = o.next()).done) && (a.push(r.value), !t || a.length !== t);
                  l = !0
                );
              } catch (e) {
                (i = !0), (n = e);
              } finally {
                try {
                  l || null == o.return || o.return();
                } finally {
                  if (i) throw n;
                }
              }
              return a;
            }
          })(e, t) ||
          i(e, t) ||
          (function () {
            throw TypeError(
              'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
            );
          })()
        );
      }
      function i(e, t) {
        if (e) {
          if ('string' == typeof e) return u(e, t);
          var r = Object.prototype.toString.call(e).slice(8, -1);
          if (
            ('Object' === r && e.constructor && (r = e.constructor.name),
            'Map' === r || 'Set' === r)
          )
            return Array.from(e);
          if ('Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))
            return u(e, t);
        }
      }
      function u(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
        return n;
      }
      for (var s = [], d = o.default.keys(), c = 0; c < d.length; c++) {
        var p = d[c],
          m = o.default.get(p),
          f = [];
        if (m) {
          for (var b = [].concat(m.baseConcepts, m.relatedConcepts), y = 0; y < b.length; y++) {
            var v = b[y];
            if ('HTML' === v.module) {
              var h = v.concept;
              null != h && f.push(h);
            }
          }
          f.length > 0 && s.push([p, f]);
        }
      }
      var g = {
        entries: function () {
          return s;
        },
        forEach: function (e) {
          var t,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
            n = (function (e, t) {
              var r = ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator'];
              if (!r) {
                if (Array.isArray(e) || (r = i(e))) {
                  r && (e = r);
                  var n = 0,
                    o = function () {};
                  return {
                    s: o,
                    n: function () {
                      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };
                    },
                    e: function (e) {
                      throw e;
                    },
                    f: o,
                  };
                }
                throw TypeError(
                  'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
                );
              }
              var a,
                l = !0,
                u = !1;
              return {
                s: function () {
                  r = r.call(e);
                },
                n: function () {
                  var e = r.next();
                  return (l = e.done), e;
                },
                e: function (e) {
                  (u = !0), (a = e);
                },
                f: function () {
                  try {
                    l || null == r.return || r.return();
                  } finally {
                    if (u) throw a;
                  }
                },
              };
            })(s);
          try {
            for (n.s(); !(t = n.n()).done; ) {
              var o = l(t.value, 2),
                a = o[0],
                u = o[1];
              e.call(r, u, a, s);
            }
          } catch (e) {
            n.e(e);
          } finally {
            n.f();
          }
        },
        get: function (e) {
          var t = s.find(function (t) {
            return t[0] === e;
          });
          return t && t[1];
        },
        has: function (e) {
          return !!g.get(e);
        },
        keys: function () {
          return s.map(function (e) {
            return l(e, 1)[0];
          });
        },
        values: function () {
          return s.map(function (e) {
            return l(e, 2)[1];
          });
        },
      };
      t.default = (0, n.default)(g, g.entries());
    },
    55853: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.defaultKeyMap = void 0);
      var n = r(88719);
      t.defaultKeyMap = [
        ...'0123456789'.split('').map(e => ({ code: `Digit${e}`, key: e })),
        ...')!@#$%^&*('.split('').map((e, t) => ({ code: `Digit${t}`, key: e, shiftKey: !0 })),
        ...'abcdefghijklmnopqrstuvwxyz'
          .split('')
          .map(e => ({ code: `Key${e.toUpperCase()}`, key: e })),
        ...'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
          .split('')
          .map(e => ({ code: `Key${e}`, key: e, shiftKey: !0 })),
        { code: 'Space', key: ' ' },
        { code: 'AltLeft', key: 'Alt', location: n.DOM_KEY_LOCATION.LEFT, keyCode: 18 },
        { code: 'AltRight', key: 'Alt', location: n.DOM_KEY_LOCATION.RIGHT, keyCode: 18 },
        { code: 'ShiftLeft', key: 'Shift', location: n.DOM_KEY_LOCATION.LEFT, keyCode: 16 },
        { code: 'ShiftRight', key: 'Shift', location: n.DOM_KEY_LOCATION.RIGHT, keyCode: 16 },
        { code: 'ControlLeft', key: 'Control', location: n.DOM_KEY_LOCATION.LEFT, keyCode: 17 },
        { code: 'ControlRight', key: 'Control', location: n.DOM_KEY_LOCATION.RIGHT, keyCode: 17 },
        { code: 'MetaLeft', key: 'Meta', location: n.DOM_KEY_LOCATION.LEFT, keyCode: 93 },
        { code: 'MetaRight', key: 'Meta', location: n.DOM_KEY_LOCATION.RIGHT, keyCode: 93 },
        { code: 'OSLeft', key: 'OS', location: n.DOM_KEY_LOCATION.LEFT, keyCode: 91 },
        { code: 'OSRight', key: 'OS', location: n.DOM_KEY_LOCATION.RIGHT, keyCode: 91 },
        { code: 'CapsLock', key: 'CapsLock', keyCode: 20 },
        { code: 'Backspace', key: 'Backspace', keyCode: 8 },
        { code: 'Enter', key: 'Enter', keyCode: 13 },
        { code: 'Escape', key: 'Escape', keyCode: 27 },
        { code: 'ArrowUp', key: 'ArrowUp', keyCode: 38 },
        { code: 'ArrowDown', key: 'ArrowDown', keyCode: 40 },
        { code: 'ArrowLeft', key: 'ArrowLeft', keyCode: 37 },
        { code: 'ArrowRight', key: 'ArrowRight', keyCode: 39 },
        { code: 'Home', key: 'Home', keyCode: 36 },
        { code: 'End', key: 'End', keyCode: 35 },
        { code: 'Delete', key: 'Delete', keyCode: 46 },
        { code: 'PageUp', key: 'PageUp', keyCode: 33 },
        { code: 'PageDown', key: 'PageDown', keyCode: 34 },
      ];
    },
    58154: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'acknowledgments [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    58465: (e, t, r) => {
      'use strict';
      t.Ot = t.wZ = t._s = void 0;
      var n = u(r(10102)),
        o = u(r(5185)),
        a = u(r(38310)),
        l = u(r(3737)),
        i = u(r(55841));
      function u(e) {
        return e && e.__esModule ? e : { default: e };
      }
      n.default, o.default, (t.Ot = a.default), (t._s = l.default), (t.wZ = i.default);
    },
    59203: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'Device Independence Delivery Unit' } },
            { concept: { name: 'html' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    59243: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-valuetext': null, 'aria-valuemax': '100', 'aria-valuemin': '0' },
          relatedConcepts: [{ concept: { name: 'meter' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-valuenow': null },
          superClass: [['roletype', 'structure', 'range']],
        });
    },
    59307: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.getSpaceUntilMaxLength = function (e) {
          let t = (0, a.getValue)(e);
          if (null === t) return;
          let r = (function (e) {
            var t, r;
            if (
              ((r = e),
              !(
                (0, o.isElementType)(r, 'textarea') ||
                ((0, o.isElementType)(r, 'input') && n[r.type])
              ))
            )
              return;
            let a = null != (t = e.getAttribute('maxlength')) ? t : '';
            return /^\d+$/.test(a) && Number(a) >= 0 ? Number(a) : void 0;
          })(e);
          return r ? r - t.length : void 0;
        });
      var n,
        o = r(26581),
        a = r(20894);
      !(function (e) {
        (e.email = 'email'),
          (e.password = 'password'),
          (e.search = 'search'),
          (e.telephone = 'telephone'),
          (e.text = 'text'),
          (e.url = 'url');
      })(n || (n = {}));
    },
    60376: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'notice [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'note']],
        });
    },
    60853: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'img']],
        });
    },
    61682: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'toc [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']],
        });
    },
    62148: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'p' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    62268: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [
            { module: 'GRAPHICS', concept: { name: 'graphics-object' } },
            { module: 'ARIA', concept: { name: 'img' } },
            { module: 'ARIA', concept: { name: 'article' } },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'document']],
        });
    },
    62503: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = l(r(62268)),
        o = l(r(71198)),
        a = l(r(60853));
      function l(e) {
        return e && e.__esModule ? e : { default: e };
      }
      t.default = [
        ['graphics-document', n.default],
        ['graphics-object', o.default],
        ['graphics-symbol', a.default],
      ];
    },
    62538: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'rearnotes [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['doc-endnote']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    63239: (e, t) => {
      var r = Object.prototype.hasOwnProperty;
      t.dequal = function e(t, n) {
        var o, a;
        if (t === n) return !0;
        if (t && n && (o = t.constructor) === n.constructor) {
          if (o === Date) return t.getTime() === n.getTime();
          if (o === RegExp) return t.toString() === n.toString();
          if (o === Array) {
            if ((a = t.length) === n.length) for (; a-- && e(t[a], n[a]); );
            return -1 === a;
          }
          if (!o || 'object' == typeof t) {
            for (o in ((a = 0), t))
              if ((r.call(t, o) && ++a && !r.call(n, o)) || !(o in n) || !e(t[o], n[o])) return !1;
            return Object.keys(n).length === a;
          }
        }
        return t != t && n != n;
      };
    },
    66154: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'sup' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    66549: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isFocusable = function (e) {
          return !(0, n.isLabelWithInternallyDisabledControl)(e) && e.matches(o.FOCUSABLE_SELECTOR);
        });
      var n = r(97698),
        o = r(14042);
    },
    67665: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    69121: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-level': null,
            'aria-multiselectable': null,
            'aria-orientation': 'horizontal',
          },
          relatedConcepts: [{ module: 'DAISY', concept: { name: 'guide' } }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['tab']],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'composite']],
        });
    },
    69638: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'dialog' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'window']],
        });
    },
    69752: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
          },
          relatedConcepts: [{ concept: { name: 'list' }, module: 'ARIA' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['radio']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select'],
            ['roletype', 'structure', 'section', 'group', 'select'],
          ],
        });
    },
    70929: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.getActiveElement = function e(t) {
          let r = t.activeElement;
          return null != r && r.shadowRoot
            ? e(r.shadowRoot)
            : (0, n.isDisabled)(r)
              ? t.ownerDocument
                ? t.ownerDocument.body
                : t.body
              : r;
        });
      var n = r(43819);
    },
    71198: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [
            { module: 'GRAPHICS', concept: { name: 'graphics-document' } },
            { module: 'ARIA', concept: { name: 'group' } },
            { module: 'ARIA', concept: { name: 'img' } },
            { module: 'GRAPHICS', concept: { name: 'graphics-symbol' } },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'group']],
        });
    },
    71713: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-level': null, 'aria-posinset': null, 'aria-setsize': null },
          relatedConcepts: [
            {
              concept: {
                constraints: [
                  'direct descendant of ol',
                  'direct descendant of ul',
                  'direct descendant of menu',
                ],
                name: 'li',
              },
              module: 'HTML',
            },
            { concept: { name: 'item' }, module: 'XForms' },
          ],
          requireContextRole: ['directory', 'list'],
          requiredContextRole: ['directory', 'list'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    73164: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.focus = function (e) {
          (0, n.isFocusable)(e) &&
            (0, n.getActiveElement)(e.ownerDocument) !== e &&
            (0, n.eventWrapper)(() => e.focus());
        });
      var n = r(22154);
    },
    73711: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'conclusion [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    73747: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'sub' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    74026: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-colindex': null,
            'aria-colspan': null,
            'aria-rowindex': null,
            'aria-rowspan': null,
          },
          relatedConcepts: [
            {
              concept: { constraints: ['ancestor table element has table role'], name: 'td' },
              module: 'HTML',
            },
          ],
          requireContextRole: ['row'],
          requiredContextRole: ['row'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    74033: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-orientation': 'vertical' },
          relatedConcepts: [
            { concept: { name: 'MENU' }, module: 'JAPI' },
            { concept: { name: 'list' }, module: 'ARIA' },
            { concept: { name: 'select' }, module: 'XForms' },
            { concept: { name: 'sidebar' }, module: 'DTB' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [
            ['menuitem', 'group'],
            ['menuitemradio', 'group'],
            ['menuitemcheckbox', 'group'],
            ['menuitem'],
            ['menuitemcheckbox'],
            ['menuitemradio'],
          ],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select'],
            ['roletype', 'structure', 'section', 'group', 'select'],
          ],
        });
    },
    74304: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'dfn' }, module: 'HTML' },
            { concept: { name: 'dt' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    74349: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'part [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    74442: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    75118: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'colophon [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    75190: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'abstract [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    75286: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: { 'aria-disabled': null, 'aria-expanded': null, 'aria-haspopup': null },
          relatedConcepts: [
            {
              concept: { attributes: [{ constraints: ['set'], name: 'href' }], name: 'a' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ constraints: ['set'], name: 'href' }], name: 'area' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command']],
        });
    },
    76537: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.default = void 0);
      var n = H(r(75190)),
        o = H(r(58154)),
        a = H(r(51110)),
        l = H(r(86353)),
        i = H(r(3071)),
        u = H(r(7313)),
        s = H(r(86348)),
        d = H(r(50292)),
        c = H(r(54613)),
        p = H(r(75118)),
        m = H(r(73711)),
        f = H(r(16163)),
        b = H(r(33473)),
        y = H(r(35546)),
        v = H(r(14658)),
        h = H(r(5393)),
        g = H(r(62538)),
        E = H(r(41712)),
        C = H(r(5012)),
        P = H(r(94149)),
        q = H(r(97362)),
        w = H(r(14780)),
        O = H(r(99770)),
        x = H(r(15692)),
        _ = H(r(28407)),
        R = H(r(89460)),
        T = H(r(6636)),
        M = H(r(43185)),
        k = H(r(60376)),
        j = H(r(80290)),
        A = H(r(35615)),
        S = H(r(74349)),
        B = H(r(33976)),
        I = H(r(23617)),
        L = H(r(98253)),
        N = H(r(50498)),
        F = H(r(86548)),
        D = H(r(32957)),
        U = H(r(61682));
      function H(e) {
        return e && e.__esModule ? e : { default: e };
      }
      t.default = [
        ['doc-abstract', n.default],
        ['doc-acknowledgments', o.default],
        ['doc-afterword', a.default],
        ['doc-appendix', l.default],
        ['doc-backlink', i.default],
        ['doc-biblioentry', u.default],
        ['doc-bibliography', s.default],
        ['doc-biblioref', d.default],
        ['doc-chapter', c.default],
        ['doc-colophon', p.default],
        ['doc-conclusion', m.default],
        ['doc-cover', f.default],
        ['doc-credit', b.default],
        ['doc-credits', y.default],
        ['doc-dedication', v.default],
        ['doc-endnote', h.default],
        ['doc-endnotes', g.default],
        ['doc-epigraph', E.default],
        ['doc-epilogue', C.default],
        ['doc-errata', P.default],
        ['doc-example', q.default],
        ['doc-footnote', w.default],
        ['doc-foreword', O.default],
        ['doc-glossary', x.default],
        ['doc-glossref', _.default],
        ['doc-index', R.default],
        ['doc-introduction', T.default],
        ['doc-noteref', M.default],
        ['doc-notice', k.default],
        ['doc-pagebreak', j.default],
        ['doc-pagelist', A.default],
        ['doc-part', S.default],
        ['doc-preface', B.default],
        ['doc-prologue', I.default],
        ['doc-pullquote', L.default],
        ['doc-qna', N.default],
        ['doc-subtitle', F.default],
        ['doc-tip', D.default],
        ['doc-toc', U.default],
      ];
    },
    76696: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 });
      var n = r(96408);
      Object.keys(n).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === n[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return n[e];
              },
            }));
      });
      var o = r(11263);
      Object.keys(o).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === o[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return o[e];
              },
            }));
      });
      var a = r(52264);
      Object.keys(a).forEach(function (e) {
        'default' !== e &&
          '__esModule' !== e &&
          ((e in t && t[e] === a[e]) ||
            Object.defineProperty(t, e, {
              enumerable: !0,
              get: function () {
                return a[e];
              },
            }));
      });
    },
    77070: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.wait = function (e) {
          return new Promise(t => setTimeout(() => t(), e));
        });
    },
    77483: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-invalid': null,
            'aria-multiselectable': null,
            'aria-readonly': null,
            'aria-required': null,
            'aria-orientation': 'vertical',
          },
          relatedConcepts: [
            {
              concept: {
                attributes: [{ constraints: ['>1'], name: 'size' }],
                constraints: ['the size attribute value is greater than 1'],
                name: 'select',
              },
              module: 'HTML',
            },
            { concept: { attributes: [{ name: 'multiple' }], name: 'select' }, module: 'HTML' },
            { concept: { name: 'datalist' }, module: 'HTML' },
            { concept: { name: 'list' }, module: 'ARIA' },
            { concept: { name: 'select' }, module: 'XForms' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['option', 'group'], ['option']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select'],
            ['roletype', 'structure', 'section', 'group', 'select'],
          ],
        });
    },
    77516: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: { attributes: [{ constraints: ['set'], name: 'aria-label' }], name: 'form' },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [{ constraints: ['set'], name: 'aria-labelledby' }],
                name: 'form',
              },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ constraints: ['set'], name: 'name' }], name: 'form' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    78453: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-activedescendant': null,
            'aria-autocomplete': null,
            'aria-errormessage': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
            'aria-expanded': 'false',
            'aria-haspopup': 'listbox',
          },
          relatedConcepts: [
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'email' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'search' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'tel' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'text' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'url' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['set'], name: 'list' },
                  { name: 'type', value: 'url' },
                ],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'multiple' },
                  { constraints: ['undefined'], name: 'size' },
                ],
                constraints: [
                  'the multiple attribute is not set and the size attribute does not have a value greater than 1',
                ],
                name: 'select',
              },
              module: 'HTML',
            },
            { concept: { name: 'select' }, module: 'XForms' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-controls': null, 'aria-expanded': 'false' },
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    80065: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.specialCharMap = void 0),
        (t.specialCharMap = {
          arrowLeft: '{arrowleft}',
          arrowRight: '{arrowright}',
          arrowDown: '{arrowdown}',
          arrowUp: '{arrowup}',
          enter: '{enter}',
          escape: '{esc}',
          delete: '{del}',
          backspace: '{backspace}',
          home: '{home}',
          end: '{end}',
          selectAll: '{selectall}',
          space: '{space}',
          whitespace: ' ',
          pageUp: '{pageUp}',
          pageDown: '{pageDown}',
        });
    },
    80290: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'pagebreak [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'separator']],
        });
    },
    80340: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'frontmatter' }, module: 'DTB' },
            { concept: { name: 'level' }, module: 'DTB' },
            { concept: { name: 'level' }, module: 'SMIL' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure']],
        });
    },
    80920: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.getKeyEventProps = function (e, t) {
          var r, n;
          return {
            key: e.key,
            code: e.code,
            altKey: t.modifiers.alt,
            ctrlKey: t.modifiers.ctrl,
            metaKey: t.modifiers.meta,
            shiftKey: t.modifiers.shift,
            keyCode:
              null != (r = e.keyCode)
                ? r
                : (null == (n = e.key) ? void 0 : n.length) === 1
                  ? e.key.charCodeAt(0)
                  : void 0,
          };
        }),
        (t.getMouseEventProps = function (e) {
          return {
            altKey: e.modifiers.alt,
            ctrlKey: e.modifiers.ctrl,
            metaKey: e.modifiers.meta,
            shiftKey: e.modifiers.shift,
          };
        });
    },
    81460: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-valuetext': null,
            'aria-orientation': 'vertical',
            'aria-valuemax': '100',
            'aria-valuemin': '0',
          },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-controls': null, 'aria-valuenow': null },
          superClass: [
            ['roletype', 'structure', 'range'],
            ['roletype', 'widget'],
          ],
        });
    },
    81518: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['row'], ['row', 'rowgroup']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'grid'],
            ['roletype', 'structure', 'section', 'table', 'grid'],
            ['roletype', 'widget', 'composite', 'select', 'tree'],
            ['roletype', 'structure', 'section', 'group', 'select', 'tree'],
          ],
        });
    },
    81972: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-activedescendant': null, 'aria-disabled': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget']],
        });
    },
    82662: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-posinset': null,
            'aria-setsize': null,
          },
          relatedConcepts: [
            { concept: { name: 'MENU_ITEM' }, module: 'JAPI' },
            { concept: { name: 'listitem' }, module: 'ARIA' },
            { concept: { name: 'option' }, module: 'ARIA' },
          ],
          requireContextRole: ['group', 'menu', 'menubar'],
          requiredContextRole: ['group', 'menu', 'menubar'],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'command']],
        });
    },
    82745: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isValidInputTimeValue = function (e, t) {
          let r = e.cloneNode();
          return (r.value = t), r.value === t;
        });
    },
    83684: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isContentEditable = function (e) {
          return (
            e.hasAttribute('contenteditable') &&
            ('true' == e.getAttribute('contenteditable') || '' == e.getAttribute('contenteditable'))
          );
        });
    },
    84510: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-activedescendant': null,
            'aria-autocomplete': null,
            'aria-errormessage': null,
            'aria-haspopup': null,
            'aria-invalid': null,
            'aria-multiline': null,
            'aria-placeholder': null,
            'aria-readonly': null,
            'aria-required': null,
          },
          relatedConcepts: [
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'type' },
                  { constraints: ['undefined'], name: 'list' },
                ],
                constraints: ['the list attribute is not set'],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'email' },
                ],
                constraints: ['the list attribute is not set'],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'tel' },
                ],
                constraints: ['the list attribute is not set'],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'text' },
                ],
                constraints: ['the list attribute is not set'],
                name: 'input',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [
                  { constraints: ['undefined'], name: 'list' },
                  { name: 'type', value: 'url' },
                ],
                constraints: ['the list attribute is not set'],
                name: 'input',
              },
              module: 'HTML',
            },
            { concept: { name: 'input' }, module: 'XForms' },
            { concept: { name: 'textarea' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    85229: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: [],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype']],
        });
    },
    86348: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'bibliography [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['doc-biblioentry']],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    86353: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'appendix [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    86548: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'subtitle [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'sectionhead']],
        });
    },
    86906: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    88372: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: { constraints: ['scoped to the body element'], name: 'header' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    88719: (e, t) => {
      'use strict';
      let r;
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.DOM_KEY_LOCATION = void 0),
        (t.DOM_KEY_LOCATION = r),
        (function (e) {
          (e[(e.STANDARD = 0)] = 'STANDARD'),
            (e[(e.LEFT = 1)] = 'LEFT'),
            (e[(e.RIGHT = 2)] = 'RIGHT'),
            (e[(e.NUMPAD = 3)] = 'NUMPAD');
        })(r || (t.DOM_KEY_LOCATION = r = {}));
    },
    89460: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'index [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']],
        });
    },
    89721: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            {
              concept: { attributes: [{ constraints: ['set'], name: 'alt' }], name: 'img' },
              module: 'HTML',
            },
            {
              concept: { attributes: [{ constraints: ['undefined'], name: 'alt' }], name: 'img' },
              module: 'HTML',
            },
            { concept: { name: 'imggroup' }, module: 'DTB' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    90851: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'time' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    90862: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }), (t.keypressBehavior = void 0);
      var n = r(50817),
        o = r(76696),
        a = r(22154);
      t.keypressBehavior = [
        {
          matches: (e, t) => {
            var r;
            return (
              (null == (r = e.key) ? void 0 : r.length) === 1 &&
              (0, a.isElementType)(t, 'input', { type: 'time', readOnly: !1 })
            );
          },
          handle: (e, t, r, n) => {
            var l;
            let i = e.key,
              u = (null != (l = n.carryValue) ? l : '') + i,
              s = (0, a.buildTimeValue)(u);
            (0, a.isValidInputTimeValue)(t, s) && (i = s);
            let { newValue: d, newSelectionStart: c } = (0, a.calculateNewValue)(i, t),
              p = (0, a.getValue)(t);
            p !== d &&
              (0, o.fireInputEvent)(t, {
                newValue: d,
                newSelectionStart: c,
                eventOverrides: { data: e.key, inputType: 'insertText' },
              }),
              (0, o.fireChangeForInputTimeIfValid)(t, p, s),
              (n.carryValue = u);
          },
        },
        {
          matches: (e, t) => {
            var r;
            return (
              (null == (r = e.key) ? void 0 : r.length) === 1 &&
              (0, a.isElementType)(t, 'input', { type: 'date', readOnly: !1 })
            );
          },
          handle: (e, t, r, l) => {
            var i;
            let u = e.key,
              s = (null != (i = l.carryValue) ? i : '') + u,
              d = (0, a.isValidDateValue)(t, s);
            d && (u = s);
            let { newValue: c, newSelectionStart: p } = (0, a.calculateNewValue)(u, t);
            (0, a.getValue)(t) !== c &&
              (0, o.fireInputEvent)(t, {
                newValue: c,
                newSelectionStart: p,
                eventOverrides: { data: e.key, inputType: 'insertText' },
              }),
              d && n.fireEvent.change(t, { target: { value: s } }),
              (l.carryValue = s);
          },
        },
        {
          matches: (e, t) => {
            var r;
            return (
              (null == (r = e.key) ? void 0 : r.length) === 1 &&
              (0, a.isElementType)(t, 'input', { type: 'number', readOnly: !1 })
            );
          },
          handle: (e, t, r, n) => {
            var l, i, u, s;
            if (!/[\d.\-e]/.test(e.key)) return;
            let d = null != (l = null != (i = n.carryValue) ? i : (0, a.getValue)(t)) ? l : '',
              { newValue: c, newSelectionStart: p } = (0, a.calculateNewValue)(e.key, t, d),
              m = c.split('e', 2);
            !(
              Number(null == (u = c.match(/-/g)) ? void 0 : u.length) > 2 ||
              Number(null == (s = c.match(/\./g)) ? void 0 : s.length) > 1
            ) &&
              (!m[1] || /^-?\d*$/.test(m[1])) &&
              ((0, o.fireInputEvent)(t, {
                newValue: c,
                newSelectionStart: p,
                eventOverrides: { data: e.key, inputType: 'insertText' },
              }),
              (0, a.getValue)(t) === c ? (n.carryValue = void 0) : (n.carryValue = c));
          },
        },
        {
          matches: (e, t) => {
            var r;
            return (
              (null == (r = e.key) ? void 0 : r.length) === 1 &&
              (((0, a.isElementType)(t, ['input', 'textarea'], { readOnly: !1 }) &&
                !(0, a.isClickableInput)(t)) ||
                (0, a.isContentEditable)(t)) &&
              0 !== (0, a.getSpaceUntilMaxLength)(t)
            );
          },
          handle: (e, t) => {
            let { newValue: r, newSelectionStart: n } = (0, a.calculateNewValue)(e.key, t);
            (0, o.fireInputEvent)(t, {
              newValue: r,
              newSelectionStart: n,
              eventOverrides: { data: e.key, inputType: 'insertText' },
            });
          },
        },
        {
          matches: (e, t) =>
            'Enter' === e.key &&
            ((0, a.isElementType)(t, 'textarea', { readOnly: !1 }) ||
              (0, a.isContentEditable)(t)) &&
            0 !== (0, a.getSpaceUntilMaxLength)(t),
          handle: (e, t, r, n) => {
            let { newValue: l, newSelectionStart: i } = (0, a.calculateNewValue)('\n', t),
              u =
                (0, a.isContentEditable)(t) && !n.modifiers.shift
                  ? 'insertParagraph'
                  : 'insertLineBreak';
            (0, o.fireInputEvent)(t, {
              newValue: l,
              newSelectionStart: i,
              eventOverrides: { inputType: u },
            });
          },
        },
      ];
    },
    91232: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = function (e, t) {
          return (
            'function' == typeof Symbol &&
              'symbol' === o(Symbol.iterator) &&
              Object.defineProperty(e, Symbol.iterator, { value: n.default.bind(t) }),
            e
          );
        });
      var n = (function (e) {
        return e && e.__esModule ? e : { default: e };
      })(r(91414));
      function o(e) {
        return (o =
          'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
            ? function (e) {
                return typeof e;
              }
            : function (e) {
                return e &&
                  'function' == typeof Symbol &&
                  e.constructor === Symbol &&
                  e !== Symbol.prototype
                  ? 'symbol'
                  : typeof e;
              })(e);
      }
    },
    91414: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = function () {
          var e = this,
            t = 0,
            r = {
              '@@iterator': function () {
                return r;
              },
              next: function () {
                if (!(t < e.length)) return { done: !0 };
                var r = e[t];
                return (t += 1), { done: !1, value: r };
              },
            };
          return r;
        });
    },
    93172: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [
            { concept: { name: 'aside' }, module: 'HTML' },
            {
              concept: {
                attributes: [{ constraints: ['set'], name: 'aria-label' }],
                constraints: [
                  'scoped to a sectioning content element',
                  'scoped to a sectioning root element other than body',
                ],
                name: 'aside',
              },
              module: 'HTML',
            },
            {
              concept: {
                attributes: [{ constraints: ['set'], name: 'aria-labelledby' }],
                constraints: [
                  'scoped to a sectioning content element',
                  'scoped to a sectioning root element other than body',
                ],
                name: 'aside',
              },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    93339: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'status']],
        });
    },
    93370: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-invalid': null,
            'aria-readonly': null,
            'aria-required': null,
            'aria-valuetext': null,
            'aria-valuenow': '0',
          },
          relatedConcepts: [
            {
              concept: { attributes: [{ name: 'type', value: 'number' }], name: 'input' },
              module: 'HTML',
            },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite'],
            ['roletype', 'widget', 'input'],
            ['roletype', 'structure', 'range'],
          ],
        });
    },
    93589: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !0,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-modal': null },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype']],
        });
    },
    93758: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-errormessage': null,
            'aria-invalid': null,
            'aria-multiselectable': null,
            'aria-required': null,
            'aria-orientation': 'vertical',
          },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [['treeitem', 'group'], ['treeitem']],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select'],
            ['roletype', 'structure', 'section', 'group', 'select'],
          ],
        });
    },
    94133: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ module: 'DAISY Guide' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'list']],
        });
    },
    94149: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'errata [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
    96350: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: { 'aria-orientation': 'horizontal' },
          relatedConcepts: [{ concept: { name: 'toolbar' }, module: 'ARIA' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [
            ['menuitem', 'group'],
            ['menuitemradio', 'group'],
            ['menuitemcheckbox', 'group'],
            ['menuitem'],
            ['menuitemcheckbox'],
            ['menuitemradio'],
          ],
          requiredProps: {},
          superClass: [
            ['roletype', 'widget', 'composite', 'select', 'menu'],
            ['roletype', 'structure', 'section', 'group', 'select', 'menu'],
          ],
        });
    },
    96408: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.carryValue = function (e, t, r) {
          let o = (0, n.getValue)(e);
          t.carryValue = o !== r && '' === o && (0, n.hasUnreliableEmptyValue)(e) ? r : void 0;
        });
      var n = r(22154);
    },
    97362: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    97676: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.getSelectionRange = i),
        (t.hasSelectionSupport = l),
        (t.setSelectionRange = function (e, t, r) {
          let { selectionStart: n, selectionEnd: u } = i(e);
          if (
            (n === t && u === r) ||
            (l(e) && e.setSelectionRange(t, r),
            (0, o.isElementType)(e, 'input') && (e[a] = { selectionStart: t, selectionEnd: r }),
            (0, o.isElementType)(e, 'input') || (0, o.isElementType)(e, 'textarea'))
          )
            return;
          let s = e.ownerDocument.createRange();
          s.selectNodeContents(e),
            e.firstChild && (s.setStart(e.firstChild, t), s.setEnd(e.firstChild, r));
          let d = e.ownerDocument.getSelection();
          d && (d.removeAllRanges(), d.addRange(s));
        });
      var n,
        o = r(26581);
      !(function (e) {
        (e.text = 'text'),
          (e.search = 'search'),
          (e.url = 'url'),
          (e.tel = 'tel'),
          (e.password = 'password');
      })(n || (n = {}));
      let a = Symbol('inputSelection');
      function l(e) {
        return (
          (0, o.isElementType)(e, 'textarea') || ((0, o.isElementType)(e, 'input') && !!n[e.type])
        );
      }
      function i(e) {
        if (l(e)) return { selectionStart: e.selectionStart, selectionEnd: e.selectionEnd };
        if ((0, o.isElementType)(e, 'input')) {
          var t;
          return null != (t = e[a]) ? t : { selectionStart: null, selectionEnd: null };
        }
        let r = e.ownerDocument.getSelection();
        if (!(null != r && r.rangeCount && e.contains(r.focusNode)))
          return { selectionStart: null, selectionEnd: null };
        {
          let e = r.getRangeAt(0);
          return { selectionStart: e.startOffset, selectionEnd: e.endOffset };
        }
      }
    },
    97698: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.isLabelWithInternallyDisabledControl = function (e) {
          if (!(0, o.isElementType)(e, 'label')) return !1;
          let t = e.control;
          return !!(t && e.contains(t) && (0, n.isDisabled)(t));
        });
      var n = r(43819),
        o = r(26581);
    },
    97732: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['prohibited'],
          prohibitedProps: ['aria-label', 'aria-labelledby'],
          props: {},
          relatedConcepts: [{ concept: { name: 'em' }, module: 'HTML' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section']],
        });
    },
    98027: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'menuitem' }, module: 'ARIA' }],
          requireContextRole: ['group', 'menu', 'menubar'],
          requiredContextRole: ['group', 'menu', 'menubar'],
          requiredOwnedElements: [],
          requiredProps: { 'aria-checked': null },
          superClass: [
            ['roletype', 'widget', 'input', 'checkbox'],
            ['roletype', 'widget', 'command', 'menuitem'],
          ],
        });
    },
    98253: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {},
          relatedConcepts: [{ concept: { name: 'pullquote [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['none']],
        });
    },
    99131: (e, t, r) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.paste = function (e, t, r, { initialSelectionStart: a, initialSelectionEnd: l } = {}) {
          if (
            !(
              ((0, o.isElementType)(e, 'input') && o.editableInputTypes[e.type]) ||
              (0, o.isElementType)(e, 'textarea')
            )
          )
            throw TypeError(`The given ${e.tagName} element is currently unsupported.
      A PR extending this implementation would be very much welcome at https://github.com/testing-library/user-event`);
          if (
            (0, o.isDisabled)(e) ||
            ((0, o.eventWrapper)(() => e.focus()),
            0 === e.selectionStart &&
              0 === e.selectionEnd &&
              (0, o.setSelectionRange)(
                e,
                null != a ? a : e.value.length,
                null != l ? l : e.value.length
              ),
            n.fireEvent.paste(e, r),
            e.readOnly)
          )
            return;
          t = t.substr(0, (0, o.getSpaceUntilMaxLength)(e));
          let { newValue: i, newSelectionStart: u } = (0, o.calculateNewValue)(t, e);
          n.fireEvent.input(e, { inputType: 'insertFromPaste', target: { value: i } }),
            (0, o.setSelectionRange)(e, { newSelectionStart: u, selectionEnd: u }, {});
        });
      var n = r(50817),
        o = r(22154);
    },
    99651: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !0,
          baseConcepts: [],
          childrenPresentational: !0,
          nameFrom: ['author', 'contents'],
          prohibitedProps: [],
          props: {
            'aria-checked': null,
            'aria-posinset': null,
            'aria-setsize': null,
            'aria-selected': 'false',
          },
          relatedConcepts: [
            { concept: { name: 'item' }, module: 'XForms' },
            { concept: { name: 'listitem' }, module: 'ARIA' },
            { concept: { name: 'option' }, module: 'HTML' },
          ],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: { 'aria-selected': 'false' },
          superClass: [['roletype', 'widget', 'input']],
        });
    },
    99770: (e, t) => {
      'use strict';
      Object.defineProperty(t, '__esModule', { value: !0 }),
        (t.default = void 0),
        (t.default = {
          abstract: !1,
          accessibleNameRequired: !1,
          baseConcepts: [],
          childrenPresentational: !1,
          nameFrom: ['author'],
          prohibitedProps: [],
          props: {
            'aria-disabled': null,
            'aria-errormessage': null,
            'aria-expanded': null,
            'aria-haspopup': null,
            'aria-invalid': null,
          },
          relatedConcepts: [{ concept: { name: 'foreword [EPUB-SSV]' }, module: 'EPUB' }],
          requireContextRole: [],
          requiredContextRole: [],
          requiredOwnedElements: [],
          requiredProps: {},
          superClass: [['roletype', 'structure', 'section', 'landmark']],
        });
    },
  },
]);
