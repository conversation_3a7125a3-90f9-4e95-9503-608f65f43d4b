(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [445],
  {
    50730: (e, r, t) => {
      'use strict';
      t.d(r, { Cz: () => d, Zc: () => i, ef: () => n, xj: () => o });
      var a = t(56958);
      let s = t(77051).env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
        c = a.A.create({ baseURL: s, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
        l = { log: e => {}, error: (e, r) => {} };
      c.interceptors.request.use(
        e => (l.log('Making request to: '.concat(e.baseURL).concat(e.url)), e),
        e => (l.error('Request error:', e), Promise.reject(e))
      ),
        c.interceptors.response.use(
          e => e,
          async e => {
            let { config: r } = e;
            return e.message.includes('Network Error') && r.retry > 0
              ? ((r.retry -= 1),
                l.log('Retrying request to '.concat(r.url, ', ').concat(r.retry, ' attempts left')),
                await new Promise(e => setTimeout(e, r.retryDelay)),
                c(r))
              : Promise.reject(e);
          }
        );
      let o = async function () {
          let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10;
          try {
            return (await c.get('/novels/', { params: { page: e, limit: r } })).data;
          } catch (e) {
            return (
              l.error('Error fetching novel list:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        },
        n = async e => {
          try {
            return (await c.get('/novels/'.concat(e, '/'))).data;
          } catch (e) {
            throw (l.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
          }
        },
        i = async e => {
          try {
            return (await c.get('/chapters/'.concat(e, '/'))).data;
          } catch (r) {
            throw (
              (l.error('Error fetching chapter content for chapterId '.concat(e, ':'), r),
              Error('無法載入章節內容'))
            );
          }
        },
        d = async e => {
          try {
            return (await c.get('/novels/search', { params: { query: e } })).data;
          } catch (e) {
            return (
              l.error('Error searching novels:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        };
    },
    63713: (e, r, t) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/Home',
        function () {
          return t(94119);
        },
      ]);
    },
    94119: (e, r, t) => {
      'use strict';
      t.r(r), t.d(r, { default: () => o });
      var a = t(23798),
        s = t(21462),
        c = t(71177),
        l = t(50730);
      let o = () => {
        let [e, r] = (0, s.useState)([]),
          [t, o] = (0, s.useState)(!0),
          [n, i] = (0, s.useState)(null);
        return ((0, s.useEffect)(() => {
          (async () => {
            try {
              o(!0);
              let e = await (0, l.xj)();
              r(e.novels);
            } catch (e) {
              i('無法載入小說列表'), console.error('Error fetching novels:', e);
            } finally {
              o(!1);
            }
          })();
        }, []),
        t)
          ? (0, a.jsx)('div', { className: 'text-center p-4', children: '載入中...' })
          : n
            ? (0, a.jsx)('div', { className: 'text-center text-red-500 p-4', children: n })
            : (0, a.jsx)('div', {
                className: 'max-w-6xl mx-auto p-4',
                children:
                  e.length > 0
                    ? (0, a.jsxs)('div', {
                        children: [
                          (0, a.jsx)('h2', {
                            className: 'text-2xl font-bold mb-4',
                            children: '最新小說',
                          }),
                          (0, a.jsx)('div', {
                            className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
                            children: e.map(e =>
                              (0, a.jsx)(
                                c.N_,
                                {
                                  to: '/novels/'.concat(e.slug),
                                  className:
                                    'block p-4 bg-white rounded-lg shadow hover:shadow-lg transition-shadow',
                                  children: (0, a.jsxs)('div', {
                                    className: 'flex gap-4',
                                    children: [
                                      (0, a.jsx)('img', {
                                        src:
                                          e.cover_url ||
                                          e.coverUrl ||
                                          e.coverImage ||
                                          '/default-cover.jpg',
                                        alt: e.title,
                                        className: 'w-20 h-28 object-cover rounded',
                                      }),
                                      (0, a.jsxs)('div', {
                                        children: [
                                          (0, a.jsx)('h3', {
                                            className: 'font-bold mb-1',
                                            children: e.title,
                                          }),
                                          (0, a.jsxs)('p', {
                                            className: 'text-sm text-gray-600',
                                            children: ['作者：', e.author],
                                          }),
                                          e.description &&
                                            (0, a.jsx)('p', {
                                              className: 'text-sm text-gray-500 mt-1 line-clamp-2',
                                              children: e.description,
                                            }),
                                        ],
                                      }),
                                    ],
                                  }),
                                },
                                e.id
                              )
                            ),
                          }),
                        ],
                      })
                    : !t &&
                      (0, a.jsx)('p', { className: 'text-center', children: '目前沒有小說。' }),
              });
      };
    },
  },
  e => {
    var r = r => e((e.s = r));
    e.O(0, [893, 958, 558, 636, 593, 792], () => r(63713)), (_N_E = e.O());
  },
]);
