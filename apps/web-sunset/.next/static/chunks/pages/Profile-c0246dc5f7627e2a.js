(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [39],
  {
    5879: (e, r, t) => {
      'use strict';
      t.d(r, { As: () => n }), t(23798);
      var a = t(21462);
      t(50730);
      let s = (0, a.createContext)(null),
        n = () => {
          let e = (0, a.useContext)(s);
          if (!e) throw Error('useAuth must be used within an AuthProvider');
          return e;
        };
    },
    24255: (e, r, t) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/Profile',
        function () {
          return t(37677);
        },
      ]);
    },
    37677: (e, r, t) => {
      'use strict';
      t.r(r), t.d(r, { default: () => n });
      var a = t(23798);
      t(21462);
      var s = t(5879);
      let n = () => {
        let { user: e } = (0, s.As)();
        return e
          ? (0, a.jsxs)('div', {
              className: 'max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
              children: [
                (0, a.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '個人資料' }),
                (0, a.jsxs)('div', {
                  className: 'space-y-4',
                  children: [
                    (0, a.jsxs)('div', {
                      children: [
                        (0, a.jsx)('label', { className: 'font-medium', children: '使用者名稱:' }),
                        (0, a.jsx)('p', { children: e.username }),
                      ],
                    }),
                    (0, a.jsxs)('div', {
                      children: [
                        (0, a.jsx)('label', { className: 'font-medium', children: '電子郵件:' }),
                        (0, a.jsx)('p', { children: e.email }),
                      ],
                    }),
                  ],
                }),
              ],
            })
          : (0, a.jsx)('div', { children: '請先登入' });
      };
    },
    50730: (e, r, t) => {
      'use strict';
      t.d(r, { Cz: () => u, Zc: () => i, ef: () => l, xj: () => c });
      var a = t(56958);
      let s = t(77051).env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
        n = a.A.create({ baseURL: s, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
        o = { log: e => {}, error: (e, r) => {} };
      n.interceptors.request.use(
        e => (o.log('Making request to: '.concat(e.baseURL).concat(e.url)), e),
        e => (o.error('Request error:', e), Promise.reject(e))
      ),
        n.interceptors.response.use(
          e => e,
          async e => {
            let { config: r } = e;
            return e.message.includes('Network Error') && r.retry > 0
              ? ((r.retry -= 1),
                o.log('Retrying request to '.concat(r.url, ', ').concat(r.retry, ' attempts left')),
                await new Promise(e => setTimeout(e, r.retryDelay)),
                n(r))
              : Promise.reject(e);
          }
        );
      let c = async function () {
          let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10;
          try {
            return (await n.get('/novels/', { params: { page: e, limit: r } })).data;
          } catch (e) {
            return (
              o.error('Error fetching novel list:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        },
        l = async e => {
          try {
            return (await n.get('/novels/'.concat(e, '/'))).data;
          } catch (e) {
            throw (o.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
          }
        },
        i = async e => {
          try {
            return (await n.get('/chapters/'.concat(e, '/'))).data;
          } catch (r) {
            throw (
              (o.error('Error fetching chapter content for chapterId '.concat(e, ':'), r),
              Error('無法載入章節內容'))
            );
          }
        },
        u = async e => {
          try {
            return (await n.get('/novels/search', { params: { query: e } })).data;
          } catch (e) {
            return (
              o.error('Error searching novels:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        };
    },
  },
  e => {
    var r = r => e((e.s = r));
    e.O(0, [958, 636, 593, 792], () => r(24255)), (_N_E = e.O());
  },
]);
