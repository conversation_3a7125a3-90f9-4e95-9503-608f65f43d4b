(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [153],
  {
    4415: (e, r, t) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/NovelDetail',
        function () {
          return t(48603);
        },
      ]);
    },
    48603: (e, r, t) => {
      'use strict';
      t.r(r), t.d(r, { default: () => o });
      var a = t(23798),
        s = t(21462),
        c = t(48265),
        l = t(71177),
        n = t(50730);
      let o = () => {
        let { novelSlug: e } = (0, c.g)(),
          [r, t] = (0, s.useState)(null),
          [o, i] = (0, s.useState)(!0),
          [d, h] = (0, s.useState)(null);
        return ((0, s.useEffect)(() => {
          (async () => {
            if (e)
              try {
                i(!0);
                let r = await (0, n.ef)(e);
                t(r);
              } catch (r) {
                h('無法載入小說資訊'),
                  console.error('Error fetching novel details for '.concat(e, ':'), r);
              } finally {
                i(!1);
              }
          })();
        }, [e]),
        o)
          ? (0, a.jsx)('div', { className: 'text-center p-4', children: '載入中...' })
          : d
            ? (0, a.jsx)('div', { className: 'text-center text-red-500 p-4', children: d })
            : r
              ? (0, a.jsx)('div', {
                  className: 'max-w-4xl mx-auto p-4',
                  children: (0, a.jsxs)('div', {
                    className: 'bg-white rounded-lg shadow-lg p-6',
                    children: [
                      (0, a.jsxs)('div', {
                        className: 'flex flex-col md:flex-row gap-6',
                        children: [
                          (0, a.jsx)('img', {
                            src: r.cover_url || r.coverUrl || r.coverImage || '/default-cover.jpg',
                            alt: r.title,
                            className: 'w-48 h-64 object-cover rounded-lg shadow-md',
                          }),
                          (0, a.jsxs)('div', {
                            className: 'flex-1',
                            children: [
                              (0, a.jsx)('h1', {
                                className: 'text-3xl font-bold mb-2',
                                children: r.title,
                              }),
                              (0, a.jsxs)('p', {
                                className: 'text-gray-600 mb-4',
                                children: ['作者：', r.author],
                              }),
                              (0, a.jsx)('div', {
                                className: 'space-y-2',
                                children: (0, a.jsx)('p', {
                                  className: 'text-gray-700',
                                  children: r.description || '暫無簡介',
                                }),
                              }),
                            ],
                          }),
                        ],
                      }),
                      (0, a.jsxs)('div', {
                        className: 'mt-8',
                        children: [
                          (0, a.jsx)('h2', {
                            className: 'text-xl font-bold mb-4',
                            children: '章節列表',
                          }),
                          r.chapters && r.chapters.length > 0
                            ? (0, a.jsx)('ul', {
                                className: 'space-y-2',
                                children: r.chapters.map(r =>
                                  (0, a.jsx)(
                                    'li',
                                    {
                                      className: 'border-b last:border-b-0 py-2',
                                      children: (0, a.jsx)(l.N_, {
                                        to: '/novels/'.concat(e, '/chapters/').concat(r.id),
                                        className:
                                          'text-blue-600 hover:text-blue-800 hover:underline transition-colors',
                                        children:
                                          r.title ||
                                          '第 '.concat(r.chapter_number || r.chapterNumber, ' 章'),
                                      }),
                                    },
                                    r.id
                                  )
                                ),
                              })
                            : (0, a.jsx)('p', {
                                className: 'text-gray-600',
                                children: '暫無章節。',
                              }),
                        ],
                      }),
                    ],
                  }),
                })
              : (0, a.jsx)('div', { className: 'text-center p-4', children: '找不到小說' });
      };
    },
    50730: (e, r, t) => {
      'use strict';
      t.d(r, { Cz: () => d, Zc: () => i, ef: () => o, xj: () => n });
      var a = t(56958);
      let s = t(77051).env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
        c = a.A.create({ baseURL: s, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
        l = { log: e => {}, error: (e, r) => {} };
      c.interceptors.request.use(
        e => (l.log('Making request to: '.concat(e.baseURL).concat(e.url)), e),
        e => (l.error('Request error:', e), Promise.reject(e))
      ),
        c.interceptors.response.use(
          e => e,
          async e => {
            let { config: r } = e;
            return e.message.includes('Network Error') && r.retry > 0
              ? ((r.retry -= 1),
                l.log('Retrying request to '.concat(r.url, ', ').concat(r.retry, ' attempts left')),
                await new Promise(e => setTimeout(e, r.retryDelay)),
                c(r))
              : Promise.reject(e);
          }
        );
      let n = async function () {
          let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10;
          try {
            return (await c.get('/novels/', { params: { page: e, limit: r } })).data;
          } catch (e) {
            return (
              l.error('Error fetching novel list:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        },
        o = async e => {
          try {
            return (await c.get('/novels/'.concat(e, '/'))).data;
          } catch (e) {
            throw (l.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
          }
        },
        i = async e => {
          try {
            return (await c.get('/chapters/'.concat(e, '/'))).data;
          } catch (r) {
            throw (
              (l.error('Error fetching chapter content for chapterId '.concat(e, ':'), r),
              Error('無法載入章節內容'))
            );
          }
        },
        d = async e => {
          try {
            return (await c.get('/novels/search', { params: { query: e } })).data;
          } catch (e) {
            return (
              l.error('Error searching novels:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        };
    },
  },
  e => {
    var r = r => e((e.s = r));
    e.O(0, [893, 958, 558, 636, 593, 792], () => r(4415)), (_N_E = e.O());
  },
]);
