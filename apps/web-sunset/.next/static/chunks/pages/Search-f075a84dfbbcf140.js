(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [308],
  {
    27827: (e, t, r) => {
      var s = r(82087),
        o = Object.prototype,
        a = o.hasOwnProperty,
        n = o.toString,
        l = s ? s.toStringTag : void 0;
      e.exports = function (e) {
        var t = a.call(e, l),
          r = e[l];
        try {
          e[l] = void 0;
          var s = !0;
        } catch (e) {}
        var o = n.call(e);
        return s && (t ? (e[l] = r) : delete e[l]), o;
      };
    },
    28300: e => {
      var t = /\s/;
      e.exports = function (e) {
        for (var r = e.length; r-- && t.test(e.charAt(r)); );
        return r;
      };
    },
    28811: (e, t, r) => {
      var s = r(74718),
        o = 'object' == typeof self && self && self.Object === Object && self;
      e.exports = s || o || Function('return this')();
    },
    31266: (e, t, r) => {
      var s = r(77882),
        o = r(50482);
      e.exports = function (e) {
        return 'symbol' == typeof e || (o(e) && '[object Symbol]' == s(e));
      };
    },
    42630: (e, t, r) => {
      'use strict';
      r.r(t), r.d(t, { default: () => h });
      var s = r(82536),
        o = r(93629),
        a = r(23798),
        n = r(21462),
        l = r(60929),
        i = r.n(l),
        c = r(50730);
      class u {
        constructor() {
          (this.children = new Map()), (this.isEndOfWord = !1), (this.word = '');
        }
      }
      class d {
        insert(e) {
          let t = this.root;
          for (let r of e.toLowerCase())
            t.children.has(r) || t.children.set(r, new u()), (t = t.children.get(r));
          (t.isEndOfWord = !0), (t.word = e);
        }
        search(e) {
          let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 5,
            r = [],
            s = this.findNode(e.toLowerCase());
          return s && this.dfs(s, r, t), r;
        }
        findNode(e) {
          let t = this.root;
          for (let r of e) {
            if (!t.children.has(r)) return null;
            t = t.children.get(r);
          }
          return t;
        }
        dfs(e, t, r) {
          if (!(t.length >= r))
            for (let s of (e.isEndOfWord && t.push(e.word), Array.from(e.children.values())))
              this.dfs(s, t, r);
        }
        constructor() {
          this.root = new u();
        }
      }
      let h = () => {
        let [e, t] = (0, n.useState)(''),
          [r, l] = (0, n.useState)(!1),
          [u, h] = (0, n.useState)([]),
          [f, p] = (0, n.useState)({ category: '', status: '' }),
          [m, g] = (0, n.useState)([]),
          [x, v] = (0, n.useState)(!1),
          [b, y] = (0, n.useState)([]),
          j = (0, n.useMemo)(() => {
            let e = new d();
            return (
              [
                '斗羅大陸',
                '鬥破蒼穹',
                '完美世界',
                '一念永恆',
                '我欲封天',
                '仙逆',
                '星辰變',
                '盤龍',
                '吞噬星空',
                '武動乾坤',
                '大主宰',
                '元尊',
                '天蠶土豆',
                '唐家三少',
                '我吃西紅柿',
              ].forEach(t => e.insert(t)),
              e
            );
          }, []);
        (0, n.useEffect)(() => {
          let e = localStorage.getItem('searchHistory');
          if (e)
            try {
              let t = JSON.parse(e);
              g(t.slice(0, 10));
            } catch (e) {
              console.error('Failed to parse search history:', e);
            }
        }, []);
        let w = (0, n.useCallback)(
            e => {
              if (!e.trim()) return;
              let t = [{ query: e, timestamp: Date.now() }, ...m.filter(t => t.query !== e)].slice(
                0,
                10
              );
              g(t), localStorage.setItem('searchHistory', JSON.stringify(t));
            },
            [m]
          ),
          N = (0, n.useCallback)(
            async (e, t) => {
              if (!e.trim()) return void h([]);
              l(!0);
              try {
                let r = (await (0, c.Cz)(e)).novels || [];
                t.category && (r = r.filter(e => e.category === t.category)),
                  t.status && (r = r.filter(e => e.status === t.status)),
                  h(r),
                  w(e);
              } catch (e) {
                console.error('Search failed:', e), h([]);
              } finally {
                l(!1);
              }
            },
            [w]
          ),
          E = (0, n.useMemo)(() => i()(N, 500), [N]),
          S = e => {
            if ((t(e), e.trim())) {
              let t = [
                ...new Set([
                  ...j.search(e, 5),
                  ...m
                    .filter(t => t.query.toLowerCase().includes(e.toLowerCase()))
                    .map(e => e.query)
                    .slice(0, 3),
                ]),
              ].slice(0, 5);
              y(t), v(t.length > 0);
            } else y([]), v(!1);
            E(e, f);
          },
          O = e => {
            t(e), v(!1), E(e, f);
          },
          _ = (t, r) => {
            let a = (0, o._)((0, s._)({}, f), { [t]: r });
            p(a), e.trim() && E(e, a);
          };
        return (0, a.jsxs)('div', {
          className: 'max-w-4xl mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
          children: [
            (0, a.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '搜尋小說' }),
            (0, a.jsxs)('div', {
              className: 'mb-8 relative',
              children: [
                (0, a.jsxs)('div', {
                  className: 'flex gap-2 mb-4',
                  children: [
                    (0, a.jsxs)('div', {
                      className: 'flex-1 relative',
                      children: [
                        (0, a.jsx)('input', {
                          type: 'text',
                          value: e,
                          onChange: e => S(e.target.value),
                          onFocus: () => b.length > 0 && v(!0),
                          onBlur: () => setTimeout(() => v(!1), 200),
                          placeholder: '輸入小說名稱、作者或關鍵字',
                          className:
                            'w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500',
                        }),
                        x &&
                          b.length > 0 &&
                          (0, a.jsx)('div', {
                            className:
                              'absolute top-full left-0 right-0 bg-white border border-t-0 rounded-b-md shadow-lg z-10',
                            children: b.map((e, t) =>
                              (0, a.jsx)(
                                'div',
                                {
                                  className: 'p-2 hover:bg-gray-100 cursor-pointer text-sm',
                                  onMouseDown: () => O(e),
                                  children: e,
                                },
                                t
                              )
                            ),
                          }),
                      ],
                    }),
                    (0, a.jsx)('button', {
                      type: 'button',
                      disabled: r,
                      className:
                        'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300',
                      children: r ? '搜尋中...' : '搜尋',
                    }),
                  ],
                }),
                (0, a.jsxs)('div', {
                  className: 'flex gap-4 flex-wrap',
                  children: [
                    (0, a.jsxs)('select', {
                      value: f.category,
                      onChange: e => _('category', e.target.value),
                      className:
                        'p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500',
                      children: [
                        (0, a.jsx)('option', { value: '', children: '所有分類' }),
                        (0, a.jsx)('option', { value: 'fantasy', children: '玄幻' }),
                        (0, a.jsx)('option', { value: 'cultivation', children: '修真' }),
                        (0, a.jsx)('option', { value: 'urban', children: '都市' }),
                        (0, a.jsx)('option', { value: 'romance', children: '言情' }),
                        (0, a.jsx)('option', { value: 'historical', children: '歷史' }),
                      ],
                    }),
                    (0, a.jsxs)('select', {
                      value: f.status,
                      onChange: e => _('status', e.target.value),
                      className:
                        'p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500',
                      children: [
                        (0, a.jsx)('option', { value: '', children: '所有狀態' }),
                        (0, a.jsx)('option', { value: 'completed', children: '已完結' }),
                        (0, a.jsx)('option', { value: 'ongoing', children: '連載中' }),
                        (0, a.jsx)('option', { value: 'paused', children: '暫停' }),
                      ],
                    }),
                  ],
                }),
              ],
            }),
            m.length > 0 &&
              !e &&
              (0, a.jsxs)('div', {
                className: 'mb-6 p-4 bg-gray-50 rounded',
                children: [
                  (0, a.jsxs)('div', {
                    className: 'flex justify-between items-center mb-2',
                    children: [
                      (0, a.jsx)('h3', {
                        className: 'text-sm font-semibold text-gray-700',
                        children: '搜索歷史',
                      }),
                      (0, a.jsx)('button', {
                        onClick: () => {
                          g([]), localStorage.removeItem('searchHistory');
                        },
                        className: 'text-xs text-blue-500 hover:text-blue-700',
                        children: '清除',
                      }),
                    ],
                  }),
                  (0, a.jsx)('div', {
                    className: 'flex flex-wrap gap-2',
                    children: m
                      .slice(0, 5)
                      .map((e, t) =>
                        (0, a.jsx)(
                          'button',
                          {
                            onClick: () => S(e.query),
                            className:
                              'px-3 py-1 text-sm bg-white border rounded hover:bg-gray-100',
                            children: e.query,
                          },
                          t
                        )
                      ),
                  }),
                ],
              }),
            u.length > 0
              ? (0, a.jsx)('div', {
                  className: 'grid grid-cols-1 md:grid-cols-2 gap-4',
                  children: u.map(e =>
                    (0, a.jsxs)(
                      'div',
                      {
                        className:
                          'flex gap-4 p-4 border rounded hover:shadow-md transition-shadow',
                        children: [
                          (0, a.jsx)('img', {
                            src: e.cover || '/placeholder-book.png',
                            alt: e.title,
                            className: 'w-24 h-32 object-cover rounded',
                            onError: e => {
                              e.target.src = '/placeholder-book.png';
                            },
                          }),
                          (0, a.jsxs)('div', {
                            className: 'flex-1',
                            children: [
                              (0, a.jsx)('h2', {
                                className: 'text-lg font-semibold mb-1',
                                children: e.title,
                              }),
                              (0, a.jsxs)('p', {
                                className: 'text-sm text-gray-600 mb-1',
                                children: ['作者：', e.author],
                              }),
                              e.category &&
                                (0, a.jsxs)('p', {
                                  className: 'text-xs text-blue-600 mb-1',
                                  children: ['分類：', e.category],
                                }),
                              e.status &&
                                (0, a.jsxs)('p', {
                                  className: 'text-xs text-green-600 mb-2',
                                  children: ['狀態：', e.status],
                                }),
                              (0, a.jsx)('p', {
                                className: 'text-sm text-gray-700 line-clamp-2',
                                children: e.description,
                              }),
                            ],
                          }),
                        ],
                      },
                      e.id
                    )
                  ),
                })
              : (0, a.jsx)('div', {
                  className: 'text-center py-8',
                  children: r
                    ? (0, a.jsxs)('div', {
                        className: 'flex justify-center items-center',
                        children: [
                          (0, a.jsx)('div', {
                            className:
                              'animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500',
                          }),
                          (0, a.jsx)('span', {
                            className: 'ml-2 text-gray-600',
                            children: '搜尋中...',
                          }),
                        ],
                      })
                    : (0, a.jsx)('p', {
                        className: 'text-gray-500',
                        children: e ? '沒有找到相關小說' : '請輸入搜尋關鍵字',
                      }),
                }),
          ],
        });
      };
    },
    50482: e => {
      e.exports = function (e) {
        return null != e && 'object' == typeof e;
      };
    },
    50730: (e, t, r) => {
      'use strict';
      r.d(t, { Cz: () => u, Zc: () => c, ef: () => i, xj: () => l });
      var s = r(56958);
      let o = r(77051).env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
        a = s.A.create({ baseURL: o, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
        n = { log: e => {}, error: (e, t) => {} };
      a.interceptors.request.use(
        e => (n.log('Making request to: '.concat(e.baseURL).concat(e.url)), e),
        e => (n.error('Request error:', e), Promise.reject(e))
      ),
        a.interceptors.response.use(
          e => e,
          async e => {
            let { config: t } = e;
            return e.message.includes('Network Error') && t.retry > 0
              ? ((t.retry -= 1),
                n.log('Retrying request to '.concat(t.url, ', ').concat(t.retry, ' attempts left')),
                await new Promise(e => setTimeout(e, t.retryDelay)),
                a(t))
              : Promise.reject(e);
          }
        );
      let l = async function () {
          let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10;
          try {
            return (await a.get('/novels/', { params: { page: e, limit: t } })).data;
          } catch (e) {
            return (
              n.error('Error fetching novel list:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        },
        i = async e => {
          try {
            return (await a.get('/novels/'.concat(e, '/'))).data;
          } catch (e) {
            throw (n.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
          }
        },
        c = async e => {
          try {
            return (await a.get('/chapters/'.concat(e, '/'))).data;
          } catch (t) {
            throw (
              (n.error('Error fetching chapter content for chapterId '.concat(e, ':'), t),
              Error('無法載入章節內容'))
            );
          }
        },
        u = async e => {
          try {
            return (await a.get('/novels/search', { params: { query: e } })).data;
          } catch (e) {
            return (
              n.error('Error searching novels:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        };
    },
    54557: e => {
      e.exports = function (e) {
        var t = typeof e;
        return null != e && ('object' == t || 'function' == t);
      };
    },
    60929: (e, t, r) => {
      var s = r(54557),
        o = r(98142),
        a = r(68906),
        n = Math.max,
        l = Math.min;
      e.exports = function (e, t, r) {
        var i,
          c,
          u,
          d,
          h,
          f,
          p = 0,
          m = !1,
          g = !1,
          x = !0;
        if ('function' != typeof e) throw TypeError('Expected a function');
        function v(t) {
          var r = i,
            s = c;
          return (i = c = void 0), (p = t), (d = e.apply(s, r));
        }
        function b(e) {
          var r = e - f,
            s = e - p;
          return void 0 === f || r >= t || r < 0 || (g && s >= u);
        }
        function y() {
          var e,
            r,
            s,
            a = o();
          if (b(a)) return j(a);
          h = setTimeout(y, ((e = a - f), (r = a - p), (s = t - e), g ? l(s, u - r) : s));
        }
        function j(e) {
          return ((h = void 0), x && i) ? v(e) : ((i = c = void 0), d);
        }
        function w() {
          var e,
            r = o(),
            s = b(r);
          if (((i = arguments), (c = this), (f = r), s)) {
            if (void 0 === h) return (p = e = f), (h = setTimeout(y, t)), m ? v(e) : d;
            if (g) return clearTimeout(h), (h = setTimeout(y, t)), v(f);
          }
          return void 0 === h && (h = setTimeout(y, t)), d;
        }
        return (
          (t = a(t) || 0),
          s(r) &&
            ((m = !!r.leading),
            (u = (g = 'maxWait' in r) ? n(a(r.maxWait) || 0, t) : u),
            (x = 'trailing' in r ? !!r.trailing : x)),
          (w.cancel = function () {
            void 0 !== h && clearTimeout(h), (p = 0), (i = f = c = h = void 0);
          }),
          (w.flush = function () {
            return void 0 === h ? d : j(o());
          }),
          w
        );
      };
    },
    68906: (e, t, r) => {
      var s = r(91826),
        o = r(54557),
        a = r(31266),
        n = 0 / 0,
        l = /^[-+]0x[0-9a-f]+$/i,
        i = /^0b[01]+$/i,
        c = /^0o[0-7]+$/i,
        u = parseInt;
      e.exports = function (e) {
        if ('number' == typeof e) return e;
        if (a(e)) return n;
        if (o(e)) {
          var t = 'function' == typeof e.valueOf ? e.valueOf() : e;
          e = o(t) ? t + '' : t;
        }
        if ('string' != typeof e) return 0 === e ? e : +e;
        e = s(e);
        var r = i.test(e);
        return r || c.test(e) ? u(e.slice(2), r ? 2 : 8) : l.test(e) ? n : +e;
      };
    },
    74718: (e, t, r) => {
      e.exports = 'object' == typeof r.g && r.g && r.g.Object === Object && r.g;
    },
    75059: (e, t, r) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/Search',
        function () {
          return r(42630);
        },
      ]);
    },
    77882: (e, t, r) => {
      var s = r(82087),
        o = r(27827),
        a = r(92160),
        n = s ? s.toStringTag : void 0;
      e.exports = function (e) {
        return null == e
          ? void 0 === e
            ? '[object Undefined]'
            : '[object Null]'
          : n && n in Object(e)
            ? o(e)
            : a(e);
      };
    },
    82087: (e, t, r) => {
      e.exports = r(28811).Symbol;
    },
    91826: (e, t, r) => {
      var s = r(28300),
        o = /^\s+/;
      e.exports = function (e) {
        return e ? e.slice(0, s(e) + 1).replace(o, '') : e;
      };
    },
    92160: e => {
      var t = Object.prototype.toString;
      e.exports = function (e) {
        return t.call(e);
      };
    },
    98142: (e, t, r) => {
      var s = r(28811);
      e.exports = function () {
        return s.Date.now();
      };
    },
  },
  e => {
    var t = t => e((e.s = t));
    e.O(0, [958, 636, 593, 792], () => t(75059)), (_N_E = e.O());
  },
]);
