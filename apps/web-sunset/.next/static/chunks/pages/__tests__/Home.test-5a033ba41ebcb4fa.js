(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [415],
  {
    22179: () => {},
    32999: (e, t, r) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/__tests__/Home.test',
        function () {
          return r(41213);
        },
      ]);
    },
    41213: (e, t, r) => {
      'use strict';
      r.r(t);
      var a = r(23798),
        s = r(21462),
        c = r(56733),
        o = r(48265),
        l = r(94119),
        n = r(50730);
      jest.mock('../../services/api', () => ({ __esModule: !0, getNovelList: jest.fn() })),
        describe('<Home />', () => {
          beforeEach(() => {
            jest.clearAllMocks();
          }),
            it('should fetch and display novels on initial render', async () => {
              n.xj.mockResolvedValue({
                novels: [
                  { id: '1', title: 'Novel 1', author: 'Author 1', cover: '', description: '' },
                ],
                total: 1,
              }),
                await (0, s.act)(async () => {
                  (0, c.XX)((0, a.jsx)(o.fS, { children: (0, a.jsx)(l.default, {}) }));
                }),
                expect(n.xj).toHaveBeenCalledTimes(1),
                expect(n.xj).toHaveBeenCalledWith(),
                await (0, s.act)(async () => {
                  expect(await c.nj.findByText('Novel 1')).toBeInTheDocument();
                });
            }),
            it('should display loading state while fetching novels', () => {
              n.xj.mockImplementation(() => new Promise(() => {})),
                (0, c.XX)((0, a.jsx)(o.fS, { children: (0, a.jsx)(l.default, {}) })),
                expect(c.nj.getByText('載入中...')).toBeInTheDocument();
            });
        });
    },
    50730: (e, t, r) => {
      'use strict';
      r.d(t, { Cz: () => d, Zc: () => i, ef: () => n, xj: () => l });
      var a = r(56958);
      let s = r(77051).env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
        c = a.A.create({ baseURL: s, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
        o = { log: e => {}, error: (e, t) => {} };
      c.interceptors.request.use(
        e => (o.log('Making request to: '.concat(e.baseURL).concat(e.url)), e),
        e => (o.error('Request error:', e), Promise.reject(e))
      ),
        c.interceptors.response.use(
          e => e,
          async e => {
            let { config: t } = e;
            return e.message.includes('Network Error') && t.retry > 0
              ? ((t.retry -= 1),
                o.log('Retrying request to '.concat(t.url, ', ').concat(t.retry, ' attempts left')),
                await new Promise(e => setTimeout(e, t.retryDelay)),
                c(t))
              : Promise.reject(e);
          }
        );
      let l = async function () {
          let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10;
          try {
            return (await c.get('/novels/', { params: { page: e, limit: t } })).data;
          } catch (e) {
            return (
              o.error('Error fetching novel list:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        },
        n = async e => {
          try {
            return (await c.get('/novels/'.concat(e, '/'))).data;
          } catch (e) {
            throw (o.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
          }
        },
        i = async e => {
          try {
            return (await c.get('/chapters/'.concat(e, '/'))).data;
          } catch (t) {
            throw (
              (o.error('Error fetching chapter content for chapterId '.concat(e, ':'), t),
              Error('無法載入章節內容'))
            );
          }
        },
        d = async e => {
          try {
            return (await c.get('/novels/search', { params: { query: e } })).data;
          } catch (e) {
            return (
              o.error('Error searching novels:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        };
    },
    94119: (e, t, r) => {
      'use strict';
      r.r(t), r.d(t, { default: () => l });
      var a = r(23798),
        s = r(21462),
        c = r(71177),
        o = r(50730);
      let l = () => {
        let [e, t] = (0, s.useState)([]),
          [r, l] = (0, s.useState)(!0),
          [n, i] = (0, s.useState)(null);
        return ((0, s.useEffect)(() => {
          (async () => {
            try {
              l(!0);
              let e = await (0, o.xj)();
              t(e.novels);
            } catch (e) {
              i('無法載入小說列表'), console.error('Error fetching novels:', e);
            } finally {
              l(!1);
            }
          })();
        }, []),
        r)
          ? (0, a.jsx)('div', { className: 'text-center p-4', children: '載入中...' })
          : n
            ? (0, a.jsx)('div', { className: 'text-center text-red-500 p-4', children: n })
            : (0, a.jsx)('div', {
                className: 'max-w-6xl mx-auto p-4',
                children:
                  e.length > 0
                    ? (0, a.jsxs)('div', {
                        children: [
                          (0, a.jsx)('h2', {
                            className: 'text-2xl font-bold mb-4',
                            children: '最新小說',
                          }),
                          (0, a.jsx)('div', {
                            className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
                            children: e.map(e =>
                              (0, a.jsx)(
                                c.N_,
                                {
                                  to: '/novels/'.concat(e.slug),
                                  className:
                                    'block p-4 bg-white rounded-lg shadow hover:shadow-lg transition-shadow',
                                  children: (0, a.jsxs)('div', {
                                    className: 'flex gap-4',
                                    children: [
                                      (0, a.jsx)('img', {
                                        src:
                                          e.cover_url ||
                                          e.coverUrl ||
                                          e.coverImage ||
                                          '/default-cover.jpg',
                                        alt: e.title,
                                        className: 'w-20 h-28 object-cover rounded',
                                      }),
                                      (0, a.jsxs)('div', {
                                        children: [
                                          (0, a.jsx)('h3', {
                                            className: 'font-bold mb-1',
                                            children: e.title,
                                          }),
                                          (0, a.jsxs)('p', {
                                            className: 'text-sm text-gray-600',
                                            children: ['作者：', e.author],
                                          }),
                                          e.description &&
                                            (0, a.jsx)('p', {
                                              className: 'text-sm text-gray-500 mt-1 line-clamp-2',
                                              children: e.description,
                                            }),
                                        ],
                                      }),
                                    ],
                                  }),
                                },
                                e.id
                              )
                            ),
                          }),
                        ],
                      })
                    : !r &&
                      (0, a.jsx)('p', { className: 'text-center', children: '目前沒有小說。' }),
              });
      };
    },
  },
  e => {
    var t = t => e((e.s = t));
    e.O(0, [593, 893, 958, 558, 733, 636, 792], () => t(32999)), (_N_E = e.O());
  },
]);
