(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [100],
  {
    7623: (e, r, t) => {
      (window.__NEXT_P = window.__NEXT_P || []).push([
        '/ChapterDetail',
        function () {
          return t(83374);
        },
      ]);
    },
    50730: (e, r, t) => {
      'use strict';
      t.d(r, { Cz: () => h, Zc: () => i, ef: () => o, xj: () => l });
      var a = t(56958);
      let s = t(77051).env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
        c = a.A.create({ baseURL: s, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
        n = { log: e => {}, error: (e, r) => {} };
      c.interceptors.request.use(
        e => (n.log('Making request to: '.concat(e.baseURL).concat(e.url)), e),
        e => (n.error('Request error:', e), Promise.reject(e))
      ),
        c.interceptors.response.use(
          e => e,
          async e => {
            let { config: r } = e;
            return e.message.includes('Network Error') && r.retry > 0
              ? ((r.retry -= 1),
                n.log('Retrying request to '.concat(r.url, ', ').concat(r.retry, ' attempts left')),
                await new Promise(e => setTimeout(e, r.retryDelay)),
                c(r))
              : Promise.reject(e);
          }
        );
      let l = async function () {
          let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 10;
          try {
            return (await c.get('/novels/', { params: { page: e, limit: r } })).data;
          } catch (e) {
            return (
              n.error('Error fetching novel list:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        },
        o = async e => {
          try {
            return (await c.get('/novels/'.concat(e, '/'))).data;
          } catch (e) {
            throw (n.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
          }
        },
        i = async e => {
          try {
            return (await c.get('/chapters/'.concat(e, '/'))).data;
          } catch (r) {
            throw (
              (n.error('Error fetching chapter content for chapterId '.concat(e, ':'), r),
              Error('無法載入章節內容'))
            );
          }
        },
        h = async e => {
          try {
            return (await c.get('/novels/search', { params: { query: e } })).data;
          } catch (e) {
            return (
              n.error('Error searching novels:', e), { novels: [], total: 0, page: 1, limit: 10 }
            );
          }
        };
    },
    83374: (e, r, t) => {
      'use strict';
      t.r(r), t.d(r, { default: () => o });
      var a = t(23798),
        s = t(21462),
        c = t(48265),
        n = t(71177),
        l = t(50730);
      let o = () => {
        let e = (0, c.Zp)(),
          { novelSlug: r, chapterId: t } = (0, c.g)(),
          [o, i] = (0, s.useState)(null),
          [h, d] = (0, s.useState)(!0),
          [u, p] = (0, s.useState)(null);
        return ((0, s.useEffect)(() => {
          (async () => {
            if (!r || !t) return e('/');
            try {
              d(!0);
              let e = await (0, l.Zc)(t);
              if (!e || !e.content) throw Error('章節內容為空或無效');
              i(e);
            } catch (a) {
              let e = '無法載入章節內容';
              a.response && a.response.data && a.response.data.detail
                ? (e = a.response.data.detail)
                : a.message && (e = a.message),
                p(e),
                console.error('Error fetching chapter:', {
                  error: a,
                  novelSlug: r,
                  chapterId: t,
                  errorMessage: e,
                });
            } finally {
              d(!1);
            }
          })();
        }, [r, t, e]),
        h)
          ? (0, a.jsx)('div', { className: 'text-center p-4', children: '載入中...' })
          : u
            ? (0, a.jsx)('div', { className: 'text-center text-red-500 p-4', children: u })
            : o
              ? (0, a.jsx)('div', {
                  className: 'max-w-4xl mx-auto p-4',
                  children: (0, a.jsxs)('div', {
                    className: 'bg-white rounded-lg shadow-lg p-6',
                    children: [
                      (0, a.jsxs)('div', {
                        className: 'mb-6',
                        children: [
                          (0, a.jsx)('h1', {
                            className: 'text-2xl font-bold mb-2',
                            children:
                              o.title || '第 '.concat(o.chapter_number || o.chapterNumber, ' 章'),
                          }),
                          o.title &&
                            (o.chapter_number || o.chapterNumber) &&
                            (0, a.jsxs)('p', {
                              className: 'text-lg text-gray-600',
                              children: ['第 ', o.chapter_number || o.chapterNumber, ' 章'],
                            }),
                        ],
                      }),
                      (0, a.jsx)('div', {
                        className: 'prose max-w-none mb-8',
                        children: o.content
                          ? o.content
                              .split('\n')
                              .map((e, r) => (0, a.jsx)('p', { className: 'mb-4', children: e }, r))
                          : (0, a.jsx)('p', {
                              className: 'text-gray-600',
                              children: '章節內容載入中...',
                            }),
                      }),
                      (0, a.jsxs)('div', {
                        className: 'flex justify-center items-center',
                        children: [
                          ' ',
                          (0, a.jsx)(n.N_, {
                            to: '/novels/'.concat(r),
                            className: 'px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded',
                            children: '返回目錄',
                          }),
                        ],
                      }),
                    ],
                  }),
                })
              : (0, a.jsx)('div', { className: 'text-center p-4', children: '找不到章節' });
      };
    },
  },
  e => {
    var r = r => e((e.s = r));
    e.O(0, [893, 958, 558, 636, 593, 792], () => r(7623)), (_N_E = e.O());
  },
]);
