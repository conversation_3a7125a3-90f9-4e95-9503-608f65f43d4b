.FontSizeSlider_slider__gpE_E {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 0.5rem;
  border-radius: 0.5rem;
  outline: none;
  transition: opacity 0.2s;
}
.FontSizeSlider_slider__gpE_E::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  -webkit-transition: box-shadow 0.15s ease-in-out;
  transition: box-shadow 0.15s ease-in-out;
}
.FontSizeSlider_slider__gpE_E::-webkit-slider-thumb:hover {
  box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.1);
}
.FontSizeSlider_slider__gpE_E::-webkit-slider-thumb:active {
  box-shadow: 0 0 0 12px rgba(59, 130, 246, 0.2);
}
.FontSizeSlider_slider__gpE_E::-moz-range-thumb {
  width: 1.25rem;
  height: 1.25rem;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  -moz-transition: box-shadow 0.15s ease-in-out;
  transition: box-shadow 0.15s ease-in-out;
}
.FontSizeSlider_slider__gpE_E::-moz-range-thumb:hover {
  box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.1);
}
.FontSizeSlider_slider__gpE_E::-moz-range-thumb:active {
  box-shadow: 0 0 0 12px rgba(59, 130, 246, 0.2);
}
.FontSizeSlider_slider__gpE_E:focus {
  outline: none;
}
.FontSizeSlider_slider__gpE_E:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25);
}
.FontSizeSlider_slider__gpE_E:focus::-moz-range-thumb {
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25);
}
