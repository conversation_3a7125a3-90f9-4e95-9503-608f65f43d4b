'use strict';
(() => {
  var e = {};
  (e.id = 415),
    (e.ids = [220, 415]),
    (e.modules = {
      208: (e, t) => {
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return r;
          },
        });
        var r = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, r) => {
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return i;
            },
          });
        let a = r(8752),
          s = r(6351),
          n = a._(r(2015)),
          o = r(5310);
        async function l(e) {
          let { Component: t, ctx: r } = e;
          return { pageProps: await (0, o.loadGetInitialProps)(t, r) };
        }
        class i extends n.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, s.jsx)(e, { ...t });
          }
        }
        (i.origGetInitialProps = l),
          (i.getInitialProps = l),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      802: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.d(t, { Cz: () => p, Zc: () => u, ef: () => d, xj: () => c });
            var s = r(1428),
              n = e([s]);
            s = (n.then ? (await n)() : n)[0];
            let o = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
              l = s.default.create({ baseURL: o, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
              i = { log: e => {}, error: (e, t) => {} };
            l.interceptors.request.use(
              e => (i.log(`Making request to: ${e.baseURL}${e.url}`), e),
              e => (i.error('Request error:', e), Promise.reject(e))
            ),
              l.interceptors.response.use(
                e => e,
                async e => {
                  let { config: t } = e;
                  return e.message.includes('Network Error') && t.retry > 0
                    ? ((t.retry -= 1),
                      i.log(`Retrying request to ${t.url}, ${t.retry} attempts left`),
                      await new Promise(e => setTimeout(e, t.retryDelay)),
                      l(t))
                    : Promise.reject(e);
                }
              );
            let c = async (e = 1, t = 10) => {
                try {
                  return (await l.get('/novels/', { params: { page: e, limit: t } })).data;
                } catch (e) {
                  return (
                    i.error('Error fetching novel list:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              },
              d = async e => {
                try {
                  return (await l.get(`/novels/${e}/`)).data;
                } catch (e) {
                  throw (i.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
                }
              },
              u = async e => {
                try {
                  return (await l.get(`/chapters/${e}/`)).data;
                } catch (t) {
                  throw (
                    (i.error(`Error fetching chapter content for chapterId ${e}:`, t),
                    Error('無法載入章節內容'))
                  );
                }
              },
              p = async e => {
                try {
                  return (await l.get('/novels/search', { params: { query: e } })).data;
                } catch (e) {
                  return (
                    i.error('Error searching novels:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      1428: e => {
        e.exports = import('axios');
      },
      1892: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t);
            var s = r(6351),
              n = r(2015),
              o = r(5270),
              l = r(4822),
              i = r(2213),
              c = r(802),
              d = e([i, c]);
            ([i, c] = d.then ? (await d)() : d),
              jest.mock('../../services/api', () => ({ __esModule: !0, getNovelList: jest.fn() })),
              describe('<Home />', () => {
                beforeEach(() => {
                  jest.clearAllMocks();
                }),
                  it('should fetch and display novels on initial render', async () => {
                    c.xj.mockResolvedValue({
                      novels: [
                        {
                          id: '1',
                          title: 'Novel 1',
                          author: 'Author 1',
                          cover: '',
                          description: '',
                        },
                      ],
                      total: 1,
                    }),
                      await (0, n.act)(async () => {
                        (0, o.render)(
                          (0, s.jsx)(l.MemoryRouter, { children: (0, s.jsx)(i.default, {}) })
                        );
                      }),
                      expect(c.xj).toHaveBeenCalledTimes(1),
                      expect(c.xj).toHaveBeenCalledWith(),
                      await (0, n.act)(async () => {
                        expect(await o.screen.findByText('Novel 1')).toBeInTheDocument();
                      });
                  }),
                  it('should display loading state while fetching novels', () => {
                    c.xj.mockImplementation(() => new Promise(() => {})),
                      (0, o.render)(
                        (0, s.jsx)(l.MemoryRouter, { children: (0, s.jsx)(i.default, {}) })
                      ),
                      expect(o.screen.getByText('載入中...')).toBeInTheDocument();
                  });
              }),
              a();
          } catch (e) {
            a(e);
          }
        });
      },
      2015: e => {
        e.exports = require('react');
      },
      2213: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t), r.d(t, { default: () => c });
            var s = r(6351),
              n = r(2015),
              o = r(4822),
              l = r(802),
              i = e([l]);
            l = (i.then ? (await i)() : i)[0];
            let c = () => {
              let [e, t] = (0, n.useState)([]),
                [r, a] = (0, n.useState)(!0),
                [i, c] = (0, n.useState)(null);
              return ((0, n.useEffect)(() => {
                (async () => {
                  try {
                    a(!0);
                    let e = await (0, l.xj)();
                    t(e.novels);
                  } catch (e) {
                    c('無法載入小說列表'), console.error('Error fetching novels:', e);
                  } finally {
                    a(!1);
                  }
                })();
              }, []),
              r)
                ? (0, s.jsx)('div', { className: 'text-center p-4', children: '載入中...' })
                : i
                  ? (0, s.jsx)('div', { className: 'text-center text-red-500 p-4', children: i })
                  : (0, s.jsx)('div', {
                      className: 'max-w-6xl mx-auto p-4',
                      children:
                        e.length > 0
                          ? (0, s.jsxs)('div', {
                              children: [
                                (0, s.jsx)('h2', {
                                  className: 'text-2xl font-bold mb-4',
                                  children: '最新小說',
                                }),
                                (0, s.jsx)('div', {
                                  className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
                                  children: e.map(e =>
                                    (0, s.jsx)(
                                      o.Link,
                                      {
                                        to: `/novels/${e.slug}`,
                                        className:
                                          'block p-4 bg-white rounded-lg shadow hover:shadow-lg transition-shadow',
                                        children: (0, s.jsxs)('div', {
                                          className: 'flex gap-4',
                                          children: [
                                            (0, s.jsx)('img', {
                                              src:
                                                e.cover_url ||
                                                e.coverUrl ||
                                                e.coverImage ||
                                                '/default-cover.jpg',
                                              alt: e.title,
                                              className: 'w-20 h-28 object-cover rounded',
                                            }),
                                            (0, s.jsxs)('div', {
                                              children: [
                                                (0, s.jsx)('h3', {
                                                  className: 'font-bold mb-1',
                                                  children: e.title,
                                                }),
                                                (0, s.jsxs)('p', {
                                                  className: 'text-sm text-gray-600',
                                                  children: ['作者：', e.author],
                                                }),
                                                e.description &&
                                                  (0, s.jsx)('p', {
                                                    className:
                                                      'text-sm text-gray-500 mt-1 line-clamp-2',
                                                    children: e.description,
                                                  }),
                                              ],
                                            }),
                                          ],
                                        }),
                                      },
                                      e.id
                                    )
                                  ),
                                }),
                              ],
                            })
                          : !r &&
                            (0, s.jsx)('p', {
                              className: 'text-center',
                              children: '目前沒有小說。',
                            }),
                    });
            };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      3873: e => {
        e.exports = require('path');
      },
      4822: e => {
        e.exports = require('react-router-dom');
      },
      5270: e => {
        e.exports = require('@testing-library/react');
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
      7206: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t),
              r.d(t, {
                config: () => f,
                default: () => h,
                getServerSideProps: () => x,
                getStaticPaths: () => m,
                getStaticProps: () => g,
                reportWebVitals: () => v,
                routeModule: () => w,
                unstable_getServerProps: () => _,
                unstable_getServerSideProps: () => b,
                unstable_getStaticParams: () => j,
                unstable_getStaticPaths: () => P,
                unstable_getStaticProps: () => y,
              });
            var s = r(718),
              n = r(208),
              o = r(8732),
              l = r(5488),
              i = r.n(l),
              c = r(752),
              d = r.n(c),
              u = r(1892),
              p = e([u]);
            u = (p.then ? (await p)() : p)[0];
            let h = (0, o.M)(u, 'default'),
              g = (0, o.M)(u, 'getStaticProps'),
              m = (0, o.M)(u, 'getStaticPaths'),
              x = (0, o.M)(u, 'getServerSideProps'),
              f = (0, o.M)(u, 'config'),
              v = (0, o.M)(u, 'reportWebVitals'),
              y = (0, o.M)(u, 'unstable_getStaticProps'),
              P = (0, o.M)(u, 'unstable_getStaticPaths'),
              j = (0, o.M)(u, 'unstable_getStaticParams'),
              _ = (0, o.M)(u, 'unstable_getServerProps'),
              b = (0, o.M)(u, 'unstable_getServerSideProps'),
              w = new s.PagesRouteModule({
                definition: {
                  kind: n.A.PAGES,
                  page: '/__tests__/Home.test',
                  pathname: '/__tests__/Home.test',
                  bundlePath: '',
                  filename: '',
                },
                components: { App: d(), Document: i() },
                userland: u,
              });
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      8732: (e, t) => {
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, r) {
              return r in t
                ? t[r]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, r))
                  : 'function' == typeof t && 'default' === r
                    ? t
                    : void 0;
            };
          },
        });
      },
    });
  var t = require('../../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    a = t.X(0, [488], () => r(7206));
  module.exports = a;
})();
