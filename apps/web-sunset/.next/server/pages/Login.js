'use strict';
(() => {
  var e = {};
  (e.id = 615),
    (e.ids = [220, 615]),
    (e.modules = {
      208: (e, t) => {
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return r;
          },
        });
        var r = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, r) => {
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return i;
            },
          });
        let a = r(8752),
          n = r(6351),
          s = a._(r(2015)),
          o = r(5310);
        async function l(e) {
          let { Component: t, ctx: r } = e;
          return { pageProps: await (0, o.loadGetInitialProps)(t, r) };
        }
        class i extends s.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, n.jsx)(e, { ...t });
          }
        }
        (i.origGetInitialProps = l),
          (i.getInitialProps = l),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      802: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.d(t, { Cz: () => p, Zc: () => d, ef: () => c, xj: () => u });
            var n = r(1428),
              s = e([n]);
            n = (s.then ? (await s)() : s)[0];
            let o = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
              l = n.default.create({ baseURL: o, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
              i = { log: e => {}, error: (e, t) => {} };
            l.interceptors.request.use(
              e => (i.log(`Making request to: ${e.baseURL}${e.url}`), e),
              e => (i.error('Request error:', e), Promise.reject(e))
            ),
              l.interceptors.response.use(
                e => e,
                async e => {
                  let { config: t } = e;
                  return e.message.includes('Network Error') && t.retry > 0
                    ? ((t.retry -= 1),
                      i.log(`Retrying request to ${t.url}, ${t.retry} attempts left`),
                      await new Promise(e => setTimeout(e, t.retryDelay)),
                      l(t))
                    : Promise.reject(e);
                }
              );
            let u = async (e = 1, t = 10) => {
                try {
                  return (await l.get('/novels/', { params: { page: e, limit: t } })).data;
                } catch (e) {
                  return (
                    i.error('Error fetching novel list:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              },
              c = async e => {
                try {
                  return (await l.get(`/novels/${e}/`)).data;
                } catch (e) {
                  throw (i.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
                }
              },
              d = async e => {
                try {
                  return (await l.get(`/chapters/${e}/`)).data;
                } catch (t) {
                  throw (
                    (i.error(`Error fetching chapter content for chapterId ${e}:`, t),
                    Error('無法載入章節內容'))
                  );
                }
              },
              p = async e => {
                try {
                  return (await l.get('/novels/search', { params: { query: e } })).data;
                } catch (e) {
                  return (
                    i.error('Error searching novels:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      1183: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t), r.d(t, { default: () => i });
            var n = r(6351),
              s = r(2015),
              o = r(7109),
              l = e([o]);
            o = (l.then ? (await l)() : l)[0];
            let i = () => {
              let { login: e } = (0, o.As)(),
                [t, r] = (0, s.useState)(''),
                [a, l] = (0, s.useState)(''),
                [i, u] = (0, s.useState)(!1),
                c = async r => {
                  if ((r.preventDefault(), t.trim() && a.trim())) {
                    u(!0);
                    try {
                      await e(t, a);
                    } catch (e) {
                      console.error('Login failed:', e);
                    } finally {
                      u(!1);
                    }
                  }
                };
              return (0, n.jsxs)('div', {
                className: 'max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
                children: [
                  (0, n.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '登入' }),
                  (0, n.jsx)('form', {
                    onSubmit: c,
                    children: (0, n.jsxs)('div', {
                      className: 'space-y-4',
                      children: [
                        (0, n.jsx)('input', {
                          type: 'text',
                          value: t,
                          onChange: e => r(e.target.value),
                          placeholder: '使用者名稱',
                          className: 'w-full p-2 border rounded',
                        }),
                        (0, n.jsx)('input', {
                          type: 'password',
                          value: a,
                          onChange: e => l(e.target.value),
                          placeholder: '密碼',
                          className: 'w-full p-2 border rounded',
                        }),
                        (0, n.jsx)('button', {
                          type: 'submit',
                          disabled: i,
                          className:
                            'w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300',
                          children: i ? '登入中...' : '登入',
                        }),
                      ],
                    }),
                  }),
                ],
              });
            };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      1428: e => {
        e.exports = import('axios');
      },
      2015: e => {
        e.exports = require('react');
      },
      3873: e => {
        e.exports = require('path');
      },
      6246: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t),
              r.d(t, {
                config: () => m,
                default: () => g,
                getServerSideProps: () => P,
                getStaticPaths: () => f,
                getStaticProps: () => h,
                reportWebVitals: () => b,
                routeModule: () => _,
                unstable_getServerProps: () => S,
                unstable_getServerSideProps: () => w,
                unstable_getStaticParams: () => x,
                unstable_getStaticPaths: () => v,
                unstable_getStaticProps: () => y,
              });
            var n = r(718),
              s = r(208),
              o = r(8732),
              l = r(5488),
              i = r.n(l),
              u = r(752),
              c = r.n(u),
              d = r(1183),
              p = e([d]);
            d = (p.then ? (await p)() : p)[0];
            let g = (0, o.M)(d, 'default'),
              h = (0, o.M)(d, 'getStaticProps'),
              f = (0, o.M)(d, 'getStaticPaths'),
              P = (0, o.M)(d, 'getServerSideProps'),
              m = (0, o.M)(d, 'config'),
              b = (0, o.M)(d, 'reportWebVitals'),
              y = (0, o.M)(d, 'unstable_getStaticProps'),
              v = (0, o.M)(d, 'unstable_getStaticPaths'),
              x = (0, o.M)(d, 'unstable_getStaticParams'),
              S = (0, o.M)(d, 'unstable_getServerProps'),
              w = (0, o.M)(d, 'unstable_getServerSideProps'),
              _ = new n.PagesRouteModule({
                definition: {
                  kind: s.A.PAGES,
                  page: '/Login',
                  pathname: '/Login',
                  bundlePath: '',
                  filename: '',
                },
                components: { App: c(), Document: i() },
                userland: d,
              });
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
      7109: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.d(t, { As: () => i }), r(6351);
            var n = r(2015),
              s = r(802),
              o = e([s]);
            s = (o.then ? (await o)() : o)[0];
            let l = (0, n.createContext)(null),
              i = () => {
                let e = (0, n.useContext)(l);
                if (!e) throw Error('useAuth must be used within an AuthProvider');
                return e;
              };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      8732: (e, t) => {
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, r) {
              return r in t
                ? t[r]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, r))
                  : 'function' == typeof t && 'default' === r
                    ? t
                    : void 0;
            };
          },
        });
      },
    });
  var t = require('../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    a = t.X(0, [488], () => r(6246));
  module.exports = a;
})();
