'use strict';
(() => {
  var e = {};
  (e.id = 308),
    (e.ids = [220, 308]),
    (e.modules = {
      208: (e, t) => {
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return r;
          },
        });
        var r = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, r) => {
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return i;
            },
          });
        let s = r(8752),
          a = r(6351),
          l = s._(r(2015)),
          o = r(5310);
        async function n(e) {
          let { Component: t, ctx: r } = e;
          return { pageProps: await (0, o.loadGetInitialProps)(t, r) };
        }
        class i extends l.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, a.jsx)(e, { ...t });
          }
        }
        (i.origGetInitialProps = n),
          (i.getInitialProps = n),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      802: (e, t, r) => {
        r.a(e, async (e, s) => {
          try {
            r.d(t, { Cz: () => h, Zc: () => d, ef: () => u, xj: () => c });
            var a = r(1428),
              l = e([a]);
            a = (l.then ? (await l)() : l)[0];
            let o = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
              n = a.default.create({ baseURL: o, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
              i = { log: e => {}, error: (e, t) => {} };
            n.interceptors.request.use(
              e => (i.log(`Making request to: ${e.baseURL}${e.url}`), e),
              e => (i.error('Request error:', e), Promise.reject(e))
            ),
              n.interceptors.response.use(
                e => e,
                async e => {
                  let { config: t } = e;
                  return e.message.includes('Network Error') && t.retry > 0
                    ? ((t.retry -= 1),
                      i.log(`Retrying request to ${t.url}, ${t.retry} attempts left`),
                      await new Promise(e => setTimeout(e, t.retryDelay)),
                      n(t))
                    : Promise.reject(e);
                }
              );
            let c = async (e = 1, t = 10) => {
                try {
                  return (await n.get('/novels/', { params: { page: e, limit: t } })).data;
                } catch (e) {
                  return (
                    i.error('Error fetching novel list:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              },
              u = async e => {
                try {
                  return (await n.get(`/novels/${e}/`)).data;
                } catch (e) {
                  throw (i.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
                }
              },
              d = async e => {
                try {
                  return (await n.get(`/chapters/${e}/`)).data;
                } catch (t) {
                  throw (
                    (i.error(`Error fetching chapter content for chapterId ${e}:`, t),
                    Error('無法載入章節內容'))
                  );
                }
              },
              h = async e => {
                try {
                  return (await n.get('/novels/search', { params: { query: e } })).data;
                } catch (e) {
                  return (
                    i.error('Error searching novels:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              };
            s();
          } catch (e) {
            s(e);
          }
        });
      },
      1428: e => {
        e.exports = import('axios');
      },
      1429: e => {
        e.exports = require('lodash/debounce');
      },
      2015: e => {
        e.exports = require('react');
      },
      3873: e => {
        e.exports = require('path');
      },
      6206: (e, t, r) => {
        r.a(e, async (e, s) => {
          try {
            r.r(t),
              r.d(t, {
                config: () => x,
                default: () => p,
                getServerSideProps: () => f,
                getStaticPaths: () => m,
                getStaticProps: () => g,
                reportWebVitals: () => b,
                routeModule: () => S,
                unstable_getServerProps: () => P,
                unstable_getServerSideProps: () => w,
                unstable_getStaticParams: () => j,
                unstable_getStaticPaths: () => y,
                unstable_getStaticProps: () => v,
              });
            var a = r(718),
              l = r(208),
              o = r(8732),
              n = r(5488),
              i = r.n(n),
              c = r(752),
              u = r.n(c),
              d = r(8718),
              h = e([d]);
            d = (h.then ? (await h)() : h)[0];
            let p = (0, o.M)(d, 'default'),
              g = (0, o.M)(d, 'getStaticProps'),
              m = (0, o.M)(d, 'getStaticPaths'),
              f = (0, o.M)(d, 'getServerSideProps'),
              x = (0, o.M)(d, 'config'),
              b = (0, o.M)(d, 'reportWebVitals'),
              v = (0, o.M)(d, 'unstable_getStaticProps'),
              y = (0, o.M)(d, 'unstable_getStaticPaths'),
              j = (0, o.M)(d, 'unstable_getStaticParams'),
              P = (0, o.M)(d, 'unstable_getServerProps'),
              w = (0, o.M)(d, 'unstable_getServerSideProps'),
              S = new a.PagesRouteModule({
                definition: {
                  kind: l.A.PAGES,
                  page: '/Search',
                  pathname: '/Search',
                  bundlePath: '',
                  filename: '',
                },
                components: { App: u(), Document: i() },
                userland: d,
              });
            s();
          } catch (e) {
            s(e);
          }
        });
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
      8718: (e, t, r) => {
        r.a(e, async (e, s) => {
          try {
            r.r(t), r.d(t, { default: () => h });
            var a = r(6351),
              l = r(2015),
              o = r(1429),
              n = r.n(o),
              i = r(802),
              c = e([i]);
            i = (c.then ? (await c)() : c)[0];
            class u {
              constructor() {
                (this.children = new Map()), (this.isEndOfWord = !1), (this.word = '');
              }
            }
            class d {
              insert(e) {
                let t = this.root;
                for (let r of e.toLowerCase())
                  t.children.has(r) || t.children.set(r, new u()), (t = t.children.get(r));
                (t.isEndOfWord = !0), (t.word = e);
              }
              search(e, t = 5) {
                let r = [],
                  s = this.findNode(e.toLowerCase());
                return s && this.dfs(s, r, t), r;
              }
              findNode(e) {
                let t = this.root;
                for (let r of e) {
                  if (!t.children.has(r)) return null;
                  t = t.children.get(r);
                }
                return t;
              }
              dfs(e, t, r) {
                if (!(t.length >= r))
                  for (let s of (e.isEndOfWord && t.push(e.word), Array.from(e.children.values())))
                    this.dfs(s, t, r);
              }
              constructor() {
                this.root = new u();
              }
            }
            let h = () => {
              let [e, t] = (0, l.useState)(''),
                [r, s] = (0, l.useState)(!1),
                [o, c] = (0, l.useState)([]),
                [u, h] = (0, l.useState)({ category: '', status: '' }),
                [p, g] = (0, l.useState)([]),
                [m, f] = (0, l.useState)(!1),
                [x, b] = (0, l.useState)([]),
                v = (0, l.useMemo)(() => {
                  let e = new d();
                  return (
                    [
                      '斗羅大陸',
                      '鬥破蒼穹',
                      '完美世界',
                      '一念永恆',
                      '我欲封天',
                      '仙逆',
                      '星辰變',
                      '盤龍',
                      '吞噬星空',
                      '武動乾坤',
                      '大主宰',
                      '元尊',
                      '天蠶土豆',
                      '唐家三少',
                      '我吃西紅柿',
                    ].forEach(t => e.insert(t)),
                    e
                  );
                }, []);
              (0, l.useEffect)(() => {
                let e = localStorage.getItem('searchHistory');
                if (e)
                  try {
                    let t = JSON.parse(e);
                    g(t.slice(0, 10));
                  } catch (e) {
                    console.error('Failed to parse search history:', e);
                  }
              }, []);
              let y = (0, l.useCallback)(
                  e => {
                    if (!e.trim()) return;
                    let t = [
                      { query: e, timestamp: Date.now() },
                      ...p.filter(t => t.query !== e),
                    ].slice(0, 10);
                    g(t), localStorage.setItem('searchHistory', JSON.stringify(t));
                  },
                  [p]
                ),
                j = (0, l.useCallback)(
                  async (e, t) => {
                    if (!e.trim()) return void c([]);
                    s(!0);
                    try {
                      let r = (await (0, i.Cz)(e)).novels || [];
                      t.category && (r = r.filter(e => e.category === t.category)),
                        t.status && (r = r.filter(e => e.status === t.status)),
                        c(r),
                        y(e);
                    } catch (e) {
                      console.error('Search failed:', e), c([]);
                    } finally {
                      s(!1);
                    }
                  },
                  [y]
                ),
                P = (0, l.useMemo)(() => n()(j, 500), [j]),
                w = e => {
                  if ((t(e), e.trim())) {
                    let t = v.search(e, 5),
                      r = p
                        .filter(t => t.query.toLowerCase().includes(e.toLowerCase()))
                        .map(e => e.query)
                        .slice(0, 3),
                      s = [...new Set([...t, ...r])].slice(0, 5);
                    b(s), f(s.length > 0);
                  } else b([]), f(!1);
                  P(e, u);
                },
                S = e => {
                  t(e), f(!1), P(e, u);
                },
                N = (t, r) => {
                  let s = { ...u, [t]: r };
                  h(s), e.trim() && P(e, s);
                };
              return (0, a.jsxs)('div', {
                className: 'max-w-4xl mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg',
                children: [
                  (0, a.jsx)('h1', { className: 'text-2xl font-bold mb-6', children: '搜尋小說' }),
                  (0, a.jsxs)('div', {
                    className: 'mb-8 relative',
                    children: [
                      (0, a.jsxs)('div', {
                        className: 'flex gap-2 mb-4',
                        children: [
                          (0, a.jsxs)('div', {
                            className: 'flex-1 relative',
                            children: [
                              (0, a.jsx)('input', {
                                type: 'text',
                                value: e,
                                onChange: e => w(e.target.value),
                                onFocus: () => x.length > 0 && f(!0),
                                onBlur: () => setTimeout(() => f(!1), 200),
                                placeholder: '輸入小說名稱、作者或關鍵字',
                                className:
                                  'w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500',
                              }),
                              m &&
                                x.length > 0 &&
                                (0, a.jsx)('div', {
                                  className:
                                    'absolute top-full left-0 right-0 bg-white border border-t-0 rounded-b-md shadow-lg z-10',
                                  children: x.map((e, t) =>
                                    (0, a.jsx)(
                                      'div',
                                      {
                                        className: 'p-2 hover:bg-gray-100 cursor-pointer text-sm',
                                        onMouseDown: () => S(e),
                                        children: e,
                                      },
                                      t
                                    )
                                  ),
                                }),
                            ],
                          }),
                          (0, a.jsx)('button', {
                            type: 'button',
                            disabled: r,
                            className:
                              'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300',
                            children: r ? '搜尋中...' : '搜尋',
                          }),
                        ],
                      }),
                      (0, a.jsxs)('div', {
                        className: 'flex gap-4 flex-wrap',
                        children: [
                          (0, a.jsxs)('select', {
                            value: u.category,
                            onChange: e => N('category', e.target.value),
                            className:
                              'p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500',
                            children: [
                              (0, a.jsx)('option', { value: '', children: '所有分類' }),
                              (0, a.jsx)('option', { value: 'fantasy', children: '玄幻' }),
                              (0, a.jsx)('option', { value: 'cultivation', children: '修真' }),
                              (0, a.jsx)('option', { value: 'urban', children: '都市' }),
                              (0, a.jsx)('option', { value: 'romance', children: '言情' }),
                              (0, a.jsx)('option', { value: 'historical', children: '歷史' }),
                            ],
                          }),
                          (0, a.jsxs)('select', {
                            value: u.status,
                            onChange: e => N('status', e.target.value),
                            className:
                              'p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500',
                            children: [
                              (0, a.jsx)('option', { value: '', children: '所有狀態' }),
                              (0, a.jsx)('option', { value: 'completed', children: '已完結' }),
                              (0, a.jsx)('option', { value: 'ongoing', children: '連載中' }),
                              (0, a.jsx)('option', { value: 'paused', children: '暫停' }),
                            ],
                          }),
                        ],
                      }),
                    ],
                  }),
                  p.length > 0 &&
                    !e &&
                    (0, a.jsxs)('div', {
                      className: 'mb-6 p-4 bg-gray-50 rounded',
                      children: [
                        (0, a.jsxs)('div', {
                          className: 'flex justify-between items-center mb-2',
                          children: [
                            (0, a.jsx)('h3', {
                              className: 'text-sm font-semibold text-gray-700',
                              children: '搜索歷史',
                            }),
                            (0, a.jsx)('button', {
                              onClick: () => {
                                g([]), localStorage.removeItem('searchHistory');
                              },
                              className: 'text-xs text-blue-500 hover:text-blue-700',
                              children: '清除',
                            }),
                          ],
                        }),
                        (0, a.jsx)('div', {
                          className: 'flex flex-wrap gap-2',
                          children: p
                            .slice(0, 5)
                            .map((e, t) =>
                              (0, a.jsx)(
                                'button',
                                {
                                  onClick: () => w(e.query),
                                  className:
                                    'px-3 py-1 text-sm bg-white border rounded hover:bg-gray-100',
                                  children: e.query,
                                },
                                t
                              )
                            ),
                        }),
                      ],
                    }),
                  o.length > 0
                    ? (0, a.jsx)('div', {
                        className: 'grid grid-cols-1 md:grid-cols-2 gap-4',
                        children: o.map(e =>
                          (0, a.jsxs)(
                            'div',
                            {
                              className:
                                'flex gap-4 p-4 border rounded hover:shadow-md transition-shadow',
                              children: [
                                (0, a.jsx)('img', {
                                  src: e.cover || '/placeholder-book.png',
                                  alt: e.title,
                                  className: 'w-24 h-32 object-cover rounded',
                                  onError: e => {
                                    e.target.src = '/placeholder-book.png';
                                  },
                                }),
                                (0, a.jsxs)('div', {
                                  className: 'flex-1',
                                  children: [
                                    (0, a.jsx)('h2', {
                                      className: 'text-lg font-semibold mb-1',
                                      children: e.title,
                                    }),
                                    (0, a.jsxs)('p', {
                                      className: 'text-sm text-gray-600 mb-1',
                                      children: ['作者：', e.author],
                                    }),
                                    e.category &&
                                      (0, a.jsxs)('p', {
                                        className: 'text-xs text-blue-600 mb-1',
                                        children: ['分類：', e.category],
                                      }),
                                    e.status &&
                                      (0, a.jsxs)('p', {
                                        className: 'text-xs text-green-600 mb-2',
                                        children: ['狀態：', e.status],
                                      }),
                                    (0, a.jsx)('p', {
                                      className: 'text-sm text-gray-700 line-clamp-2',
                                      children: e.description,
                                    }),
                                  ],
                                }),
                              ],
                            },
                            e.id
                          )
                        ),
                      })
                    : (0, a.jsx)('div', {
                        className: 'text-center py-8',
                        children: r
                          ? (0, a.jsxs)('div', {
                              className: 'flex justify-center items-center',
                              children: [
                                (0, a.jsx)('div', {
                                  className:
                                    'animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500',
                                }),
                                (0, a.jsx)('span', {
                                  className: 'ml-2 text-gray-600',
                                  children: '搜尋中...',
                                }),
                              ],
                            })
                          : (0, a.jsx)('p', {
                              className: 'text-gray-500',
                              children: e ? '沒有找到相關小說' : '請輸入搜尋關鍵字',
                            }),
                      }),
                ],
              });
            };
            s();
          } catch (e) {
            s(e);
          }
        });
      },
      8732: (e, t) => {
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, r) {
              return r in t
                ? t[r]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, r))
                  : 'function' == typeof t && 'default' === r
                    ? t
                    : void 0;
            };
          },
        });
      },
    });
  var t = require('../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    s = t.X(0, [488], () => r(6206));
  module.exports = s;
})();
