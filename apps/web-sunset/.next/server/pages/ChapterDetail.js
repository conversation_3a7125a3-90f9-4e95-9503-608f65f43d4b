'use strict';
(() => {
  var e = {};
  (e.id = 100),
    (e.ids = [100, 220]),
    (e.modules = {
      208: (e, t) => {
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return r;
          },
        });
        var r = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, r) => {
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return l;
            },
          });
        let a = r(8752),
          s = r(6351),
          n = a._(r(2015)),
          o = r(5310);
        async function i(e) {
          let { Component: t, ctx: r } = e;
          return { pageProps: await (0, o.loadGetInitialProps)(t, r) };
        }
        class l extends n.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, s.jsx)(e, { ...t });
          }
        }
        (l.origGetInitialProps = i),
          (l.getInitialProps = i),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      802: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.d(t, { Cz: () => p, Zc: () => d, ef: () => u, xj: () => c });
            var s = r(1428),
              n = e([s]);
            s = (n.then ? (await n)() : n)[0];
            let o = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
              i = s.default.create({ baseURL: o, timeout: 1e4, retry: 3, retryDelay: 1e3 }),
              l = { log: e => {}, error: (e, t) => {} };
            i.interceptors.request.use(
              e => (l.log(`Making request to: ${e.baseURL}${e.url}`), e),
              e => (l.error('Request error:', e), Promise.reject(e))
            ),
              i.interceptors.response.use(
                e => e,
                async e => {
                  let { config: t } = e;
                  return e.message.includes('Network Error') && t.retry > 0
                    ? ((t.retry -= 1),
                      l.log(`Retrying request to ${t.url}, ${t.retry} attempts left`),
                      await new Promise(e => setTimeout(e, t.retryDelay)),
                      i(t))
                    : Promise.reject(e);
                }
              );
            let c = async (e = 1, t = 10) => {
                try {
                  return (await i.get('/novels/', { params: { page: e, limit: t } })).data;
                } catch (e) {
                  return (
                    l.error('Error fetching novel list:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              },
              u = async e => {
                try {
                  return (await i.get(`/novels/${e}/`)).data;
                } catch (e) {
                  throw (l.error('Error fetching novel detail:', e), Error('無法載入小說詳情'));
                }
              },
              d = async e => {
                try {
                  return (await i.get(`/chapters/${e}/`)).data;
                } catch (t) {
                  throw (
                    (l.error(`Error fetching chapter content for chapterId ${e}:`, t),
                    Error('無法載入章節內容'))
                  );
                }
              },
              p = async e => {
                try {
                  return (await i.get('/novels/search', { params: { query: e } })).data;
                } catch (e) {
                  return (
                    l.error('Error searching novels:', e),
                    { novels: [], total: 0, page: 1, limit: 10 }
                  );
                }
              };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      1428: e => {
        e.exports = import('axios');
      },
      1436: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t),
              r.d(t, {
                config: () => P,
                default: () => h,
                getServerSideProps: () => f,
                getStaticPaths: () => g,
                getStaticProps: () => m,
                reportWebVitals: () => x,
                routeModule: () => S,
                unstable_getServerProps: () => _,
                unstable_getServerSideProps: () => j,
                unstable_getStaticParams: () => v,
                unstable_getStaticPaths: () => b,
                unstable_getStaticProps: () => y,
              });
            var s = r(718),
              n = r(208),
              o = r(8732),
              i = r(5488),
              l = r.n(i),
              c = r(752),
              u = r.n(c),
              d = r(5878),
              p = e([d]);
            d = (p.then ? (await p)() : p)[0];
            let h = (0, o.M)(d, 'default'),
              m = (0, o.M)(d, 'getStaticProps'),
              g = (0, o.M)(d, 'getStaticPaths'),
              f = (0, o.M)(d, 'getServerSideProps'),
              P = (0, o.M)(d, 'config'),
              x = (0, o.M)(d, 'reportWebVitals'),
              y = (0, o.M)(d, 'unstable_getStaticProps'),
              b = (0, o.M)(d, 'unstable_getStaticPaths'),
              v = (0, o.M)(d, 'unstable_getStaticParams'),
              _ = (0, o.M)(d, 'unstable_getServerProps'),
              j = (0, o.M)(d, 'unstable_getServerSideProps'),
              S = new s.PagesRouteModule({
                definition: {
                  kind: n.A.PAGES,
                  page: '/ChapterDetail',
                  pathname: '/ChapterDetail',
                  bundlePath: '',
                  filename: '',
                },
                components: { App: u(), Document: l() },
                userland: d,
              });
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      2015: e => {
        e.exports = require('react');
      },
      3873: e => {
        e.exports = require('path');
      },
      4822: e => {
        e.exports = require('react-router-dom');
      },
      5878: (e, t, r) => {
        r.a(e, async (e, a) => {
          try {
            r.r(t), r.d(t, { default: () => c });
            var s = r(6351),
              n = r(2015),
              o = r(4822),
              i = r(802),
              l = e([i]);
            i = (l.then ? (await l)() : l)[0];
            let c = () => {
              let e = (0, o.useNavigate)(),
                { novelSlug: t, chapterId: r } = (0, o.useParams)(),
                [a, l] = (0, n.useState)(null),
                [c, u] = (0, n.useState)(!0),
                [d, p] = (0, n.useState)(null);
              return ((0, n.useEffect)(() => {
                (async () => {
                  if (!t || !r) return e('/');
                  try {
                    u(!0);
                    let e = await (0, i.Zc)(r);
                    if (!e || !e.content) throw Error('章節內容為空或無效');
                    l(e);
                  } catch (a) {
                    let e = '無法載入章節內容';
                    a.response && a.response.data && a.response.data.detail
                      ? (e = a.response.data.detail)
                      : a.message && (e = a.message),
                      p(e),
                      console.error('Error fetching chapter:', {
                        error: a,
                        novelSlug: t,
                        chapterId: r,
                        errorMessage: e,
                      });
                  } finally {
                    u(!1);
                  }
                })();
              }, [t, r, e]),
              c)
                ? (0, s.jsx)('div', { className: 'text-center p-4', children: '載入中...' })
                : d
                  ? (0, s.jsx)('div', { className: 'text-center text-red-500 p-4', children: d })
                  : a
                    ? (0, s.jsx)('div', {
                        className: 'max-w-4xl mx-auto p-4',
                        children: (0, s.jsxs)('div', {
                          className: 'bg-white rounded-lg shadow-lg p-6',
                          children: [
                            (0, s.jsxs)('div', {
                              className: 'mb-6',
                              children: [
                                (0, s.jsx)('h1', {
                                  className: 'text-2xl font-bold mb-2',
                                  children:
                                    a.title || `第 ${a.chapter_number || a.chapterNumber} 章`,
                                }),
                                a.title &&
                                  (a.chapter_number || a.chapterNumber) &&
                                  (0, s.jsxs)('p', {
                                    className: 'text-lg text-gray-600',
                                    children: ['第 ', a.chapter_number || a.chapterNumber, ' 章'],
                                  }),
                              ],
                            }),
                            (0, s.jsx)('div', {
                              className: 'prose max-w-none mb-8',
                              children: a.content
                                ? a.content
                                    .split('\n')
                                    .map((e, t) =>
                                      (0, s.jsx)('p', { className: 'mb-4', children: e }, t)
                                    )
                                : (0, s.jsx)('p', {
                                    className: 'text-gray-600',
                                    children: '章節內容載入中...',
                                  }),
                            }),
                            (0, s.jsxs)('div', {
                              className: 'flex justify-center items-center',
                              children: [
                                ' ',
                                (0, s.jsx)(o.Link, {
                                  to: `/novels/${t}`,
                                  className: 'px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded',
                                  children: '返回目錄',
                                }),
                              ],
                            }),
                          ],
                        }),
                      })
                    : (0, s.jsx)('div', { className: 'text-center p-4', children: '找不到章節' });
            };
            a();
          } catch (e) {
            a(e);
          }
        });
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
      8732: (e, t) => {
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, r) {
              return r in t
                ? t[r]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, r))
                  : 'function' == typeof t && 'default' === r
                    ? t
                    : void 0;
            };
          },
        });
      },
    });
  var t = require('../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    a = t.X(0, [488], () => r(1436));
  module.exports = a;
})();
