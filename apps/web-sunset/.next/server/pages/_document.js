'use strict';
(() => {
  var e = {};
  (e.id = 220),
    (e.ids = [220]),
    (e.modules = {
      361: e => {
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      2015: e => {
        e.exports = require('react');
      },
      3873: e => {
        e.exports = require('path');
      },
      6351: e => {
        e.exports = require('react/jsx-runtime');
      },
    });
  var r = require('../webpack-runtime.js');
  r.C(e);
  var s = e => r((r.s = e)),
    t = r.X(0, [488], () => s(5488));
  module.exports = t;
})();
