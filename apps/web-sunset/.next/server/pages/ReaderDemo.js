(() => {
  var e = {};
  (e.id = 800),
    (e.ids = [220, 800]),
    (e.modules = {
      208: (e, t) => {
        'use strict';
        Object.defineProperty(t, 'A', {
          enumerable: !0,
          get: function () {
            return r;
          },
        });
        var r = (function (e) {
          return (
            (e.PAGES = 'PAGES'),
            (e.PAGES_API = 'PAGES_API'),
            (e.APP_PAGE = 'APP_PAGE'),
            (e.APP_ROUTE = 'APP_ROUTE'),
            (e.IMAGE = 'IMAGE'),
            e
          );
        })({});
      },
      361: e => {
        'use strict';
        e.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');
      },
      752: (e, t, r) => {
        'use strict';
        Object.defineProperty(t, '__esModule', { value: !0 }),
          Object.defineProperty(t, 'default', {
            enumerable: !0,
            get: function () {
              return i;
            },
          });
        let s = r(8752),
          l = r(6351),
          a = s._(r(2015)),
          o = r(5310);
        async function n(e) {
          let { Component: t, ctx: r } = e;
          return { pageProps: await (0, o.loadGetInitialProps)(t, r) };
        }
        class i extends a.default.Component {
          render() {
            let { Component: e, pageProps: t } = this.props;
            return (0, l.jsx)(e, { ...t });
          }
        }
        (i.origGetInitialProps = n),
          (i.getInitialProps = n),
          ('function' == typeof t.default ||
            ('object' == typeof t.default && null !== t.default)) &&
            void 0 === t.default.__esModule &&
            (Object.defineProperty(t.default, '__esModule', { value: !0 }),
            Object.assign(t.default, t),
            (e.exports = t.default));
      },
      1555: e => {
        e.exports = { slider: 'FontSizeSlider_slider__gpE_E' };
      },
      2015: e => {
        'use strict';
        e.exports = require('react');
      },
      3873: e => {
        'use strict';
        e.exports = require('path');
      },
      6351: e => {
        'use strict';
        e.exports = require('react/jsx-runtime');
      },
      6791: (e, t, r) => {
        'use strict';
        r.r(t),
          r.d(t, {
            config: () => L,
            default: () => M,
            getServerSideProps: () => z,
            getStaticPaths: () => E,
            getStaticProps: () => _,
            reportWebVitals: () => $,
            routeModule: () => F,
            unstable_getServerProps: () => I,
            unstable_getServerSideProps: () => B,
            unstable_getStaticParams: () => G,
            unstable_getStaticPaths: () => O,
            unstable_getStaticProps: () => A,
          });
        var s = {};
        r.r(s), r.d(s, { default: () => C });
        var l = r(718),
          a = r(208),
          o = r(8732),
          n = r(5488),
          i = r.n(n),
          d = r(752),
          c = r.n(d),
          u = r(6351),
          f = r(2015);
        let x = ({
            children: e,
            maxWidth: t = 'lg',
            padding: r = 'md',
            centered: s = !0,
            safeArea: l = !1,
            className: a = '',
            testId: o = 'layout',
          }) => {
            let n = [
              'w-full',
              {
                sm: 'max-w-sm',
                md: 'max-w-md',
                lg: 'max-w-4xl',
                xl: 'max-w-6xl',
                '2xl': 'max-w-7xl',
                full: 'max-w-full',
                reading: 'max-w-reading',
              }[t],
              {
                none: '',
                sm: 'p-2 sm:p-4',
                md: 'p-4 sm:p-6 lg:p-8',
                lg: 'p-6 sm:p-8 lg:p-12',
                xl: 'p-8 sm:p-12 lg:p-16',
              }[r],
              s ? 'mx-auto' : '',
              l ? 'safe-area-padding' : '',
              a,
            ]
              .filter(Boolean)
              .join(' ');
            return (0, u.jsx)('div', { className: n, 'data-testid': o, children: e });
          },
          m = (e = {}) => {
            let {
                initialOpen: t = !1,
                closeOnOutsideClick: r = !0,
                closeOnEscape: s = !0,
                lockBodyScroll: l = !0,
                onOpen: a,
                onClose: o,
              } = e,
              [n, i] = (0, f.useState)(t),
              d = (0, f.useRef)(null),
              c = (0, f.useRef)(null),
              u = (0, f.useCallback)(() => {
                i(!0), a?.();
              }, [a]),
              x = (0, f.useCallback)(() => {
                i(!1), o?.(), c.current && c.current.focus();
              }, [o]),
              m = (0, f.useCallback)(() => {
                n ? x() : u();
              }, [n, x, u]);
            return (
              (0, f.useEffect)(() => {
                if (l) {
                  if (n) {
                    let e = window.scrollY;
                    (document.body.style.position = 'fixed'),
                      (document.body.style.top = `-${e}px`),
                      (document.body.style.width = '100%');
                  } else {
                    let e = document.body.style.top;
                    if (
                      ((document.body.style.position = ''),
                      (document.body.style.top = ''),
                      (document.body.style.width = ''),
                      e)
                    ) {
                      let t = -1 * parseInt(e.replace('px', ''));
                      window.scrollTo(0, t);
                    }
                  }
                  return () => {
                    (document.body.style.position = ''),
                      (document.body.style.top = ''),
                      (document.body.style.width = '');
                  };
                }
              }, [n, l]),
              (0, f.useEffect)(() => {
                if (!s) return;
                let e = e => {
                  'Escape' === e.key && n && x();
                };
                return (
                  document.addEventListener('keydown', e),
                  () => document.removeEventListener('keydown', e)
                );
              }, [n, s, x]),
              (0, f.useEffect)(() => {
                if (!r) return;
                let e = e => {
                  if (!n) return;
                  let t = e.target,
                    r = d.current && !d.current.contains(t),
                    s = c.current && !c.current.contains(t);
                  r && s && x();
                };
                return (
                  document.addEventListener('mousedown', e),
                  () => document.removeEventListener('mousedown', e)
                );
              }, [n, r, x]),
              { isOpen: n, toggleMenu: m, closeMenu: x, openMenu: u, menuRef: d, buttonRef: c }
            );
          },
          g = [
            { label: '首頁', path: '/' },
            { label: '小說分類', path: '/categories' },
            { label: '排行榜', path: '/rankings' },
            { label: '最新更新', path: '/latest' },
            { label: '完本小說', path: '/completed' },
          ],
          b = ({
            brand: e = '小說閱讀',
            showSearch: t = !0,
            showAuth: r = !0,
            variant: s = 'default',
            fixed: l = !0,
            className: a = '',
            onNavigate: o,
            onSearch: n,
            onLogin: i,
            onRegister: d,
          }) => {
            let [c, x] = (0, f.useState)(''),
              {
                isOpen: b,
                toggleMenu: p,
                closeMenu: h,
                menuRef: y,
                buttonRef: v,
              } = m({ closeOnOutsideClick: !0, closeOnEscape: !0, lockBodyScroll: !0 }),
              j = { default: 'text-gray-900', transparent: 'text-gray-900', solid: 'text-white' },
              w = [
                'w-full z-50 transition-all duration-200',
                l ? 'fixed top-0 left-0 right-0' : 'relative',
                {
                  default: 'bg-white border-b border-gray-200 shadow-sm',
                  transparent: 'bg-white/80 backdrop-blur-md border-b border-white/20',
                  solid: 'bg-gray-900 border-b border-gray-800',
                }[s],
                a,
              ]
                .filter(Boolean)
                .join(' '),
              N = e => {
                h(), o?.(e);
              },
              k = e => {
                e.preventDefault(), c.trim() && (n?.(c.trim()), x(''), h());
              },
              S = e => {
                h(), 'login' === e ? i?.() : d?.();
              };
            return (0, u.jsx)('header', {
              className: w,
              children: (0, u.jsxs)('nav', {
                className: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
                role: 'navigation',
                'aria-label': '主要導航',
                children: [
                  (0, u.jsxs)('div', {
                    className: 'flex justify-between items-center h-16',
                    children: [
                      (0, u.jsx)('div', {
                        className: 'flex-shrink-0',
                        children: (0, u.jsx)('button', {
                          className: `text-xl font-bold ${j[s]} hover:opacity-80 transition-opacity focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1`,
                          onClick: () => N('/'),
                          'aria-label': `返回 ${e} 首頁`,
                          children: e,
                        }),
                      }),
                      (0, u.jsx)('div', {
                        className: 'hidden lg:flex lg:items-center lg:space-x-8',
                        children: (0, u.jsx)('div', {
                          className: 'flex space-x-6',
                          children: g.map(e =>
                            (0, u.jsx)(
                              'button',
                              {
                                className: `${j[s]} hover:text-blue-600 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1`,
                                onClick: () => N(e.path),
                                children: e.label,
                              },
                              e.path
                            )
                          ),
                        }),
                      }),
                      (0, u.jsxs)('div', {
                        className: 'hidden lg:flex lg:items-center lg:space-x-4',
                        children: [
                          t &&
                            (0, u.jsxs)('form', {
                              onSubmit: k,
                              className: 'relative',
                              children: [
                                (0, u.jsx)('label', {
                                  htmlFor: 'desktop-search',
                                  className: 'sr-only',
                                  children: '搜尋小說',
                                }),
                                (0, u.jsx)('input', {
                                  id: 'desktop-search',
                                  type: 'search',
                                  placeholder: '搜尋小說...',
                                  value: c,
                                  onChange: e => x(e.target.value),
                                  className:
                                    'w-64 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                                  'aria-label': '搜尋小說',
                                }),
                                (0, u.jsx)('button', {
                                  type: 'submit',
                                  className:
                                    'absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600',
                                  'aria-label': '執行搜尋',
                                  children: (0, u.jsx)('svg', {
                                    className: 'w-5 h-5',
                                    fill: 'none',
                                    stroke: 'currentColor',
                                    viewBox: '0 0 24 24',
                                    children: (0, u.jsx)('path', {
                                      strokeLinecap: 'round',
                                      strokeLinejoin: 'round',
                                      strokeWidth: 2,
                                      d: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z',
                                    }),
                                  }),
                                }),
                              ],
                            }),
                          r &&
                            (0, u.jsxs)('div', {
                              className: 'flex space-x-3',
                              children: [
                                (0, u.jsx)('button', {
                                  className: `${j[s]} border border-current px-4 py-2 rounded-lg hover:bg-current hover:text-white transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`,
                                  onClick: () => S('login'),
                                  children: '登入',
                                }),
                                (0, u.jsx)('button', {
                                  className:
                                    'bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                                  onClick: () => S('register'),
                                  children: '註冊',
                                }),
                              ],
                            }),
                        ],
                      }),
                      (0, u.jsx)('div', {
                        className: 'lg:hidden',
                        children: (0, u.jsx)('button', {
                          ref: v,
                          className: `${j[s]} p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors`,
                          onClick: p,
                          'aria-expanded': b,
                          'aria-controls': 'mobile-menu',
                          'aria-label': b ? '關閉選單' : '開啟選單',
                          children: (0, u.jsx)('svg', {
                            className: 'w-6 h-6',
                            fill: 'none',
                            stroke: 'currentColor',
                            viewBox: '0 0 24 24',
                            children: b
                              ? (0, u.jsx)('path', {
                                  strokeLinecap: 'round',
                                  strokeLinejoin: 'round',
                                  strokeWidth: 2,
                                  d: 'M6 18L18 6M6 6l12 12',
                                })
                              : (0, u.jsx)('path', {
                                  strokeLinecap: 'round',
                                  strokeLinejoin: 'round',
                                  strokeWidth: 2,
                                  d: 'M4 6h16M4 12h16M4 18h16',
                                }),
                          }),
                        }),
                      }),
                    ],
                  }),
                  b &&
                    (0, u.jsx)('div', {
                      ref: y,
                      id: 'mobile-menu',
                      className:
                        'lg:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 shadow-lg',
                      role: 'menu',
                      'aria-orientation': 'vertical',
                      'aria-labelledby': 'mobile-menu-button',
                      children: (0, u.jsxs)('div', {
                        className: 'px-4 py-6 space-y-4',
                        children: [
                          t &&
                            (0, u.jsxs)('form', {
                              onSubmit: k,
                              className: 'mb-6',
                              children: [
                                (0, u.jsx)('label', {
                                  htmlFor: 'mobile-search',
                                  className: 'sr-only',
                                  children: '搜尋小說',
                                }),
                                (0, u.jsxs)('div', {
                                  className: 'relative',
                                  children: [
                                    (0, u.jsx)('input', {
                                      id: 'mobile-search',
                                      type: 'search',
                                      placeholder: '搜尋小說...',
                                      value: c,
                                      onChange: e => x(e.target.value),
                                      className:
                                        'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                                      'aria-label': '搜尋小說',
                                    }),
                                    (0, u.jsx)('button', {
                                      type: 'submit',
                                      className:
                                        'absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600',
                                      'aria-label': '執行搜尋',
                                      children: (0, u.jsx)('svg', {
                                        className: 'w-5 h-5',
                                        fill: 'none',
                                        stroke: 'currentColor',
                                        viewBox: '0 0 24 24',
                                        children: (0, u.jsx)('path', {
                                          strokeLinecap: 'round',
                                          strokeLinejoin: 'round',
                                          strokeWidth: 2,
                                          d: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z',
                                        }),
                                      }),
                                    }),
                                  ],
                                }),
                              ],
                            }),
                          (0, u.jsx)('div', {
                            className: 'space-y-2',
                            children: g.map(e =>
                              (0, u.jsx)(
                                'button',
                                {
                                  className:
                                    'block w-full text-left px-4 py-3 text-gray-900 hover:bg-gray-50 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                                  onClick: () => N(e.path),
                                  role: 'menuitem',
                                  children: e.label,
                                },
                                e.path
                              )
                            ),
                          }),
                          r &&
                            (0, u.jsxs)('div', {
                              className: 'border-t border-gray-200 pt-4 space-y-3',
                              children: [
                                (0, u.jsx)('button', {
                                  className:
                                    'w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                                  onClick: () => S('login'),
                                  children: '登入',
                                }),
                                (0, u.jsx)('button', {
                                  className:
                                    'w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                                  onClick: () => S('register'),
                                  children: '註冊',
                                }),
                              ],
                            }),
                        ],
                      }),
                    }),
                ],
              }),
            });
          },
          p = {
            fontSize: { min: 12, max: 24, step: 1 },
            lineHeight: { min: 1.2, max: 2, step: 0.1 },
          },
          h = (0, f.createContext)(null),
          y = () => {
            let e = (0, f.useContext)(h);
            if (!e) throw Error('useSettings must be used within a SettingsProvider');
            return e;
          },
          v = () => {
            let e = y(),
              { settings: t } = e,
              r =
                16 === t.fontSize &&
                1.6 === t.lineHeight &&
                'light' === t.theme &&
                'serif' === t.fontFamily;
            return {
              ...e,
              isDefaultSettings: r,
              constraints: p,
              getCSSVariables: () => ({
                '--reader-font-size': `${t.fontSize}px`,
                '--reader-line-height': t.lineHeight.toString(),
                '--reader-font-family': j(t.fontFamily),
                ...w(t.theme),
              }),
            };
          },
          j = e => {
            switch (e) {
              case 'serif':
              default:
                return 'Georgia, "Times New Roman", Times, serif';
              case 'sans-serif':
                return 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            }
          },
          w = e => {
            switch (e) {
              case 'light':
                return {
                  '--reader-bg-color': '#ffffff',
                  '--reader-text-color': '#1f2937',
                  '--reader-border-color': '#e5e7eb',
                };
              case 'dark':
                return {
                  '--reader-bg-color': '#111827',
                  '--reader-text-color': '#f9fafb',
                  '--reader-border-color': '#374151',
                };
              case 'sepia':
                return {
                  '--reader-bg-color': '#f7f3e9',
                  '--reader-text-color': '#5d4e37',
                  '--reader-border-color': '#d4c5a9',
                };
              default:
                return w('light');
            }
          };
        var N = r(1555),
          k = r.n(N);
        let S = ({
            value: e,
            onChange: t,
            min: r = 12,
            max: s = 32,
            step: l = 1,
            showValue: a = !0,
            showMinMax: o = !0,
            ariaLabel: n = '字體大小調整',
            className: i = '',
            testId: d = 'font-size-slider',
          }) => {
            let c = ((e - r) / (s - r)) * 100;
            return (0, u.jsxs)('div', {
              className: `font-size-slider ${i}`,
              'data-testid': d,
              children: [
                (0, u.jsxs)('div', {
                  className: 'flex items-center justify-between mb-2',
                  children: [
                    (0, u.jsx)('label', {
                      htmlFor: `${d}-input`,
                      className: 'text-sm font-medium text-gray-700',
                      children: '字體大小',
                    }),
                    a &&
                      (0, u.jsxs)('span', {
                        className: 'text-sm font-mono text-gray-600',
                        'data-testid': `${d}-value`,
                        children: [e, 'px'],
                      }),
                  ],
                }),
                (0, u.jsxs)('div', {
                  className: 'relative',
                  children: [
                    (0, u.jsx)('input', {
                      id: `${d}-input`,
                      type: 'range',
                      min: r,
                      max: s,
                      step: l,
                      value: e,
                      onChange: e => {
                        t(parseInt(e.target.value, 10));
                      },
                      'aria-label': n,
                      'aria-valuemin': r,
                      'aria-valuemax': s,
                      'aria-valuenow': e,
                      className: `w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer ${k().slider}`,
                      style: {
                        background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${c}%, #e5e7eb ${c}%, #e5e7eb 100%)`,
                      },
                    }),
                    o &&
                      (0, u.jsxs)('div', {
                        className: 'flex justify-between mt-1',
                        children: [
                          (0, u.jsx)('span', { className: 'text-xs text-gray-500', children: r }),
                          (0, u.jsx)('span', { className: 'text-xs text-gray-500', children: s }),
                        ],
                      }),
                  ],
                }),
              ],
            });
          },
          P = ({ isOpen: e = !0, onClose: t, className: r = '', testId: s = 'settings-panel' }) => {
            let {
              settings: l,
              updateSetting: a,
              resetSettings: o,
              isDefaultSettings: n,
              constraints: i,
            } = v();
            return e
              ? (0, u.jsxs)('div', {
                  className: `settings-panel bg-white rounded-lg shadow-lg p-6 ${r}`,
                  'data-testid': s,
                  children: [
                    (0, u.jsxs)('div', {
                      className: 'flex items-center justify-between mb-6',
                      children: [
                        (0, u.jsx)('h2', {
                          className: 'text-lg font-semibold text-gray-900',
                          children: '閱讀設定',
                        }),
                        t &&
                          (0, u.jsx)('button', {
                            onClick: t,
                            className: 'text-gray-400 hover:text-gray-600 transition-colors',
                            'aria-label': '關閉設定面板',
                            'data-testid': `${s}-close`,
                            children: (0, u.jsx)('svg', {
                              className: 'w-5 h-5',
                              fill: 'none',
                              stroke: 'currentColor',
                              viewBox: '0 0 24 24',
                              children: (0, u.jsx)('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M6 18L18 6M6 6l12 12',
                              }),
                            }),
                          }),
                      ],
                    }),
                    (0, u.jsx)('div', {
                      className: 'space-y-6',
                      children: (0, u.jsx)('div', {
                        children: (0, u.jsx)(S, {
                          value: l.fontSize,
                          onChange: e => a('fontSize', e),
                          min: i.fontSize.min,
                          max: i.fontSize.max,
                          testId: `${s}-font-size`,
                        }),
                      }),
                    }),
                    (0, u.jsx)('div', {
                      className: 'mt-6 pt-6 border-t border-gray-200',
                      children: (0, u.jsx)('button', {
                        onClick: o,
                        disabled: n,
                        className: `
            w-full py-2 px-4 rounded-md text-sm font-medium transition-colors
            ${n ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          `,
                        'data-testid': `${s}-reset`,
                        children: '重置為預設值',
                      }),
                    }),
                  ],
                })
              : null;
          },
          C = () => {
            let [e, t] = (0, f.useState)(!1),
              r = {
                title: '第一章：旅程的開始',
                content: `晨曦初現，東方的天際泛起一抹魚肚白。李雲站在山頂，眺望著遠方連綿起伏的群山。晨風拂過他的面頰，帶來一絲涼意。

他深吸一口氣，感受著清晨特有的清新空氣。今天，是他踏上修仙之路的第一天。作為青雲門的新入門弟子，他心中既有期待，也有些許不安。

"師弟，該走了。"身後傳來一個溫和的聲音。李雲回過頭，看到他的師兄王陽正微笑著看著他。王陽是外門弟子中的佼佼者，為人和善，深受同門喜愛。

"是，師兄。"李雲點點頭，最後看了一眼山下的村莊。那裡，是他生活了十六年的地方。如今，他即將開始一段全新的人生旅程。

兩人並肩走在山間小道上，朝陽的光芒透過樹葉的縫隙灑在地上，形成斑駁的光影。山林中不時傳來鳥鳴聲，為這寧靜的清晨增添了幾分生機。

"師弟不必緊張，"王陽似乎看出了李雲的心思，"青雲門雖然規矩森嚴，但對待弟子還是很寬厚的。只要你勤奮修煉，假以時日，必能有所成就。"

李雲感激地看了師兄一眼："多謝師兄指點。"

隨著太陽逐漸升高，前方的道路也變得越來越寬闊。遠處，青雲門的山門已經隱約可見。那巍峨的建築群依山而建，在陽光下顯得格外壯觀。

這一刻，李雲的心中充滿了對未來的憧憬。他知道，真正的挑戰才剛剛開始...`,
              };
            return (0, u.jsxs)(u.Fragment, {
              children: [
                (0, u.jsx)(b, {}),
                (0, u.jsxs)(x, {
                  maxWidth: 'reading',
                  padding: 'md',
                  children: [
                    (0, u.jsxs)('div', {
                      className: 'min-h-screen',
                      children: [
                        (0, u.jsx)('div', {
                          className:
                            'sticky top-16 z-30 bg-white border-b border-gray-200 px-4 py-2',
                          children: (0, u.jsxs)('div', {
                            className: 'flex items-center justify-between max-w-4xl mx-auto',
                            children: [
                              (0, u.jsx)('h1', {
                                className: 'text-lg font-medium text-gray-900',
                                children: r.title,
                              }),
                              (0, u.jsx)('button', {
                                onClick: () => t(!e),
                                className:
                                  'p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors',
                                'aria-label': '開啟設定',
                                children: (0, u.jsx)('svg', {
                                  width: '20',
                                  height: '20',
                                  viewBox: '0 0 24 24',
                                  fill: 'currentColor',
                                  children: (0, u.jsx)('path', {
                                    d: 'M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z',
                                  }),
                                }),
                              }),
                            ],
                          }),
                        }),
                        (0, u.jsxs)('div', {
                          className: 'flex gap-8 mt-8',
                          children: [
                            (0, u.jsx)('article', {
                              className: 'flex-1 prose prose-gray max-w-none',
                              style: {
                                fontSize: 'var(--reader-font-size, 16px)',
                                lineHeight: 'var(--reader-line-height, 1.6)',
                                fontFamily: 'var(--reader-font-family, serif)',
                                color: 'var(--reader-text-color, #1f2937)',
                              },
                              children: (0, u.jsxs)('div', {
                                className: 'px-8 py-12 rounded-lg',
                                style: {
                                  backgroundColor: 'var(--reader-bg-color, #ffffff)',
                                  borderColor: 'var(--reader-border-color, #e5e7eb)',
                                  borderWidth: '1px',
                                  borderStyle: 'solid',
                                },
                                children: [
                                  (0, u.jsx)('h1', {
                                    className: 'text-3xl font-bold mb-8',
                                    style: { color: 'inherit' },
                                    children: r.title,
                                  }),
                                  (0, u.jsx)('div', {
                                    className: 'whitespace-pre-line',
                                    children: r.content,
                                  }),
                                  (0, u.jsxs)('div', {
                                    className:
                                      'flex justify-between items-center mt-12 pt-8 border-t',
                                    style: { borderColor: 'var(--reader-border-color, #e5e7eb)' },
                                    children: [
                                      (0, u.jsx)('button', {
                                        className: 'px-4 py-2 text-sm rounded-lg transition-colors',
                                        style: {
                                          backgroundColor: 'var(--reader-bg-color, #ffffff)',
                                          color: 'var(--reader-text-color, #1f2937)',
                                          border: '1px solid var(--reader-border-color, #e5e7eb)',
                                        },
                                        disabled: !0,
                                        children: '上一章',
                                      }),
                                      (0, u.jsx)('span', {
                                        className: 'text-sm',
                                        style: { color: 'var(--reader-text-color, #1f2937)' },
                                        children: '第 1 / 100 章',
                                      }),
                                      (0, u.jsx)('button', {
                                        className:
                                          'px-4 py-2 text-sm rounded-lg transition-colors hover:opacity-80',
                                        style: {
                                          backgroundColor: 'var(--reader-text-color, #1f2937)',
                                          color: 'var(--reader-bg-color, #ffffff)',
                                        },
                                        children: '下一章',
                                      }),
                                    ],
                                  }),
                                ],
                              }),
                            }),
                            (0, u.jsx)('aside', {
                              className: `hidden lg:block transition-all duration-300 ${e ? 'w-80' : 'w-0'}`,
                              children:
                                e &&
                                (0, u.jsx)('div', {
                                  className: 'sticky top-32',
                                  children: (0, u.jsx)(P, { isOpen: !0, onClose: () => t(!1) }),
                                }),
                            }),
                          ],
                        }),
                      ],
                    }),
                    e &&
                      (0, u.jsxs)('div', {
                        className: 'lg:hidden',
                        children: [
                          (0, u.jsx)('div', {
                            className: 'fixed inset-0 bg-black bg-opacity-50 z-40',
                            onClick: () => t(!1),
                          }),
                          (0, u.jsx)('div', {
                            className: 'fixed inset-x-0 bottom-0 z-50',
                            children: (0, u.jsx)(P, {
                              isOpen: !0,
                              onClose: () => t(!1),
                              className: 'rounded-t-xl',
                            }),
                          }),
                        ],
                      }),
                  ],
                }),
              ],
            });
          },
          M = (0, o.M)(s, 'default'),
          _ = (0, o.M)(s, 'getStaticProps'),
          E = (0, o.M)(s, 'getStaticPaths'),
          z = (0, o.M)(s, 'getServerSideProps'),
          L = (0, o.M)(s, 'config'),
          $ = (0, o.M)(s, 'reportWebVitals'),
          A = (0, o.M)(s, 'unstable_getStaticProps'),
          O = (0, o.M)(s, 'unstable_getStaticPaths'),
          G = (0, o.M)(s, 'unstable_getStaticParams'),
          I = (0, o.M)(s, 'unstable_getServerProps'),
          B = (0, o.M)(s, 'unstable_getServerSideProps'),
          F = new l.PagesRouteModule({
            definition: {
              kind: a.A.PAGES,
              page: '/ReaderDemo',
              pathname: '/ReaderDemo',
              bundlePath: '',
              filename: '',
            },
            components: { App: c(), Document: i() },
            userland: s,
          });
      },
      8732: (e, t) => {
        'use strict';
        Object.defineProperty(t, 'M', {
          enumerable: !0,
          get: function () {
            return function e(t, r) {
              return r in t
                ? t[r]
                : 'then' in t && 'function' == typeof t.then
                  ? t.then(t => e(t, r))
                  : 'function' == typeof t && 'default' === r
                    ? t
                    : void 0;
            };
          },
        });
      },
    });
  var t = require('../webpack-runtime.js');
  t.C(e);
  var r = e => t((t.s = e)),
    s = t.X(0, [488], () => r(6791));
  module.exports = s;
})();
