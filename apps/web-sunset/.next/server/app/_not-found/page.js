(() => {
  var e = {};
  (e.id = 492),
    (e.ids = [492]),
    (e.modules = {
      846: e => {
        'use strict';
        e.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js');
      },
      3033: e => {
        'use strict';
        e.exports = require('next/dist/server/app-render/work-unit-async-storage.external.js');
      },
      3295: e => {
        'use strict';
        e.exports = require('next/dist/server/app-render/after-task-async-storage.external.js');
      },
      3362: (e, r, t) => {
        Promise.resolve().then(t.t.bind(t, 477, 23)),
          Promise.resolve().then(t.t.bind(t, 7197, 23)),
          Promise.resolve().then(t.t.bind(t, 5301, 23)),
          Promise.resolve().then(t.t.bind(t, 4852, 23)),
          Promise.resolve().then(t.t.bind(t, 252, 23)),
          Promise.resolve().then(t.t.bind(t, 8640, 23)),
          Promise.resolve().then(t.t.bind(t, 254, 23)),
          Promise.resolve().then(t.t.bind(t, 2024, 23));
      },
      3873: e => {
        'use strict';
        e.exports = require('path');
      },
      4133: (e, r, t) => {
        Promise.resolve().then(t.t.bind(t, 751, 23)),
          Promise.resolve().then(t.t.bind(t, 6483, 23)),
          Promise.resolve().then(t.t.bind(t, 6631, 23)),
          Promise.resolve().then(t.t.bind(t, 9638, 23)),
          Promise.resolve().then(t.t.bind(t, 7618, 23)),
          Promise.resolve().then(t.t.bind(t, 8454, 23)),
          Promise.resolve().then(t.t.bind(t, 5780, 23)),
          Promise.resolve().then(t.t.bind(t, 6454, 23));
      },
      4837: () => {},
      6297: () => {},
      8175: (e, r, t) => {
        'use strict';
        t.r(r),
          t.d(r, {
            GlobalError: () => i.a,
            __next_app__: () => p,
            pages: () => u,
            routeModule: () => m,
            tree: () => a,
          });
        var n = t(8602),
          s = t(669),
          o = t(5301),
          i = t.n(o),
          d = t(2266),
          l = {};
        for (let e in d)
          0 >
            ['default', 'tree', 'pages', 'GlobalError', '__next_app__', 'routeModule'].indexOf(e) &&
            (l[e] = () => d[e]);
        t.d(r, l);
        let a = {
            children: [
              '',
              {
                children: [
                  '/_not-found',
                  {
                    children: [
                      '__PAGE__',
                      {},
                      {
                        page: [
                          () => Promise.resolve().then(t.t.bind(t, 9419, 23)),
                          'next/dist/client/components/not-found-error',
                        ],
                      },
                    ],
                  },
                  {},
                ],
              },
              {
                layout: [
                  () => Promise.resolve().then(t.bind(t, 9692)),
                  '/Users/<USER>/Documents/project/NovelWebsite/frontend/app/layout.tsx',
                ],
                'not-found': [
                  () => Promise.resolve().then(t.t.bind(t, 9419, 23)),
                  'next/dist/client/components/not-found-error',
                ],
                forbidden: [
                  () => Promise.resolve().then(t.t.bind(t, 4470, 23)),
                  'next/dist/client/components/forbidden-error',
                ],
                unauthorized: [
                  () => Promise.resolve().then(t.t.bind(t, 4636, 23)),
                  'next/dist/client/components/unauthorized-error',
                ],
              },
            ],
          }.children,
          u = [],
          p = { require: t, loadChunk: () => Promise.resolve() },
          m = new n.AppPageRouteModule({
            definition: {
              kind: s.RouteKind.APP_PAGE,
              page: '/_not-found/page',
              pathname: '/_not-found',
              bundlePath: '',
              filename: '',
              appPaths: [],
            },
            userland: { loaderTree: a },
          });
      },
      9121: e => {
        'use strict';
        e.exports = require('next/dist/server/app-render/action-async-storage.external.js');
      },
      9294: e => {
        'use strict';
        e.exports = require('next/dist/server/app-render/work-async-storage.external.js');
      },
      9692: (e, r, t) => {
        'use strict';
        t.r(r), t.d(r, { default: () => d, metadata: () => i });
        var n = t(4234),
          s = t(281),
          o = t.n(s);
        t(4837);
        let i = {
          title: 'NovelWebsite - Next.js',
          description: 'A modern novel reading platform.',
        };
        function d({ children: e }) {
          return (0, n.jsx)('html', {
            lang: 'en',
            children: (0, n.jsx)('body', { className: o().className, children: e }),
          });
        }
      },
      9849: () => {},
    });
  var r = require('../../webpack-runtime.js');
  r.C(e);
  var t = e => r((r.s = e)),
    n = r.X(0, [387], () => t(8175));
  module.exports = n;
})();
