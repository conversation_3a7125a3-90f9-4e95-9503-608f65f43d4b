globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST['/page'] = {
  moduleLoading: { prefix: '/_next/' },
  ssrModuleMapping: {
    24421: { '*': { id: '6483', name: '*', chunks: [], async: false } },
    24684: { '*': { id: '6454', name: '*', chunks: [], async: false } },
    25350: { '*': { id: '7618', name: '*', chunks: [], async: false } },
    30542: { '*': { id: '8454', name: '*', chunks: [], async: false } },
    56619: { '*': { id: '7613', name: '*', chunks: [], async: false } },
    57927: { '*': { id: '751', name: '*', chunks: [], async: false } },
    62673: { '*': { id: '6631', name: '*', chunks: [], async: false } },
    63118: { '*': { id: '9638', name: '*', chunks: [], async: false } },
    83006: { '*': { id: '5780', name: '*', chunks: [], async: false } },
  },
  edgeSSRModuleMapping: {},
  clientModules: {
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js':
      { id: 57927, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/esm/client/components/client-page.js':
      { id: 57927, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-segment.js':
      { id: 24421, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/esm/client/components/client-segment.js':
      { id: 24421, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js':
      { id: 62673, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/esm/client/components/error-boundary.js':
      { id: 62673, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js':
      { id: 63118, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js':
      { id: 63118, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js':
      { id: 25350, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/esm/client/components/layout-router.js':
      { id: 25350, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/async-metadata.js':
      { id: 30542, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/esm/client/components/metadata/async-metadata.js':
      { id: 30542, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js':
      { id: 83006, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js':
      { id: 83006, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js':
      { id: 24684, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/esm/client/components/render-from-template-context.js':
      { id: 24684, name: '*', chunks: [], async: false },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{"path":"app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"}':
      {
        id: 29857,
        name: '*',
        chunks: ['177', 'static/chunks/app/layout-8d2c2be35d29cb81.js'],
        async: false,
      },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/app/globals.css': {
      id: 9588,
      name: '*',
      chunks: ['177', 'static/chunks/app/layout-8d2c2be35d29cb81.js'],
      async: false,
    },
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/app/page.tsx': {
      id: 56619,
      name: '*',
      chunks: ['974', 'static/chunks/app/page-fb3742ee787f119f.js'],
      async: false,
    },
  },
  entryCSSFiles: {
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/': [],
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/app/layout': [
      { inlined: false, path: 'static/css/8527a788a2fb2a5a.css' },
    ],
    '/Users/<USER>/Documents/project/NovelWebsite/frontend/app/page': [],
  },
  rscModuleMapping: {
    9588: { '*': { id: '4837', name: '*', chunks: [], async: false } },
    24421: { '*': { id: '7197', name: '*', chunks: [], async: false } },
    24684: { '*': { id: '2024', name: '*', chunks: [], async: false } },
    25350: { '*': { id: '252', name: '*', chunks: [], async: false } },
    30542: { '*': { id: '8640', name: '*', chunks: [], async: false } },
    56619: { '*': { id: '5331', name: '*', chunks: [], async: false } },
    57927: { '*': { id: '477', name: '*', chunks: [], async: false } },
    62673: { '*': { id: '5301', name: '*', chunks: [], async: false } },
    63118: { '*': { id: '4852', name: '*', chunks: [], async: false } },
    83006: { '*': { id: '254', name: '*', chunks: [], async: false } },
  },
  edgeRscModuleMapping: {},
};
