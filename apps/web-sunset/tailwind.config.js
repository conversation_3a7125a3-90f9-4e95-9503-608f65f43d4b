/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}', './.storybook/**/*.{js,jsx,ts,tsx}'],
  darkMode: 'class',
  theme: {
    screens: {
      xs: '475px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },
    extend: {
      colors: {
        // 小說網站主題色彩
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        secondary: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        },
        // 閱讀模式色彩
        reading: {
          light: '#fefefe',
          sepia: '#f4f1ea',
          dark: '#1a1a1a',
          night: '#000000',
        },
        // 狀態色彩
        success: {
          50: '#f0fdf4',
          500: '#22c55e',
          600: '#16a34a',
        },
        warning: {
          50: '#fffbeb',
          500: '#f59e0b',
          600: '#d97706',
        },
        error: {
          50: '#fef2f2',
          500: '#ef4444',
          600: '#dc2626',
        },
      },
      fontFamily: {
        // 閱讀字體
        reading: ['Georgia', 'Times New Roman', 'serif'],
        // 界面字體
        ui: ['Inter', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        // 閱讀字體大小
        'reading-xs': ['14px', { lineHeight: '1.6' }],
        'reading-sm': ['16px', { lineHeight: '1.6' }],
        'reading-base': ['18px', { lineHeight: '1.7' }],
        'reading-lg': ['20px', { lineHeight: '1.7' }],
        'reading-xl': ['22px', { lineHeight: '1.8' }],
        'reading-2xl': ['24px', { lineHeight: '1.8' }],
      },
      spacing: {
        // 響應式間距
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
        'safe-left': 'env(safe-area-inset-left)',
        'safe-right': 'env(safe-area-inset-right)',
      },
      animation: {
        // 載入動畫
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
      maxWidth: {
        // 閱讀容器最大寬度
        reading: '65ch',
        'prose-sm': '60ch',
        'prose-base': '65ch',
        'prose-lg': '70ch',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    // 添加自定義工具類
    function ({ addUtilities }) {
      const newUtilities = {
        '.reading-mode-light': {
          backgroundColor: '#fefefe',
          color: '#1a1a1a',
        },
        '.reading-mode-sepia': {
          backgroundColor: '#f4f1ea',
          color: '#5c4b37',
        },
        '.reading-mode-dark': {
          backgroundColor: '#1a1a1a',
          color: '#e5e5e5',
        },
        '.reading-mode-night': {
          backgroundColor: '#000000',
          color: '#cccccc',
        },
        '.safe-area-padding': {
          paddingTop: 'env(safe-area-inset-top)',
          paddingBottom: 'env(safe-area-inset-bottom)',
          paddingLeft: 'env(safe-area-inset-left)',
          paddingRight: 'env(safe-area-inset-right)',
        },
      };
      addUtilities(newUtilities);
    },
  ],
};
