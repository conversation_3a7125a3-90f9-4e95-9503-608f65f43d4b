import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

import ChapterDetail from './pages/ChapterDetail';
import Home from './pages/Home';
import Login from './pages/Login';
import NovelDetail from './pages/NovelDetail';
import Profile from './pages/Profile';
import Register from './pages/Register';
import Search from './pages/Search';
import ReaderDemo from './pages/ReaderDemo';

const AppRoutes: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/novels/:novelSlug" element={<NovelDetail />} />
        <Route path="/novels/:novelSlug/chapters/:chapterId" element={<ChapterDetail />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/profile" element={<Profile />} />
        <Route path="/search" element={<Search />} />
        <Route path="/reader-demo" element={<ReaderDemo />} />
      </Routes>
    </Router>
  );
};

export default AppRoutes;
