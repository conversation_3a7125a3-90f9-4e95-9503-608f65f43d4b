/**
 * 閱讀器設定介面定義
 *
 * 這個檔案定義了所有與閱讀體驗相關的設定選項
 */

/**
 * 主題選項
 */
export type ReaderTheme = 'light' | 'dark' | 'sepia';

/**
 * 字體類型選項
 */
export type FontFamily = 'serif' | 'sans-serif';

/**
 * 閱讀器設定介面
 *
 * 包含所有影響閱讀體驗的設定選項
 */
export interface ReaderSettings {
  /** 字體大小 (px) - 範圍: 12-24, 預設: 16 */
  fontSize: number;

  /** 行高 - 範圍: 1.2-2.0, 預設: 1.6 */
  lineHeight: number;

  /** 主題模式 - 預設: 'light' */
  theme: ReaderTheme;

  /** 字體類型 - 預設: 'serif' */
  fontFamily: FontFamily;
}

/**
 * 預設閱讀器設定
 */
export const DEFAULT_READER_SETTINGS: ReaderSettings = {
  fontSize: 16,
  lineHeight: 1.6,
  theme: 'light',
  fontFamily: 'serif',
} as const;

/**
 * 設定範圍限制
 */
export const SETTINGS_CONSTRAINTS = {
  fontSize: {
    min: 12,
    max: 24,
    step: 1,
  },
  lineHeight: {
    min: 1.2,
    max: 2.0,
    step: 0.1,
  },
} as const;

/**
 * 設定更新函數類型
 */
export type UpdateSettingFunction = <K extends keyof ReaderSettings>(
  key: K,
  value: ReaderSettings[K]
) => void;

/**
 * 設定 Context 狀態介面
 */
export interface SettingsContextState {
  /** 當前設定 */
  settings: ReaderSettings;
  /** 更新單一設定項 */
  updateSetting: UpdateSettingFunction;
  /** 重置為預設設定 */
  resetSettings: () => void;
  /** 是否正在載入 */
  isLoading: boolean;
}
