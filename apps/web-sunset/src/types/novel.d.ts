export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
}

export interface Tag {
  id: number;
  name: string;
  slug: string;
}

export interface Novel {
  id: number;
  title: string;
  slug: string;
  author: string;
  description?: string;
  cover_url?: string;
  source_url?: string;
  category?: Category;
  tags?: Tag[];
  chapters?: Chapter[];
  total_chapters: number;
  views: number;
  favorites: number;
  status: 'ongoing' | 'completed';
  created_at: string;
  updated_at: string;
  last_chapter_update?: string;
  // Keep legacy properties for backward compatibility
  coverUrl?: string;
  coverImage?: string;
}

export interface Chapter {
  id: number;
  title: string;
  chapter_number: number;
  content?: string; // Only available in detail view
  views: number;
  updated_at: string;
  // Keep legacy properties for backward compatibility
  novelId?: string;
  chapterNumber?: number;
}

export interface SearchResult {
  novels: Novel[];
  total: number;
  page: number;
  limit: number;
}
