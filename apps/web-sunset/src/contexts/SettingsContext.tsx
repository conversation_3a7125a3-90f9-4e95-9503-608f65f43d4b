import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import {
  ReaderSettings,
  DEFAULT_READER_SETTINGS,
  SettingsContextState,
  UpdateSettingFunction,
  SETTINGS_CONSTRAINTS,
} from '../types/reader-settings';

/**
 * localStorage 鍵名常數
 */
const STORAGE_KEY = 'readerSettings';

/**
 * 驗證設定值是否在允許範圍內
 */
const validateSettingValue = (
  key: keyof ReaderSettings,
  value: ReaderSettings[keyof ReaderSettings]
): ReaderSettings[keyof ReaderSettings] => {
  switch (key) {
    case 'fontSize':
      const fontSize = value as number;
      return Math.max(
        SETTINGS_CONSTRAINTS.fontSize.min,
        Math.min(SETTINGS_CONSTRAINTS.fontSize.max, fontSize)
      );

    case 'lineHeight':
      const lineHeight = value as number;
      return Math.max(
        SETTINGS_CONSTRAINTS.lineHeight.min,
        Math.min(SETTINGS_CONSTRAINTS.lineHeight.max, lineHeight)
      );

    case 'theme':
    case 'fontFamily':
      // 對於枚舉類型，直接返回（TypeScript 會確保類型安全）
      return value;

    default:
      return value;
  }
};

/**
 * 從 localStorage 載入設定
 */
const loadSettingsFromStorage = (): ReaderSettings => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (!saved) {
      return DEFAULT_READER_SETTINGS;
    }

    const parsed = JSON.parse(saved);

    // 驗證載入的設定並與預設值合併（確保新增的設定有預設值）
    const validatedSettings: ReaderSettings = {
      ...DEFAULT_READER_SETTINGS,
      ...parsed,
    };

    // 驗證每個設定值
    Object.keys(validatedSettings).forEach(key => {
      const settingKey = key as keyof ReaderSettings;
      const validatedValue = validateSettingValue(settingKey, validatedSettings[settingKey]);
      (validatedSettings as any)[settingKey] = validatedValue;
    });

    return validatedSettings;
  } catch (error) {
    console.warn('Failed to load settings from localStorage:', error);
    return DEFAULT_READER_SETTINGS;
  }
};

/**
 * 儲存設定到 localStorage
 */
const saveSettingsToStorage = (settings: ReaderSettings): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Failed to save settings to localStorage:', error);
  }
};

/**
 * Settings Context
 */
const SettingsContext = createContext<SettingsContextState | null>(null);

/**
 * useSettings Hook
 *
 * 提供閱讀器設定的狀態管理功能
 */
export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

/**
 * SettingsProvider Props
 */
interface SettingsProviderProps {
  children: React.ReactNode;
}

/**
 * Settings Provider 組件
 *
 * 提供全域的閱讀器設定狀態管理
 */
export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<ReaderSettings>(DEFAULT_READER_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化時載入設定
  useEffect(() => {
    const loadedSettings = loadSettingsFromStorage();
    setSettings(loadedSettings);
    setIsLoading(false);
  }, []);

  /**
   * 更新單一設定項
   */
  const updateSetting: UpdateSettingFunction = useCallback((key, value) => {
    const validatedValue = validateSettingValue(key, value);

    setSettings(prevSettings => {
      const newSettings = {
        ...prevSettings,
      };
      (newSettings as any)[key] = validatedValue;

      // 儲存到 localStorage
      saveSettingsToStorage(newSettings);

      // TODO: 未來在此處添加 API 同步邏輯
      // if (user && user.isAuthenticated) {
      //   syncSettingsToAPI(newSettings);
      // }

      return newSettings;
    });
  }, []);

  /**
   * 重置為預設設定
   */
  const resetSettings = useCallback(() => {
    setSettings(DEFAULT_READER_SETTINGS);
    saveSettingsToStorage(DEFAULT_READER_SETTINGS);

    // TODO: 未來在此處添加 API 同步邏輯
    // if (user && user.isAuthenticated) {
    //   syncSettingsToAPI(DEFAULT_READER_SETTINGS);
    // }
  }, []);

  const value: SettingsContextState = {
    settings,
    updateSetting,
    resetSettings,
    isLoading,
  };

  return <SettingsContext.Provider value={value}>{children}</SettingsContext.Provider>;
};

export default SettingsContext;
