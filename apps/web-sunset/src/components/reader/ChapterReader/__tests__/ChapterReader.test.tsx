import React from 'react';
import { render, screen, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import ChapterReader from '../ChapterReader';

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('ChapterReader', () => {
  const defaultProps = {
    title: '測試章節',
    content: '這是測試內容',
    prevChapter: '1',
    nextChapter: '3',
  };

  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    mockNavigate.mockClear();
    user = userEvent.setup();
  });

  it('renders chapter title and content', () => {
    render(
      <MemoryRouter>
        <ChapterReader {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByText(defaultProps.title)).toBeInTheDocument();
    expect(screen.getByText(defaultProps.content)).toBeInTheDocument();
  });

  it('navigates to previous chapter when clicking previous button', async () => {
    render(
      <MemoryRouter>
        <ChapterReader {...defaultProps} />
      </MemoryRouter>
    );

    const prevButton = screen.getByLabelText('previous chapter');

    await act(async () => {
      await user.click(prevButton);
    });

    expect(mockNavigate).toHaveBeenCalledWith(`/chapter/${defaultProps.prevChapter}`);
  });

  it('navigates to next chapter when clicking next button', async () => {
    render(
      <MemoryRouter>
        <ChapterReader {...defaultProps} />
      </MemoryRouter>
    );

    const nextButton = screen.getByLabelText('next chapter');

    await act(async () => {
      await user.click(nextButton);
    });

    expect(mockNavigate).toHaveBeenCalledWith(`/chapter/${defaultProps.nextChapter}`);
  });

  it('disables navigation buttons when no prev/next chapter', () => {
    render(
      <MemoryRouter>
        <ChapterReader title={defaultProps.title} content={defaultProps.content} />
      </MemoryRouter>
    );

    const prevButton = screen.getByRole('button', { name: /previous chapter/i });
    const nextButton = screen.getByRole('button', { name: /next chapter/i });

    expect(prevButton).toBeDisabled();
    expect(nextButton).toBeDisabled();
  });

  it('does not navigate when clicking disabled buttons', async () => {
    render(
      <MemoryRouter>
        <ChapterReader title={defaultProps.title} content={defaultProps.content} />
      </MemoryRouter>
    );

    const prevButton = screen.getByRole('button', { name: /previous chapter/i });
    const nextButton = screen.getByRole('button', { name: /next chapter/i });

    // 驗證按鈕確實被禁用
    expect(prevButton).toBeDisabled();
    expect(nextButton).toBeDisabled();

    // 點擊 disabled 按鈕不應觸發導航
    await act(async () => {
      await user.click(prevButton);
      await user.click(nextButton);
    });

    expect(mockNavigate).not.toHaveBeenCalled();
  });

  // 新增邊界情況測試
  describe('邊界情況處理', () => {
    it('handles invalid chapter IDs gracefully', () => {
      const invalidProps = {
        title: '測試章節',
        content: '這是測試內容',
        prevChapter: null,
        nextChapter: undefined,
      };

      render(
        <MemoryRouter>
          <ChapterReader {...invalidProps} />
        </MemoryRouter>
      );

      const prevButton = screen.getByRole('button', { name: /previous chapter/i });
      const nextButton = screen.getByRole('button', { name: /next chapter/i });

      expect(prevButton).toBeDisabled();
      expect(nextButton).toBeDisabled();
    });

    it('handles negative chapter IDs', () => {
      const negativeProps = {
        title: '測試章節',
        content: '這是測試內容',
        prevChapter: '-1',
        nextChapter: '0',
      };

      render(
        <MemoryRouter>
          <ChapterReader {...negativeProps} />
        </MemoryRouter>
      );

      // 即使是負數或0，只要提供了值就應該啟用按鈕（業務邏輯決定）
      const prevButton = screen.getByRole('button', { name: /previous chapter/i });
      const nextButton = screen.getByRole('button', { name: /next chapter/i });

      expect(prevButton).not.toBeDisabled();
      expect(nextButton).not.toBeDisabled();
    });

    it('renders correctly with empty content', () => {
      const emptyContentProps = {
        title: '空章節',
        content: '',
        prevChapter: '1',
        nextChapter: '3',
      };

      render(
        <MemoryRouter>
          <ChapterReader {...emptyContentProps} />
        </MemoryRouter>
      );

      expect(screen.getByText(emptyContentProps.title)).toBeInTheDocument();
      // 空內容應該還是有元素存在，只是內容為空
      expect(screen.queryByText(emptyContentProps.content)).toBeInTheDocument();
    });
  });

  // 可訪問性測試
  describe('可訪問性 (ARIA)', () => {
    it('provides proper ARIA labels for navigation buttons', () => {
      render(
        <MemoryRouter>
          <ChapterReader {...defaultProps} />
        </MemoryRouter>
      );

      const prevButton = screen.getByLabelText('previous chapter');
      const nextButton = screen.getByLabelText('next chapter');

      // 驗證 ARIA 屬性
      expect(prevButton).toHaveAttribute('aria-label', 'previous chapter');
      expect(nextButton).toHaveAttribute('aria-label', 'next chapter');
    });

    it('properly handles disabled state with ARIA attributes', () => {
      render(
        <MemoryRouter>
          <ChapterReader title={defaultProps.title} content={defaultProps.content} />
        </MemoryRouter>
      );

      const prevButton = screen.getByRole('button', { name: /previous chapter/i });
      const nextButton = screen.getByRole('button', { name: /next chapter/i });

      // 檢查 disabled 狀態是否正確反映在 ARIA 中
      expect(prevButton).toHaveAttribute('disabled');
      expect(nextButton).toHaveAttribute('disabled');
    });
  });
});
