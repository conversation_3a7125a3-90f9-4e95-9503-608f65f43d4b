import type { Meta, StoryObj } from '@storybook/react-webpack5';
import Layout from './Layout';

const meta = {
  title: 'Components/Layout',
  component: Layout,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'A responsive layout container with configurable max-width, padding, and alignment options. Ideal for creating consistent page layouts.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    maxWidth: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg', 'xl', '2xl', 'full', 'reading'],
      description: 'Maximum width of the container',
    },
    padding: {
      control: { type: 'select' },
      options: ['none', 'sm', 'md', 'lg', 'xl'],
      description: 'Responsive padding levels',
    },
    centered: {
      control: { type: 'boolean' },
      description: 'Whether to center the container horizontally',
    },
    safeArea: {
      control: { type: 'boolean' },
      description: 'Whether to include safe area padding for mobile devices',
    },
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes',
    },
  },
  decorators: [
    Story => (
      <div style={{ minHeight: '100vh', backgroundColor: '#f3f4f6' }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof Layout>;

export default meta;
type Story = StoryObj<typeof meta>;

// 預設示例
export const Default: Story = {
  args: {
    maxWidth: 'lg',
    padding: 'md',
    centered: true,
    safeArea: false,
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">預設佈局容器</h2>
        <p className="text-gray-600 mb-4">
          這是一個響應式佈局容器，適用於大部分頁面內容。它提供了合理的最大寬度限制和內邊距。
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <div key={i} className="bg-gray-100 rounded p-4 text-center">
              內容區塊 {i}
            </div>
          ))}
        </div>
      </div>
    ),
  },
};

// 不同最大寬度
export const SmallMaxWidth: Story = {
  args: {
    ...Default.args,
    maxWidth: 'sm',
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">小型容器 (max-w-sm)</h2>
        <p className="text-gray-600">適用於簡單的對話框或側邊欄內容。</p>
      </div>
    ),
  },
};

export const LargeMaxWidth: Story = {
  args: {
    ...Default.args,
    maxWidth: 'xl',
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">大型容器 (max-w-6xl)</h2>
        <p className="text-gray-600 mb-6">適用於需要更多空間的內容，如儀表板或複雜佈局。</p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
            <div key={i} className="bg-blue-50 rounded p-4 text-center">
              項目 {i}
            </div>
          ))}
        </div>
      </div>
    ),
  },
};

export const FullWidth: Story = {
  args: {
    ...Default.args,
    maxWidth: 'full',
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">全寬容器</h2>
        <p className="text-gray-600 mb-6">佔據整個視窗寬度，適用於需要充分利用空間的內容。</p>
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-8 rounded-lg">
          <h3 className="text-xl font-semibold">全寬展示區域</h3>
        </div>
      </div>
    ),
  },
};

export const ReadingWidth: Story = {
  args: {
    ...Default.args,
    maxWidth: 'reading',
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">閱讀容器 (65ch)</h2>
        <div className="prose prose-lg">
          <p>
            這個容器專為閱讀體驗設計，使用字符寬度 (65ch) 作為最大寬度。
            這個寬度被認為是最適合閱讀的行長度，能提供良好的視覺流暢性。
          </p>
          <p>
            在小說網站中，這個寬度特別適用於章節內容、文章頁面或任何需要長時間閱讀的內容。
            它確保文字不會在寬螢幕上延伸得太長，影響閱讀體驗。
          </p>
          <p>同時，這個寬度也符合網頁設計的最佳實踐，為用戶提供舒適的閱讀環境。</p>
        </div>
      </div>
    ),
  },
};

// 不同內邊距
export const NoPadding: Story = {
  args: {
    ...Default.args,
    padding: 'none',
    children: (
      <div className="bg-white shadow-sm">
        <div className="bg-red-500 text-white p-4">
          <h2 className="text-xl font-bold">無內邊距容器</h2>
          <p>容器本身沒有內邊距，內容直接貼邊。</p>
        </div>
      </div>
    ),
  },
};

export const LargePadding: Story = {
  args: {
    ...Default.args,
    padding: 'xl',
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">大內邊距容器</h2>
        <p className="text-gray-600">提供更多的呼吸空間，適用於重要內容或著陸頁。</p>
      </div>
    ),
  },
};

// 行動裝置安全區域
export const WithSafeArea: Story = {
  args: {
    ...Default.args,
    safeArea: true,
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">包含安全區域</h2>
        <p className="text-gray-600 mb-4">
          在支援的裝置上，容器會自動避開瀏海、主頁指示器等系統 UI 元素。
        </p>
        <div className="bg-yellow-100 border border-yellow-400 rounded p-4">
          <p className="text-yellow-800">📱 在 iPhone X 系列或其他有瀏海的裝置上查看效果最佳</p>
        </div>
      </div>
    ),
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
};

// 非居中佈局
export const NotCentered: Story = {
  args: {
    ...Default.args,
    centered: false,
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">左對齊容器</h2>
        <p className="text-gray-600">容器不會自動居中，而是貼左對齊。</p>
      </div>
    ),
  },
};

// 響應式測試
export const ResponsiveTest: Story = {
  args: {
    ...Default.args,
    maxWidth: 'lg',
    padding: 'md',
    children: (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-4">響應式測試</h2>
        <p className="text-gray-600 mb-6">切換不同的視窗大小來測試響應式行為：</p>

        {/* 響應式網格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
          {['手機', '平板', '桌機', '大螢幕'].map((device, i) => (
            <div key={i} className="bg-blue-50 border border-blue-200 rounded p-4 text-center">
              <div className="font-semibold text-blue-900">{device}</div>
              <div className="text-sm text-blue-600 mt-1">
                {i === 0 && <span className="sm:hidden">顯示中</span>}
                {i === 1 && <span className="hidden sm:block lg:hidden">顯示中</span>}
                {i === 2 && <span className="hidden lg:block xl:hidden">顯示中</span>}
                {i === 3 && <span className="hidden xl:block">顯示中</span>}
                <span
                  className={`${
                    (i === 0 && 'sm:inline') ||
                    (i === 1 && 'sm:hidden lg:inline') ||
                    (i === 2 && 'lg:hidden xl:inline') ||
                    (i === 3 && 'xl:hidden')
                  } text-gray-400`}
                >
                  隱藏
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* 響應式文字 */}
        <div className="bg-gray-50 rounded p-4">
          <h3 className="font-semibold mb-2">當前斷點資訊：</h3>
          <div className="text-sm text-gray-600">
            <span className="sm:hidden">📱 手機視圖 (&lt; 640px)</span>
            <span className="hidden sm:block md:hidden">📱 小平板視圖 (640px - 768px)</span>
            <span className="hidden md:block lg:hidden">💻 平板視圖 (768px - 1024px)</span>
            <span className="hidden lg:block xl:hidden">🖥️ 桌機視圖 (1024px - 1280px)</span>
            <span className="hidden xl:block 2xl:hidden">🖥️ 大桌機視圖 (1280px - 1536px)</span>
            <span className="hidden 2xl:block">🖥️ 超大螢幕視圖 (&gt; 1536px)</span>
          </div>
        </div>
      </div>
    ),
  },
};

// 小說內容示例
export const NovelContentExample: Story = {
  args: {
    maxWidth: 'reading',
    padding: 'lg',
    centered: true,
    safeArea: true,
    children: (
      <article className="bg-white rounded-lg shadow-sm p-8">
        <header className="mb-8 border-b border-gray-200 pb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">第一章：開始的地方</h1>
          <div className="flex items-center text-sm text-gray-500 space-x-4">
            <span>作者：範例作者</span>
            <span>•</span>
            <span>字數：2,847 字</span>
            <span>•</span>
            <span>更新時間：2024-06-21</span>
          </div>
        </header>

        <div className="prose prose-lg prose-gray max-w-none">
          <p>
            這是一個春天的早晨，陽光透過薄紗窗簾灑進房間，在地板上投下斑駁的光影。
            李小明揉了揉惺忪的睡眼，看了看床頭的鬧鐘——八點三十分。
          </p>

          <p>
            "糟了！要遲到了！"他一個翻身跳下床，匆忙地穿上校服。
            今天是他轉學第一天，絕對不能遲到給新同學留下不好的印象。
          </p>

          <p>
            匆匆洗漱完畢，他抓起書包就往外跑。路上的景色快速地在眼前閃過，
            他想起媽媽昨晚說的話："小明，新學校是個新開始，要好好把握機會。"
          </p>

          <p>
            當他氣喘吁吁地跑到校門口時，發現校園裡異常安靜。 看了看手錶，才發現原來今天是星期六...
          </p>
        </div>

        <footer className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex justify-between items-center text-sm text-gray-500">
            <div className="space-x-4">
              <button className="hover:text-blue-600">👍 讚 (42)</button>
              <button className="hover:text-blue-600">💬 評論 (15)</button>
              <button className="hover:text-blue-600">⭐ 收藏</button>
            </div>
            <div className="space-x-2">
              <button className="hover:text-blue-600">上一章</button>
              <span>•</span>
              <button className="hover:text-blue-600">下一章</button>
            </div>
          </div>
        </footer>
      </article>
    ),
  },
};
