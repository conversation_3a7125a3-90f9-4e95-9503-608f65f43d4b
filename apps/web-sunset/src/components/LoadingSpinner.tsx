import React from 'react';

interface LoadingSpinnerProps {
  /** Display text shown alongside the spinner */
  label?: string;
  /** Size variant of the spinner */
  size?: 'sm' | 'md' | 'lg';
  /** Color theme of the spinner */
  color?: 'blue' | 'gray' | 'green';
  /** Whether to center the spinner in its container */
  centered?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  label = '載入中...',
  size = 'md',
  color = 'blue',
  centered = false,
}) => {
  // Size configurations
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  // Color configurations
  const colorClasses = {
    blue: 'border-blue-500',
    gray: 'border-gray-500',
    green: 'border-green-500',
  };

  // Text size based on spinner size
  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const containerClasses = centered
    ? 'flex items-center justify-center w-full h-full'
    : 'flex items-center justify-center';

  return (
    <div className={containerClasses} role="status" aria-label={label} aria-live="polite">
      <div
        className={`mr-2 animate-spin rounded-full border-b-2 ${sizeClasses[size]} ${colorClasses[color]}`}
        aria-hidden="true"
      />
      <span className={`text-gray-600 ${textSizeClasses[size]}`}>{label}</span>
    </div>
  );
};

export default LoadingSpinner;
