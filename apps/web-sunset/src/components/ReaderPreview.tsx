/**
 * Reader Preview Component
 *
 * 用於演示和測試 CSS 變數的效果
 * 這個組件展示了閱讀器樣式在實際使用中的表現
 */

import React from 'react';

interface ReaderPreviewProps {
  /** 預覽標題 */
  title?: string;
  /** 預覽內容 */
  content?: string;
  /** 自定義 CSS 類別 */
  className?: string;
}

const ReaderPreview: React.FC<ReaderPreviewProps> = ({
  title = '第一章：開始',
  content = `這是一個示例段落，用來展示閱讀器的字體大小、行高、字體族和主題色彩設定。

  您可以通過設定面板調整這些參數，並即時看到效果。這個預覽組件使用了 CSS 變數來應用樣式，確保所有設定都能正確反映在實際的閱讀體驗中。

  支援的設定包括：
  • 字體大小調整
  • 行高調整
  • 字體族選擇（襯線體/無襯線體）
  • 主題切換（淺色/深色/棕黃色）`,
  className = '',
}) => {
  return (
    <div
      className={`reader-preview ${className}`}
      style={{
        // 使用 CSS 變數
        fontSize: 'var(--reader-font-size, 16px)',
        lineHeight: 'var(--reader-line-height, 1.6)',
        fontFamily: 'var(--reader-font-family, serif)',
        backgroundColor: 'var(--reader-bg-color, #ffffff)',
        color: 'var(--reader-text-color, #000000)',
        borderColor: 'var(--reader-border-color, #e5e7eb)',

        // 基本樣式
        padding: '2rem',
        border: '1px solid',
        borderRadius: '0.5rem',
        maxWidth: '40rem',
        margin: '0 auto',
      }}
    >
      <h2
        style={{
          fontSize: 'calc(var(--reader-font-size, 16px) * 1.5)',
          marginBottom: '1.5rem',
          fontWeight: '600',
          color: 'var(--reader-text-color, #000000)',
        }}
      >
        {title}
      </h2>

      <div
        style={{
          whiteSpace: 'pre-line',
          lineHeight: 'var(--reader-line-height, 1.6)',
        }}
      >
        {content}
      </div>
    </div>
  );
};

export default ReaderPreview;
