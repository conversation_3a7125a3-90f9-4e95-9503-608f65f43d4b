import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { act } from 'react';
import SettingsPanel from '../SettingsPanel';
import { SettingsProvider } from '../../contexts/SettingsContext';
import { DEFAULT_READER_SETTINGS } from '../../types/reader-settings';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <SettingsProvider>{children}</SettingsProvider>
);

describe('SettingsPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('Rendering', () => {
    it('renders when isOpen is true', () => {
      render(
        <TestWrapper>
          <SettingsPanel isOpen={true} />
        </TestWrapper>
      );

      expect(screen.getByTestId('settings-panel')).toBeInTheDocument();
      expect(screen.getByText('閱讀設定')).toBeInTheDocument();
    });

    it('does not render when isOpen is false', () => {
      render(
        <TestWrapper>
          <SettingsPanel isOpen={false} />
        </TestWrapper>
      );

      expect(screen.queryByTestId('settings-panel')).not.toBeInTheDocument();
    });

    it('renders with default isOpen value (true)', () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      );

      expect(screen.getByTestId('settings-panel')).toBeInTheDocument();
    });

    it('shows close button when onClose is provided', () => {
      const onClose = jest.fn();

      render(
        <TestWrapper>
          <SettingsPanel onClose={onClose} />
        </TestWrapper>
      );

      expect(screen.getByLabelText('關閉設定面板')).toBeInTheDocument();
    });

    it('does not show close button when onClose is not provided', () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      );

      expect(screen.queryByLabelText('關閉設定面板')).not.toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(
        <TestWrapper>
          <SettingsPanel className="custom-panel" />
        </TestWrapper>
      );

      expect(screen.getByTestId('settings-panel')).toHaveClass('custom-panel');
    });

    it('uses custom testId', () => {
      render(
        <TestWrapper>
          <SettingsPanel testId="custom-settings" />
        </TestWrapper>
      );

      expect(screen.getByTestId('custom-settings')).toBeInTheDocument();
    });
  });

  describe('Components Integration', () => {
    it('renders FontSizeSlider with correct props', () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      );

      const fontSizeSlider = screen.getByTestId('settings-panel-font-size');
      expect(fontSizeSlider).toBeInTheDocument();

      // Check the slider value matches default settings
      expect(screen.getByTestId('settings-panel-font-size-value')).toHaveTextContent(
        `${DEFAULT_READER_SETTINGS.fontSize}px`
      );
    });

    it('renders reset button', () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      );

      const resetButton = screen.getByTestId('settings-panel-reset');
      expect(resetButton).toBeInTheDocument();
      expect(resetButton).toHaveTextContent('重置為預設值');
    });
  });

  describe('Interactions', () => {
    it('calls onClose when close button is clicked', async () => {
      const onClose = jest.fn();

      render(
        <TestWrapper>
          <SettingsPanel onClose={onClose} />
        </TestWrapper>
      );

      const closeButton = screen.getByLabelText('關閉設定面板');
      await act(async () => {
        await userEvent.click(closeButton);
      });

      expect(onClose).toHaveBeenCalledTimes(1);
    });

    it('updates font size when slider changes', async () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      );

      const slider = screen.getByLabelText('字體大小調整');

      // Change font size
      await act(async () => {
        fireEvent.change(slider, { target: { value: '20' } });
      });

      // LocalStorage should be updated
      expect(mockLocalStorage.setItem).toHaveBeenCalled();
    });

    it('resets settings when reset button is clicked', async () => {
      // Set non-default settings first
      const customSettings = {
        fontSize: 20,
        lineHeight: 1.8,
        theme: 'dark',
        fontFamily: 'sans-serif',
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(customSettings));

      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      );

      const resetButton = screen.getByTestId('settings-panel-reset');

      // Button should be enabled with non-default settings
      expect(resetButton).not.toBeDisabled();

      // Click reset
      await act(async () => {
        await userEvent.click(resetButton);
      });

      // Should save default settings
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'readerSettings',
        JSON.stringify(DEFAULT_READER_SETTINGS)
      );
    });

    it('disables reset button when settings are default', () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      );

      const resetButton = screen.getByTestId('settings-panel-reset');

      // Button should be disabled with default settings
      expect(resetButton).toBeDisabled();
      expect(resetButton).toHaveClass('cursor-not-allowed');
    });
  });

  describe('Settings State', () => {
    it('reflects settings from localStorage', () => {
      const savedSettings = {
        fontSize: 24,
        lineHeight: 1.8,
        theme: 'dark',
        fontFamily: 'sans-serif',
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(savedSettings));

      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      );

      // Font size should match saved settings
      expect(screen.getByTestId('settings-panel-font-size-value')).toHaveTextContent('24px');

      // Reset button should be enabled
      expect(screen.getByTestId('settings-panel-reset')).not.toBeDisabled();
    });
  });
});
