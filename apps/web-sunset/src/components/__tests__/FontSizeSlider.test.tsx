import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { act } from 'react';
import FontSizeSlider from '../FontSizeSlider';

describe('FontSizeSlider', () => {
  const defaultProps = {
    value: 16,
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders with default props', () => {
      render(<FontSizeSlider {...defaultProps} />);

      expect(screen.getByLabelText('字體大小調整')).toBeInTheDocument();
      expect(screen.getByText('字體大小')).toBeInTheDocument();
      expect(screen.getByTestId('font-size-slider-value')).toHaveTextContent('16px');
    });

    it('renders with custom aria label', () => {
      render(<FontSizeSlider {...defaultProps} ariaLabel="Adjust text size" />);

      expect(screen.getByLabelText('Adjust text size')).toBeInTheDocument();
    });

    it('renders without value display when showValue is false', () => {
      render(<FontSizeSlider {...defaultProps} showValue={false} />);

      expect(screen.queryByTestId('font-size-slider-value')).not.toBeInTheDocument();
    });

    it('renders without min/max labels when showMinMax is false', () => {
      render(<FontSizeSlider {...defaultProps} showMinMax={false} />);

      expect(screen.queryByText('12')).not.toBeInTheDocument();
      expect(screen.queryByText('32')).not.toBeInTheDocument();
    });

    it('renders with custom min/max values', () => {
      render(<FontSizeSlider {...defaultProps} min={14} max={28} />);

      expect(screen.getByText('14')).toBeInTheDocument();
      expect(screen.getByText('28')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(<FontSizeSlider {...defaultProps} className="custom-slider" />);

      expect(screen.getByTestId('font-size-slider')).toHaveClass('custom-slider');
    });

    it('uses custom testId', () => {
      render(<FontSizeSlider {...defaultProps} testId="custom-font-slider" />);

      expect(screen.getByTestId('custom-font-slider')).toBeInTheDocument();
      expect(screen.getByTestId('custom-font-slider-value')).toBeInTheDocument();
    });
  });

  describe('Interaction', () => {
    it('calls onChange when slider value changes', async () => {
      const onChange = jest.fn();

      render(<FontSizeSlider value={16} onChange={onChange} />);

      const slider = screen.getByLabelText('字體大小調整');

      // Simulate change event on range input
      await act(async () => {
        fireEvent.change(slider, { target: { value: '20' } });
      });

      expect(onChange).toHaveBeenCalledWith(20);
    });

    it('respects min and max constraints', () => {
      render(<FontSizeSlider {...defaultProps} value={16} min={14} max={24} />);

      const slider = screen.getByLabelText('字體大小調整') as HTMLInputElement;

      expect(slider.min).toBe('14');
      expect(slider.max).toBe('24');
      expect(slider.value).toBe('16');
    });

    it('respects step value', () => {
      render(<FontSizeSlider {...defaultProps} step={2} />);

      const slider = screen.getByLabelText('字體大小調整') as HTMLInputElement;

      expect(slider.step).toBe('2');
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<FontSizeSlider value={18} onChange={jest.fn()} min={12} max={32} />);

      const slider = screen.getByLabelText('字體大小調整');

      expect(slider).toHaveAttribute('aria-valuemin', '12');
      expect(slider).toHaveAttribute('aria-valuemax', '32');
      expect(slider).toHaveAttribute('aria-valuenow', '18');
      expect(slider).toHaveAttribute('type', 'range');
    });

    it('associates label with input correctly', () => {
      render(<FontSizeSlider {...defaultProps} />);

      const label = screen.getByText('字體大小');
      const input = screen.getByLabelText('字體大小調整');

      expect(label).toHaveAttribute('for', 'font-size-slider-input');
      expect(input).toHaveAttribute('id', 'font-size-slider-input');
    });

    it('maintains label association with custom testId', () => {
      render(<FontSizeSlider {...defaultProps} testId="custom-slider" />);

      const label = screen.getByText('字體大小');
      const input = screen.getByLabelText('字體大小調整');

      expect(label).toHaveAttribute('for', 'custom-slider-input');
      expect(input).toHaveAttribute('id', 'custom-slider-input');
    });
  });

  describe('Visual feedback', () => {
    it('displays value correctly', () => {
      const { rerender } = render(
        <FontSizeSlider value={12} onChange={jest.fn()} min={12} max={32} />
      );

      expect(screen.getByTestId('font-size-slider-value')).toHaveTextContent('12px');

      rerender(<FontSizeSlider value={22} onChange={jest.fn()} min={12} max={32} />);

      expect(screen.getByTestId('font-size-slider-value')).toHaveTextContent('22px');

      rerender(<FontSizeSlider value={32} onChange={jest.fn()} min={12} max={32} />);

      expect(screen.getByTestId('font-size-slider-value')).toHaveTextContent('32px');
    });

    it('has correct value attribute', () => {
      render(<FontSizeSlider value={20} onChange={jest.fn()} />);

      const slider = screen.getByLabelText('字體大小調整') as HTMLInputElement;
      expect(slider.value).toBe('20');
    });
  });
});
