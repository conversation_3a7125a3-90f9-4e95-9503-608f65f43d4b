import React from 'react';
import { render } from '@testing-library/react';
import { act } from 'react';
import { CSSVariablesBridge } from '../CSSVariablesBridge';
import { SettingsProvider } from '../../contexts/SettingsContext';
import { useSettings } from '../../hooks/useSettings';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <SettingsProvider>{children}</SettingsProvider>
);

// Test component to trigger setting updates
const SettingsUpdater: React.FC<{
  fontSize?: number;
  theme?: 'light' | 'dark' | 'sepia';
}> = ({ fontSize, theme }) => {
  const { updateSetting } = useSettings();

  React.useEffect(() => {
    if (fontSize) {
      updateSetting('fontSize', fontSize);
    }
    if (theme) {
      updateSetting('theme', theme);
    }
  }, [fontSize, theme, updateSetting]);

  return null;
};

describe('CSSVariablesBridge', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);

    // Clear any existing CSS variables
    const documentElement = document.documentElement;
    documentElement.style.cssText = '';
  });

  it('applies default CSS variables on mount', () => {
    render(
      <TestWrapper>
        <CSSVariablesBridge />
      </TestWrapper>
    );

    const documentElement = document.documentElement;

    // Check default CSS variables are applied
    expect(documentElement.style.getPropertyValue('--reader-font-size')).toBe('16px');
    expect(documentElement.style.getPropertyValue('--reader-line-height')).toBe('1.6');
    expect(documentElement.style.getPropertyValue('--reader-font-family')).toBe(
      'Georgia, "Times New Roman", Times, serif'
    );
    expect(documentElement.style.getPropertyValue('--reader-bg-color')).toBe('#ffffff');
    expect(documentElement.style.getPropertyValue('--reader-text-color')).toBe('#1f2937');
    expect(documentElement.style.getPropertyValue('--reader-border-color')).toBe('#e5e7eb');
  });

  it('updates CSS variables when settings change', () => {
    const { rerender } = render(
      <TestWrapper>
        <CSSVariablesBridge />
      </TestWrapper>
    );

    const documentElement = document.documentElement;

    // First, check default values
    expect(documentElement.style.getPropertyValue('--reader-font-size')).toBe('16px');

    // Update with fontSize setting
    act(() => {
      rerender(
        <TestWrapper>
          <CSSVariablesBridge />
          <SettingsUpdater fontSize={20} />
        </TestWrapper>
      );
    });

    // Check that font size CSS variable is updated
    expect(documentElement.style.getPropertyValue('--reader-font-size')).toBe('20px');

    // Update theme
    act(() => {
      rerender(
        <TestWrapper>
          <CSSVariablesBridge />
          <SettingsUpdater fontSize={20} theme="dark" />
        </TestWrapper>
      );
    });

    // Check that theme CSS variables are updated
    expect(documentElement.style.getPropertyValue('--reader-bg-color')).toBe('#111827');
    expect(documentElement.style.getPropertyValue('--reader-text-color')).toBe('#f9fafb');
    expect(documentElement.style.getPropertyValue('--reader-border-color')).toBe('#374151');
  });

  it('handles theme changes correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <CSSVariablesBridge />
      </TestWrapper>
    );

    const documentElement = document.documentElement;

    // Apply sepia theme
    act(() => {
      rerender(
        <TestWrapper>
          <CSSVariablesBridge />
          <SettingsUpdater theme="sepia" />
        </TestWrapper>
      );
    });

    // Check sepia theme CSS variables
    expect(documentElement.style.getPropertyValue('--reader-bg-color')).toBe('#f7f3e9');
    expect(documentElement.style.getPropertyValue('--reader-text-color')).toBe('#5d4e37');
    expect(documentElement.style.getPropertyValue('--reader-border-color')).toBe('#d4c5a9');
  });

  it('loads settings from localStorage and applies CSS variables', () => {
    const savedSettings = {
      fontSize: 18,
      lineHeight: 1.8,
      theme: 'dark',
      fontFamily: 'sans-serif',
    };
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(savedSettings));

    render(
      <TestWrapper>
        <CSSVariablesBridge />
      </TestWrapper>
    );

    const documentElement = document.documentElement;

    // Check that saved settings are applied as CSS variables
    expect(documentElement.style.getPropertyValue('--reader-font-size')).toBe('18px');
    expect(documentElement.style.getPropertyValue('--reader-line-height')).toBe('1.8');
    expect(documentElement.style.getPropertyValue('--reader-font-family')).toBe(
      'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    );
    expect(documentElement.style.getPropertyValue('--reader-bg-color')).toBe('#111827');
    expect(documentElement.style.getPropertyValue('--reader-text-color')).toBe('#f9fafb');
    expect(documentElement.style.getPropertyValue('--reader-border-color')).toBe('#374151');
  });

  it('does not render any DOM elements', () => {
    const { container } = render(
      <TestWrapper>
        <CSSVariablesBridge />
      </TestWrapper>
    );

    // The component should not render any DOM content
    expect(container.firstChild).toBeNull();
  });
});
