import React from 'react';
import { render, screen } from '@testing-library/react';
import LoadingSpinner from '../LoadingSpinner';

describe('LoadingSpinner', () => {
  it('renders with default props', () => {
    render(<LoadingSpinner />);

    expect(screen.getByText('載入中...')).toBeInTheDocument();
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('renders with custom label', () => {
    const customLabel = '載入小說列表...';
    render(<LoadingSpinner label={customLabel} />);

    expect(screen.getByText(customLabel)).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { rerender } = render(<LoadingSpinner size="sm" />);

    // Check small size
    let spinner = document.querySelector('[aria-hidden="true"]');
    expect(spinner).toHaveClass('h-4', 'w-4');

    // Check medium size
    rerender(<LoadingSpinner size="md" />);
    spinner = document.querySelector('[aria-hidden="true"]');
    expect(spinner).toHaveClass('h-8', 'w-8');

    // Check large size
    rerender(<LoadingSpinner size="lg" />);
    spinner = document.querySelector('[aria-hidden="true"]');
    expect(spinner).toHaveClass('h-12', 'w-12');
  });

  it('applies correct color classes', () => {
    const { rerender } = render(<LoadingSpinner color="blue" />);

    // Check blue color
    let spinner = document.querySelector('[aria-hidden="true"]');
    expect(spinner).toHaveClass('border-blue-500');

    // Check gray color
    rerender(<LoadingSpinner color="gray" />);
    spinner = document.querySelector('[aria-hidden="true"]');
    expect(spinner).toHaveClass('border-gray-500');

    // Check green color
    rerender(<LoadingSpinner color="green" />);
    spinner = document.querySelector('[aria-hidden="true"]');
    expect(spinner).toHaveClass('border-green-500');
  });

  it('applies centered layout when specified', () => {
    const { rerender } = render(<LoadingSpinner centered={false} />);

    // Check non-centered layout
    let container = screen.getByRole('status');
    expect(container).toHaveClass('flex', 'items-center', 'justify-center');
    expect(container).not.toHaveClass('w-full', 'h-full');

    // Check centered layout
    rerender(<LoadingSpinner centered={true} />);
    container = screen.getByRole('status');
    expect(container).toHaveClass('flex', 'items-center', 'justify-center', 'w-full', 'h-full');
  });

  it('applies correct text size based on spinner size', () => {
    const { rerender } = render(<LoadingSpinner size="sm" label="Small text" />);

    // Check small text
    let text = screen.getByText('Small text');
    expect(text).toHaveClass('text-sm');

    // Check medium text
    rerender(<LoadingSpinner size="md" label="Medium text" />);
    text = screen.getByText('Medium text');
    expect(text).toHaveClass('text-base');

    // Check large text
    rerender(<LoadingSpinner size="lg" label="Large text" />);
    text = screen.getByText('Large text');
    expect(text).toHaveClass('text-lg');
  });

  it('has proper accessibility attributes', () => {
    const customLabel = '載入內容';
    render(<LoadingSpinner label={customLabel} />);

    const statusElement = screen.getByRole('status');
    expect(statusElement).toHaveAttribute('aria-label', customLabel);
    expect(statusElement).toHaveAttribute('aria-live', 'polite');

    // Spinner should be hidden from screen readers
    const spinner = document.querySelector('[aria-hidden="true"]');
    expect(spinner).toBeInTheDocument();
  });

  it('renders empty label correctly', () => {
    render(<LoadingSpinner label="" />);

    const statusElement = screen.getByRole('status');
    expect(statusElement).toHaveAttribute('aria-label', '');

    // Check that empty text doesn't break layout
    const textElement = statusElement.querySelector('span');
    expect(textElement).toHaveTextContent('');
  });

  it('handles long labels gracefully', () => {
    const longLabel = '正在載入非常長的內容，請耐心等待處理完成...';
    render(<LoadingSpinner label={longLabel} />);

    expect(screen.getByText(longLabel)).toBeInTheDocument();
  });

  it('applies animation classes', () => {
    render(<LoadingSpinner />);

    const spinner = document.querySelector('[aria-hidden="true"]');
    expect(spinner).toHaveClass('animate-spin');
  });
});
