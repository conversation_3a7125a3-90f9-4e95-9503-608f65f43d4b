import type { <PERSON>a, StoryObj } from '@storybook/react-webpack5';
import Header from './Header';
import Layout from './Layout';

const meta = {
  title: 'Components/Header',
  component: Header,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
A responsive navigation header with mobile menu support.

## Features
- 📱 **Mobile-first design** with hamburger menu
- ♿ **Accessible** with keyboard navigation and screen reader support
- 🎨 **Themeable** with light/dark mode support
- 🔒 **Body scroll lock** when mobile menu is open
- ⌨️ **Keyboard navigation** (Escape to close, Tab navigation)
- 🖱️ **Outside click** to close mobile menu
- 🎯 **Focus management** for better UX

## Usage
The Header component automatically adapts to different screen sizes and provides a consistent navigation experience across devices.
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    brand: {
      control: { type: 'text' },
      description: 'Brand name or logo text',
    },
    showSearch: {
      control: { type: 'boolean' },
      description: 'Whether to show the search functionality',
    },
    showAuth: {
      control: { type: 'boolean' },
      description: 'Whether to show authentication buttons',
    },
    variant: {
      control: { type: 'select' },
      options: ['default', 'transparent', 'solid'],
      description: 'Visual variant of the header',
    },
    fixed: {
      control: { type: 'boolean' },
      description: 'Whether the header should be fixed to the top',
    },
  },
  decorators: [
    Story => (
      <div style={{ minHeight: '100vh', backgroundColor: '#f3f4f6' }}>
        <Story />
        <Layout maxWidth="lg" padding="lg">
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">頁面內容示例</h2>
              <p className="text-gray-600 mb-4">
                這裡展示 Header 組件在實際頁面中的效果。試試以下功能：
              </p>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>在行動裝置視窗下點擊漢堡選單</li>
                <li>使用鍵盤導航 (Tab 鍵和 Escape 鍵)</li>
                <li>點擊選單外部區域關閉選單</li>
                <li>觀察背景滾動鎖定效果</li>
              </ul>
            </section>

            <section className="bg-white rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">小說列表</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[1, 2, 3, 4, 5, 6].map(i => (
                  <div key={i} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900">小說標題 {i}</h4>
                    <p className="text-sm text-gray-600 mt-1">作者名稱</p>
                    <p className="text-xs text-gray-500 mt-2">更新於 2024-06-21</p>
                  </div>
                ))}
              </div>
            </section>

            <section className="bg-white rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">最新章節</h3>
              <div className="space-y-3">
                {[1, 2, 3, 4, 5].map(i => (
                  <div
                    key={i}
                    className="flex justify-between items-center py-2 border-b border-gray-100"
                  >
                    <div>
                      <h4 className="font-medium text-gray-900">第 {i} 章：章節標題</h4>
                      <p className="text-sm text-gray-600">來自《範例小說》</p>
                    </div>
                    <span className="text-xs text-gray-500">2小時前</span>
                  </div>
                ))}
              </div>
            </section>

            {/* 添加更多內容來展示滾動效果 */}
            {Array.from({ length: 10 }, (_, i) => (
              <section key={i} className="bg-white rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">內容區塊 {i + 1}</h3>
                <p className="text-gray-600">
                  這是第 {i + 1} 個內容區塊，用於展示頁面滾動效果。
                  當行動端選單開啟時，背景內容將被鎖定無法滾動， 這提供了更好的使用者體驗。
                </p>
              </section>
            ))}
          </div>
        </Layout>
      </div>
    ),
  ],
} satisfies Meta<typeof Header>;

export default meta;
type Story = StoryObj<typeof meta>;

// 預設桌面版 Header
export const Default: Story = {
  args: {
    brand: '小說閱讀',
    showSearch: true,
    showAuth: true,
    variant: 'default',
    fixed: true,
  },
};

// 行動版展示 (關閉狀態)
export const MobileClosed: Story = {
  args: {
    ...Default.args,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
};

// 行動版展示 (開啟狀態)
export const MobileOpen: Story = {
  args: {
    ...Default.args,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
    docs: {
      description: {
        story: '在行動裝置上，點擊漢堡選單按鈕可以開啟/關閉導航選單。選單開啟時背景會被鎖定。',
      },
    },
  },
};

// 透明背景變體
export const Transparent: Story = {
  args: {
    ...Default.args,
    variant: 'transparent',
  },
  decorators: [
    Story => (
      <div
        style={{
          minHeight: '100vh',
          backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        <Story />
        <Layout maxWidth="lg" padding="lg">
          <div className="pt-20">
            <div className="bg-white/90 backdrop-blur rounded-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">透明 Header 展示</h2>
              <p className="text-gray-700">
                透明背景的 Header 適合用在有背景圖片或漸層背景的頁面上， 提供更現代的視覺效果。
              </p>
            </div>
          </div>
        </Layout>
      </div>
    ),
  ],
};

// 實心背景變體
export const Solid: Story = {
  args: {
    ...Default.args,
    variant: 'solid',
  },
};

// 簡化版 (無搜尋和認證)
export const Minimal: Story = {
  args: {
    brand: '極簡小說',
    showSearch: false,
    showAuth: false,
    variant: 'default',
    fixed: true,
  },
};

// 僅搜尋功能
export const SearchOnly: Story = {
  args: {
    brand: '搜尋導向',
    showSearch: true,
    showAuth: false,
    variant: 'default',
    fixed: true,
  },
};

// 僅認證功能
export const AuthOnly: Story = {
  args: {
    brand: '會員專區',
    showSearch: false,
    showAuth: true,
    variant: 'default',
    fixed: true,
  },
};

// 非固定 Header
export const NonFixed: Story = {
  args: {
    ...Default.args,
    fixed: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Header 不固定在頂部，會隨著頁面內容一起滾動。',
      },
    },
  },
};

// 無障礙功能展示
export const AccessibilityDemo: Story = {
  args: {
    ...Default.args,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
    docs: {
      description: {
        story: `
## 無障礙功能測試

這個示例專門用於測試 Header 的無障礙功能：

### 鍵盤導航
- **Tab 鍵**: 在可互動元素間切換焦點
- **Enter/Space**: 啟動按鈕或連結
- **Escape**: 關閉開啟的行動端選單

### 螢幕閱讀器支援
- 漢堡選單按鈕有適當的 \`aria-label\`
- 選單狀態透過 \`aria-expanded\` 屬性通知
- 導航項目有清晰的文字標籤

### 焦點管理
- 選單關閉時焦點回到觸發按鈕
- 清晰的焦點視覺指示器
- 合理的 Tab 順序

### 測試步驟
1. 使用 Tab 鍵導航到漢堡選單按鈕
2. 按 Enter 或 Space 開啟選單
3. 使用 Tab 鍵在選單項目間導航
4. 按 Escape 關閉選單，觀察焦點是否回到按鈕
        `,
      },
    },
  },
};

// 深色主題
export const DarkTheme: Story = {
  args: {
    ...Default.args,
  },
  decorators: [
    Story => (
      <div className="dark min-h-screen bg-gray-900">
        <Story />
        <Layout maxWidth="lg" padding="lg">
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">深色主題展示</h2>
              <p className="text-gray-300 mb-4">
                Header 組件自動適應深色主題，提供一致的視覺體驗。
              </p>
            </section>

            <section className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-3">深色內容區塊</h3>
              <p className="text-gray-300">
                在深色主題下，所有元素都會自動調整顏色以確保良好的對比度和可讀性。
              </p>
            </section>
          </div>
        </Layout>
      </div>
    ),
  ],
};

// 響應式行為測試
export const ResponsiveTest: Story = {
  args: {
    ...Default.args,
  },
  parameters: {
    docs: {
      description: {
        story: `
## 響應式行為測試

使用 Storybook 的視窗切換工具來測試 Header 在不同螢幕尺寸下的行為：

### 桌面版 (≥ 1024px)
- 顯示完整的水平導航選單
- 搜尋框和認證按鈕並列顯示
- 品牌標誌和導航項目清晰可見

### 平板版 (768px - 1023px)
- 可能需要壓縮部分導航項目
- 搜尋功能可能合併到選單中

### 行動版 (< 768px)
- 切換到漢堡選單模式
- 導航項目收納到下拉選單中
- 搜尋和認證功能整合到選單內

### 測試步驟
1. 在不同視窗大小下觀察佈局變化
2. 測試觸控和滑鼠互動
3. 驗證內容優先級和可用性
        `,
      },
    },
  },
};

// 效能測試 (大量內容)
export const PerformanceTest: Story = {
  args: {
    ...Default.args,
  },
  decorators: [
    Story => (
      <div style={{ minHeight: '300vh', backgroundColor: '#f3f4f6' }}>
        <Story />
        <Layout maxWidth="lg" padding="lg">
          <div className="space-y-4">
            <h2 className="text-2xl font-bold text-gray-900">效能測試頁面</h2>
            <p className="text-gray-600 mb-8">
              這個長頁面用於測試 Header 的滾動效能和固定定位行為。
            </p>

            {/* 生成大量內容 */}
            {Array.from({ length: 100 }, (_, i) => (
              <div key={i} className="bg-white rounded p-4 shadow-sm">
                <h3 className="font-semibold">內容項目 {i + 1}</h3>
                <p className="text-sm text-gray-600">
                  這是第 {i + 1} 個內容項目，用於測試長頁面的滾動效能。 Header
                  應該始終保持在視窗頂部（如果設定為 fixed）。
                </p>
              </div>
            ))}
          </div>
        </Layout>
      </div>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story: '測試 Header 在長頁面滾動時的效能表現和視覺穩定性。',
      },
    },
  },
};
