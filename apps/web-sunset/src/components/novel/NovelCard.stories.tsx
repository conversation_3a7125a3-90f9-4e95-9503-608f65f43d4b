import NovelCard from './NovelCard';

export default {
  title: 'Components/NovelCard',
  component: NovelCard,
  parameters: {
    // 可選：設置這個組件的布局
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    // 定義可控制的屬性
  },
};

// 基礎範例
export const Default = {
  args: {
    id: '1',
    title: '鬥破蒼穹',
    author: '天蠶土豆',
    cover: '/default-cover.jpg',
    description: '這裡是一個屬於鬥氣的世界，沒有花俏艷麗的魔法，有的，僅僅是繁衍到巔峰的鬥氣！',
  },
};

// 長標題範例
export const LongTitle = {
  args: {
    id: '2',
    title: '這是一個非常非常長的小說標題用來測試文字截斷效果是否正常工作',
    author: '測試作者',
    cover: '/default-cover.jpg',
    description: '測試描述文字，用來檢驗長內容的顯示效果',
  },
};

// 無封面範例
export const NoCover = {
  args: {
    id: '3',
    title: '無封面測試',
    author: '作者名',
    cover: '',
    description: '這是一個沒有封面圖片的小說，測試fallback顯示效果',
  },
};

// 移動端視圖
export const Mobile = {
  args: {
    id: '4',
    title: '移動端測試',
    author: '移動作者',
    cover: '/default-cover.jpg',
    description: '測試在移動端的顯示效果，檢驗響應式設計',
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

// 暗色模式
export const DarkMode = {
  args: {
    id: '5',
    title: '暗黑風格',
    author: '暗夜作者',
    cover: '/default-cover.jpg',
    description: '測試暗色模式下的顯示效果，驗證主題切換',
  },
  parameters: {
    backgrounds: { default: 'dark' },
    theme: 'dark',
  },
};
