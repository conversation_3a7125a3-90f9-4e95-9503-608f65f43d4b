import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { getChapterContent } from '../services/api'; // Removed getWenxianChapter
import { Chapter } from '../types/novel';

// Removed ChapterDetailProps and props-based logic

const ChapterDetail: React.FC = () => {
  const navigate = useNavigate();
  const { novelSlug, chapterId } = useParams<{ novelSlug: string; chapterId: string }>();

  const [chapter, setChapter] = useState<Chapter | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChapter = async () => {
      if (!novelSlug || !chapterId) {
        // Or handle error appropriately if slugs/ids are missing
        navigate('/');
        return;
      }

      try {
        setLoading(true);
        // Removed wenxian logic
        // Assuming getChapterContent will be updated to take only chapterId or (novelSlug, chapterId)
        // For now, sticking to the subtask: getChapterContent(chapterId: string)
        const data = await getChapterContent(chapterId);

        if (!data || !data.content) {
          // Check if data itself is null or content is missing
          throw new Error('章節內容為空或無效');
        }

        setChapter(data);
      } catch (err: unknown) {
        let errorMessage = '無法載入章節內容'; // Default message
        if (err && typeof err === 'object' && 'response' in err) {
          const error = err as { response?: { data?: { detail?: string } } };
          if (error.response?.data?.detail) {
            errorMessage = error.response.data.detail; // Use backend-provided error message if available
          }
        } else if (err && typeof err === 'object' && 'message' in err) {
          const error = err as { message: string };
          errorMessage = error.message;
        }
        setError(errorMessage);
        console.error('Error fetching chapter:', {
          error: err,
          novelSlug,
          chapterId,
          errorMessage,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchChapter();
  }, [novelSlug, chapterId, navigate]);

  if (loading) return <div className="text-center p-4">載入中...</div>;
  if (error) return <div className="text-center text-red-500 p-4">{error}</div>;
  if (!chapter) return <div className="text-center p-4">找不到章節</div>;

  // Simplified navigation: Removed Prev/Next chapter links for MVP
  // These would require chapter list context to determine correct IDs

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">
            {chapter.title || `第 ${chapter.chapter_number || chapter.chapterNumber} 章`}
          </h1>
          {/* Display chapter number if title is not present or as a sub-header if desired */}
          {chapter.title && (chapter.chapter_number || chapter.chapterNumber) && (
            <p className="text-lg text-gray-600">
              第 {chapter.chapter_number || chapter.chapterNumber} 章
            </p>
          )}
        </div>

        <div className="prose max-w-none mb-8">
          {chapter.content ? (
            chapter.content.split('\n').map((paragraph, index) => (
              <p key={index} className="mb-4">
                {paragraph}
              </p>
            ))
          ) : (
            <p className="text-gray-600">章節內容載入中...</p>
          )}
        </div>

        <div className="flex justify-center items-center">
          {' '}
          {/* Centering the single link */}
          <Link
            to={`/novels/${novelSlug}`} // Use novelSlug from useParams
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded"
          >
            返回目錄
          </Link>
          {/* Prev/Next chapter links removed for MVP as per subtask decision */}
        </div>
      </div>
    </div>
  );
};

export default ChapterDetail;
