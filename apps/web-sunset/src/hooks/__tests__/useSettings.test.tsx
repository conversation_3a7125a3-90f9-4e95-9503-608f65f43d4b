import React from 'react';
import { renderHook } from '@testing-library/react';
import { act } from 'react';
import { SettingsProvider } from '../../contexts/SettingsContext';
import { useSettings } from '../useSettings';
import { DEFAULT_READER_SETTINGS, SETTINGS_CONSTRAINTS } from '../../types/reader-settings';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Console mock to suppress warnings in tests
const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <SettingsProvider>{children}</SettingsProvider>
);

describe('useSettings Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    consoleSpy.mockClear();
  });

  afterAll(() => {
    consoleSpy.mockRestore();
  });

  describe('Initialization', () => {
    it('initializes with default settings when localStorage is empty', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      expect(result.current.settings).toEqual(DEFAULT_READER_SETTINGS);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isDefaultSettings).toBe(true);
    });

    it('loads settings from localStorage on initialization', () => {
      const savedSettings = {
        fontSize: 18,
        lineHeight: 1.8,
        theme: 'dark',
        fontFamily: 'sans-serif',
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(savedSettings));

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      expect(result.current.settings).toEqual(savedSettings);
      expect(result.current.isDefaultSettings).toBe(false);
    });

    it('handles corrupted localStorage data gracefully', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-json');

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      expect(result.current.settings).toEqual(DEFAULT_READER_SETTINGS);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to load settings from localStorage:',
        expect.any(Error)
      );
    });

    it('merges partial settings with defaults', () => {
      const partialSettings = {
        fontSize: 20,
        theme: 'sepia',
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(partialSettings));

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      expect(result.current.settings).toEqual({
        ...DEFAULT_READER_SETTINGS,
        fontSize: 20,
        theme: 'sepia',
      });
    });
  });

  describe('Setting Updates', () => {
    it('updates fontSize setting correctly', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateSetting('fontSize', 20);
      });

      expect(result.current.settings.fontSize).toBe(20);
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'readerSettings',
        JSON.stringify({
          ...DEFAULT_READER_SETTINGS,
          fontSize: 20,
        })
      );
    });

    it('updates theme setting correctly', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateSetting('theme', 'dark');
      });

      expect(result.current.settings.theme).toBe('dark');
      expect(result.current.isDefaultSettings).toBe(false);
    });

    it('updates fontFamily setting correctly', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateSetting('fontFamily', 'sans-serif');
      });

      expect(result.current.settings.fontFamily).toBe('sans-serif');
    });

    it('updates lineHeight setting correctly', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateSetting('lineHeight', 1.8);
      });

      expect(result.current.settings.lineHeight).toBe(1.8);
    });
  });

  describe('Setting Validation', () => {
    it('clamps fontSize to valid range', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      // Test minimum constraint
      act(() => {
        result.current.updateSetting('fontSize', 5); // Below minimum
      });
      expect(result.current.settings.fontSize).toBe(SETTINGS_CONSTRAINTS.fontSize.min);

      // Test maximum constraint
      act(() => {
        result.current.updateSetting('fontSize', 50); // Above maximum
      });
      expect(result.current.settings.fontSize).toBe(SETTINGS_CONSTRAINTS.fontSize.max);
    });

    it('clamps lineHeight to valid range', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      // Test minimum constraint
      act(() => {
        result.current.updateSetting('lineHeight', 0.5); // Below minimum
      });
      expect(result.current.settings.lineHeight).toBe(SETTINGS_CONSTRAINTS.lineHeight.min);

      // Test maximum constraint
      act(() => {
        result.current.updateSetting('lineHeight', 5.0); // Above maximum
      });
      expect(result.current.settings.lineHeight).toBe(SETTINGS_CONSTRAINTS.lineHeight.max);
    });

    it('validates settings loaded from localStorage', () => {
      const invalidSettings = {
        fontSize: 100, // Above maximum
        lineHeight: 0.1, // Below minimum
        theme: 'light',
        fontFamily: 'serif',
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(invalidSettings));

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      expect(result.current.settings.fontSize).toBe(SETTINGS_CONSTRAINTS.fontSize.max);
      expect(result.current.settings.lineHeight).toBe(SETTINGS_CONSTRAINTS.lineHeight.min);
    });

    it('preserves theme and fontFamily values during validation', () => {
      // Test that theme and fontFamily values pass through validation unchanged
      const validSettings = {
        fontSize: 18,
        lineHeight: 1.8,
        theme: 'sepia',
        fontFamily: 'sans-serif',
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(validSettings));

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      expect(result.current.settings.theme).toBe('sepia');
      expect(result.current.settings.fontFamily).toBe('sans-serif');
      expect(result.current.settings.fontSize).toBe(18);
      expect(result.current.settings.lineHeight).toBe(1.8);
    });
  });

  describe('Reset Functionality', () => {
    it('resets settings to defaults', () => {
      mockLocalStorage.getItem.mockReturnValue(
        JSON.stringify({
          fontSize: 20,
          lineHeight: 1.8,
          theme: 'dark',
          fontFamily: 'sans-serif',
        })
      );

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      // Verify non-default settings are loaded
      expect(result.current.isDefaultSettings).toBe(false);

      act(() => {
        result.current.resetSettings();
      });

      expect(result.current.settings).toEqual(DEFAULT_READER_SETTINGS);
      expect(result.current.isDefaultSettings).toBe(true);
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'readerSettings',
        JSON.stringify(DEFAULT_READER_SETTINGS)
      );
    });
  });

  describe('CSS Variables Generation', () => {
    it('generates correct CSS variables for default settings', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      const cssVars = result.current.getCSSVariables();

      expect(cssVars).toEqual({
        '--reader-font-size': '16px',
        '--reader-line-height': '1.6',
        '--reader-font-family': 'Georgia, "Times New Roman", Times, serif',
        '--reader-bg-color': '#ffffff',
        '--reader-text-color': '#1f2937',
        '--reader-border-color': '#e5e7eb',
      });
    });

    it('generates correct CSS variables for dark theme', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateSetting('theme', 'dark');
      });

      const cssVars = result.current.getCSSVariables();

      expect(cssVars['--reader-bg-color']).toBe('#111827');
      expect(cssVars['--reader-text-color']).toBe('#f9fafb');
      expect(cssVars['--reader-border-color']).toBe('#374151');
    });

    it('generates correct CSS variables for sepia theme', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateSetting('theme', 'sepia');
      });

      const cssVars = result.current.getCSSVariables();

      expect(cssVars['--reader-bg-color']).toBe('#f7f3e9');
      expect(cssVars['--reader-text-color']).toBe('#5d4e37');
      expect(cssVars['--reader-border-color']).toBe('#d4c5a9');
    });

    it('generates correct font family CSS for sans-serif', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateSetting('fontFamily', 'sans-serif');
      });

      const cssVars = result.current.getCSSVariables();

      expect(cssVars['--reader-font-family']).toBe(
        'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      );
    });

    it('handles edge cases in font family mapping', () => {
      // Test by loading settings with an invalid font family from localStorage
      const settingsWithInvalidFont = {
        fontSize: 16,
        lineHeight: 1.6,
        theme: 'light',
        fontFamily: 'unknown-font' as any, // Force invalid font type
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(settingsWithInvalidFont));

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      // The default case should be triggered
      const cssVars = result.current.getCSSVariables();
      expect(cssVars['--reader-font-family']).toBe('Georgia, "Times New Roman", Times, serif');
    });

    it('handles edge cases in theme mapping', () => {
      // Test by loading settings with an invalid theme from localStorage
      const settingsWithInvalidTheme = {
        fontSize: 16,
        lineHeight: 1.6,
        theme: 'unknown-theme' as any, // Force invalid theme type
        fontFamily: 'serif',
      };
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(settingsWithInvalidTheme));

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      // The default case should be triggered, returning light theme colors
      const cssVars = result.current.getCSSVariables();
      expect(cssVars['--reader-bg-color']).toBe('#ffffff');
      expect(cssVars['--reader-text-color']).toBe('#1f2937');
      expect(cssVars['--reader-border-color']).toBe('#e5e7eb');
    });
  });

  describe('Constraints and Helper Properties', () => {
    it('exposes settings constraints', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      expect(result.current.constraints).toEqual(SETTINGS_CONSTRAINTS);
    });

    it('correctly identifies default settings', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      expect(result.current.isDefaultSettings).toBe(true);

      act(() => {
        result.current.updateSetting('fontSize', 18);
      });

      expect(result.current.isDefaultSettings).toBe(false);
    });
  });

  describe('localStorage Error Handling', () => {
    it('handles localStorage.setItem errors gracefully', () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('localStorage is full');
      });

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useSettings(), {
        wrapper: TestWrapper,
      });

      act(() => {
        result.current.updateSetting('fontSize', 18);
      });

      expect(result.current.settings.fontSize).toBe(18); // State still updates
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Failed to save settings to localStorage:',
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Context Error Handling', () => {
    it('throws error when useSettings is used outside provider', () => {
      // Test using the hook without the provider wrapper
      expect(() => {
        renderHook(() => useSettings());
      }).toThrow('useSettings must be used within a SettingsProvider');
    });
  });
});
