import axios, { AxiosInstance, CreateAxiosDefaults } from 'axios';
import { Novel, Chapter } from '../types/novel';

interface CustomAxiosConfig extends CreateAxiosDefaults {
  retry?: number;
  retryDelay?: number;
}

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

const config: CustomAxiosConfig = {
  baseURL: API_BASE_URL,
  timeout: 10000,
  retry: 3,
  retryDelay: 1000,
};

const api: AxiosInstance = axios.create(config);

// 日誌函數 - 在開發環境和測試環境記錄
const logger = {
  log: (message: string) => {
    if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
      // eslint-disable-next-line no-console
      console.log(message);
    }
  },
  error: (message: string, error?: any) => {
    if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
      // eslint-disable-next-line no-console
      console.error(message, error);
    }
  },
};

api.interceptors.request.use(
  config => {
    // 只在開發環境中記錄請求
    logger.log(`Making request to: ${config.baseURL}${config.url}`);
    return config;
  },
  error => {
    logger.error('Request error:', error);
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  response => response,
  async error => {
    const { config } = error;

    if (error.message.includes('Network Error') && config.retry > 0) {
      config.retry -= 1;
      logger.log(`Retrying request to ${config.url}, ${config.retry} attempts left`);

      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
      return api(config);
    }

    return Promise.reject(error);
  }
);

export const getNovelList = async (page = 1, limit = 10) => {
  try {
    const response = await api.get('/novels/', { params: { page, limit } });
    return response.data;
  } catch (error) {
    logger.error('Error fetching novel list:', error);
    return { novels: [], total: 0, page: 1, limit: 10 };
  }
};

export const getNovelDetail = async (slug: string): Promise<Novel> => {
  try {
    const response = await api.get(`/novels/${slug}/`);
    return response.data;
  } catch (error) {
    logger.error('Error fetching novel detail:', error);
    throw new Error('無法載入小說詳情');
  }
};

// Updated getChapterContent to fetch by chapterId (primary key)
export const getChapterContent = async (chapterId: string): Promise<Chapter> => {
  try {
    // The Django backend ChapterViewSet is typically registered at /api/chapters/
    // and uses PK for lookup by default.
    const response = await api.get(`/chapters/${chapterId}/`);
    return response.data;
  } catch (error) {
    logger.error(`Error fetching chapter content for chapterId ${chapterId}:`, error);
    throw new Error('無法載入章節內容');
  }
};

export const searchNovels = async (query: string) => {
  try {
    const response = await api.get('/novels/search', { params: { query } });
    return response.data;
  } catch (error) {
    logger.error('Error searching novels:', error);
    return { novels: [], total: 0, page: 1, limit: 10 };
  }
};

// Removed getWenxianNovel and getWenxianChapter

export const login = async (username: string, password: string) => {
  try {
    const response = await api.post('/auth/login/', { username, password });
    return response.data;
  } catch (error) {
    logger.error('Login failed:', error);
    throw new Error('登入失敗');
  }
};
