# NovelWebsite 樣式開發指南

## 🎯 CSS 策略五大黃金規則

### 1. 🎨 樣式層級決策樹

```
需要樣式嗎？
├─ 否 → 結束
└─ 是 → 這是什麼類型的樣式？
    ├─ 單一值工具類 → Tailwind CSS
    ├─ 設計系統元件 → MUI 元件 + sx prop
    ├─ 複雜動畫/互動 → styled-components
    └─ 主題相關變數 → CSS 變數 + MUI 主題
```

### 2. 📐 Tailwind CSS 使用原則

**適用場景：**

- 間距調整：`p-4`, `mx-auto`, `gap-2`
- 簡單佈局：`flex`, `grid`, `items-center`
- 響應式工具：`md:flex`, `lg:hidden`
- 狀態樣式：`hover:bg-gray-100`, `focus:ring-2`

**範例：**

```tsx
// ✅ 好的做法
<div className="flex items-center gap-4 p-4 md:p-6">
  <Avatar className="w-12 h-12" />
  <Typography className="text-gray-700">使用者名稱</Typography>
</div>

// ❌ 避免的做法
<div className="flex items-center justify-between p-4 bg-blue-500 text-white rounded-lg shadow-xl hover:shadow-2xl transition-all duration-300">
  <!-- 太複雜，應該使用 styled-components -->
</div>
```

### 3. 🎯 MUI + sx prop 最佳實踐

**適用場景：**

- MUI 元件客製化
- 主題感知樣式
- 條件樣式邏輯

**範例：**

```tsx
// ✅ 主題感知的條件樣式
<Card
  sx={{
    p: { xs: 2, md: 3 },
    bgcolor: theme => (theme.palette.mode === 'dark' ? 'grey.900' : 'grey.50'),
    borderRadius: 2,
    transition: 'transform 0.2s',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: 3,
    },
  }}
>
  <CardContent>內容</CardContent>
</Card>
```

### 4. 💅 styled-components 進階使用

**適用場景：**

- 複雜動畫序列
- 動態樣式計算
- 可重用的樣式元件
- CSS-in-JS 優勢場景

**範例：**

```tsx
// ✅ 複雜動畫和狀態管理
const AnimatedCard = styled(motion.div)<{ $isActive: boolean }>`
  background: ${({ theme, $isActive }) =>
    $isActive ? theme.palette.primary.main : theme.palette.background.paper};

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  ${({ $isActive }) =>
    $isActive &&
    css`
      animation: pulse 2s infinite;
    `}
`;

// 結合 Tailwind 工具類
<AnimatedCard className="p-4 rounded-lg" $isActive={isActive} whileHover={{ scale: 1.02 }}>
  內容
</AnimatedCard>;
```

### 5. 🔄 整合使用模式

**完美組合範例：**

```tsx
// 1. 基礎佈局用 Tailwind
// 2. MUI 元件用 sx
// 3. 複雜樣式用 styled-components

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

function NovelReader() {
  return (
    <PageContainer className="flex flex-col">
      {/* Tailwind 處理佈局 */}
      <Box className="container mx-auto p-4 flex-1">
        {/* MUI sx 處理元件樣式 */}
        <Paper
          sx={{
            p: 3,
            maxWidth: 800,
            mx: 'auto',
            bgcolor: 'background.paper',
            borderRadius: 2,
          }}
        >
          {/* 內容區域混合使用 */}
          <div className="space-y-4">
            <Typography variant="h4" className="text-center">
              小說標題
            </Typography>

            {/* 複雜互動用 styled-components */}
            <AnimatedContent>章節內容...</AnimatedContent>
          </div>
        </Paper>
      </Box>
    </PageContainer>
  );
}
```

## 📋 決策檢查清單

在寫樣式前，問自己：

- [ ] 這是單一工具類嗎？→ 用 Tailwind
- [ ] 這是 MUI 元件嗎？→ 用 sx prop
- [ ] 需要複雜邏輯嗎？→ 用 styled-components
- [ ] 可以組合使用嗎？→ 混合最佳方案

## 🚀 快速參考

### Tailwind 常用類別

```
佈局: flex, grid, flex-col, items-center, justify-between
間距: p-4, m-2, px-6, space-y-4, gap-2
響應式: sm:, md:, lg:, xl:
狀態: hover:, focus:, active:, disabled:
```

### MUI sx 常用屬性

```tsx
sx={{
  // 間距
  p: 2, m: 1, px: 3, py: 2,
  // 顏色
  bgcolor: 'primary.main',
  color: 'text.secondary',
  // 響應式
  display: { xs: 'none', md: 'block' },
  // 偽類
  '&:hover': { bgcolor: 'primary.dark' }
}}
```

### styled-components 片段

```tsx
// 主題存取
${({ theme }) => theme.palette.primary.main}

// 條件樣式
${({ $active }) => $active && css`...`}

// 媒體查詢
@media (min-width: ${({ theme }) => theme.breakpoints.values.md}px) {}
```

## 🎓 最佳實踐總結

1. **優先使用 Tailwind** 處理簡單樣式
2. **MUI 元件永遠用 sx** 而非 style
3. **複雜邏輯交給 styled-components**
4. **保持一致性** - 同類型樣式用同樣方法
5. **性能考量** - 避免過度使用動態樣式

記住：沒有絕對的對錯，只有更適合的選擇！
