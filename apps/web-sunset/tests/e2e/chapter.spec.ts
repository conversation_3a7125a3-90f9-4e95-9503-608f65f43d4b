import { test, expect } from '@playwright/test';

test('read chapter flow', async ({ page }) => {
  await page.route('**/api/chapters/123/**', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        id: '123',
        title: 'Chapter 1',
        chapter_number: 1,
        content: 'This is chapter content.',
      }),
    });
  });

  await page.goto('/novels/demo-novel/chapters/123');
  await expect(page.getByText('Chapter 1')).toBeVisible();
  await expect(page.getByText('This is chapter content.')).toBeVisible();
});
