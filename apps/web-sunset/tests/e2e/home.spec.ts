import { test, expect } from '@playwright/test';

test('home page shows novel list', async ({ page }) => {
  await page.route('**/api/novels/**', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        novels: [
          {
            id: '1',
            slug: 'demo-novel',
            title: 'Demo Novel',
            author: 'Author',
            cover_url: '/cover.jpg',
            description: 'A demo novel',
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      }),
    });
  });

  await page.goto('/');
  await expect(page.getByText('Demo Novel')).toBeVisible();
});
