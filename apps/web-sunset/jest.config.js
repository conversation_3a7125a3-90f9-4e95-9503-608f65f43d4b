module.exports = {
  // 解決 Jest + Storybook TS 軟連結失敗問題
  moduleNameMapper: {
    '^@/(.*)': '<rootDir>/src/$1',
    // CSS 模組處理
    '\\.module\\.(css|scss|sass)$': 'identity-obj-proxy',
    // 一般 CSS 文件處理
    '\\.(css|scss|sass)$': 'identity-obj-proxy',
  },

  // 其他 Jest 配置
  testEnvironment: 'jsdom',
  testEnvironmentOptions: {
    resources: 'usable'
  },
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx'],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['react-app'] }],
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.tsx',
    '!src/reportWebVitals.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
};
