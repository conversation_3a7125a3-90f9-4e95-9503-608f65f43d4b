<!-- Google Fonts for Material UI -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

<!-- Meta tags for responsive design -->
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />

<style>
  /* 確保 Storybook 的 canvas 正確顯示 */
  #storybook-root {
    height: 100%;
  }

  /* 為深色模式準備 */
  .dark {
    color-scheme: dark;
  }
</style>
