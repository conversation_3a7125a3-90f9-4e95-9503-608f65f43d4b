/** @type { import('@storybook/react-webpack5').StorybookConfig } */
const config = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],

  // 排除測試文件
  exclude: ['../src/**/*.test.@(js|jsx|ts|tsx)', '../src/**/__tests__/**'],

  addons: [
    '@storybook/addon-links',
    '@storybook/preset-typescript',
    '@storybook/addon-docs',
    '@storybook/preset-create-react-app',
  ],

  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },

  staticDirs: ['../public'],

  typescript: {
    check: false,
    checkOptions: {},
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      propFilter: prop => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
    },
  },

  webpackFinal: async config => {
    // 禁用 TypeScript 檢查以避免測試文件類型錯誤
    config.plugins = config.plugins.filter(
      plugin => plugin.constructor.name !== 'ForkTsCheckerWebpackPlugin'
    );

    // Storybook 應該能自動檢測到 postcss.config.js
    // 如果需要其他自定義配置，可以在這裡添加
    return config;
  },
};

export default config;
