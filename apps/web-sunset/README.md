# 📮 @novelwebsite/web-sunset

## 🚨 SUNSET - 已進入日落階段

此模組已進入**日落階段**，正在逐步遷移至 Next.js 15 App Router (`apps/web-next`)。

### 📊 狀態

- ❌ **停止新功能開發**
- ❌ **停止測試執行**
- ⚠️ **僅維持基本運行**
- 🎯 **目標**: 完全遷移到 `apps/web-next`

### 🏗️ 技術棧

- **框架**: Create React App (CRA)
- **原因**: Jest 版本衝突、架構限制
- **替代方案**: `apps/web-next` (Next.js 15 + App Router)

### 🚀 開發指南

如需開發新功能，請使用 `apps/web-next`:

```bash
# 開發新功能
cd apps/web-next
pnpm dev

# 執行測試
pnpm --filter @novelwebsite/web-next test
```

### 📅 遷移時間線

- ✅ **Phase 1**: 基礎架構設置 (已完成)
- 🔄 **Phase 2**: 核心功能遷移 (進行中)
- ⏳ **Phase 3**: 完全遷移並移除此模組

---

**⚠️ 注意**: 此模組的測試已設置為自動跳過，避免 Jest 版本衝突影響整體 CI pipeline。
