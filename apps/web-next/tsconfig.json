{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/app/*": ["./app/*"], "@/types/*": ["./types/*"], "@novelwebsite/tailwind-config": ["../packages/tailwind-config"], "@novelwebsite/typescript-config": ["../packages/typescript-config"]}, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "downlevelIteration": true, "allowSyntheticDefaultImports": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "coverage", "__tests__/**/*"]}