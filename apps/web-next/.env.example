# Next.js 15 App Router 環境變數配置

# API 設置
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_MEDIA_URL=http://localhost:8000/media
NEXT_PUBLIC_BASE_URL=http://localhost:3001

# 應用設置
NEXT_PUBLIC_APP_NAME=瘟仙小說 (Next.js 15)
NEXT_PUBLIC_APP_DESCRIPTION=線上小說閱讀平台 - Next.js 15 App Router 版本
NEXT_PUBLIC_APP_VERSION=1.0.0

# 功能開關
NEXT_PUBLIC_ENABLE_ADS=false
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_PWA=true

# 廣告設置 (生產環境請替換為真實值)
NEXT_PUBLIC_ADSENSE_CLIENT=ca-pub-xxxxxxxxxxxxxxxx
NEXT_PUBLIC_ADSENSE_SLOT_TOP=xxxxxxxxxx
NEXT_PUBLIC_ADSENSE_SLOT_SIDEBAR=xxxxxxxxxx

# 分析設置
NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX

# 其他設置
NEXT_PUBLIC_ITEMS_PER_PAGE=20
NEXT_PUBLIC_MAX_SEARCH_HISTORY=50

# 開發模式設置
NEXT_PUBLIC_DEBUG=false
NEXT_PUBLIC_MOCK_API=false

# Turborepo 遠程快取 (可選)
TURBO_TOKEN=
TURBO_TEAM=
TURBO_REMOTE_CACHE_SIGNATURE_KEY=

# 安全設置 (生產環境必須設置)
REVALIDATE_SECRET=your-long-random-secret-key-here
