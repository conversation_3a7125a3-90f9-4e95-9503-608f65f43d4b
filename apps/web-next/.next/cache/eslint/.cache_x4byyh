[{"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx": "1", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts": "2", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts": "3", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx": "4", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx": "5", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/ReloadButton.tsx": "6", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/page.tsx": "7", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx": "8", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx": "9", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx": "10", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts": "11", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/types/api.ts": "12"}, {"size": 5654, "mtime": 1751131210778, "results": "13", "hashOfConfig": "14"}, {"size": 6599, "mtime": 1751178225381, "results": "15", "hashOfConfig": "14"}, {"size": 4444, "mtime": 1751131210780, "results": "16", "hashOfConfig": "14"}, {"size": 7161, "mtime": 1751178376716, "results": "17", "hashOfConfig": "14"}, {"size": 2460, "mtime": 1751131210781, "results": "18", "hashOfConfig": "14"}, {"size": 367, "mtime": 1751131210781, "results": "19", "hashOfConfig": "14"}, {"size": 7744, "mtime": 1751181428133, "results": "20", "hashOfConfig": "14"}, {"size": 6529, "mtime": 1751131210782, "results": "21", "hashOfConfig": "14"}, {"size": 2444, "mtime": 1751131210783, "results": "22", "hashOfConfig": "14"}, {"size": 4071, "mtime": 1751131210783, "results": "23", "hashOfConfig": "14"}, {"size": 8644, "mtime": 1751178395668, "results": "24", "hashOfConfig": "14"}, {"size": 2310, "mtime": 1751179871769, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nmrpg4", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts", ["62"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/ReloadButton.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/chapters/[chapterId]/page.tsx", ["63"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts", ["64", "65"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/types/api.ts", [], [], {"ruleId": "66", "severity": 1, "message": "67", "line": 251, "column": 31, "nodeType": null, "messageId": "68", "endLine": 251, "endColumn": 39}, {"ruleId": "66", "severity": 1, "message": "69", "line": 29, "column": 12, "nodeType": null, "messageId": "68", "endLine": 29, "endColumn": 17}, {"ruleId": "66", "severity": 1, "message": "70", "line": 9, "column": 3, "nodeType": null, "messageId": "68", "endLine": 9, "endColumn": 14}, {"ruleId": "66", "severity": 1, "message": "71", "line": 10, "column": 20, "nodeType": null, "messageId": "68", "endLine": 10, "endColumn": 38}, "@typescript-eslint/no-unused-vars", "'_request' is defined but never used.", "unusedVar", "'error' is defined but never used.", "'ApiResponse' is defined but never used.", "'TypedRequestConfig' is defined but never used."]