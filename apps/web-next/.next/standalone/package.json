{"name": "@novelwebsite/root", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.4.0", "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "workspaces": ["apps/*", "packages/*"], "dependencies": {}, "devDependencies": {"turbo": "^2.3.4"}, "pnpm": {"overrides": {"postcss@<8.4.31": ">=8.4.31", "nth-check@<2.0.1": ">=2.0.1", "braces@<3.0.3": ">=3.0.3", "micromatch@<4.0.8": ">=4.0.8", "vue-template-compiler@>=2.0.0 <3.0.0": ">=3.0.0", "esbuild@<=0.24.2": ">=0.25.0", "webpack-dev-server@<4.15.2": ">=4.15.2", "tar-fs@<3.0.9": ">=3.0.9", "ws@<8.17.1": ">=8.17.1", "cookie@<0.7.0": ">=0.7.0", "@babel/plugin-proposal-private-property-in-object": "npm:@babel/plugin-transform-private-property-in-object@^7.24.7"}}, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "test": "turbo test", "type-check": "turbo type-check", "typecheck": "turbo type-check", "clean": "./scripts/clean.sh --build --cache", "clean:turbo": "turbo clean", "clean:all": "./scripts/clean.sh --all", "clean:build": "./scripts/clean.sh --build", "clean:deps": "./scripts/clean.sh --deps", "clean:cache": "./scripts/clean.sh --cache", "clean:preview": "./scripts/clean.sh --dry-run", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "storybook": "turbo storybook", "build-storybook": "turbo build-storybook"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}