(()=>{var e={};e.id=931,e.ids=[931],e.modules={703:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return a},getSortedRoutes:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let a=e[0];if(a.startsWith("[")&&a.endsWith("]")){let r=a.slice(1,-1),o=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),o=!0),r.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+r+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(r.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function i(e,r){if(null!==e&&e!==r)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});t.forEach(e=>{if(e===r)throw Object.defineProperty(Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(e.replace(/\W/g,"")===a.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),t.push(r)}if(n)if(o){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});i(this.optionalRestSlugName,r),this.optionalRestSlugName=r,a="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});i(this.restSlugName,r),this.restSlugName=r,a="[...]"}else{if(o)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});i(this.slugName,r),this.slugName=r,a="[]"}}this.children.has(a)||this.children.set(a,new r),this.children.get(a)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}function a(e,t){let r={},a=[];for(let n=0;n<e.length;n++){let i=t(e[n]);r[i]=n,a[n]=i}return n(a).map(t=>e[r[t]])}},773:(e,t,r)=>{"use strict";Object.defineProperty(t,"e",{enumerable:!0,get:function(){return c}});let n=r(5410),a=r(5312),i=r(9294),o=r(3033),s=r(5296),l=0;async function u(e,t,r,a,i,o,l){await t.set(r,{kind:s.CachedRouteKind.FETCH,data:{headers:{},body:JSON.stringify(e),status:200,url:""},revalidate:"number"!=typeof i?n.CACHE_ONE_YEAR:i},{fetchCache:!0,tags:a,fetchIdx:o,fetchUrl:l})}function c(e,t,r={}){if(0===r.revalidate)throw Object.defineProperty(Error(`Invariant revalidate: 0 can not be passed to unstable_cache(), must be "false" or "> 0" ${e.toString()}`),"__NEXT_ERROR_CODE",{value:"E57",enumerable:!1,configurable:!0});let n=r.tags?(0,a.validateTags)(r.tags,`unstable_cache ${e.toString()}`):[];(0,a.validateRevalidate)(r.revalidate,`unstable_cache ${e.name||e.toString()}`);let d=`${e.toString()}-${Array.isArray(t)&&t.join(",")}`;return async(...t)=>{let a=i.workAsyncStorage.getStore(),c=o.workUnitAsyncStorage.getStore(),p=(null==a?void 0:a.incrementalCache)||globalThis.__incrementalCache;if(!p)throw Object.defineProperty(Error(`Invariant: incrementalCache missing in unstable_cache ${e.toString()}`),"__NEXT_ERROR_CODE",{value:"E469",enumerable:!1,configurable:!0});let h=c&&"prerender"===c.type?c.cacheSignal:null;h&&h.beginRead();try{let i=c&&"request"===c.type?c:void 0,h=(null==i?void 0:i.url.pathname)??(null==a?void 0:a.route)??"",f=new URLSearchParams((null==i?void 0:i.url.search)??""),g=[...f.keys()].sort((e,t)=>e.localeCompare(t)).map(e=>`${e}=${f.get(e)}`).join("&"),_=`${d}-${JSON.stringify(t)}`,m=await p.generateCacheKey(_),b=`unstable_cache ${h}${g.length?"?":""}${g} ${e.name?` ${e.name}`:m}`,v=(a?a.nextFetchId:l)??1,E=null==c?void 0:c.implicitTags,y={type:"unstable-cache",phase:"render",implicitTags:E,draftMode:c&&a&&(0,o.getDraftModeProviderForCacheScope)(a,c)};if(a){if(a.nextFetchId=v+1,c&&("cache"===c.type||"prerender"===c.type||"prerender-ppr"===c.type||"prerender-legacy"===c.type)){"number"==typeof r.revalidate&&(c.revalidate<r.revalidate||(c.revalidate=r.revalidate));let e=c.tags;if(null===e)c.tags=n.slice();else for(let t of n)e.includes(t)||e.push(t)}if(!(c&&"unstable-cache"===c.type)&&"force-no-store"!==a.fetchCache&&!a.isOnDemandRevalidate&&!p.isOnDemandRevalidate&&!a.isDraftMode){let i=await p.get(m,{kind:s.IncrementalCacheKind.FETCH,revalidate:r.revalidate,tags:n,softTags:null==E?void 0:E.tags,fetchIdx:v,fetchUrl:b});if(i&&i.value)if(i.value.kind!==s.CachedRouteKind.FETCH)console.error(`Invariant invalid cacheEntry returned for ${_}`);else{let s=void 0!==i.value.data.body?JSON.parse(i.value.data.body):void 0;return i.isStale&&(a.pendingRevalidates||(a.pendingRevalidates={}),a.pendingRevalidates[_]=o.workUnitAsyncStorage.run(y,e,...t).then(e=>u(e,p,m,n,r.revalidate,v,b)).catch(e=>console.error(`revalidating cache with key: ${_}`,e))),s}}let i=await o.workUnitAsyncStorage.run(y,e,...t);return a.isDraftMode||u(i,p,m,n,r.revalidate,v,b),i}{if(l+=1,!p.isOnDemandRevalidate){let e=await p.get(m,{kind:s.IncrementalCacheKind.FETCH,revalidate:r.revalidate,tags:n,fetchIdx:v,fetchUrl:b,softTags:null==E?void 0:E.tags});if(e&&e.value){if(e.value.kind!==s.CachedRouteKind.FETCH)console.error(`Invariant invalid cacheEntry returned for ${_}`);else if(!e.isStale)return void 0!==e.value.data.body?JSON.parse(e.value.data.body):void 0}}let a=await o.workUnitAsyncStorage.run(y,e,...t);return u(a,p,m,n,r.revalidate,v,b),a}}finally{h&&h.endRead()}}}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1822:()=>{},2097:(e,t,r)=>{"use strict";function n(e){throw Object.defineProperty(Error("cacheLife() is only available with the experimental.useCache config."),"__NEXT_ERROR_CODE",{value:"E627",enumerable:!1,configurable:!0})}Object.defineProperty(t,"F",{enumerable:!0,get:function(){return n}}),r(9294),r(3033)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3670:()=>{},4433:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return o}});let n=r(9644),a=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,i=/\/\[[^/]+\](?=\/|$)/;function o(e,t){return(void 0===t&&(t=!0),(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),t)?i.test(e):a.test(e)}},4727:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return o}});let n=r(5430),a=r(9604);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,a.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5388:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var n={};r.r(n),r.d(n,{GET:()=>c,POST:()=>u});var a=r(1924),i=r(1049),o=r(5312),s=r(1581),l=r(7384);async function u(e){try{let{path:t,tag:r,secret:n}=await e.json(),a=process.env.REVALIDATE_SECRET;if(!a)return console.error("REVALIDATE_SECRET is not set. Revalidation is disabled."),s.NextResponse.json({error:"Revalidation not configured"},{status:500});if(n!==a)return s.NextResponse.json({error:"Invalid secret"},{status:401});if(t)return(0,l.revalidatePath)(t),s.NextResponse.json({success:!0,message:`Path "${t}" revalidated successfully`,timestamp:new Date().toISOString(),type:"path"});if(r)return(0,l.revalidateTag)(r),s.NextResponse.json({success:!0,message:`Tag "${r}" revalidated successfully`,timestamp:new Date().toISOString(),type:"tag"});return s.NextResponse.json({error:"Missing path or tag parameter"},{status:400})}catch(e){return console.error("Revalidation error:",e),s.NextResponse.json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function c(e){let t=e.nextUrl.searchParams,r=t.get("path"),n=t.get("tag"),a=t.get("secret"),i=process.env.REVALIDATE_SECRET;if(!i)return console.error("REVALIDATE_SECRET is not set. Revalidation is disabled."),s.NextResponse.json({error:"Revalidation not configured"},{status:500});if(a!==i)return s.NextResponse.json({error:"Invalid secret"},{status:401});try{if(r)return(0,l.revalidatePath)(r),s.NextResponse.json({success:!0,message:`Path "${r}" revalidated successfully`,timestamp:new Date().toISOString(),type:"path"});if(n)return(0,l.revalidateTag)(n),s.NextResponse.json({success:!0,message:`Tag "${n}" revalidated successfully`,timestamp:new Date().toISOString(),type:"tag"});return s.NextResponse.json({message:"Next.js Revalidation API",usage:{POST:{url:"/api/revalidate",body:{secret:"your-secret-key",path:"/novels/1/chapters/1",tag:"novels"}},GET:{url:"/api/revalidate?secret=your-secret-key&path=/novels/1/chapters/1",url2:"/api/revalidate?secret=your-secret-key&tag=novels"}},examples:[{description:"重新驗證特定章節",method:"POST",body:{secret:"your-revalidate-secret",path:"/novels/1/chapters/1"}},{description:"重新驗證所有小說相關頁面",method:"POST",body:{secret:"your-revalidate-secret",tag:"novels"}},{description:"重新驗證小說詳情頁",method:"POST",body:{secret:"your-revalidate-secret",path:"/novels/1"}}]})}catch(e){return console.error("Revalidation error:",e),s.NextResponse.json({error:"Internal server error",message:e instanceof Error?e.message:"Unknown error"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/revalidate/route",pathname:"/api/revalidate",filename:"route",bundlePath:"app/api/revalidate/route"},resolvedPagePath:"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:f}=d;function g(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},5430:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},6367:(e,t,r)=>{"use strict";function n(...e){throw Object.defineProperty(Error("cacheTag() is only available with the experimental.useCache config."),"__NEXT_ERROR_CODE",{value:"E628",enumerable:!1,configurable:!0})}Object.defineProperty(t,"z",{enumerable:!0,get:function(){return n}}),r(3033),r(5312)},6797:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return n.getSortedRouteObjects},getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return a.isDynamicRoute}});let n=r(703),a=r(4433)},7384:(e,t,r)=>{let n={unstable_cache:r(773).e,revalidateTag:r(8121).revalidateTag,revalidatePath:r(8121).revalidatePath,unstable_expireTag:r(8121).unstable_expireTag,unstable_expirePath:r(8121).unstable_expirePath,unstable_noStore:r(8564).M,unstable_cacheLife:r(2097).F,unstable_cacheTag:r(6367).z};e.exports=n,t.unstable_cache=n.unstable_cache,t.revalidatePath=n.revalidatePath,t.revalidateTag=n.revalidateTag,t.unstable_expireTag=n.unstable_expireTag,t.unstable_expirePath=n.unstable_expirePath,t.unstable_noStore=n.unstable_noStore,t.unstable_cacheLife=n.unstable_cacheLife,t.unstable_cacheTag=n.unstable_cacheTag},8121:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{revalidatePath:function(){return p},revalidateTag:function(){return u},unstable_expirePath:function(){return c},unstable_expireTag:function(){return d}});let n=r(5422),a=r(6797),i=r(5410),o=r(9294),s=r(3033),l=r(9220);function u(e){return h([e],`revalidateTag ${e}`)}function c(e,t){if(e.length>i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH)return void console.warn(`Warning: expirePath received "${e}" which exceeded max length of ${i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`);let r=`${i.NEXT_CACHE_IMPLICIT_TAG_ID}${e}`;return t?r+=`${r.endsWith("/")?"":"/"}${t}`:(0,a.isDynamicRoute)(e)&&console.warn(`Warning: a dynamic page path "${e}" was passed to "expirePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`),h([r],`unstable_expirePath ${e}`)}function d(...e){return h(e,`unstable_expireTag ${e.join(", ")}`)}function p(e,t){if(e.length>i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH)return void console.warn(`Warning: revalidatePath received "${e}" which exceeded max length of ${i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`);let r=`${i.NEXT_CACHE_IMPLICIT_TAG_ID}${e}`;return t?r+=`${r.endsWith("/")?"":"/"}${t}`:(0,a.isDynamicRoute)(e)&&console.warn(`Warning: a dynamic page path "${e}" was passed to "revalidatePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`),h([r],`revalidatePath ${e}`)}function h(e,t){let r=o.workAsyncStorage.getStore();if(!r||!r.incrementalCache)throw Object.defineProperty(Error(`Invariant: static generation store missing in ${t}`),"__NEXT_ERROR_CODE",{value:"E263",enumerable:!1,configurable:!0});let a=s.workUnitAsyncStorage.getStore();if(a){if("cache"===a.type)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" inside a "use cache" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E181",enumerable:!1,configurable:!0});if("unstable-cache"===a.type)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" inside a function cached with "unstable_cache(...)" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E306",enumerable:!1,configurable:!0});if("render"===a.phase)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E7",enumerable:!1,configurable:!0});if("prerender"===a.type){let e=Object.defineProperty(Error(`Route ${r.route} used ${t} without first calling \`await connection()\`.`),"__NEXT_ERROR_CODE",{value:"E406",enumerable:!1,configurable:!0});(0,n.abortAndThrowOnSynchronousRequestDataAccess)(r.route,t,e,a)}else if("prerender-ppr"===a.type)(0,n.postponeWithTracking)(r.route,t,a.dynamicTracking);else if("prerender-legacy"===a.type){a.revalidate=0;let e=Object.defineProperty(new l.DynamicServerError(`Route ${r.route} couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.dynamicUsageDescription=t,r.dynamicUsageStack=e.stack,e}}for(let t of(r.pendingRevalidatedTags||(r.pendingRevalidatedTags=[]),e))r.pendingRevalidatedTags.includes(t)||r.pendingRevalidatedTags.push(t);r.pathWasRevalidated=!0}},8564:(e,t,r)=>{"use strict";Object.defineProperty(t,"M",{enumerable:!0,get:function(){return o}});let n=r(9294),a=r(3033),i=r(5422);function o(){let e=n.workAsyncStorage.getStore(),t=a.workUnitAsyncStorage.getStore();if(e)!e.forceStatic&&(e.isUnstableNoStore=!0,t&&"prerender"===t.type||(0,i.markCurrentScopeAsDynamic)(e,t,"unstable_noStore()"))}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9604:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}function n(e){return e.startsWith("@")&&"@children"!==e}function a(e,t){if(e.includes(i)){let e=JSON.stringify(t);return"{}"!==e?i+"?"+e:i}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return o},PAGE_SEGMENT_KEY:function(){return i},addSearchParamsIfPageSegment:function(){return a},isGroupSegment:function(){return r},isParallelRouteSegment:function(){return n}});let i="__PAGE__",o="__DEFAULT__"},9644:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return a},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return i}});let n=r(4727),a=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>a.find(t=>e.startsWith(t)))}function o(e){let t,r,i;for(let n of e.split("/"))if(r=a.find(e=>n.startsWith(e))){[t,i]=e.split(r,2);break}if(!t||!r||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":i="/"===t?"/"+i:t+"/"+i;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=o.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:i}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[67,968],()=>r(5388));module.exports=n})();
