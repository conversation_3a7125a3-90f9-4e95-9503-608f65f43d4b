(()=>{var e={};e.id=984,e.ids=[984],e.modules={375:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2309:(e,t,s)=>{let{createProxy:r}=s(9369);e.exports=r("/Users/<USER>/Documents/project/NovelWebsite/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_babel-plugin-macros@3.1.0_react-dom@18_4jqqjup23g4vegjq5p5xeio7v4/node_modules/next/dist/client/app-dir/link.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3961:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9751,23))},4193:()=>{},4276:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>i});var r=s(1862),n=s(7221),o=s.n(n);s(4193);let i={title:{default:"瘟仙小說 - Next.js 15 App Router",template:"%s | 瘟仙小說"},description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",keywords:["小說","線上閱讀","Next.js","App Router"],authors:[{name:"NovelWebsite Team"}],creator:"NovelWebsite",publisher:"NovelWebsite",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3001"),openGraph:{type:"website",locale:"zh_TW",url:"/",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",siteName:"瘟仙小說"},twitter:{card:"summary_large_image",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function l({children:e}){return(0,r.jsx)("html",{lang:"zh-TW",className:"h-full",children:(0,r.jsx)("body",{className:`${o().className} h-full bg-gray-50`,children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"瘟仙小說 (Next.js 15 App Router)"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"與 CRA 應用並行運行 - 端口: 3001"})]})}),(0,r.jsx)("main",{className:"max-w-6xl mx-auto px-4 py-6",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t mt-12",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:(0,r.jsx)("p",{className:"text-center text-gray-600 text-sm",children:"\xa9 2025 瘟仙小說 - Next.js 15 App Router 版本"})})})]})})})}},4321:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>i});var r=s(1862),n=s(2309),o=s.n(n);let i={title:"小說列表",description:"瀏覽所有可用的小說"};function l(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"小說列表"}),(0,r.jsx)(o(),{href:"/",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回首頁"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3 text-blue-900",children:"\uD83D\uDEA7 開發中"}),(0,r.jsx)("p",{className:"text-blue-800 mb-4",children:"這個頁面正在開發中。未來將整合 Django 後端 API 來顯示小說列表。"}),(0,r.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,r.jsx)("p",{children:"• 將使用 Next.js 15 的 Server Components"}),(0,r.jsx)("p",{children:"• 支援 SSR 和 SSG 以提升 SEO"}),(0,r.jsx)("p",{children:"• 整合現有的 Django API 端點"}),(0,r.jsx)("p",{children:"• 保持與 CRA 版本的功能一致性"})]})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow",children:[(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500",children:"封面圖片"})}),(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-2 text-gray-900",children:["示例小說 ",e]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局..."}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsx)("span",{children:"作者: 示例作者"}),(0,r.jsx)("span",{children:"更新: 2025-01-01"})]}),(0,r.jsx)(o(),{href:`/novels/${e}`,className:"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"閱讀小說"})]},e))})]})}},4349:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,3859,23)),Promise.resolve().then(s.t.bind(s,7535,23)),Promise.resolve().then(s.t.bind(s,3555,23)),Promise.resolve().then(s.t.bind(s,7426,23)),Promise.resolve().then(s.t.bind(s,5558,23)),Promise.resolve().then(s.t.bind(s,634,23)),Promise.resolve().then(s.t.bind(s,4344,23)),Promise.resolve().then(s.t.bind(s,2194,23))},4457:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2309,23))},5103:()=>{},5842:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>x,tree:()=>d});var r=s(5406),n=s(1049),o=s(9073),i=s.n(o),l=s(430),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);s.d(t,a);let d={children:["",{children:["novels",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4321)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/novels/page",pathname:"/novels",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7085:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6913,23)),Promise.resolve().then(s.t.bind(s,9481,23)),Promise.resolve().then(s.t.bind(s,9073,23)),Promise.resolve().then(s.t.bind(s,1424,23)),Promise.resolve().then(s.t.bind(s,2416,23)),Promise.resolve().then(s.t.bind(s,7060,23)),Promise.resolve().then(s.t.bind(s,7090,23)),Promise.resolve().then(s.t.bind(s,516,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[67,847,751],()=>s(5842));module.exports=r})();