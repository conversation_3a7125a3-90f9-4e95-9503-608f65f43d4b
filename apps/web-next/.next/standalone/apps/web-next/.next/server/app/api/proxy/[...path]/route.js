(()=>{var e={};e.id=524,e.ids=[524],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1822:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3670:()=>{},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5344:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>P,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>A});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>p,OPTIONS:()=>y,PATCH:()=>x,POST:()=>l,PUT:()=>d});var o=r(1924),n=r(1049),a=r(5312),i=r(1581);let c=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000",u=["auth/login","auth/logout","auth/register","auth/profile","auth/refresh","novels","novels/\\d+","novels/\\d+/chapters","novels/\\d+/chapters/\\d+","users/profile","users/bookmarks","users/reading-history","search","search/novels","search/authors","health","version","status"];async function p(e,{params:t}){return g(e,t,"GET")}async function l(e,{params:t}){return g(e,t,"POST")}async function d(e,{params:t}){return g(e,t,"PUT")}async function h(e,{params:t}){return g(e,t,"DELETE")}async function x(e,{params:t}){return g(e,t,"PATCH")}async function g(e,t,r){try{let s,o,{path:n}=t,a=n.join("/");if(!function(e){let t=e.replace(/^\/?(api\/v1\/)?/,"");return u.some(e=>RegExp(`^${e}(/.*)?$`).test(t))}(a)){let t=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",r=e.headers.get("user-agent")||"unknown";return console.warn("\uD83D\uDEA8 Blocked suspicious API proxy request:",{path:a,ip:t,userAgent:r,timestamp:new Date().toISOString()}),i.NextResponse.json({error:"Forbidden",message:"API path not allowed",code:"PATH_NOT_ALLOWED"},{status:403})}let p=new URL(`/api/v1/${a}`,c);e.nextUrl.searchParams.forEach((e,t)=>{p.searchParams.append(t,e)});let l={"Content-Type":"application/json",Accept:"application/json"};if(["authorization","x-api-key","user-agent","accept-language"].forEach(t=>{let r=e.headers.get(t);r&&(l[t]=r)}),["POST","PUT","PATCH"].includes(r))try{let t=await e.text();t&&(s=t)}catch(e){console.error("Error reading request body:",e)}let d=await fetch(p.toString(),{method:r,headers:l,body:s,signal:AbortSignal.timeout(3e4)}),h=await d.text();try{o=JSON.parse(h)}catch{o=h}let x={"Content-Type":d.headers.get("content-type")||"application/json"};return["access-control-allow-origin","access-control-allow-methods","access-control-allow-headers","access-control-allow-credentials"].forEach(e=>{let t=d.headers.get(e);t&&(x[e]=t)}),new i.NextResponse("string"==typeof o?o:JSON.stringify(o),{status:d.status,statusText:d.statusText,headers:x})}catch(e){if(console.error("API Proxy Error:",e),e instanceof TypeError&&e.message.includes("Network error"))return i.NextResponse.json({error:"Backend service unavailable",message:"Unable to connect to Django backend",details:void 0},{status:503});if(e instanceof DOMException&&"AbortError"===e.name)return i.NextResponse.json({error:"Request timeout",message:"Backend request timed out"},{status:504});return i.NextResponse.json({error:"Internal server error",message:"An unexpected error occurred",details:void 0},{status:500})}}async function y(e){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, PATCH, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","Access-Control-Max-Age":"86400"}})}let f=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/proxy/[...path]/route",pathname:"/api/proxy/[...path]",filename:"route",bundlePath:"app/api/proxy/[...path]/route"},resolvedPagePath:"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:T,workUnitAsyncStorage:A,serverHooks:P}=f;function v(){return(0,a.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:A})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[67,968],()=>r(5344));module.exports=s})();