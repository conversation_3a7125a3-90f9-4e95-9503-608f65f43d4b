(()=>{var e={};e.id=492,e.ids=[492],e.modules={375:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3582:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>p,routeModule:()=>x,tree:()=>d});var s=r(5406),o=r(1049),n=r(9073),i=r.n(n),a=r(430),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,5559,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=[],m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3873:e=>{"use strict";e.exports=require("path")},4193:()=>{},4276:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>i});var s=r(1862),o=r(7221),n=r.n(o);r(4193);let i={title:{default:"瘟仙小說 - Next.js 15 App Router",template:"%s | 瘟仙小說"},description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",keywords:["小說","線上閱讀","Next.js","App Router"],authors:[{name:"NovelWebsite Team"}],creator:"NovelWebsite",publisher:"NovelWebsite",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3001"),openGraph:{type:"website",locale:"zh_TW",url:"/",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",siteName:"瘟仙小說"},twitter:{card:"summary_large_image",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function a({children:e}){return(0,s.jsx)("html",{lang:"zh-TW",className:"h-full",children:(0,s.jsx)("body",{className:`${n().className} h-full bg-gray-50`,children:(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"瘟仙小說 (Next.js 15 App Router)"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"與 CRA 應用並行運行 - 端口: 3001"})]})}),(0,s.jsx)("main",{className:"max-w-6xl mx-auto px-4 py-6",children:e}),(0,s.jsx)("footer",{className:"bg-white border-t mt-12",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:(0,s.jsx)("p",{className:"text-center text-gray-600 text-sm",children:"\xa9 2025 瘟仙小說 - Next.js 15 App Router 版本"})})})]})})})}},4349:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3859,23)),Promise.resolve().then(r.t.bind(r,7535,23)),Promise.resolve().then(r.t.bind(r,3555,23)),Promise.resolve().then(r.t.bind(r,7426,23)),Promise.resolve().then(r.t.bind(r,5558,23)),Promise.resolve().then(r.t.bind(r,634,23)),Promise.resolve().then(r.t.bind(r,4344,23)),Promise.resolve().then(r.t.bind(r,2194,23))},5103:()=>{},7085:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6913,23)),Promise.resolve().then(r.t.bind(r,9481,23)),Promise.resolve().then(r.t.bind(r,9073,23)),Promise.resolve().then(r.t.bind(r,1424,23)),Promise.resolve().then(r.t.bind(r,2416,23)),Promise.resolve().then(r.t.bind(r,7060,23)),Promise.resolve().then(r.t.bind(r,7090,23)),Promise.resolve().then(r.t.bind(r,516,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[67,847],()=>r(3582));module.exports=s})();
