(()=>{var e={};e.id=130,e.ids=[130],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3961:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,9751,23))},4267:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d,generateMetadata:()=>i});var t=r(1862),a=r(2309),l=r.n(a),n=r(7909);async function i({params:e}){let{id:s}=e;return{title:`小說 ${s} - 詳情頁`,description:`查看小說 ${s} 的詳細資訊和章節列表`}}function d({params:e}){let{id:s}=e;return(!s||isNaN(Number(s)))&&(0,n.notFound)(),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["小說 ",s," - 詳情頁"]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(l(),{href:"/novels",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回列表"}),(0,t.jsx)(l(),{href:"/",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"首頁"})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-3 text-blue-900",children:"\uD83D\uDEA7 動態路由展示"}),(0,t.jsx)("p",{className:"text-blue-800 mb-4",children:"這是 Next.js 15 App Router 的動態路由頁面，展示如何處理動態參數。"}),(0,t.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsxs)("p",{children:["• 當前小說 ID: ",(0,t.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:s})]}),(0,t.jsxs)("p",{children:["• 路由路徑: ",(0,t.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:"/novels/[id]"})]}),(0,t.jsx)("p",{children:"• 支援 generateMetadata 動態 SEO"}),(0,t.jsx)("p",{children:"• 未來將整合 Django API 獲取真實數據"})]})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:(0,t.jsxs)("div",{className:"flex gap-6",children:[(0,t.jsx)("div",{className:"w-32 h-48 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-gray-500 text-sm",children:"封面圖片"})}),(0,t.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:["示例小說 ",s]}),(0,t.jsx)("p",{className:"text-gray-600",children:"這是一個示例小說的詳細描述。在實際應用中，這裡會顯示從 Django API 獲取的真實小說資訊，包括作者、分類、標籤、更新狀態等。"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm",children:"玄幻"}),(0,t.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded text-sm",children:"連載中"}),(0,t.jsx)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm",children:"熱門"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500 space-y-1",children:[(0,t.jsx)("p",{children:"作者: 示例作者"}),(0,t.jsx)("p",{children:"字數: 1,234,567 字"}),(0,t.jsx)("p",{children:"更新時間: 2025-01-01"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-900",children:"章節列表"}),(0,t.jsx)("div",{className:"space-y-2",children:Array.from({length:10},(e,s)=>s+1).map(e=>(0,t.jsx)(l(),{href:`/novels/${s}/chapters/${e}`,className:"block p-3 rounded-lg hover:bg-gray-50 transition-colors border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"font-medium text-gray-900",children:["第 ",e," 章: 示例章節標題"]}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["2025-01-0",e%9+1]})]})},e))}),(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsx)("button",{className:"text-blue-600 hover:text-blue-800 transition-colors",children:"載入更多章節..."})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-900",children:"操作"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("button",{className:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"開始閱讀"}),(0,t.jsx)("button",{className:"w-full bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"加入書架"}),(0,t.jsx)("button",{className:"w-full bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"分享小說"})]})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-900",children:"統計資訊"}),(0,t.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"總點擊:"}),(0,t.jsx)("span",{className:"font-medium",children:"123,456"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"收藏數:"}),(0,t.jsx)("span",{className:"font-medium",children:"12,345"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"推薦票:"}),(0,t.jsx)("span",{className:"font-medium",children:"1,234"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"評分:"}),(0,t.jsx)("span",{className:"font-medium",children:"4.8/5.0"})]})]})]})]})]})]})}},4457:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,2309,23))},5944:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c});var t=r(5406),a=r(1049),l=r(9073),n=r.n(l),i=r(430),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c={children:["",{children:["novels",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4267)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[id]/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/novels/[id]/page",pathname:"/novels/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[67,847,751,108],()=>r(5944));module.exports=t})();