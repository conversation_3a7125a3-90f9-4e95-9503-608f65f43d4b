(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[868],{1608:(e,t,s)=>{Promise.resolve().then(s.bind(s,4253))},4253:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(3762),r=s(2570),l=s(9839),n=s.n(l),i=s(1535),c=s(4411);let o={baseURL:"/api/proxy",timeout:3e4,retries:3};class d extends Error{static isRetryableError(e){return e>=500||-1!==[408,429].indexOf(e)}getUserFriendlyMessage(){switch(this.status){case 400:return"請求參數錯誤，請檢查輸入內容";case 401:return"未授權訪問，請重新登入";case 403:return"權限不足，無法執行此操作";case 404:return"請求的資源不存在";case 408:return"請求超時，請稍後重試";case 429:return"請求過於頻繁，請稍後重試";case 500:return"伺服器內部錯誤，請稍後重試";case 502:return"網關錯誤，請稍後重試";case 503:return"服務暫時不可用，請稍後重試";case 504:return"網關超時，請稍後重試";default:return this.message||"發生未知錯誤"}}constructor(e,t,s,a=!1){super(e),this.status=t,this.data=s,this.isRetryable=a,this.name="APIError"}}async function h(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:a="GET",headers:r={},body:l,timeout:n=o.timeout,retries:c=o.retries}=s,h=e.startsWith("http")?e:"".concat(o.baseURL).concat(e.startsWith("/")?e:"/".concat(e)),x=(0,i._)({"Content-Type":"application/json",Accept:"application/json"},r);s.token&&(x.Authorization="Bearer ".concat(s.token)),s.sessionCookie&&(x.Cookie=s.sessionCookie),s.isServerSide&&(x["X-Requested-With"]="SSR"),l&&-1!==["POST","PUT","PATCH"].indexOf(a)&&(t="string"==typeof l?l:JSON.stringify(l));let u=null;for(let e=0;e<=c;e++)try{let e,s=new AbortController,r=setTimeout(()=>s.abort(),n),l=await fetch(h,{method:a,headers:x,body:t,signal:s.signal});clearTimeout(r);let i=l.headers.get("content-type");if(e=(null==i?void 0:i.includes("application/json"))?await l.json():await l.text(),!l.ok){let t=d.isRetryableError(l.status),s="object"==typeof e&&null!==e||"string"==typeof e?e:null;throw new d("API request failed: ".concat(l.statusText),l.status,s,t)}return{data:e,status:l.status,statusText:l.statusText,headers:l.headers}}catch(s){if(u=s,e===c)break;if(s instanceof TypeError||s instanceof DOMException){await new Promise(t=>setTimeout(t,1e3*(e+1)));continue}if(s instanceof d&&!s.isRetryable)throw s;let t=Math.min(1e3*Math.pow(2,e)+1e3*Math.random(),1e4);await new Promise(e=>setTimeout(e,t))}if(u instanceof d)throw u;throw new d((null==u?void 0:u.message)||"Unknown API error",0,u?{message:u.message,stack:u.stack}:null)}class x{static async get(e,t){return h(e,(0,c._)((0,i._)({},t),{method:"GET"}))}static async post(e,t,s){return h(e,(0,c._)((0,i._)({},s),{method:"POST",body:t}))}static async put(e,t,s){return h(e,(0,c._)((0,i._)({},s),{method:"PUT",body:t}))}static async delete(e,t){return h(e,(0,c._)((0,i._)({},t),{method:"DELETE"}))}static async patch(e,t,s){return h(e,(0,c._)((0,i._)({},s),{method:"PATCH",body:t}))}}let u={getList:e=>{let t=e?"?".concat(new URLSearchParams(e).toString()):"";return x.get("/novels".concat(t))},getDetail:e=>x.get("/novels/".concat(e)),getChapters:(e,t)=>{let s=t?"?".concat(new URLSearchParams(t).toString()):"";return x.get("/novels/".concat(e,"/chapters").concat(s))}};function m(){let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!1),i=(e,s,a,r)=>{t(t=>[...t,{test:e,success:s,data:a,error:r,timestamp:new Date().toLocaleTimeString()}])},c=async()=>{l(!0),t([]);try{try{let e=await x.get("/health");i("健康檢查",!0,e.data)}catch(e){i("健康檢查",!1,null,e)}try{let e=await u.getList({page:1,limit:5});i("小說列表 API",!0,e.data)}catch(e){i("小說列表 API",!1,null,e)}try{let e=await u.getDetail(1);i("小說詳情 API",!0,e.data)}catch(e){i("小說詳情 API",!1,null,e)}try{let e=await u.getChapters(1,{page:1,limit:5});i("章節列表 API",!0,e.data)}catch(e){i("章節列表 API",!1,null,e)}try{let e=await fetch("/api/proxy/novels"),t=await e.json();i("API 代理測試",e.ok,t)}catch(e){i("API 代理測試",!1,null,e)}}finally{l(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"API 連接測試"}),(0,a.jsx)(n(),{href:"/",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回首頁"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-3 text-blue-900",children:"\uD83D\uDD27 API 代理測試工具"}),(0,a.jsx)("p",{className:"text-blue-800 mb-4",children:"這個頁面用於測試 Next.js 15 App Router 與 Django 後端的 API 連接。 點擊下方按鈕開始測試各個 API 端點。"}),(0,a.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsxs)("p",{children:["• 測試 API 代理路由: ",(0,a.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:"/api/proxy/[...path]"})]}),(0,a.jsxs)("p",{children:["• 測試 Django 後端連接: ",(0,a.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:"http://localhost:8000"})]}),(0,a.jsx)("p",{children:"• 測試 API 客戶端功能"}),(0,a.jsx)("p",{children:"• 驗證錯誤處理機制"})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("button",{onClick:c,disabled:s,className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:s?"測試中...":"開始 API 測試"}),(0,a.jsx)("button",{onClick:()=>{t([])},disabled:s,className:"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors",children:"清除結果"})]}),e.length>0&&(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-900",children:"測試結果"}),(0,a.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border ".concat(e.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("h4",{className:"font-semibold ".concat(e.success?"text-green-900":"text-red-900"),children:[e.success?"✅":"❌"," ",e.test]}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:e.timestamp})]}),e.success&&void 0!==e.data&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"text-sm text-green-800 mb-2",children:"響應數據:"}),(0,a.jsx)("pre",{className:"bg-green-100 p-2 rounded text-xs overflow-x-auto",children:JSON.stringify(e.data,null,2)})]}),!e.success&&void 0!==e.error&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"text-sm text-red-800 mb-2",children:"錯誤信息:"}),(0,a.jsx)("pre",{className:"bg-red-100 p-2 rounded text-xs overflow-x-auto",children:JSON.stringify(e.error,null,2)})]})]},t))})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg border",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-900",children:"API 端點說明"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-2 text-gray-700",children:"Django 後端 API"}),(0,a.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"/api/v1/health"})," - 健康檢查"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"/api/v1/novels"})," - 小說列表"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"/api/v1/novels/:id"})," - 小說詳情"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"/api/v1/novels/:id/chapters"})," - 章節列表"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-2 text-gray-700",children:"Next.js API 代理"}),(0,a.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"/api/proxy/health"})," - 代理健康檢查"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"/api/proxy/novels"})," - 代理小說列表"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("code",{children:"/api/proxy/novels/:id"})," - 代理小說詳情"]}),(0,a.jsx)("li",{children:"• 支援所有 HTTP 方法 (GET, POST, PUT, DELETE)"})]})]})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[121,76,358],()=>t(1608)),_N_E=e.O()}]);