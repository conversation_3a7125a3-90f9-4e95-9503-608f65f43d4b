(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[648],{2542:(e,n,s)=>{Promise.resolve().then(s.bind(s,3833)),Promise.resolve().then(s.t.bind(s,9839,23))},3833:(e,n,s)=>{"use strict";s.d(n,{default:()=>l});var t=s(3762);function l(e){let{className:n,children:s}=e;return(0,t.jsx)("button",{onClick:()=>{window.location.reload()},className:n,children:s})}}},e=>{var n=n=>e(e.s=n);e.O(0,[121,76,358],()=>n(2542)),_N_E=e.O()}]);
