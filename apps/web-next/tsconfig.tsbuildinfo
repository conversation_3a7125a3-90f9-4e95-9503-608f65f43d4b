{"program": {"fileNames": ["../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es5.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.dom.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./next-env.d.ts", "./app/api/proxy/[...path]/route.ts", "./app/api/revalidate/route.ts", "./lib/types/api.ts", "./lib/api.ts", "./app/layout.tsx", "./app/page.tsx", "./app/about/page.tsx", "./app/api-test/page.tsx", "./app/novels/page.tsx", "./app/novels/[id]/page.tsx", "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "./app/novels/[id]/chapters/[chapterid]/page.tsx", "./__tests__/app/api/proxy.test.ts", "./__tests__/app/api/revalidate.test.ts", "./__tests__/app/page.test.tsx", "./__tests__/lib/api-error-handling.test.ts", "./__tests__/lib/api.test.ts"], "fileInfos": [{"version": "8730f4bf322026ff5229336391a18bcaa1f94d4f82416c8b2f3954e2ccaae2ba", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", {"version": "3aafcb693fe5b5c3bd277bd4c3a617b53db474fe498fc5df067c5603b1eebde7", "affectsGlobalScope": true}, {"version": "f3d4da15233e593eacb3965cde7960f3fddf5878528d882bcedd5cbaba0193c7", "affectsGlobalScope": true}, {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "5f406584aef28a331c36523df688ca3650288d14f39c5d2e555c95f0d2ff8f6f", "affectsGlobalScope": true}, {"version": "22f230e544b35349cfb3bd9110b6ef37b41c6d6c43c3314a31bd0d9652fcec72", "affectsGlobalScope": true}, {"version": "7ea0b55f6b315cf9ac2ad622b0a7813315bb6e97bf4bb3fbf8f8affbca7dc695", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "eb26de841c52236d8222f87e9e6a235332e0788af8c87a71e9e210314300410a", "affectsGlobalScope": true}, "f2b3bca04d1bfe583daae1e1f798c92ec24bb6693bd88d0a09ba6802dee362a8", {"version": "d98fc80104bf7d6af21364845fea4681f1244759c295d578e499351b3522ba52", "signature": "8838b244376250174fbadfb69959cab5f023251ac5bf0da74444baf108d9bde0"}, "61f895f3b73a270f8d715f28c539019b3c23e26933023db244bed16271b454ff", {"version": "e7b8597be761749711a3d9f712016817356711643efeda260bb00d088e89e0e6", "signature": "9adb5abb73091c1f78811224f69783658f56c7f55ecf66858f95e0ae106fce69"}, {"version": "b660f23c98eb8433592dffd2137fdf48cf2f719baab96c9eace75f9c4ceddb91", "signature": "7d25ab4da48ccdb2abfbc0cf8485d868af52963fbc812234795794fdf0714cd7"}, "37c9836863d4c1d3de56b93648ce30c06f0766f7e7d8449d248b64d442168617", "1f4d36f87539b2cbff250d7f4ddb0de08de4bbad7172a7bcba62b34c2d0fd90b", "15cb0def468a4896a61daf87b35452ab9f2a1769b2f58830ac67f4bdf89193ba", {"version": "cfe7e3cb0f9fb850926a4f8dda94bca80a05dacfd91e0e7b835c2032d6b0c34d", "signature": "8575a2617138e41d42c3b1d266baf9da0ef65bbbe05c5fda58d4c809ab290de0"}, "9eb3f41fed530625e691c4ff57b004f73dd3954ed302b917a6a07f9ce77e586a", "8923f2add21535d98b1c6ab9499b2dae4cbe41f216be1c37011fbb7e1864c8dc", "9e107890a49df03a1bad589e89a92208db5cc2b9b762035e1289f91eac49aad3", {"version": "19468aad1a7f372381dc6c99ce1709e59bbc183ec391c6b0e39323f861a676e1", "signature": "b37a6127d37b24f52b9a6a15e3c09a1c120addf9525106a2175b1af85ee5cfe7"}], "options": {"allowSyntheticDefaultImports": true, "downlevelIteration": true, "esModuleInterop": true, "jsx": 1, "module": 99, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "strict": true, "target": 4}, "fileIdsList": [[18], [18, 25], [17]], "referencedMap": [[22, 1], [26, 2], [18, 3]], "exportedModulesMap": [[18, 3]], "semanticDiagnosticsPerFile": [[21, [{"file": "./app/about/page.tsx", "start": 25, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/about/page.tsx", "start": 49, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/about/page.tsx", "start": 222, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 256, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 316, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 381, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 543, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 557, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 600, "length": 93, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 704, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 761, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 777, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 916, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 929, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 945, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 999, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1039, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1099, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1118, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1160, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1225, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1287, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1309, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1457, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1476, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1498, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1563, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1624, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1646, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1788, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1807, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1829, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1894, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1954, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1976, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2114, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2133, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2152, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2169, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2187, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2227, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2286, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2305, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2347, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2432, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2489, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2511, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2653, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2672, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2694, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2779, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2833, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2855, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2983, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3002, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3024, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3109, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3164, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3186, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3287, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3306, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3325, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3342, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3357, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3373, "length": 75, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3459, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3527, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3543, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3599, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3604, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3618, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3646, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3663, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3668, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3686, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3713, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3730, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3735, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3752, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3780, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3797, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3802, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3815, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3850, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3865, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3880, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3896, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3973, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4038, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4054, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4132, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4152, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4192, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4212, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4255, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4275, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4297, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4315, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4337, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4359, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4381, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4406, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4428, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4452, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4472, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4490, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4509, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4529, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4568, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4588, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4631, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4651, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4673, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4691, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4713, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4736, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4758, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4769, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4791, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4803, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4823, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4841, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4858, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4873, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4886, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4897, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [22, [{"file": "./app/api-test/page.tsx", "start": 39, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./app/api-test/page.tsx", "start": 64, "length": 11, "messageText": "Cannot find module 'next/link' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./app/api-test/page.tsx", "start": 500, "length": 4, "messageText": "Parameter 'prev' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api-test/page.tsx", "start": 2058, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2092, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2152, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2209, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2371, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2385, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2460, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2550, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2564, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2700, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2713, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2773, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2791, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2854, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2861, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2876, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2897, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2961, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2968, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2983, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3000, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3015, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3028, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3041, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3054, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3068, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3105, "length": 237, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3398, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3417, "length": 235, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3676, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3692, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3743, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3812, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3873, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3889, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3947, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api-test/page.tsx", "start": 3955, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api-test/page.tsx", "start": 3981, "length": 256, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4254, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4329, "length": 126, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4537, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4561, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4659, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4683, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4775, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4818, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4866, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4891, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5039, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5064, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5177, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5220, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5266, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5291, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5438, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5463, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5503, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5536, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5551, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5574, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5633, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5698, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5712, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5774, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5792, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5854, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5872, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5927, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5933, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5953, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5967, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5987, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5993, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6013, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6027, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6047, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6053, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6077, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6091, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6111, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6117, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6150, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6164, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6182, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6198, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6215, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6233, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6296, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6314, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6369, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6375, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6398, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6414, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6434, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6440, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6463, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6479, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6499, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6505, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6532, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6548, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6568, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6611, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6629, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6645, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6660, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6673, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6684, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [15, [{"file": "./app/api/proxy/[...path]/route.ts", "start": 42, "length": 13, "messageText": "Cannot find module 'next/server' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 78, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 2782, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 2789, "length": 3, "messageText": "Parameter 'key' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 3388, "length": 8, "code": 2550, "category": 1, "messageText": "Property 'includes' does not exist on type 'string[]'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2016' or later."}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 5187, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 5695, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [16, [{"file": "./app/api/revalidate/route.ts", "start": 42, "length": 13, "messageText": "Cannot find module 'next/server'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/api/revalidate/route.ts", "start": 102, "length": 12, "messageText": "Cannot find module 'next/cache'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/api/revalidate/route.ts", "start": 305, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./app/api/revalidate/route.ts", "start": 1907, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [19, [{"file": "./app/layout.tsx", "start": 30, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/layout.tsx", "start": 59, "length": 18, "messageText": "Cannot find module 'next/font/google'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/layout.tsx", "start": 590, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./app/layout.tsx", "start": 1281, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./app/layout.tsx", "start": 1317, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1362, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1428, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1480, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1532, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1592, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1701, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1721, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1818, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1835, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1852, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1872, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1952, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1970, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2027, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2087, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2206, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2223, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2240, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2258, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2271, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2283, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [26, [{"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 49, "length": 11, "messageText": "Cannot find module 'next/link' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 86, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1542, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1578, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1640, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1695, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1711, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2116, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2131, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2147, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2222, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2312, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2328, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2428, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2443, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2485, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ className: string; }' but required in type 'ReloadButtonProps'.", "relatedInformation": [{"file": "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "start": 67, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}]}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2865, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2880, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2893, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2926, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2977, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3037, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3148, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3162, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3541, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3554, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3591, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3666, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3736, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3813, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3830, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3926, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3941, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3954, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3987, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4071, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4112, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4232, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4248, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4324, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4341, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4361, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4385, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4405, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4447, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4467, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4513, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4531, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4546, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4583, "length": 21, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4653, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4702, "length": 67, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4822, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4841, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4876, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4926, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5005, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5026, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5108, "length": 20, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5252, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5273, "length": 20, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5433, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5454, "length": 20, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5576, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5595, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5614, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5642, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5655, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5688, "length": 92, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6161, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6201, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6244, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6261, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6321, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6336, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6575, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6608, "length": 72, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6689, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6733, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6747, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6784, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6812, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6828, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6855, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6871, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6888, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6904, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6922, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6936, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6948, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6959, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [25, [{"file": "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "start": 77, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "start": 259, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "start": 351, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [24, [{"file": "./app/novels/[id]/page.tsx", "start": 25, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/page.tsx", "start": 49, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/page.tsx", "start": 86, "length": 17, "messageText": "Cannot find module 'next/navigation'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/page.tsx", "start": 518, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 552, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 612, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 674, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 688, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1051, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1064, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1078, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1153, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1239, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1253, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1351, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1364, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1424, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1438, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1485, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1492, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1507, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1518, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1573, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1580, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1595, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1626, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1641, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1669, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1682, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1695, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1709, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1782, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1834, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1905, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1948, "length": 83, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2048, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2092, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2114, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2135, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2186, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2244, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2266, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2415, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2436, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2493, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2565, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2591, "length": 72, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2666, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2692, "length": 74, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2768, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2792, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2815, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2883, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2894, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2917, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2935, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2958, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2977, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2998, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3019, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3038, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3055, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3096, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3167, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3228, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3246, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3596, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3668, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3777, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3805, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3915, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3941, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4002, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4021, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4070, "length": 72, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4183, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4205, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4222, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4237, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4273, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4311, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4382, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4441, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4459, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4501, "length": 102, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4639, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4663, "length": 105, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4804, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4828, "length": 105, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4969, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4991, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5008, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5026, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5097, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5158, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5176, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5226, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5281, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5317, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5341, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5378, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5400, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5421, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5476, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5512, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5536, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5572, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5594, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5615, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5670, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5706, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5730, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5765, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5787, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5808, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5863, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5898, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5922, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5959, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5981, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6000, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6017, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6032, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6045, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6056, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [23, [{"file": "./app/novels/page.tsx", "start": 25, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/page.tsx", "start": 49, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/page.tsx", "start": 200, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 234, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 294, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 347, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 509, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 523, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 598, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 681, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 695, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 787, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 800, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 860, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 899, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 914, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 939, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 954, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 978, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 993, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1014, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1027, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1040, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1054, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1188, "length": 101, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1302, "length": 83, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1400, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1436, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1456, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1475, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1569, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1587, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1702, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1719, "length": 73, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1807, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1821, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1843, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1863, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1883, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 2139, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 2164, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 2175, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [20, [{"file": "./app/page.tsx", "start": 17, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/page.tsx", "start": 54, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/page.tsx", "start": 226, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 287, "length": 92, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 388, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 477, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 491, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 590, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 603, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1080, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1093, "length": 10, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1142, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1198, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1267, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1361, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1377, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1474, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1487, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1503, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1572, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1661, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1677, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1759, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1772, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1788, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1857, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1945, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1961, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2038, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2051, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2064, "length": 10, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2111, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2182, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2264, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2278, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2332, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2372, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2420, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2438, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2501, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2525, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2545, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2567, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2587, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2609, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2629, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2650, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2670, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2689, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2707, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2723, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2740, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2780, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2835, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2853, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2916, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2939, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2959, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2980, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3000, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3015, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3035, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3054, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3074, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3091, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3109, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3125, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3140, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3153, "length": 10, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3202, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3281, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3362, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3376, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3436, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3464, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3479, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3498, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3513, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3535, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3550, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3559, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3611, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3627, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3640, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3653, "length": 10, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3668, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], 18, 17, 14, 3, 4, 6, 5, 2, 7, 8, 9, 10, 11, 12, 13, 1], "affectedFilesPendingEmit": [[27, 1], [28, 1], [29, 1], [30, 1], [31, 1], [21, 1], [22, 1], [15, 1], [16, 1], [19, 1], [26, 1], [25, 1], [24, 1], [23, 1], [20, 1], [18, 1], [17, 1], [14, 1], [2, 1]]}, "version": "4.9.5"}