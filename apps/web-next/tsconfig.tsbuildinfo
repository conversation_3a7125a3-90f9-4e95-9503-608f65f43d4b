{"program": {"fileNames": ["../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es5.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.dom.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../../../library/pnpm/global/5/.pnpm/typescript@4.9.5/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./next-env.d.ts", "./app/api/proxy/[...path]/route.ts", "./__tests__/app/api/proxy.test.ts", "./app/api/revalidate/route.ts", "./__tests__/app/api/revalidate.test.ts", "./lib/api.ts", "./__tests__/lib/api-error-handling.test.ts", "./__tests__/lib/api.test.ts", "./app/page.tsx", "./__tests__/app/page.test.tsx", "./app/layout.tsx", "./app/about/page.tsx", "./app/api-test/page.tsx", "./app/novels/page.tsx", "./app/novels/[id]/page.tsx", "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "./app/novels/[id]/chapters/[chapterid]/page.tsx"], "fileInfos": [{"version": "8730f4bf322026ff5229336391a18bcaa1f94d4f82416c8b2f3954e2ccaae2ba", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", {"version": "3aafcb693fe5b5c3bd277bd4c3a617b53db474fe498fc5df067c5603b1eebde7", "affectsGlobalScope": true}, {"version": "f3d4da15233e593eacb3965cde7960f3fddf5878528d882bcedd5cbaba0193c7", "affectsGlobalScope": true}, {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "5f406584aef28a331c36523df688ca3650288d14f39c5d2e555c95f0d2ff8f6f", "affectsGlobalScope": true}, {"version": "22f230e544b35349cfb3bd9110b6ef37b41c6d6c43c3314a31bd0d9652fcec72", "affectsGlobalScope": true}, {"version": "7ea0b55f6b315cf9ac2ad622b0a7813315bb6e97bf4bb3fbf8f8affbca7dc695", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "eb26de841c52236d8222f87e9e6a235332e0788af8c87a71e9e210314300410a", "affectsGlobalScope": true}, "f2b3bca04d1bfe583daae1e1f798c92ec24bb6693bd88d0a09ba6802dee362a8", "19004eb2d4e3684f57791ff9409f2bd7168b0fd17fff8eb54439ea571cccf1d1", "bd0de5dc30494fda6940843bd16235026ab43fe9ef5ad779f414a2122e4b078e", "61f895f3b73a270f8d715f28c539019b3c23e26933023db244bed16271b454ff", "6b5c859250ac648e9e4818f02fc1db13a2f9c9b4ee3aa21b9b0ba3104c7be0d2", "14166060d516ebd9e927e693aefed826984c7ec49d6362d48d0e1a0cf8a557fc", "3dcd8ba8f627657e2f0bfd781b5fb60a39b3e5056d7cbe9f489538d9e0242559", "02bdf4fdeb7fe9cfb5c34b1263b0318662232d4803e8f74311270a778d2708e6", "1f4d36f87539b2cbff250d7f4ddb0de08de4bbad7172a7bcba62b34c2d0fd90b", "bb7e5c5ab4c3a43f726d917523da52868dc5fa679d4ab004886dbce76c833c59", "37c9836863d4c1d3de56b93648ce30c06f0766f7e7d8449d248b64d442168617", "15cb0def468a4896a61daf87b35452ab9f2a1769b2f58830ac67f4bdf89193ba", "49226f518f1bdb06f7d7d36f0ab56dccac6c454245f4a3452e454e55c89ff541", "9eb3f41fed530625e691c4ff57b004f73dd3954ed302b917a6a07f9ce77e586a", "8923f2add21535d98b1c6ab9499b2dae4cbe41f216be1c37011fbb7e1864c8dc", "9e107890a49df03a1bad589e89a92208db5cc2b9b762035e1289f91eac49aad3", "e6eb7405e74dfac2a9bc3127b89e3730b6d065de21acb225c05a5f455942c3b9"], "options": {"allowSyntheticDefaultImports": true, "downlevelIteration": true, "esModuleInterop": true, "jsx": 1, "module": 99, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "strict": true, "target": 4}, "fileIdsList": [[15], [17], [22], [19], [19, 29]], "referencedMap": [[16, 1], [18, 2], [23, 3], [20, 4], [21, 4], [26, 4], [30, 5]], "exportedModulesMap": [[16, 1], [18, 2], [23, 3], [20, 4], [21, 4], [26, 4], [30, 5]], "semanticDiagnosticsPerFile": [[16, [{"file": "./__tests__/app/api/proxy.test.ts", "start": 63, "length": 13, "messageText": "Cannot find module 'next/server'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 152, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 167, "length": 4, "messageText": "Cannot find name 'jest'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 195, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 211, "length": 4, "messageText": "Cannot find namespace 'jest'.", "category": 1, "code": 2503}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 246, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 289, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 342, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 390, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 862, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 902, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 997, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 1036, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 1528, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 1568, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 1667, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 1706, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 1978, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2018, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2061, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2110, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2163, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2636, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2678, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2723, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2787, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 2826, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 3380, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 3420, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 3472, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4023, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4063, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4124, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4448, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4488, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4555, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4890, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4930, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 4988, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 5025, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 5696, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 5736, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 5774, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 5869, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 6009, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 6605, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 6706, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 6751, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 6912, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 6950, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 7273, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 7313, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 7389, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/proxy.test.ts", "start": 7473, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [18, [{"file": "./__tests__/app/api/revalidate.test.ts", "start": 63, "length": 13, "messageText": "Cannot find module 'next/server'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 165, "length": 4, "messageText": "Cannot find name 'jest'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 216, "length": 4, "messageText": "Cannot find name 'jest'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 244, "length": 4, "messageText": "Cannot find name 'jest'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 302, "length": 7, "messageText": "Cannot find name 'require'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 325, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 417, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 440, "length": 4, "messageText": "Cannot find name 'jest'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 481, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 541, "length": 9, "messageText": "Cannot find name 'after<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 570, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 608, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 653, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1118, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1158, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1196, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1233, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1294, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1374, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1823, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1863, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1901, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1937, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 1984, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 2049, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 2484, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 2524, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 2572, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 2630, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 3050, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 3090, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 3159, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 3519, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 3559, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 3623, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 3667, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4032, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4072, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4110, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4147, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4216, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4538, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4578, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4638, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4677, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 4725, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 5092, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/api/revalidate.test.ts", "start": 5132, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [23, [{"file": "./__tests__/app/page.test.tsx", "start": 31, "length": 24, "messageText": "Cannot find module '@testing-library/react'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./__tests__/app/page.test.tsx", "start": 116, "length": 4, "messageText": "Cannot find name 'jest'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 173, "length": 4, "messageText": "Cannot find name 'jest'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 388, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 419, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 593, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 637, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 741, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 850, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 951, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 1032, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1098, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1159, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1223, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 1312, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1369, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1426, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1494, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 1585, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1642, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1705, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1772, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 1846, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 2057, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2100, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2159, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2201, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2261, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 2356, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2430, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2501, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2570, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2638, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2708, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/app/page.test.tsx", "start": 2813, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/app/page.test.tsx", "start": 2883, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [20, [{"file": "./__tests__/lib/api-error-handling.test.ts", "start": 81, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 97, "length": 4, "messageText": "Cannot find namespace 'jest'.", "category": 1, "code": 2503}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 132, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 173, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 226, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 265, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 414, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 461, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 498, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 556, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 600, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 648, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 752, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 808, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 864, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 970, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 1037, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 1165, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 1222, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 1279, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 1336, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 1399, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 2118, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 2193, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 2352, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 2435, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 2471, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 2927, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 3045, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 3138, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 4111, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 4166, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 4221, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 4911, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 4966, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 5021, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 5724, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 5779, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 5834, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 6202, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 6325, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 6383, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 6430, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 6817, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 6865, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 6893, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 6938, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 7061, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 7478, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 7526, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 7554, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 7599, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 7728, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 7859, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 7887, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 8234, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 8282, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 8310, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 8355, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api-error-handling.test.ts", "start": 8482, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}]], [21, [{"file": "./__tests__/lib/api.test.ts", "start": 92, "length": 6, "messageText": "Cannot find name 'global'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 108, "length": 4, "messageText": "Cannot find namespace 'jest'.", "category": 1, "code": 2503}, {"file": "./__tests__/lib/api.test.ts", "start": 143, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 175, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 228, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 265, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 670, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 767, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 837, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 993, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 1039, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 1085, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 1431, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 1509, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 1547, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 2046, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 2141, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 2212, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 2413, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 2463, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 2512, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 2551, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 2683, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 2754, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 2897, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 2968, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 3455, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 3504, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 3560, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 3592, "length": 10, "messageText": "Cannot find name 'before<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 3645, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 3677, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 4059, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 4154, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 4187, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 4239, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 4632, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 4743, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 4785, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 4819, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 5211, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 5308, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 5341, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 5396, "length": 8, "messageText": "Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 5432, "length": 2, "messageText": "Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.", "category": 1, "code": 2582}, {"file": "./__tests__/lib/api.test.ts", "start": 5824, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 5930, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}, {"file": "./__tests__/lib/api.test.ts", "start": 5963, "length": 6, "messageText": "Cannot find name 'expect'.", "category": 1, "code": 2304}]], [25, [{"file": "./app/about/page.tsx", "start": 25, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/about/page.tsx", "start": 49, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/about/page.tsx", "start": 222, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 256, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 316, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 381, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 543, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 557, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 600, "length": 93, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 704, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 761, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 777, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 916, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 929, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 945, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 999, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1039, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1099, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1118, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1160, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1225, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1287, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1309, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1457, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1476, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1498, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1563, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1624, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1646, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1788, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1807, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1829, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1894, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1954, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 1976, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2114, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2133, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2152, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2169, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2187, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2227, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2286, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2305, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2347, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2432, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2489, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2511, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2653, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2672, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2694, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2779, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2833, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2855, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 2983, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3002, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3024, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3109, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3164, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3186, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3287, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3306, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3325, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3342, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3357, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3373, "length": 75, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3459, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3527, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3543, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3599, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3604, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3618, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3646, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3663, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3668, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3686, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3713, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3730, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3735, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3752, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3780, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3797, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3802, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3815, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3850, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3865, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3880, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3896, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 3973, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4038, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4054, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4132, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4152, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4192, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4212, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4255, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4275, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4297, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4315, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4337, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4359, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4381, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4406, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4428, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4452, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4472, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4490, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4509, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4529, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4568, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4588, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4631, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4651, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4673, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4691, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4713, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4736, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4758, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4769, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4791, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4803, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4823, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4841, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4858, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4873, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4886, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/about/page.tsx", "start": 4897, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [26, [{"file": "./app/api-test/page.tsx", "start": 39, "length": 7, "messageText": "Cannot find module 'react'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/api-test/page.tsx", "start": 64, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/api-test/page.tsx", "start": 381, "length": 4, "messageText": "Parameter 'prev' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api-test/page.tsx", "start": 1939, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 1973, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2033, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2090, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2252, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2266, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2341, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2431, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2445, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2581, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2594, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2654, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2672, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2735, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2742, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2757, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2778, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2842, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2849, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2864, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2881, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2896, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2909, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2922, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2935, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2949, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 2986, "length": 237, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3279, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3298, "length": 235, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3557, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3573, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3624, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3693, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3754, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3770, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 3828, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api-test/page.tsx", "start": 3836, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api-test/page.tsx", "start": 3862, "length": 256, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4135, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4210, "length": 126, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4418, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4442, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4540, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4564, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4642, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4685, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4733, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4758, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4906, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 4931, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5030, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5073, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5119, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5144, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5291, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5316, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5356, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5389, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5404, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5427, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5486, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5551, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5565, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5627, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5645, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5707, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5725, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5780, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5786, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5806, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5820, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5840, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5846, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5866, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5880, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5900, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5906, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5930, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5944, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5964, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 5970, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6003, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6017, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6035, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6051, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6068, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6086, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6149, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6167, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6222, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6228, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6251, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6267, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6287, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6293, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6316, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6332, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6352, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6358, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6385, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6401, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6421, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6464, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6482, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6498, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6513, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6526, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/api-test/page.tsx", "start": 6537, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [15, [{"file": "./app/api/proxy/[...path]/route.ts", "start": 42, "length": 13, "messageText": "Cannot find module 'next/server'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 78, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 2782, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 2789, "length": 3, "messageText": "Parameter 'key' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 3388, "length": 8, "code": 2550, "category": 1, "messageText": "Property 'includes' does not exist on type 'string[]'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2016' or later."}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 5183, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./app/api/proxy/[...path]/route.ts", "start": 5691, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [17, [{"file": "./app/api/revalidate/route.ts", "start": 42, "length": 13, "messageText": "Cannot find module 'next/server'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/api/revalidate/route.ts", "start": 102, "length": 12, "messageText": "Cannot find module 'next/cache'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/api/revalidate/route.ts", "start": 305, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./app/api/revalidate/route.ts", "start": 1907, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [24, [{"file": "./app/layout.tsx", "start": 30, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/layout.tsx", "start": 59, "length": 18, "messageText": "Cannot find module 'next/font/google'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/layout.tsx", "start": 590, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"file": "./app/layout.tsx", "start": 1281, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./app/layout.tsx", "start": 1317, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1362, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1428, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1480, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1532, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1592, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1701, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1721, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1818, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1835, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1852, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1872, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1952, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 1970, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2027, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2087, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2206, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2223, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2240, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2258, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2271, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/layout.tsx", "start": 2283, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [30, [{"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 25, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 49, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 86, "length": 17, "messageText": "Cannot find module 'next/navigation'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1392, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1428, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1490, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1545, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1561, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1966, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1981, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 1997, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2072, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2162, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2178, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2278, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2293, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2335, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ className: string; }' but required in type 'ReloadButtonProps'.", "relatedInformation": [{"file": "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "start": 67, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}]}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2715, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2730, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2743, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2776, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2827, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2887, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 2998, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3012, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3391, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3404, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3441, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3516, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3586, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3663, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3680, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3776, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3791, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3804, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3837, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3921, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 3962, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4082, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4098, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4174, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4191, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4211, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4235, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4255, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4297, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4317, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4363, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4381, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4396, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4433, "length": 21, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4503, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4552, "length": 67, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4672, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4691, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4726, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4776, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4855, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4876, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 4958, "length": 20, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5102, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5123, "length": 20, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5283, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5304, "length": 20, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5426, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5445, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5464, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5492, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5505, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 5538, "length": 92, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6011, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6051, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6094, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6111, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6171, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6186, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6425, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6458, "length": 72, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6539, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6583, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6597, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6634, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6662, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6678, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6705, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6721, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6738, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6754, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6772, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6786, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6798, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/page.tsx", "start": 6809, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [29, [{"file": "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "start": 77, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "start": 259, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/chapters/[chapterid]/reloadbutton.tsx", "start": 351, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [28, [{"file": "./app/novels/[id]/page.tsx", "start": 25, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/page.tsx", "start": 49, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/page.tsx", "start": 86, "length": 17, "messageText": "Cannot find module 'next/navigation'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/[id]/page.tsx", "start": 518, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 552, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 612, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 674, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 688, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1051, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1064, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1078, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1153, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1239, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1253, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1351, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1364, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1424, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1438, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1485, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1492, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1507, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1518, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1573, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1580, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1595, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1626, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1641, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1669, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1682, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1695, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1709, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1782, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1834, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1905, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 1948, "length": 83, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2048, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2092, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2114, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2135, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2186, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2244, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2266, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2415, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2436, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2493, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2565, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2591, "length": 72, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2666, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2692, "length": 74, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2768, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2792, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2815, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2883, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2894, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2917, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2935, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2958, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2977, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 2998, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3019, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3038, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3055, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3096, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3167, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3228, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3246, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3596, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3668, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3777, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3805, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3915, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 3941, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4002, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4021, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4070, "length": 72, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4183, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4205, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4222, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4237, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4273, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4311, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4382, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4441, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4459, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4501, "length": 102, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4639, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4663, "length": 105, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4804, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4828, "length": 105, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4969, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 4991, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5008, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5026, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5097, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5158, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5176, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5226, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5281, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5317, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5341, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5378, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5400, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5421, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5476, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5512, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5536, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5572, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5594, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5615, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5670, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5706, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5730, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5765, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5787, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5808, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5863, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5898, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5922, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5959, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 5981, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6000, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6017, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6032, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6045, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/[id]/page.tsx", "start": 6056, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [27, [{"file": "./app/novels/page.tsx", "start": 25, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/page.tsx", "start": 49, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/novels/page.tsx", "start": 200, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 234, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 294, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 347, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 509, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 523, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 598, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 681, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 695, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 787, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 800, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 860, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 899, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 914, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 939, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 954, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 978, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 993, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1014, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1027, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1040, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1054, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1188, "length": 101, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1302, "length": 83, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1400, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1436, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1456, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1475, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1569, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1587, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1702, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1719, "length": 73, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1807, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1821, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1843, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1863, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 1883, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 2139, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 2164, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/novels/page.tsx", "start": 2175, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [22, [{"file": "./app/page.tsx", "start": 17, "length": 11, "messageText": "Cannot find module 'next/link'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/page.tsx", "start": 54, "length": 6, "messageText": "Cannot find module 'next'. Did you mean to set the 'moduleResolution' option to 'node', or to add aliases to the 'paths' option?", "category": 1, "code": 2792}, {"file": "./app/page.tsx", "start": 226, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 287, "length": 92, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 388, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 477, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 491, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 590, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 603, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1080, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1093, "length": 10, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1142, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1198, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1267, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1361, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1377, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1474, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1487, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1503, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1572, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1661, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1677, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1759, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1772, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1788, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1857, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1945, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 1961, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2038, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2051, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2064, "length": 10, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2111, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2182, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2264, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2278, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2332, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2372, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2420, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2438, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2501, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2525, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2545, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2567, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2587, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2609, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2629, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2650, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2670, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2689, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2707, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2723, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2740, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2780, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2835, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2853, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2916, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2939, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2959, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 2980, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3000, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3015, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3035, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3054, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3074, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3091, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3109, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3125, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3140, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3153, "length": 10, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3202, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3281, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3362, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3376, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3436, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3464, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3479, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3498, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3513, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3535, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3550, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3559, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3611, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3627, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3640, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3653, "length": 10, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./app/page.tsx", "start": 3668, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [19, [{"file": "./lib/api.ts", "start": 628, "length": 8, "code": 2550, "category": 1, "messageText": "Property 'includes' does not exist on type 'number[]'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2016' or later."}, {"file": "./lib/api.ts", "start": 2646, "length": 8, "code": 2550, "category": 1, "messageText": "Property 'includes' does not exist on type 'string[]'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2016' or later."}]], 14, 3, 4, 6, 5, 2, 7, 8, 9, 10, 11, 12, 13, 1], "affectedFilesPendingEmit": [[16, 1], [18, 1], [23, 1], [20, 1], [21, 1], [25, 1], [26, 1], [15, 1], [17, 1], [24, 1], [30, 1], [29, 1], [28, 1], [27, 1], [22, 1], [19, 1], [14, 1], [2, 1]]}, "version": "4.9.5"}