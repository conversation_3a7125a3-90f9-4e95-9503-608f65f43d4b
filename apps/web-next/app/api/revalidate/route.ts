import { NextRequest, NextResponse } from 'next/server'
import { revalidatePath, revalidateTag } from 'next/cache'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { path, tag, secret } = body

    // 驗證密鑰 - 必須設置環境變數
    const revalidateSecret = process.env.REVALIDATE_SECRET
    if (!revalidateSecret) {
      console.error('REVALIDATE_SECRET is not set. Revalidation is disabled.')
      return NextResponse.json(
        { error: 'Revalidation not configured' },
        { status: 500 }
      )
    }

    if (secret !== revalidateSecret) {
      return NextResponse.json(
        { error: 'Invalid secret' },
        { status: 401 }
      )
    }

    // 根據類型進行重新驗證
    if (path) {
      // 重新驗證特定路徑
      revalidatePath(path)

      return NextResponse.json({
        success: true,
        message: `Path "${path}" revalidated successfully`,
        timestamp: new Date().toISOString(),
        type: 'path'
      })
    }

    if (tag) {
      // 重新驗證特定標籤
      revalidateTag(tag)

      return NextResponse.json({
        success: true,
        message: `Tag "${tag}" revalidated successfully`,
        timestamp: new Date().toISOString(),
        type: 'tag'
      })
    }

    return NextResponse.json(
      { error: 'Missing path or tag parameter' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Revalidation error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// 支援 GET 請求用於測試
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const path = searchParams.get('path')
  const tag = searchParams.get('tag')
  const secret = searchParams.get('secret')

  // 驗證密鑰 - 必須設置環境變數
  const revalidateSecret = process.env.REVALIDATE_SECRET
  if (!revalidateSecret) {
    console.error('REVALIDATE_SECRET is not set. Revalidation is disabled.')
    return NextResponse.json(
      { error: 'Revalidation not configured' },
      { status: 500 }
    )
  }

  if (secret !== revalidateSecret) {
    return NextResponse.json(
      { error: 'Invalid secret' },
      { status: 401 }
    )
  }

  try {
    if (path) {
      revalidatePath(path)
      return NextResponse.json({
        success: true,
        message: `Path "${path}" revalidated successfully`,
        timestamp: new Date().toISOString(),
        type: 'path'
      })
    }

    if (tag) {
      revalidateTag(tag)
      return NextResponse.json({
        success: true,
        message: `Tag "${tag}" revalidated successfully`,
        timestamp: new Date().toISOString(),
        type: 'tag'
      })
    }

    // 如果沒有指定路徑或標籤，返回使用說明
    return NextResponse.json({
      message: 'Next.js Revalidation API',
      usage: {
        POST: {
          url: '/api/revalidate',
          body: {
            secret: 'your-secret-key',
            path: '/novels/1/chapters/1', // 重新驗證特定路徑
            // 或
            tag: 'novels' // 重新驗證特定標籤
          }
        },
        GET: {
          url: '/api/revalidate?secret=your-secret-key&path=/novels/1/chapters/1',
          // 或
          url2: '/api/revalidate?secret=your-secret-key&tag=novels'
        }
      },
      examples: [
        {
          description: '重新驗證特定章節',
          method: 'POST',
          body: {
            secret: 'your-revalidate-secret',
            path: '/novels/1/chapters/1'
          }
        },
        {
          description: '重新驗證所有小說相關頁面',
          method: 'POST',
          body: {
            secret: 'your-revalidate-secret',
            tag: 'novels'
          }
        },
        {
          description: '重新驗證小說詳情頁',
          method: 'POST',
          body: {
            secret: 'your-revalidate-secret',
            path: '/novels/1'
          }
        }
      ]
    })

  } catch (error) {
    console.error('Revalidation error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
