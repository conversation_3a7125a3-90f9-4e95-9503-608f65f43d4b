import { Metadata } from 'next'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { novelsAPI } from '@/lib/api'
import ReloadButton from './ReloadButton'

interface Props {
  params: {
    id: string
    chapterId: string
  }
}

// ISR 配置：每 60 秒重新驗證
export const revalidate = 60

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id, chapterId } = params

  try {
    // 嘗試獲取章節資訊用於 SEO
    const response = await novelsAPI.getChapterContent(id, chapterId)
    const chapter = response.data as { title?: string } | null

    return {
      title: `${chapter?.title || `第 ${chapterId} 章`} - 小說 ${id}`,
      description: `閱讀小說 ${id} 的第 ${chapterId} 章內容`,
    }
  } catch (error) {
    return {
      title: `第 ${chapterId} 章 - 小說 ${id}`,
      description: `閱讀小說 ${id} 的第 ${chapterId} 章內容`,
    }
  }
}

export default async function ChapterPage({ params }: Props) {
  const { id, chapterId } = params

  // 驗證參數
  if (!id || !chapterId || isNaN(Number(id)) || isNaN(Number(chapterId))) {
    notFound()
  }

  let chapterData = null
  let error = null

  try {
    // SSR: 在伺服器端獲取章節內容
    const response = await novelsAPI.getChapterContent(id, chapterId)
    chapterData = response.data as {
      title?: string;
      content?: string;
      wordCount?: number;
      publishedAt?: string;
    } | null
  } catch (err) {
    error = err
    console.error('Failed to fetch chapter content:', err)
  }

  // 如果獲取失敗，顯示錯誤頁面
  if (error && !chapterData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">章節載入失敗</h1>
          <div className="flex gap-4">
            <Link
              href={`/novels/${id}`}
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              ← 返回小說
            </Link>
            <Link
              href="/novels"
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              小說列表
            </Link>
          </div>
        </div>

        <div className="bg-red-50 p-6 rounded-lg border border-red-200">
          <h2 className="text-xl font-semibold mb-3 text-red-900">
            ❌ 無法載入章節內容
          </h2>
          <p className="text-red-800 mb-4">
            抱歉，無法載入第 {chapterId} 章的內容。這可能是由於網路問題或章節不存在。
          </p>
          <div className="flex gap-4">
            <ReloadButton className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
              重新載入
            </ReloadButton>
            <Link
              href={`/novels/${id}`}
              className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
            >
              返回小說頁面
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 導航 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          {chapterData?.title || `第 ${chapterId} 章`}
        </h1>
        <div className="flex gap-4">
          <Link
            href={`/novels/${id}`}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            ← 返回小說
          </Link>
          <Link
            href="/novels"
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            小說列表
          </Link>
        </div>
      </div>

      {/* ISR 狀態指示 */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <div className="flex items-center justify-between text-sm">
          <div className="text-blue-800">
            🔄 ISR 啟用：每 60 秒重新驗證內容
          </div>
          <div className="text-blue-600">
            最後更新：{new Date().toLocaleString('zh-TW')}
          </div>
        </div>
      </div>

      {/* 章節內容 */}
      <div className="bg-white rounded-lg shadow-sm border">
        {/* 章節標題 */}
        <div className="p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {chapterData?.title || `第 ${chapterId} 章`}
          </h2>
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span>小說 ID: {id}</span>
            <span>章節 ID: {chapterId}</span>
            <span>字數: {chapterData?.wordCount || '未知'}</span>
            <span>發布時間: {chapterData?.publishedAt || '未知'}</span>
          </div>
        </div>

        {/* 章節正文 */}
        <div className="p-6">
          {chapterData?.content ? (
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap leading-relaxed text-gray-800">
                {chapterData.content}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-4">
                📖 這是示例章節內容
              </div>
              <div className="max-w-2xl mx-auto text-gray-700 leading-relaxed">
                <p className="mb-4">
                  這是小說 {id} 第 {chapterId} 章的示例內容。在實際應用中，
                  這裡會顯示從 Django 後端 API 獲取的真實章節內容。
                </p>
                <p className="mb-4">
                  Next.js 15 App Router 的 ISR (Incremental Static Regeneration)
                  功能確保章節內容會定期更新，同時保持良好的性能。
                </p>
                <p className="mb-4">
                  當作者發布新章節或更新現有章節時，內容會在 60 秒內自動重新生成，
                  為讀者提供最新的閱讀體驗。
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 章節導航 */}
      <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm border">
        <Link
          href={`/novels/${id}/chapters/${Math.max(1, Number(chapterId) - 1)}`}
          className={`px-4 py-2 rounded-lg transition-colors ${
            Number(chapterId) <= 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          ← 上一章
        </Link>

        <div className="text-center">
          <div className="text-sm text-gray-500">當前章節</div>
          <div className="font-semibold text-gray-900">第 {chapterId} 章</div>
        </div>

        <Link
          href={`/novels/${id}/chapters/${Number(chapterId) + 1}`}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          下一章 →
        </Link>
      </div>

      {/* 開發資訊 */}
      <div className="bg-gray-50 p-4 rounded-lg border text-sm text-gray-600">
        <h3 className="font-semibold mb-2">ISR 技術特色：</h3>
        <ul className="space-y-1">
          <li>• 伺服器端渲染 (SSR) 確保 SEO 友好</li>
          <li>• 增量靜態再生 (ISR) 平衡性能與即時性</li>
          <li>• 自動錯誤處理和降級顯示</li>
          <li>• 支援手動重新驗證 API</li>
        </ul>
      </div>
    </div>
  )
}
