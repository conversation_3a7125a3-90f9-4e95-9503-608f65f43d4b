/**
 * API 客戶端配置
 * 支援 Next.js 15 App Router 的 API 調用
 */

// API 配置
const API_CONFIG = {
  baseURL: '/api/proxy',
  timeout: 30000, // 30 秒超時
  retries: 3, // 重試次數
}

// API 錯誤類型
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public data?: any,
    public isRetryable: boolean = false
  ) {
    super(message)
    this.name = 'APIError'
  }

  // 判斷錯誤是否可重試
  static isRetryableError(status: number): boolean {
    // 5xx 伺服器錯誤通常可重試
    if (status >= 500) return true

    // 特定的 4xx 錯誤可重試
    const retryable4xxCodes = [408, 429] // 請求超時、請求過多
    if (retryable4xxCodes.includes(status)) return true

    return false
  }

  // 獲取用戶友好的錯誤訊息
  getUserFriendlyMessage(): string {
    switch (this.status) {
      case 400:
        return '請求參數錯誤，請檢查輸入內容'
      case 401:
        return '未授權訪問，請重新登入'
      case 403:
        return '權限不足，無法執行此操作'
      case 404:
        return '請求的資源不存在'
      case 408:
        return '請求超時，請稍後重試'
      case 429:
        return '請求過於頻繁，請稍後重試'
      case 500:
        return '伺服器內部錯誤，請稍後重試'
      case 502:
        return '網關錯誤，請稍後重試'
      case 503:
        return '服務暫時不可用，請稍後重試'
      case 504:
        return '網關超時，請稍後重試'
      default:
        return this.message || '發生未知錯誤'
    }
  }
}

// 請求配置接口
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: any
  timeout?: number
  retries?: number
  // 認證相關
  token?: string
  sessionCookie?: string
  // SSR 相關
  isServerSide?: boolean
}

// 響應接口
interface APIResponse<T = any> {
  data: T
  status: number
  statusText: string
  headers: Headers
}

/**
 * 基礎 API 請求函數
 */
async function request<T = any>(
  endpoint: string,
  config: RequestConfig = {}
): Promise<APIResponse<T>> {
  const {
    method = 'GET',
    headers = {},
    body,
    timeout = API_CONFIG.timeout,
    retries = API_CONFIG.retries,
  } = config

  // 構建完整 URL
  const url = endpoint.startsWith('http')
    ? endpoint
    : `${API_CONFIG.baseURL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`

  // 準備請求標頭
  const requestHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...headers,
  }

  // 添加認證標頭
  if (config.token) {
    requestHeaders['Authorization'] = `Bearer ${config.token}`
  }

  if (config.sessionCookie) {
    requestHeaders['Cookie'] = config.sessionCookie
  }

  // SSR 環境標識
  if (config.isServerSide || typeof window === 'undefined') {
    requestHeaders['X-Requested-With'] = 'SSR'
  }

  // 準備請求體
  let requestBody: string | undefined
  if (body && ['POST', 'PUT', 'PATCH'].includes(method)) {
    requestBody = typeof body === 'string' ? body : JSON.stringify(body)
  }

  // 重試邏輯
  let lastError: Error | null = null

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: requestBody,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      // 解析響應
      let responseData: T
      const contentType = response.headers.get('content-type')

      if (contentType?.includes('application/json')) {
        responseData = await response.json()
      } else {
        responseData = (await response.text()) as T
      }

      // 檢查響應狀態
      if (!response.ok) {
        const isRetryable = APIError.isRetryableError(response.status)
        throw new APIError(
          `API request failed: ${response.statusText}`,
          response.status,
          responseData,
          isRetryable
        )
      }

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      }

    } catch (error) {
      lastError = error as Error

      // 如果是最後一次嘗試，拋出錯誤
      if (attempt === retries) {
        break
      }

      // 如果是網路錯誤或超時，等待後重試
      if (error instanceof TypeError || error instanceof DOMException) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)))
        continue
      }

      // 如果是 API 錯誤且不可重試，直接拋出
      if (error instanceof APIError && !error.isRetryable) {
        throw error
      }

      // 計算退避延遲 (指數退避 + 隨機抖動)
      const baseDelay = 1000 * Math.pow(2, attempt) // 指數退避
      const jitter = Math.random() * 1000 // 隨機抖動
      const delay = Math.min(baseDelay + jitter, 10000) // 最大 10 秒

      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  // 拋出最後的錯誤
  if (lastError instanceof APIError) {
    throw lastError
  }

  throw new APIError(
    lastError?.message || 'Unknown API error',
    0,
    lastError
  )
}

/**
 * API 客戶端類
 */
export class APIClient {
  /**
   * GET 請求
   */
  static async get<T = any>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>) {
    return request<T>(endpoint, { ...config, method: 'GET' })
  }

  /**
   * POST 請求
   */
  static async post<T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) {
    return request<T>(endpoint, { ...config, method: 'POST', body: data })
  }

  /**
   * PUT 請求
   */
  static async put<T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) {
    return request<T>(endpoint, { ...config, method: 'PUT', body: data })
  }

  /**
   * DELETE 請求
   */
  static async delete<T = any>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>) {
    return request<T>(endpoint, { ...config, method: 'DELETE' })
  }

  /**
   * PATCH 請求
   */
  static async patch<T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) {
    return request<T>(endpoint, { ...config, method: 'PATCH', body: data })
  }
}

/**
 * 小說相關 API
 */
export const novelsAPI = {
  /**
   * 獲取小說列表
   */
  getList: (params?: Record<string, any>) => {
    const searchParams = params ? `?${new URLSearchParams(params).toString()}` : ''
    return APIClient.get(`/novels${searchParams}`)
  },

  /**
   * 獲取小說詳情
   */
  getDetail: (id: string | number) => {
    return APIClient.get(`/novels/${id}`)
  },

  /**
   * 獲取小說章節列表
   */
  getChapters: (novelId: string | number, params?: Record<string, any>) => {
    const searchParams = params ? `?${new URLSearchParams(params).toString()}` : ''
    return APIClient.get(`/novels/${novelId}/chapters${searchParams}`)
  },

  /**
   * 獲取章節內容
   */
  getChapterContent: (novelId: string | number, chapterId: string | number) => {
    return APIClient.get(`/novels/${novelId}/chapters/${chapterId}`)
  },
}

/**
 * 用戶相關 API
 */
export const userAPI = {
  /**
   * 用戶登入
   */
  login: (credentials: { username: string; password: string }) => {
    return APIClient.post('/auth/login', credentials)
  },

  /**
   * 用戶註冊
   */
  register: (userData: { username: string; email: string; password: string }) => {
    return APIClient.post('/auth/register', userData)
  },

  /**
   * 獲取用戶資料
   */
  getProfile: () => {
    return APIClient.get('/auth/profile')
  },

  /**
   * 登出
   */
  logout: () => {
    return APIClient.post('/auth/logout')
  },
}

// 預設導出
export default APIClient
