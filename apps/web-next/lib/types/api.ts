/**
 * 統一的 API 型別定義
 * 用於替換項目中的 any 型別使用
 */

// 基礎型別定義
export type ApiErrorData = Record<string, unknown> | string | null;
export type RequestBody = BodyInit | Record<string, unknown> | null;
export type QueryParams = Record<string, string | number | boolean | (string | number | boolean)[]>;

// API 錯誤響應型別
export interface ApiErrorResponse {
  error: string;
  message: string;
  details?: Record<string, unknown>;
  code?: string | number;
}

// HTTP 方法型別
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// 認證相關型別
export interface AuthHeaders {
  Authorization?: string;
  'X-API-Key'?: string;
}

// 小說相關 API 響應型別
export interface NovelListParams {
  page?: number;
  limit?: number;
  category?: string;
  author?: string;
  status?: string;
  search?: string;
}

export interface NovelListResponse {
  novels: Novel[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// 重新匯出現有的型別定義，確保一致性
export interface Novel {
  id: string;
  title: string;
  author: string;
  description: string;
  category: string;
  status: 'ongoing' | 'completed' | 'paused';
  coverUrl?: string;
  tags: string[];
  rating: number;
  chapterCount: number;
  wordCount: number;
  lastUpdated: string;
  createdAt: string;
}

export interface Chapter {
  id: string;
  novelId: string;
  title: string;
  content: string;
  chapterNumber: number;
  wordCount: number;
  publishedAt: string;
  isVip: boolean;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
}

// 搜索相關型別
export interface SearchResult {
  novels: Novel[];
  total: number;
  query: string;
  suggestions: string[];
}

// API 響應包裝型別
export interface ApiResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// 請求配置型別
export interface RequestConfig {
  method?: HttpMethod;
  headers?: Record<string, string>;
  body?: RequestBody;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

// 錯誤類別型別
export interface ApiErrorClass {
  status: number;
  message: string;
  data?: ApiErrorData;
  isRetryable: boolean;
}
