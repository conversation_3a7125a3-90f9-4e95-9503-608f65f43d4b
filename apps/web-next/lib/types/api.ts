/**
 * 統一 API 型別定義
 * 用於消除 any 型別使用，提升型別安全性
 */

// ===== 基礎型別 =====

// API 錯誤數據型別
export interface ApiErrorData {
  message?: string;
  code?: string;
  details?: Record<string, unknown>;
  stack?: string;
}

// 請求體型別
export type RequestBody = 
  | string 
  | number 
  | boolean 
  | Record<string, unknown> 
  | Array<unknown>
  | FormData
  | null;

// 查詢參數型別
export type QueryParams = Record<string, string | number | boolean | undefined>;

// ===== API 響應型別 =====

// 標準 API 錯誤響應
export interface ApiErrorResponse {
  error: {
    message: string;
    code?: string;
    status: number;
  };
  timestamp?: string;
  path?: string;
}

// 分頁信息
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 小說列表響應
export interface NovelListResponse {
  novels: Novel[];
  pagination: PaginationInfo;
}

// 章節列表響應  
export interface ChapterListResponse {
  chapters: Chapter[];
  novel: Novel;
  pagination?: PaginationInfo;
}

// 搜索響應
export interface SearchResponse {
  results: Novel[];
  query: string;
  pagination: PaginationInfo;
}

// ===== 實體型別 =====

// 小說型別
export interface Novel {
  id: number;
  title: string;
  author: string;
  description: string;
  coverImage?: string;
  status: 'ongoing' | 'completed' | 'paused';
  category: Category;
  tags: string[];
  chapterCount: number;
  wordCount: number;
  lastUpdated: string;
  createdAt: string;
  rating?: number;
  viewCount?: number;
  sourceUrl?: string;
  sourceSite?: string;
}

// 章節型別
export interface Chapter {
  id: number;
  novelId: number;
  chapterNumber: number;
  title: string;
  content: string;
  wordCount: number;
  publishedAt: string;
  createdAt: string;
  sourceUrl?: string;
  previousChapterId?: number;
  nextChapterId?: number;
}

// 分類型別
export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  novelCount?: number;
}

// ===== 配置型別 =====

// 請求配置型別（更嚴格的版本）
export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: RequestBody;
  timeout?: number;
  retries?: number;
  token?: string;
  sessionCookie?: string;
  isServerSide?: boolean;
}

// API 響應型別（泛型約束）
export interface ApiResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

// ===== 錯誤類別型別 =====

// API 錯誤類別的建構函數參數
export interface ApiErrorClass {
  message: string;
  status: number;
  data?: ApiErrorData;
  isRetryable?: boolean;
}

// ===== 實用型別 =====

// 未知但安全的型別
export type SafeAny = unknown;

// HTTP 方法型別
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// API 端點型別
export type ApiEndpoint = string;

// 型別防護函數
export function isApiErrorResponse(data: unknown): data is ApiErrorResponse {
  return (
    typeof data === 'object' &&
    data !== null &&
    'error' in data &&
    typeof (data as any).error === 'object'
  );
}

export function isNovel(data: unknown): data is Novel {
  return (
    typeof data === 'object' &&
    data !== null &&
    'id' in data &&
    'title' in data &&
    'author' in data
  );
} 