# Monorepo CI Deployment Simulation Configuration
# 全新設計：基於 Monorepo 架構的智能緩存部署模擬
# 目標：30-60 秒完成緩存命中後的部署測試

# version: '3.8' # 移除廢棄的 version 屬性

services:
  # PostgreSQL 測試資料庫 - 優化版
  postgres-ci:
    image: postgres:15-alpine
    container_name: novel-postgres-ci-v2
    environment:
      POSTGRES_DB: test_novelwebsite_ci
      POSTGRES_USER: ci_user
      POSTGRES_PASSWORD: ci_password
      POSTGRES_INITDB_ARGS: --encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5433:5432"
    tmpfs:
      # 使用內存文件系統提高性能
      - /var/lib/postgresql/data
    command: >
      postgres
      -c log_statement=none
      -c log_destination=stderr
      -c logging_collector=off
      -c max_connections=20
      -c shared_buffers=32MB
      -c effective_cache_size=128MB
      -c fsync=off
      -c synchronous_commit=off
      -c full_page_writes=off
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ci_user -d test_novelwebsite_ci"]
      interval: 3s
      timeout: 2s
      retries: 5
      start_period: 5s
    networks:
      - ci_network

  # Redis 快取服務 - 優化版
  redis-ci:
    image: redis:7-alpine
    container_name: novel-redis-ci-v2
    ports:
      - "6380:6379"
    tmpfs:
      # 使用內存文件系統
      - /data
    command: >
      redis-server
      --maxmemory 64mb
      --maxmemory-policy allkeys-lru
      --save ""
      --appendonly no
      --tcp-keepalive 60
      --timeout 0
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 3s
      timeout: 2s
      retries: 5
      start_period: 3s
    networks:
      - ci_network

  # 後端應用服務 - Monorepo 架構適配版 (使用預構建映像)
  backend-ci:
    image: ${ECR_REGISTRY}/novel-web-backend:${BACKEND_IMAGE_TAG:-latest}
    # [OPTIMIZATION] 使用預構建映像，避免重複構建耗時 6+ 分鐘
    # build:
    #   context: .
    #   dockerfile: infra/docker/backend-ci-v2.Dockerfile
    #   cache_from:
    #     - type=gha
    #   cache_to:
    #     - type=gha,mode=max
    container_name: novel-backend-ci-v2
    environment:
      # Django 設置 - 使用正確的模組路徑
      - DJANGO_SETTINGS_MODULE=config.django_settings
      - PYTHONPATH=/workspace/backend
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1

      # [FIX] 設置環境模式以確保正確的配置驗證
      - ENVIRONMENT=ci

      # 資料庫設置
      - DATABASE_URL=*************************************************/test_novelwebsite_ci
      - DB_HOST=postgres-ci
      - DB_PORT=5432
      - DB_NAME=test_novelwebsite_ci
      - DB_USER=ci_user
      - DB_PASSWORD=ci_password

      # Redis 設置
      - REDIS_URL=redis://redis-ci:6379/0
      - REDIS_HOST=redis-ci
      - REDIS_PORT=6379
      - REDIS_DB=0

      # CI 專用設置
      - DEBUG=False
      - SECRET_KEY=ci-test-secret-key-not-for-production-v2
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend-ci
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend-ci:3000

      # 爬蟲設置（最小化）
      - CRAWLER_MAX_WORKERS=1
      - CRAWLER_CHUNK_SIZE=5
      - CRAWLER_DELAY=0.5
      - CRAWLER_TIMEOUT=10
      - CRAWLER_RETRIES=1
    ports:
      - "8001:8000"
    # [OPTIMIZATION] 移除 volume mount 以啟用 Docker Layer Cache
    # volumes: - .:/workspace  # 這會覆蓋 Dockerfile 中的緩存層
    working_dir: /workspace
    depends_on:
      postgres-ci:
        condition: service_healthy
      redis-ci:
        condition: service_healthy
    command: >
      bash -c "
        echo '🚀 Starting Monorepo CI Backend Deployment Simulation v2...' &&
        cd backend &&
        echo '📋 Running Django checks...' &&
        python manage.py check --deploy &&
        echo '🗄️ Running database migrations...' &&
        python manage.py migrate --noinput &&
        echo '✅ Static files already collected at build time' &&
        echo '🔥 Starting Gunicorn server...' &&
        gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers 2 --worker-class sync --timeout 30 --keep-alive 2 --max-requests 100 --max-requests-jitter 10 --access-logfile - --error-logfile - --log-level info
      "
    # [FIX] 啟用健康檢查 - 增加耐心等待 Django 完整啟動
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/api/v1/health/ || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 10
      # [THE FIX] 大幅增加啟動寬限期，讓 Django 有充足時間完成靜態文件處理
      start_period: 90s
    networks:
      - ci_network

  # 前端應用服務 - Monorepo 架構適配版 (使用預構建映像)
  frontend-ci:
    image: ${ECR_REGISTRY}/novel-web-frontend:${FRONTEND_IMAGE_TAG:-latest}
    # [OPTIMIZATION] 使用預構建映像，避免重複構建
    # build:
    #   context: .
    #   dockerfile: infra/docker/frontend-ci-v2.Dockerfile
    #   cache_from:
    #     - type=gha
    #   cache_to:
    #     - type=gha,mode=max
    container_name: novel-frontend-ci-v2
    environment:
      - NODE_ENV=production
      - CI=true
      - NEXT_PUBLIC_API_URL=http://backend-ci:8000/api/v1
      - NEXT_PUBLIC_MEDIA_URL=http://backend-ci:8000/media
      - NEXT_PUBLIC_TITLE=NovelWebsite (CI Test v2)
      - NEXT_PUBLIC_DESCRIPTION=Next.js 15 CI 部署測試環境
      - NEXT_TELEMETRY_DISABLED=1
    ports:
      - "3001:3000"
    # [OPTIMIZATION] 移除 volume mount 以啟用 Docker Layer Cache
    # volumes: - .:/workspace  # 這會覆蓋 Dockerfile 中的緩存層
    working_dir: /workspace/apps/web-next
    depends_on:
      backend-ci:
        condition: service_healthy
    command: >
      bash -c "
        echo '🚀 Starting Monorepo CI Frontend Deployment Simulation v2 (Next.js 15)...' &&
        echo '✅ Dependencies already installed via Docker Layer Cache' &&
        echo '✅ Application already built via Docker Layer Cache' &&
        echo '🌐 Starting Next.js production server...' &&
        npm start
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/", "||", "exit", "1"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 30s
    networks:
      - ci_network

# 網路配置
networks:
  ci_network:
    driver: bridge
    name: novel_ci_network_v2

# 卷配置（全部使用臨時卷以提高性能）
volumes:
  postgres_ci_data:
    driver: local
  redis_ci_data:
    driver: local
