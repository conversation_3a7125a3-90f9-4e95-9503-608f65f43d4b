from rest_framework import serializers
from django.contrib.auth.models import User
from ..models import (
    Category,
    Tag,
    Novel,
    Chapter,
    UserProfile,
    ReadingHistory,
    AdPlacement,
    SearchHistory,
)


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ["id", "name", "slug"]


class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ["id", "name", "slug"]


class NovelListSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)

    class Meta:
        model = Novel
        fields = [
            "id",
            "title",
            "author",
            "description",
            "category",
            "views",
            "favorites",
            "status",
            "updated_at",
        ]


class ChapterListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Chapter
        fields = ["id", "title", "chapter_number", "views", "updated_at"]


class ChapterDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Chapter
        fields = ["id", "title", "chapter_number", "content", "views", "updated_at"]


class NovelDetailSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    chapters = ChapterListSerializer(many=True, read_only=True)

    class Meta:
        model = Novel
        fields = [
            "id",
            "title",
            "author",
            "description",
            "source_url",
            "category",
            "chapters",
            "views",
            "favorites",
            "status",
            "created_at",
            "updated_at",
        ]


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "username", "email"]
        extra_kwargs = {"password": {"write_only": True}}


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    favorite_novels = NovelListSerializer(many=True, read_only=True)

    class Meta:
        model = UserProfile
        fields = ["id", "user", "favorite_novels"]


class ReadingHistorySerializer(serializers.ModelSerializer):
    chapter = ChapterListSerializer(read_only=True)
    novel = serializers.SerializerMethodField()

    class Meta:
        model = ReadingHistory
        fields = ["id", "chapter", "novel", "last_read"]

    def get_novel(self, obj):
        return NovelListSerializer(obj.chapter.novel).data


class AdPlacementSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdPlacement
        fields = ["id", "name", "position", "ad_code", "is_active"]


class SearchHistorySerializer(serializers.ModelSerializer):
    category_name = serializers.SerializerMethodField()

    class Meta:
        model = SearchHistory
        fields = ["id", "query", "category_name", "filters", "created_at"]

    def get_category_name(self, obj):
        return obj.category.name if obj.category else None
