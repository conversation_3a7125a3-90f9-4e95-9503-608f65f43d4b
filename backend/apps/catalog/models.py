from django.db import models
from django.contrib.auth.models import User


class Category(models.Model):
    """Represents a category for novels."""

    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True)

    class Meta:
        verbose_name_plural = "categories"

    def __str__(self):
        return self.name


class Tag(models.Model):
    """Represents a tag for categorizing novels."""

    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True)

    class Meta:
        pass

    def __str__(self):
        return self.name


class Novel(models.Model):
    """Core novel model containing metadata and classification."""

    STATUS_CHOICES = (
        ("ongoing", "連載中"),
        ("completed", "已完結"),
    )

    title = models.CharField(max_length=200)
    author = models.CharField(max_length=100)
    description = models.TextField()
    cover = models.ImageField(upload_to="covers/", null=True, blank=True)
    category = models.ForeignKey(
        Category, on_delete=models.SET_NULL, null=True, db_index=True
    )
    tags = models.ManyToManyField(Tag)
    status = models.Char<PERSON>ield(max_length=20, choices=STATUS_CHOICES, default="ongoing")
    views = models.IntegerField(default=0)
    likes = models.IntegerField(default=0)
    favorites = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    source = models.CharField(max_length=50)
    source_id = models.CharField(max_length=100)
    source_url = models.URLField()

    class Meta:
        unique_together = ("source", "source_id")
        ordering = ["-updated_at"]

    def __str__(self):
        return self.title


class Chapter(models.Model):
    """Stores chapter content for a novel."""

    novel = models.ForeignKey(Novel, on_delete=models.CASCADE, related_name="chapters")
    title = models.CharField(max_length=200)
    content = models.TextField()
    chapter_number = models.IntegerField()
    views = models.IntegerField(default=0)
    is_vip = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    source_url = models.URLField()

    class Meta:
        unique_together = ("novel", "chapter_number")
        ordering = ["chapter_number"]

    def __str__(self):
        return f"{self.novel.title} - Chapter {self.chapter_number}: {self.title}"


class UserProfile(models.Model):
    """Extends the default user with novel-specific data."""

    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="catalog_profile"
    )
    favorite_novels = models.ManyToManyField(Novel, related_name="favorited_by")
    reading_history = models.ManyToManyField(Chapter, through="ReadingHistory")

    class Meta:
        pass

    def __str__(self):
        return self.user.username


class ReadingHistory(models.Model):
    """Tracks which chapters a user has read."""

    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE)
    last_read = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-last_read"]
        unique_together = ["user_profile", "chapter"]


class AdPlacement(models.Model):
    """Stores advertising placement configuration."""

    POSITION_CHOICES = [
        ("chapter_top", "章節頂部"),
        ("chapter_bottom", "章節底部"),
        ("chapter_middle", "章節中間"),
        ("sidebar", "側邊欄"),
        ("homepage", "首頁"),
    ]

    name = models.CharField(max_length=100)
    position = models.CharField(max_length=20, choices=POSITION_CHOICES)
    ad_code = models.TextField()  # HTML/JavaScript 廣告代碼
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(null=True, blank=True)

    # 統計信息
    impressions = models.IntegerField(default=0)
    clicks = models.IntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        pass

    def __str__(self):
        return f"{self.name} - {self.position}"


class SearchHistory(models.Model):
    """Records search queries for analytics."""

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="catalog_search_history",
    )
    query = models.CharField(max_length=200)
    category = models.ForeignKey(
        Category, on_delete=models.SET_NULL, null=True, blank=True
    )
    filters = models.JSONField(default=dict, blank=True)  # 存儲其他過濾條件
    created_at = models.DateTimeField(auto_now_add=True)
    is_anonymous = models.BooleanField(default=False)  # 是否為匿名用戶的搜索
    session_id = models.CharField(
        max_length=100, null=True, blank=True
    )  # 用於追踪匿名用戶

    class Meta:
        pass
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["session_id", "-created_at"]),
        ]

    def __str__(self):
        return f"{self.user.username if self.user else 'Anonymous'} - {self.query}"
