# 測試環境配置模板
# 複製此檔案為 .env.test (git 會忽略) 並填入實際值

# Django 測試設定
SECRET_KEY=test-secret-key-for-django-tests
DEBUG=true

# 測試資料庫配置
TEST_DB_NAME=test_novelwebsite
TEST_DB_USER=postgres
TEST_DB_PASSWORD=postgres
TEST_DB_HOST=localhost
TEST_DB_PORT=5432

# 測試用戶憑證
TEST_USER_PASSWORD=secure_test_password_123
TEST_ADMIN_PASSWORD=secure_admin_password_456

# Redis 測試配置
REDIS_URL=redis://localhost:6379/1

# 測試用 API 金鑰 (使用假值)
TEST_API_KEY=test_api_key_12345
TEST_JWT_SECRET=test_jwt_secret_key_for_testing
