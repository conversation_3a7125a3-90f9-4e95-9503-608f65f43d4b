# ===========================
# NovelWebsite 環境變數配置
# ===========================

# PostgreSQL 數據庫配置
DATABASE_URL=postgresql://[DB_USER]:[DB_PASSWORD]@[DB_HOST]:5432/[DB_NAME]
POSTGRES_DB=novelwebsite
POSTGRES_USER=[YOUR_DB_USERNAME]
POSTGRES_PASSWORD=[YOUR_SECURE_PASSWORD]
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Django 配置
SECRET_KEY=[YOUR_DJANGO_SECRET_KEY_CHANGE_THIS_IN_PRODUCTION]
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,your-production-domain.com

# Admin Security (change in production)
ADMIN_URL=secure-admin-path-here/

# Redis 配置 (Celery 與 Scrapy-Redis)
REDIS_PORT=6379
REDIS_URL=redis://localhost:${REDIS_PORT}/0
# 若 6379 已佔用，可調整 REDIS_PORT 並同步 docker-compose.redis.yml
CELERY_BROKER_URL=redis://localhost:${REDIS_PORT}/0
CELERY_RESULT_BACKEND=redis://localhost:${REDIS_PORT}/0

# Scrapy 爬蟲配置
SCRAPY_LOG_LEVEL=INFO
DOWNLOAD_DELAY=1
RANDOMIZE_DOWNLOAD_DELAY=0.5
CONCURRENT_REQUESTS=16

# ttkan.co 爬蟲特定配置
TTKAN_BASE_URL=https://ttkan.co
USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36

# 安全配置 (Production-first approach)
CORS_ALLOW_ALL_ORIGINS=False
CORS_ALLOWED_ORIGINS=http://localhost:8000,https://your-frontend-domain.com
CORS_ALLOW_CREDENTIALS=True

# 靜態文件配置
STATIC_URL=/static/
MEDIA_URL=/media/

# 郵件設置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=[YOUR_EMAIL]@gmail.com
EMAIL_HOST_PASSWORD=[YOUR_APP_PASSWORD]

# 其他設置
TIME_ZONE=Asia/Taipei
LANGUAGE_CODE=zh-hant

# Node.js 服務設置 (Node.js 18+)
PORT=8000
DB_SSL=false
