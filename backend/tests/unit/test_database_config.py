"""
DatabaseConfig 單元測試
防止資料庫配置回歸問題的安全網
"""

import os
import pytest
from unittest.mock import patch

from config.database import DatabaseConfig


class TestDatabaseConfig:
    """DatabaseConfig 類的單元測試"""

    def test_case_1_database_url_only(self):
        """
        Case 1: 當只設置 DATABASE_URL 時，postgres_config_dict 返回的字典是否正確？
        """
        database_url = (
            "*************************************************/test_novelwebsite_ci"
        )

        with patch.dict(
            os.environ,
            {
                "DATABASE_URL": database_url,
                # 清除其他可能的環境變數
                "DB_HOST": "",
                "DB_USER": "",
                "DB_PASSWORD": "",
                "DB_NAME": "",
                "DB_PORT": "",
            },
            clear=False,
        ):
            config = DatabaseConfig()
            result = config.postgres_config_dict

            # 驗證解析結果
            assert result["HOST"] == "postgres-ci"
            assert result["PORT"] == 5432
            assert result["NAME"] == "test_novelwebsite_ci"
            assert result["USER"] == "ci_user"
            assert result["PASSWORD"] == "ci_password"
            assert result["ENGINE"] == "django.db.backends.postgresql"
            assert "client_encoding" in result["OPTIONS"]
            assert result["TEST"]["NAME"] == "test_test_novelwebsite_ci"

    def test_case_2_database_url_priority(self):
        """
        Case 2: 當同時設置了 DATABASE_URL 和單獨的 DB_HOST 等變量時，
        postgres_config_dict 是否優先使用了 DATABASE_URL 的值？
        """
        database_url = "************************************************/url_database"

        with patch.dict(
            os.environ,
            {
                "DATABASE_URL": database_url,
                # 設置衝突的個別環境變數
                "DB_HOST": "individual-host",
                "DB_USER": "individual_user",
                "DB_PASSWORD": "individual_password",
                "DB_NAME": "individual_database",
                "DB_PORT": "5434",
            },
            clear=False,
        ):
            config = DatabaseConfig()
            result = config.postgres_config_dict

            # 驗證優先使用 DATABASE_URL 的值
            assert result["HOST"] == "url-host"  # 來自 DATABASE_URL
            assert result["PORT"] == 5433  # 來自 DATABASE_URL
            assert result["NAME"] == "url_database"  # 來自 DATABASE_URL
            assert result["USER"] == "url_user"  # 來自 DATABASE_URL
            assert result["PASSWORD"] == "url_password"  # 來自 DATABASE_URL

            # 不應該使用個別環境變數的值
            assert result["HOST"] != "individual-host"
            assert result["USER"] != "individual_user"

    def test_case_3_individual_variables_fallback(self):
        """
        Case 3: 當沒有設置 DATABASE_URL，只設置了單獨變量時，
        postgres_config_dict 返回的字典是否正確？
        """
        # 直接傳入參數來避免 .env 文件干擾
        import secrets

        test_password = secrets.token_hex(8)  # 生成隨機測試密碼
        config = DatabaseConfig(
            database_url=None,  # 確保沒有 DATABASE_URL
            postgres_host="fallback-host",
            postgres_user="fallback_user",
            postgres_password=test_password,
            postgres_db="fallback_database",
            postgres_port=5435,
        )
        result = config.postgres_config_dict

        # 驗證使用個別環境變數的值
        assert result["HOST"] == "fallback-host"
        assert result["PORT"] == 5435
        assert result["NAME"] == "fallback_database"
        assert result["USER"] == "fallback_user"
        assert result["PASSWORD"] == test_password

    def test_database_url_parsing_edge_cases(self):
        """測試 DATABASE_URL 解析的邊界情況"""

        # 測試沒有密碼的情況
        database_url = "postgresql://user@host:5432/database"
        with patch.dict(os.environ, {"DATABASE_URL": database_url}, clear=False):
            config = DatabaseConfig()
            result = config.postgres_config_dict

            assert result["USER"] == "user"
            assert result["PASSWORD"] == ""
            assert result["HOST"] == "host"
            assert result["NAME"] == "database"

        # 測試沒有端口的情況
        database_url = "************************************"
        with patch.dict(os.environ, {"DATABASE_URL": database_url}, clear=False):
            config = DatabaseConfig()
            result = config.postgres_config_dict

            assert result["PORT"] == 5432  # 預設端口

    def test_postgres_url_property(self):
        """測試 postgres_url 屬性的正確性"""
        database_url = "***********************************************/test_db"

        with patch.dict(os.environ, {"DATABASE_URL": database_url}, clear=False):
            config = DatabaseConfig()

            # postgres_url 應該返回原始的 DATABASE_URL
            assert config.postgres_url == database_url

    def test_django_database_config_structure(self):
        """測試 django_database_config 返回的結構是否正確"""
        database_url = "*****************************************************/django_db"

        with patch.dict(os.environ, {"DATABASE_URL": database_url}, clear=False):
            config = DatabaseConfig()
            result = config.django_database_config

            # 驗證結構
            assert "default" in result
            assert isinstance(result["default"], dict)
            assert result["default"]["ENGINE"] == "django.db.backends.postgresql"
            assert result["default"]["HOST"] == "django_host"

    def test_ssl_configuration(self):
        """測試 SSL 配置是否正確處理"""
        database_url = "********************************************/ssl_db"

        with patch.dict(
            os.environ,
            {"DATABASE_URL": database_url, "DB_SSL": "true", "DB_SSL_MODE": "require"},
            clear=False,
        ):
            config = DatabaseConfig()
            result = config.postgres_config_dict

            # 驗證 SSL 配置
            assert "sslmode" in result["OPTIONS"]
            assert result["OPTIONS"]["sslmode"] == "require"

    def test_empty_database_url_fallback(self):
        """測試空的 DATABASE_URL 時的回退行為"""
        # 直接傳入參數來避免 .env 文件干擾
        config = DatabaseConfig(
            database_url=None,  # 空的 DATABASE_URL
            postgres_host="localhost",
            postgres_user="postgres",
            postgres_db="testdb",
        )
        result = config.postgres_config_dict

        # 應該回退到個別環境變數
        assert result["HOST"] == "localhost"
        assert result["USER"] == "postgres"
        assert result["NAME"] == "testdb"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
