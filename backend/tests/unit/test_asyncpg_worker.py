import asyncio
import json
import os
import sys

sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
)  # noqa: E402

import pytest  # noqa: E402

from crawler_engine.worker.asyncpg_worker import AsyncPGWorker  # noqa: E402


class FakeRedis:
    def __init__(self, items):
        self.items = items

    async def blpop(self, queue, timeout=0):
        if self.items:
            return queue, json.dumps(self.items.pop(0))
        return None

    async def rpush(self, queue, item):
        self.items.append(json.loads(item))


class FakePoolManager:
    def __init__(self, redis_client):
        self.redis_client = redis_client

    async def ensure_connections(self):
        return True

    def get_redis(self):
        return self.redis_client

    async def close_pools(self):
        pass


class DummyWriter:
    def __init__(self):
        self.novels = []
        self.chapters = []

    async def init(self):
        pass

    async def save_novel(self, info):
        self.novels.append(info)
        return info["source_url"]

    async def save_chapters(self, novel_id, chapters):
        self.chapters.append((novel_id, chapters))
        return True


@pytest.mark.asyncio
async def test_asyncpg_worker_processes_items():
    items = [
        {
            "source_url": "n1",
            "title": "T1",
            "author": "A",
            "chapter_number": 1,
            "chapter_content": "c1",
        },
        {
            "source_url": "n1",
            "chapter_number": 2,
            "chapter_content": "c2",
        },
    ]
    redis_client = FakeRedis(items)
    pool = FakePoolManager(redis_client)
    writer = DummyWriter()

    worker = AsyncPGWorker(
        pool_manager=pool, db_writer=writer, batch_size=2, fetch_timeout=0
    )
    task = asyncio.create_task(worker.start())

    await asyncio.sleep(0.05)
    worker.stop()
    await task

    assert len(writer.novels) == 1
    assert writer.novels[0]["title"] == "T1"
    assert len(writer.chapters) == 1
    assert writer.chapters[0][0] == "n1"
    assert len(writer.chapters[0][1]) == 2
    assert writer.chapters[0][1][0]["chapter_number"] == 1
    assert writer.chapters[0][1][1]["chapter_number"] == 2
