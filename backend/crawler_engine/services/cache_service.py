import logging
from typing import Dict, Any, Optional
import json
import redis.asyncio as aioredis
from redis.exceptions import RedisError
from cachetools import TTL<PERSON>ache

logger = logging.getLogger(__name__)


class CacheService:
    """統一緩存服務 - Python 3.13兼容的純異步架構"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis: Optional[aioredis.Redis] = None
        self.local_cache = TTLCache(
            maxsize=config.get("local_cache_size", 1000),
            ttl=config.get("local_cache_ttl", 300),
        )
        self._connection_pool: Optional[aioredis.ConnectionPool] = None

    async def init_redis(self):
        """初始化Redis異步連接池 - 使用redis.asyncio"""
        try:
            # 創建連接池以優化性能
            self._connection_pool = aioredis.ConnectionPool.from_url(
                self.config["redis_url"],
                encoding="utf-8",
                decode_responses=True,
                max_connections=20,  # 連接池大小
                retry_on_timeout=True,
                health_check_interval=30,
            )

            # 創建Redis客戶端實例
            self.redis = aioredis.Redis(connection_pool=self._connection_pool)

            # 測試連接可用性
            await self.redis.ping()
            logger.info("Redis異步連接池初始化成功 (redis.asyncio)")

        except (RedisError, Exception) as e:
            logger.error(f"Redis連接初始化失敗: {str(e)}")
            self.redis = None

    async def close(self):
        """關閉Redis連接池"""
        if self.redis:
            await self.redis.close()
        if self._connection_pool:
            await self._connection_pool.disconnect()

    async def get(self, key: str) -> Optional[Any]:
        """獲取緩存 - 純異步實現"""
        try:
            # 首先檢查本地緩存（最快）
            if key in self.local_cache:
                return self.local_cache[key]

            # 從Redis異步獲取
            if self.redis:
                value = await self.redis.get(key)
                if value:
                    try:
                        data = json.loads(value)
                        # 同步更新本地緩存
                        self.local_cache[key] = data
                        return data
                    except json.JSONDecodeError:
                        logger.warning(f"緩存數據解析失敗: {key}")
                        # 清理損壞的數據
                        await self.redis.delete(key)
            return None

        except Exception as e:
            logger.error(f"獲取緩存失敗: {key}, {str(e)}")
            return None

    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """設置緩存 - 純異步實現"""
        try:
            # 立即更新本地緩存
            self.local_cache[key] = value

            # 異步更新Redis
            if self.redis:
                expire = expire or self.config.get("default_expire", 86400)
                serialized_value = json.dumps(value, ensure_ascii=False)
                await self.redis.setex(key, expire, serialized_value)

            return True

        except Exception as e:
            logger.error(f"設置緩存失敗: {key}, {str(e)}")
            return False

    async def delete(self, key: str) -> bool:
        """刪除緩存 - 純異步實現"""
        try:
            # 刪除本地緩存
            if key in self.local_cache:
                del self.local_cache[key]

            # 異步刪除Redis緩存
            if self.redis:
                result = await self.redis.delete(key)
                return bool(result)

            return True

        except Exception as e:
            logger.error(f"刪除緩存失敗: {key}, {str(e)}")
            return False

    async def exists(self, key: str) -> bool:
        """檢查緩存是否存在"""
        try:
            # 先檢查本地緩存
            if key in self.local_cache:
                return True

            # 檢查Redis
            if self.redis:
                return bool(await self.redis.exists(key))

            return False

        except Exception as e:
            logger.error(f"檢查緩存存在性失敗: {key}, {str(e)}")
            return False

    async def expire(self, key: str, expire: int) -> bool:
        """設置緩存過期時間"""
        try:
            if self.redis:
                result = await self.redis.expire(key, expire)
                return bool(result)
            return False

        except Exception as e:
            logger.error(f"設置緩存過期時間失敗: {key}, {str(e)}")
            return False

    async def get_stats(self) -> Dict[str, Any]:
        """獲取緩存統計信息 - 純異步實現"""
        stats = {
            "local_cache": {
                "size": len(self.local_cache),
                "maxsize": self.local_cache.maxsize,
                "ttl": self.local_cache.ttl,
                "currsize": (
                    self.local_cache.currsize
                    if hasattr(self.local_cache, "currsize")
                    else 0
                ),
            }
        }

        if self.redis:
            try:
                # 獲取Redis統計信息
                info_memory = await self.redis.info("memory")
                info_stats = await self.redis.info("stats")
                dbsize = await self.redis.dbsize()

                stats["redis"] = {
                    "keys": dbsize,
                    "memory_used": info_memory.get("used_memory_human", "N/A"),
                    "memory_peak": info_memory.get("used_memory_peak_human", "N/A"),
                    "total_commands_processed": info_stats.get(
                        "total_commands_processed", 0
                    ),
                    "connected_clients": info_stats.get("connected_clients", 0),
                }

                # 連接池統計
                if self._connection_pool:
                    stats["connection_pool"] = {
                        "max_connections": self._connection_pool.max_connections,
                        "connection_kwargs": str(
                            self._connection_pool.connection_kwargs
                        ),
                    }

            except RedisError as e:
                logger.error(f"獲取Redis統計信息失敗: {str(e)}")
                stats["redis"] = {"error": str(e)}

        return stats

    async def clear_local_cache(self):
        """清空本地緩存"""
        self.local_cache.clear()
        logger.info("本地緩存已清空")

    async def bulk_delete(self, pattern: str) -> int:
        """批量刪除匹配模式的緩存"""
        try:
            if not self.redis:
                return 0

            # 使用SCAN命令避免阻塞
            deleted_count = 0
            async for key in self.redis.scan_iter(match=pattern):
                await self.redis.delete(key)
                # 同時清理本地緩存
                if key in self.local_cache:
                    del self.local_cache[key]
                deleted_count += 1

            logger.info(f"批量刪除緩存完成，模式: {pattern}, 刪除數量: {deleted_count}")
            return deleted_count

        except Exception as e:
            logger.error(f"批量刪除緩存失敗: {pattern}, {str(e)}")
            return 0

    async def mget(self, *keys: str) -> Dict[str, Any]:
        """批量獲取緩存"""
        try:
            result = {}
            missing_keys = []

            # 先從本地緩存獲取
            for key in keys:
                if key in self.local_cache:
                    result[key] = self.local_cache[key]
                else:
                    missing_keys.append(key)

            # 從Redis批量獲取缺失的鍵
            if self.redis and missing_keys:
                redis_values = await self.redis.mget(*missing_keys)
                for key, value in zip(missing_keys, redis_values):
                    if value:
                        try:
                            data = json.loads(value)
                            result[key] = data
                            # 更新本地緩存
                            self.local_cache[key] = data
                        except json.JSONDecodeError:
                            logger.warning(f"緩存數據解析失敗: {key}")
                    else:
                        result[key] = None

            return result

        except Exception as e:
            logger.error(f"批量獲取緩存失敗: {keys}, {str(e)}")
            return {key: None for key in keys}

    async def mset(self, mapping: Dict[str, Any], expire: Optional[int] = None) -> bool:
        """批量設置緩存"""
        try:
            # 更新本地緩存
            self.local_cache.update(mapping)

            # 批量更新Redis
            if self.redis:
                expire = expire or self.config.get("default_expire", 86400)
                serialized_mapping = {
                    key: json.dumps(value, ensure_ascii=False)
                    for key, value in mapping.items()
                }

                # 使用pipeline提高效率
                async with self.redis.pipeline() as pipe:
                    for key, value in serialized_mapping.items():
                        pipe.setex(key, expire, value)
                    await pipe.execute()

            return True

        except Exception as e:
            logger.error(f"批量設置緩存失敗: {mapping.keys()}, {str(e)}")
            return False
