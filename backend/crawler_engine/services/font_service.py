import logging
from typing import Dict, Optional, Any
from io import BytesIO
from fontTools.ttLib import TTFont
from cachetools import T<PERSON><PERSON><PERSON>
import requests

logger = logging.getLogger(__name__)


class FontService:
    """字體解析服務"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.cache = TTLCache(
            maxsize=config.get("font_cache_size", 100),
            ttl=config.get("font_cache_ttl", 3600),
        )

    async def parse_font(self, font_url: str) -> Optional[Dict[int, str]]:
        """解析字體文件"""
        try:
            # 檢查緩存
            if font_url in self.cache:
                return self.cache[font_url]

            # 下載字體文件
            response = requests.get(font_url)
            if response.status_code != 200:
                logger.error(f"下載字體文件失敗: {font_url}, {response.status_code}")
                return None

            # 解析字體
            font = TTFont(BytesIO(response.content))
            cmap = font.getBestCmap()

            # 建立映射關係
            number_map = {
                "zero": "0",
                "one": "1",
                "two": "2",
                "three": "3",
                "four": "4",
                "five": "5",
                "six": "6",
                "seven": "7",
                "eight": "8",
                "nine": "9",
                "period": ".",
            }

            font_map = {}
            for k, v in cmap.items():
                if v in number_map:
                    font_map[k] = number_map[v]

            # 更新緩存
            self.cache[font_url] = font_map
            return font_map

        except Exception as e:
            logger.error(f"解析字體文件失敗: {font_url}, {str(e)}")
            return None

    async def decode_text(self, text: str, font_url: str) -> str:
        """解碼加密文本"""
        font_map = await self.parse_font(font_url)
        if not font_map:
            return text

        try:
            for k, v in font_map.items():
                text = text.replace(f"&#{k};", v)
            return text
        except Exception as e:
            logger.error(f"解碼文本失敗: {str(e)}")
            return text

    async def get_cache_stats(self) -> Dict[str, Any]:
        """獲取緩存統計信息"""
        return {
            "size": len(self.cache),
            "hits": self.cache.hits,
            "misses": self.cache.misses,
        }
