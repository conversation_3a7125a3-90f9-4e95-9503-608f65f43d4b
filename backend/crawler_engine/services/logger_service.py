import logging
import sys
from typing import Dict, Any
from logging.handlers import RotatingFileHandler, HTTPHandler
import gzip
import os
from datetime import datetime


class LoggerService:
    """統一日誌服務"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("novel_crawler")
        self.logger.setLevel(getattr(logging, config["level"]))

        # 清除現有的處理器
        self.logger.handlers.clear()

        # 添加控制台處理器
        if config.get("console_output", True):
            self._add_console_handler()

        # 添加文件處理器
        if config.get("filename"):
            self._add_file_handler()

        # 添加遠程日誌處理器
        if config.get("remote_url"):
            self._add_remote_handler()

    def _add_console_handler(self):
        """添加控制台日誌處理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter(self.config["format"]))
        self.logger.addHandler(console_handler)

    def _add_file_handler(self):
        """添加文件日誌處理器"""
        filename = self.config["filename"]
        max_size = self.config.get("max_size", 10 * 1024 * 1024)  # 10MB
        backup_count = self.config.get("backup_count", 5)

        file_handler = RotatingFileHandler(
            filename, maxBytes=max_size, backupCount=backup_count, encoding="utf-8"
        )
        file_handler.setFormatter(logging.Formatter(self.config["format"]))
        self.logger.addHandler(file_handler)

        # 壓縮舊的日誌文件
        self._compress_old_logs(filename, backup_count)

    def _add_remote_handler(self):
        """添加遠程日誌處理器"""
        remote_handler = HTTPHandler(
            self.config["remote_url"], method="POST", secure=True
        )
        self.logger.addHandler(remote_handler)

    def _compress_old_logs(self, base_filename: str, backup_count: int):
        """壓縮舊的日誌文件"""
        for i in range(1, backup_count + 1):
            log_file = f"{base_filename}.{i}"
            if os.path.exists(log_file):
                compressed_file = f"{log_file}.gz"
                if not os.path.exists(compressed_file):
                    try:
                        with open(log_file, "rb") as f_in:
                            with gzip.open(compressed_file, "wb") as f_out:
                                f_out.writelines(f_in)
                        os.remove(log_file)
                    except Exception as e:
                        self.logger.error(f"壓縮日誌文件失敗: {log_file}, {str(e)}")

    def get_logger(self):
        """獲取日誌記錄器"""
        return self.logger

    def log_context(self, context: Dict[str, Any]):
        """記錄上下文信息"""
        extra = {"timestamp": datetime.now().isoformat(), "context": context}
        self.logger.info("Context info", extra=extra)
