from datetime import datetime

BOT_NAME = "novel_crawler"

SPIDER_MODULES = ["crawler_engine.spiders"]
NEWSPIDER_MODULE = "crawler_engine.spiders"

# 爬蟲設置
ROBOTSTXT_OBEY = False
CONCURRENT_REQUESTS = 1
DOWNLOAD_DELAY = 1
COOKIES_ENABLED = True  # 啟用cookies以處理cloudflare

# Playwright integration - 暫時禁用
# TWISTED_REACTOR = "twisted.internet.asyncioreactor.AsyncioSelectorReactor"
# DOWNLOAD_HANDLERS = {
#     "http": "scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler",
#     "https": "scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler",
# }
# PLAYWRIGHT_BROWSER_TYPE = "chromium"
# PLAYWRIGHT_LAUNCH_OPTIONS = {"headless": True}

# 起點文學特定設置
QIDIAN_YUEPIAO_URL = "https://www.qidian.com/rank/yuepiao/"
QIDIAN_NOVEL_URL = "https://book.qidian.com/info/{}/"
QIDIAN_CHAPTER_URL = "https://read.qidian.com/chapter/{}/"

# --- 這是修復403錯誤的關鍵 ---
DEFAULT_REQUEST_HEADERS = {
    "Accept": (
        "text/html,application/xhtml+xml,application/xml;q=0.9,"
        "image/avif,image/webp,image/apng,*/*;q=0.8,"
        "application/signed-exchange;v=b3;q=0.7"
    ),
    "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
    "Cache-Control": "max-age=0",
    "User-Agent": (
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/********* Safari/537.36"
    ),
    # Referer通常很重要，但讓我們先不設置
    # Scrapy通常會根據前一個請求智能設置它
}
# --- 修復結束 ---

# Item Pipeline
ITEM_PIPELINES = {
    # HACK: 臨時直寫數據庫Pipeline，跳過Redis隊列以快速驗證
    "crawler_engine.pipelines.DjangoPipeline": 100,
    # 透過 RedisQueuePipeline 推送至工作隊列，由 worker 異步寫入資料庫
    # "crawler_engine.pipelines.RedisQueuePipeline": 100,
}

EXTENSIONS = {
    # "crawler_engine.monitoring.PrometheusStatsExtension": 500,  # 暫時禁用
}

# 中間件和管道設置
DOWNLOADER_MIDDLEWARES = {
    # "scrapy.downloadermiddlewares.useragent.UserAgentMiddleware": None,
    # "scrapy.downloadermiddlewares.retry.RetryMiddleware": None,
    # "scrapy.downloadermiddlewares.redirect.RedirectMiddleware": None,
    # "crawler_engine.middlewares.RandomUserAgentMiddleware": 400,
    # "crawler_engine.middlewares.ProxyMiddleware": 410,
    # "scrapy_playwright.middleware.PlaywrightMiddleware": 543,
}

# 緩存設置
HTTPCACHE_ENABLED = True
HTTPCACHE_EXPIRATION_SECS = 60 * 60  # 1小時
HTTPCACHE_DIR = "httpcache"
HTTPCACHE_IGNORE_HTTP_CODES = [404, 500, 503]
HTTPCACHE_STORAGE = "scrapy.extensions.httpcache.FilesystemCacheStorage"

# 日誌設置
LOG_ENABLED = True
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s [%(name)s] %(levelname)s: %(message)s"
LOG_FILE = f'logs/crawler_{datetime.now().strftime("%Y%m%d")}.log'

# 重試設置
RETRY_ENABLED = True
RETRY_TIMES = 3
RETRY_HTTP_CODES = [500, 502, 503, 504, 522, 524, 408, 429]

# 下載超時
DOWNLOAD_TIMEOUT = 10

# Prometheus exporter configuration
PROMETHEUS_PORT = 9000

# Rotating user agents and proxies
USER_AGENT_LIST = []
PROXY_LIST = []

# 自定義設置
NOVEL_CATEGORY_MAP = {
    "玄幻": "xuanhuan",
    "奇幻": "qihuan",
    "武俠": "wuxia",
    "仙俠": "xianxia",
    "都市": "dushi",
    "現實": "xianshi",
    "軍事": "junshi",
    "歷史": "lishi",
    "游戲": "youxi",
    "體育": "tiyu",
    "科幻": "kehuan",
    "懸疑": "xuanyi",
}

# 請求隊列設置
SCHEDULER_QUEUE_KEY = "%(spider)s:requests"
SCHEDULER_DUPEFILTER_KEY = "%(spider)s:dupefilter"
