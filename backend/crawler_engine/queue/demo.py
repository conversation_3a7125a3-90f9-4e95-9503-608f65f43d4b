#!/usr/bin/env python3
"""
Redis Queue系統演示腳本

演示基本用法和高級功能，包括：
1. 基本的發布/消費操作
2. 優先級任務處理
3. 錯誤處理和重試
4. 隊列狀態監控
"""

import asyncio
import json
import logging

from crawler_engine.queue.redis_queue import (
    RedisBridge,
    RedisQueueError,
)

# 設置日誌
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def demo_basic_usage():
    """演示基本用法"""
    print("\n=== 基本用法演示 ===")

    async with RedisBridge(queue_name="demo_basic_queue") as bridge:
        # 發布任務
        task1 = {
            "type": "crawl_novel",
            "url": "https://example.com/novel/1",
            "title": "演示小說1",
        }
        task2 = {
            "type": "crawl_chapter",
            "url": "https://example.com/chapter/1",
            "novel_id": 1,
        }

        print(f"發布任務1: {task1}")
        await bridge.publish_task(task1)

        print(f"發布任務2: {task2}")
        await bridge.publish_task(task2)

        # 檢查隊列狀態
        status = await bridge.get_queue_status()
        print(f"隊列狀態: {json.dumps(status, indent=2, ensure_ascii=False)}")

        # 消費任務
        print("\n開始消費任務:")
        while True:
            task = await bridge.consume_task(timeout=2)
            if task is None:
                print("隊列已空，停止消費")
                break
            print(f"消費到任務: {task}")


async def demo_priority_tasks():
    """演示優先級任務"""
    print("\n=== 優先級任務演示 ===")

    async with RedisBridge(queue_name="demo_priority_queue") as bridge:
        # 清空隊列
        await bridge.clear_queue()

        # 發布多個任務，包括普通和高優先級
        tasks = [
            {"id": 1, "type": "normal", "message": "普通任務1"},
            {"id": 2, "type": "normal", "message": "普通任務2"},
            {"id": 3, "type": "urgent", "message": "緊急任務！"},  # 高優先級
            {"id": 4, "type": "normal", "message": "普通任務3"},
        ]

        for task in tasks:
            is_priority = task.get("type") == "urgent"
            print(f"發布任務 (優先級={is_priority}): {task}")
            await bridge.publish_task(task, priority=is_priority)

        # 消費任務，觀察優先級效果
        print("\n消費順序:")
        for i in range(len(tasks)):
            task = await bridge.consume_task(timeout=2)
            if task:
                print(f"第{i+1}個消費: {task}")


async def demo_error_handling():
    """演示錯誤處理"""
    print("\n=== 錯誤處理演示 ===")

    # 創建帶有短重試配置的Bridge
    bridge = RedisBridge(queue_name="demo_error_queue", max_retries=2, retry_delay=0.5)

    try:
        await bridge.clear_queue()

        # 正常操作
        normal_task = {"type": "normal", "status": "success"}
        await bridge.publish_task(normal_task)
        print("✓ 正常發布任務成功")

        consumed = await bridge.consume_task(timeout=1)
        print(f"✓ 正常消費任務: {consumed}")

        # 測試序列化錯誤
        try:
            invalid_task = {"function": lambda x: x}  # 不可序列化
            await bridge.publish_task(invalid_task)
        except RedisQueueError as e:
            print(f"✓ 捕獲序列化錯誤: {e}")

        print("錯誤處理演示完成")

    finally:
        await bridge.close()


async def main():
    """主函數 - 運行所有演示"""
    print("🚀 Redis Queue系統演示開始")
    print("=" * 50)

    try:
        # 運行各種演示
        await demo_basic_usage()
        await demo_priority_tasks()
        await demo_error_handling()

        print("\n" + "=" * 50)
        print("✅ 所有演示完成！")

    except Exception as e:
        print(f"\n❌ 演示過程中發生錯誤: {e}")
        logger.exception("演示錯誤詳情:")


if __name__ == "__main__":
    # 運行演示
    asyncio.run(main())
