"""
Redis Queue系統核心實現

提供以下功能：
1. RedisQueue - 基本的隊列發布/消費功能
2. TaskSerializer - 任務序列化/反序列化
3. RedisConnectionManager - 連接池管理
4. RetryHandler - 重試機制
5. RedisBridge - 統一的高層API
"""

import json
import pickle
import asyncio
import logging
from typing import Any, Dict, Optional, Callable
from contextlib import asynccontextmanager

from redis.asyncio import Redis, ConnectionPool
from redis.exceptions import RedisError

# 導入統一配置管理
from crawler_engine.config.settings import settings

# 設置日誌
logger = logging.getLogger(__name__)


class RedisQueueError(Exception):
    """Redis隊列操作異常"""

    pass


class TaskSerializer:
    """任務序列化/反序列化器

    提供多種序列化方式：JSON（預設）和Pickle（用於複雜對象）
    """

    @staticmethod
    def serialize_json(task: Dict[str, Any]) -> str:
        """JSON序列化（推薦用於簡單數據結構）"""
        try:
            return json.dumps(task, ensure_ascii=False)
        except (TypeError, ValueError) as e:
            raise RedisQueueError(f"JSON serialization failed: {str(e)}")

    @staticmethod
    def deserialize_json(serialized_task: str) -> Dict[str, Any]:
        """JSON反序列化"""
        try:
            return json.loads(serialized_task)
        except (TypeError, ValueError) as e:
            raise RedisQueueError(f"JSON deserialization failed: {str(e)}")

    @staticmethod
    def serialize_pickle(task: Any) -> bytes:
        """Pickle序列化（用於複雜對象）"""
        try:
            return pickle.dumps(task)
        except (TypeError, pickle.PicklingError) as e:
            raise RedisQueueError(f"Pickle serialization failed: {str(e)}")

    @staticmethod
    def deserialize_pickle(serialized_task: bytes) -> Any:
        """Pickle反序列化"""
        try:
            return pickle.loads(serialized_task)
        except (TypeError, pickle.UnpicklingError) as e:
            raise RedisQueueError(f"Pickle deserialization failed: {str(e)}")


class RedisConnectionManager:
    """Redis連接池管理器"""

    def __init__(self, redis_url: str, max_connections: int = 20):
        """
        初始化Redis連接管理器

        Args:
            redis_url: Redis連接URL
            max_connections: 最大連接數
        """
        self.redis_url = redis_url
        self.max_connections = max_connections
        self.pool: Optional[ConnectionPool] = None
        self._closed = False

    async def initialize(self):
        """初始化連接池"""
        if self.pool is None:
            self.pool = ConnectionPool.from_url(
                self.redis_url,
                max_connections=self.max_connections,
                decode_responses=True,
            )
            msg = (
                f"Redis connection pool initialized with "
                f"{self.max_connections} max connections"
            )
            logger.info(msg)

    async def get_connection(self) -> Redis:
        """獲取Redis連接"""
        if self.pool is None:
            await self.initialize()
        return Redis(connection_pool=self.pool)

    async def close(self):
        """關閉連接池"""
        if self.pool and not self._closed:
            await self.pool.disconnect()
            self._closed = True
            logger.info("Redis connection pool closed")

    @asynccontextmanager
    async def connection(self):
        """上下文管理器，自動管理連接"""
        redis_conn = await self.get_connection()
        try:
            yield redis_conn
        finally:
            await redis_conn.close()


class RetryHandler:
    """重試機制處理器"""

    def __init__(
        self, max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0
    ):
        """
        初始化重試處理器

        Args:
            max_retries: 最大重試次數
            delay: 初始延遲時間（秒）
            backoff_factor: 指數退避因子
        """
        self.max_retries = max_retries
        self.delay = delay
        self.backoff_factor = backoff_factor

    async def retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        執行帶重試的函數

        Args:
            func: 要執行的異步函數
            *args: 函數參數
            **kwargs: 函數關鍵字參數

        Returns:
            函數執行結果

        Raises:
            RedisQueueError: 重試失敗後拋出
        """
        last_exception = None
        current_delay = self.delay

        for attempt in range(self.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except RedisError as e:
                last_exception = e
                if attempt == self.max_retries:
                    logger.error(f"Final retry attempt {attempt + 1} failed: {str(e)}")
                    break

                msg = (
                    f"Retry attempt {attempt + 1}/{self.max_retries + 1} "
                    f"failed: {str(e)}, retrying in {current_delay}s"
                )
                logger.warning(msg)
                await asyncio.sleep(current_delay)
                current_delay *= self.backoff_factor

        raise RedisQueueError(
            f"Operation failed after {self.max_retries + 1} attempts: "
            f"{str(last_exception)}"
        )


class RedisQueue:
    """Redis隊列核心實現"""

    def __init__(self, connection_manager: RedisConnectionManager, queue_name: str):
        """
        初始化Redis隊列

        Args:
            connection_manager: Redis連接管理器
            queue_name: 隊列名稱
        """
        self.connection_manager = connection_manager
        self.queue_name = queue_name
        logger.info(f"RedisQueue initialized for queue: {queue_name}")

    async def publish(self, task: str, priority: bool = False) -> int:
        """
        發布任務到隊列

        Args:
            task: 序列化後的任務數據
            priority: 是否為高優先級任務（插入隊列頭部）

        Returns:
            隊列長度
        """
        async with self.connection_manager.connection() as redis_conn:
            if priority:
                # 高優先級任務插入隊列頭部
                length = await redis_conn.lpush(self.queue_name, task)
            else:
                # 正常任務插入隊列尾部
                length = await redis_conn.rpush(self.queue_name, task)

            logger.debug(f"Published task to {self.queue_name}, queue length: {length}")
            return length

    async def consume(self, timeout: int = 0) -> Optional[str]:
        """
        從隊列消費任務

        Args:
            timeout: 阻塞超時時間（秒），0表示永不超時

        Returns:
            序列化的任務數據，如果超時則返回None
        """
        async with self.connection_manager.connection() as redis_conn:
            result = await redis_conn.blpop(self.queue_name, timeout=timeout)
            if result:
                _, task = result
                logger.debug(f"Consumed task from {self.queue_name}")
                return task
            return None

    async def get_length(self) -> int:
        """獲取隊列長度"""
        async with self.connection_manager.connection() as redis_conn:
            return await redis_conn.llen(self.queue_name)

    async def clear(self) -> bool:
        """清空隊列"""
        async with self.connection_manager.connection() as redis_conn:
            result = await redis_conn.delete(self.queue_name)
            logger.info(f"Cleared queue {self.queue_name}")
            return result > 0


class RedisBridge:
    """Redis Bridge - 統一的高層API

    整合了連接管理、序列化、重試等功能，提供簡潔的使用介面
    """

    def __init__(
        self,
        redis_url: Optional[str] = None,
        queue_name: Optional[str] = None,
        max_connections: int = 20,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        初始化RedisBridge

        Args:
            redis_url: Redis連接URL，默認使用配置
            queue_name: 隊列名稱，默認使用配置
            max_connections: 最大連接數
            max_retries: 最大重試次數
            retry_delay: 重試延遲時間
        """
        self.redis_url = redis_url or settings.redis_url
        self.queue_name = queue_name or settings.redis_item_queue

        # 初始化組件
        self.connection_manager = RedisConnectionManager(
            self.redis_url, max_connections
        )
        self.queue = RedisQueue(self.connection_manager, self.queue_name)
        self.retry_handler = RetryHandler(max_retries, retry_delay)

        logger.info(
            f"RedisBridge initialized - URL: {self.redis_url}, Queue: {self.queue_name}"
        )

    async def publish_task(
        self, task: Dict[str, Any], priority: bool = False, use_pickle: bool = False
    ) -> int:
        """
        發布任務

        Args:
            task: 任務數據
            priority: 是否為高優先級
            use_pickle: 是否使用Pickle序列化

        Returns:
            隊列長度
        """
        try:
            # 序列化任務
            if use_pickle:
                serialized_task = TaskSerializer.serialize_pickle(task)
            else:
                serialized_task = TaskSerializer.serialize_json(task)

            # 發布任務（帶重試）
            return await self.retry_handler.retry(
                self.queue.publish, serialized_task, priority
            )

        except Exception as e:
            logger.error(f"Failed to publish task: {str(e)}")
            raise RedisQueueError(f"Publish failed: {str(e)}")

    async def consume_task(
        self, timeout: int = 0, use_pickle: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        消費任務

        Args:
            timeout: 超時時間
            use_pickle: 是否使用Pickle反序列化

        Returns:
            任務數據
        """
        try:
            # 消費任務（帶重試）
            serialized_task = await self.retry_handler.retry(
                self.queue.consume, timeout
            )

            if serialized_task is None:
                return None

            # 反序列化任務
            if use_pickle:
                return TaskSerializer.deserialize_pickle(serialized_task)
            else:
                return TaskSerializer.deserialize_json(serialized_task)

        except Exception as e:
            logger.error(f"Failed to consume task: {str(e)}")
            raise RedisQueueError(f"Consume failed: {str(e)}")

    async def get_queue_status(self) -> Dict[str, Any]:
        """獲取隊列狀態資訊"""
        try:
            length = await self.retry_handler.retry(self.queue.get_length)
            return {
                "queue_name": self.queue_name,
                "length": length,
                "redis_url": (
                    self.redis_url.split("@")[-1]
                    if "@" in self.redis_url
                    else self.redis_url
                ),  # 隱藏密碼
                "status": "healthy",
            }
        except Exception as e:
            logger.error(f"Failed to get queue status: {str(e)}")
            return {
                "queue_name": self.queue_name,
                "length": -1,
                "redis_url": "connection_failed",
                "status": "error",
                "error": str(e),
            }

    async def clear_queue(self) -> bool:
        """清空隊列"""
        try:
            return await self.retry_handler.retry(self.queue.clear)
        except Exception as e:
            logger.error(f"Failed to clear queue: {str(e)}")
            raise RedisQueueError(f"Clear queue failed: {str(e)}")

    async def close(self):
        """關閉連接"""
        await self.connection_manager.close()
        logger.info("RedisBridge closed")

    async def __aenter__(self):
        """異步上下文管理器進入"""
        await self.connection_manager.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器退出"""
        await self.close()


# 工廠函數，提供便捷的實例創建
def create_redis_bridge(queue_name: Optional[str] = None, **kwargs) -> RedisBridge:
    """
    創建RedisBridge實例的工廠函數

    Args:
        queue_name: 隊列名稱
        **kwargs: 其他RedisBridge參數

    Returns:
        RedisBridge實例
    """
    return RedisBridge(queue_name=queue_name, **kwargs)


# 預設實例（可直接使用）
default_bridge = RedisBridge()
