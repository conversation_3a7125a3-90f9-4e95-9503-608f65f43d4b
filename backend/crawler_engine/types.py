from typing import List, TypedDict


class ErrorConfig:
    def __init__(self, retryable_errors: List[str], max_retries: int):
        self.retryable_errors = retryable_errors
        self.max_retries = max_retries


class DbConfig(TypedDict):
    host: str
    port: int
    dbname: str
    user: str
    password: str


class RedisConfig(TypedDict):
    host: str
    port: int
    db: int
    decode_responses: bool
