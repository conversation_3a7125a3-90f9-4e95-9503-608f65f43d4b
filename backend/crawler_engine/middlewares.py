import random
from typing import List
from scrapy import Request


class RandomUserAgentMiddleware:
    def __init__(self, user_agents: List[str]):
        self.user_agents = user_agents

    @classmethod
    def from_crawler(cls, crawler):
        return cls(crawler.settings.getlist("USER_AGENT_LIST"))

    def process_request(self, request: Request, spider):
        if self.user_agents:
            request.headers["User-Agent"] = random.choice(self.user_agents)


class ProxyMiddleware:
    def __init__(self, proxies: List[str]):
        self.proxies = proxies

    @classmethod
    def from_crawler(cls, crawler):
        return cls(crawler.settings.getlist("PROXY_LIST"))

    def process_request(self, request: Request, spider):
        if self.proxies:
            request.meta["proxy"] = random.choice(self.proxies)
