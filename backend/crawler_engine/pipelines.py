from itemadapter import ItemAdapter
from scrapy.exceptions import DropItem
import logging
import json
import redis

# from novel.crawler.models import Category, Chapter, Novel, Tag  # 暫時禁用
from .config.settings import settings
from .monitoring.prometheus_exporter import (
    queue_length_gauge,
    spider_error_counter,
    spider_items_counter,
)

# HACK: 配置Django環境以支持直寫數據庫
import os
import django
from django.conf import settings as django_settings

if not django_settings.configured:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.django_settings")
    django.setup()

logger = logging.getLogger(__name__)


class DjangoPipeline:
    """HACK: 臨時直寫數據庫Pipeline - 跳過Redis隊列以快速驗證"""

    def open_spider(self, spider):
        """This method is called when the spider is opened."""
        try:
            from apps.catalog.models import Novel  # noqa: F401

            logger.info(
                "✅ SUCCESS: DjangoPipeline can successfully import "
                "'catalog.models.Novel'."
            )
            # 避免在異步上下文中直接調用ORM
            logger.info("🔄 DB check: Skipping count check in async context")
        except ImportError:
            logger.error(
                "❌ FAILURE: DjangoPipeline CANNOT import "
                "'catalog.models.Novel'. Check INSTALLED_APPS and sys.path."
            )
        except Exception as e:
            logger.error(
                "❌ FAILURE: An unexpected error occurred during Django "
                f"setup in pipeline: {e}"
            )

    def process_item(self, item, spider):
        """處理爬取到的item，直接寫入Django數據庫"""
        # 使用執行緒來避免async context問題
        import threading
        import queue

        result_queue = queue.Queue()

        def db_operation():
            try:
                # 只處理novel類型的item
                if item.get("type") != "novel":
                    result_queue.put(("success", item))
                    return

                logger.info(f"正在處理小說: {item.get('title', 'Unknown')}")

                # 創建或更新小說記錄
                from apps.catalog.models import Novel  # 導入Django模型

                # 從source_url提取唯一ID (如 https://tw.hjwzw.com/Book/35120 -> 35120)
                import re
                from scrapy.exceptions import DropItem

                url_match = re.search(r"/Book/(\d+)", item["source_url"])
                if url_match:
                    source_id = url_match.group(1)
                else:
                    # CRITICAL: Never use title as fallback for unique constraint
                    error_msg = (
                        f"❌ CRITICAL: Could not extract source_id from URL: "
                        f"{item['source_url']}. Dropping item to prevent "
                        f"data corruption."
                    )
                    logger.error(error_msg)
                    raise DropItem(error_msg)

                novel, created = Novel.objects.update_or_create(
                    source=item["source_site"],  # 網站來源
                    source_id=source_id,  # 從URL提取的唯一ID
                    defaults={
                        "title": item["title"],
                        "author": item["author"],
                        "description": item["description"],
                        "status": item["status"],
                        "source_url": item["source_url"],
                        # 注意：Django模型可能沒有total_chapters字段
                        # "total_chapters": item.get("total_chapters", 0),
                        # HACK: 簡化字段處理 - 暫時跳過categories和tags
                        # "categories": item.get("categories", []),
                        # "tags": item.get("tags", []),
                    },
                )

                action = "創建" if created else "更新"
                logger.info(f"✅ 成功{action}小說: {novel.title} (ID: {novel.id})")

                result_queue.put(("success", item))

            except Exception as e:
                logger.error(f"❌ 處理item失敗: {e}")
                result_queue.put(("error", item))

        # 在新線程中執行數據庫操作
        thread = threading.Thread(target=db_operation)
        thread.start()
        thread.join()  # 等待完成

        # 獲取結果
        status, result_item = result_queue.get()
        return result_item


class NovelPipeline:
    """暫時禁用的舊Pipeline - 需要修復imports"""

    def process_item(self, item, spider):
        # TODO: 修復imports並重新啟用
        logger.warning("NovelPipeline暫時禁用")
        return item


class ChapterPipeline:
    """暫時禁用的舊Pipeline - 需要修復imports"""

    def process_item(self, item, spider):
        # TODO: 修復imports並重新啟用
        logger.warning("ChapterPipeline暫時禁用")
        return item

    def _remove_ads(self, content):
        """移除常見的廣告文本"""
        ad_patterns = [
            "推薦下載APP閱讀",
            "本章未完，請點擊下一頁繼續閱讀",
            "訪問網址繼續閱讀",
            "最新章節請到網址",
        ]

        cleaned_content = content
        for pattern in ad_patterns:
            cleaned_content = cleaned_content.replace(pattern, "")

        return cleaned_content.strip()


class RedisQueuePipeline:
    """Pipeline 將 item 推送至 Redis 佇列供後續異步處理"""

    def __init__(self):
        self.queue_name = settings.redis_config.get("item_queue", "crawler:item_queue")
        self.client = redis.from_url(settings.get_redis_url(), decode_responses=True)

    def process_item(self, item, spider):
        data = ItemAdapter(item).asdict()
        try:
            serialized_data = json.dumps(data, ensure_ascii=False)
            self.client.rpush(self.queue_name, serialized_data)
            spider_items_counter.inc()
            queue_length = self.client.llen(self.queue_name)
            queue_length_gauge.set(queue_length)
        except redis.exceptions.RedisError as e:
            logger.error(
                "RedisError: Could not push item to queue '%s'. " "Error: %s. Item: %s",
                self.queue_name,
                e,
                data,
            )
            spider_error_counter.inc()
            raise DropItem(f"Failed to queue item due to Redis error: {e}")
        except json.JSONDecodeError as e:
            logger.error(
                "JSONError: Could not serialize item for Redis queue. "
                "Error: %s. Item: %s",
                e,
                item,
            )
            spider_error_counter.inc()
            raise DropItem(f"Failed to serialize item for Redis queue: {e}")
        return item
