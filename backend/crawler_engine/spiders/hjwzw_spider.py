"""
黃金屋中文網 (hjwzw.com) Scrapy Spider - CLEAN VERSION
應用所有czbooks經驗教訓的純淨爬蟲實現
"""

import scrapy
import logging
import re

logger = logging.getLogger(__name__)


class HjwzwSpider(scrapy.Spider):
    """黃金屋中文網專用爬蟲 - 乾淨版本"""

    name = "hjwzw"
    allowed_domains = ["tw.hjwzw.com"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 直接指向多個分類頁面，確保豐富的小說來源
        self.start_urls = [
            "https://tw.hjwzw.com/List/玄幻",  # 玄幻小說
            "https://tw.hjwzw.com/List/都市",  # 都市小說
            "https://tw.hjwzw.com/List/歷史",  # 歷史小說
            "https://tw.hjwzw.com/List/言情",  # 言情小說
            "https://tw.hjwzw.com/List/武俠",  # 武俠小說
        ]

    def parse(self, response):
        """解析分類頁面，提取小說連結 - CLEAN VERSION"""
        logger.info(f"正在解析分類頁面: {response.url}")

        # 提取所有小說詳情頁連結 (/Book/ID格式)
        novel_links = response.css('a[href*="/Book/"]::attr(href)').getall()

        # 去重並過濾，只保留純數字ID的書籍連結
        unique_books = set()
        for link in novel_links:
            # 匹配 /Book/數字 格式
            book_match = re.match(r"/Book/(\d+)$", link)
            if book_match:
                unique_books.add(link)

        logger.info(f"找到 {len(unique_books)} 個唯一小說連結")

        # 限制爬取數量以快速驗證 (每個分類最多20本)
        max_novels = 20

        for i, link in enumerate(list(unique_books)[:max_novels]):
            novel_url = response.urljoin(link)
            logger.info(f"準備爬取小說 {i+1}: {novel_url}")

            # 使用Scrapy的異步請求 - 吸取czbooks教訓
            yield scrapy.Request(
                url=novel_url,
                callback=self.parse_novel_detail,
                meta={"novel_url": novel_url},
                dont_filter=True,
            )

    def parse_novel_detail(self, response):
        """解析小說詳情頁面 - CLEAN VERSION"""
        novel_url = response.meta.get("novel_url", response.url)
        logger.info(f"正在解析小說詳情: {novel_url}")

        try:
            # 優先使用meta標籤 - 更可靠的結構化數據
            title = self._extract_meta_content(response, "og:novel:book_name")
            author = self._extract_meta_content(response, "og:novel:author")
            category = self._extract_meta_content(response, "og:novel:category")
            status = self._extract_meta_content(response, "og:novel:status")
            description = self._extract_meta_content(response, "og:description")

            # 從title標籤備用提取
            if not title:
                title_text = response.css("title::text").get()
                if title_text:
                    # 格式: "書名/作者/書名txt下載-黃金屋中文"
                    title_parts = title_text.split("/")
                    if len(title_parts) >= 2:
                        title = title_parts[0].strip()
                        if not author:
                            author = title_parts[1].strip()

            # 數據清洗和默認值
            title = title or "未知書名"
            author = author or "未知作者"
            description = description or "暫無簡介"
            status = status or "未知狀態"
            category = category or "未分類"

            # 提取章節數 (可選，暫時跳過以求速度)
            total_chapters = 0  # TODO: 從章節列表頁提取

            # 構建數據項
            novel_data = {
                "type": "novel",
                "title": title,
                "author": author,
                "description": description,
                "status": status,
                "categories": [category],
                "tags": [],
                "total_chapters": total_chapters,
                "source_url": novel_url,
                "source_site": "黃金屋中文",
            }

            logger.info(f"成功提取小說: {title} by {author}")
            yield novel_data

        except Exception as e:
            logger.error(f"解析小說詳情失敗 {novel_url}: {e}")

    def _extract_meta_content(self, response, property_name):
        """提取meta標籤內容的輔助方法"""
        return response.css(f'meta[property="{property_name}"]::attr(content)').get()
