"""Prometheus metrics exporter for crawler components."""

from prometheus_client import Counter, Gauge, start_http_server

# Metrics definitions
queue_length_gauge = Gauge(
    "crawler_queue_length",
    "Number of items currently in the Redis queue",
)
worker_processed_counter = Counter(
    "worker_processed_total", "Total items processed by worker"
)
worker_error_counter = Counter(
    "worker_error_total", "Total errors encountered by worker"
)
spider_items_counter = Counter("spider_items_total", "Total items scraped by spiders")
spider_error_counter = Counter("spider_error_total", "Total spider errors")


def start_metrics_server(port: int = 8001) -> None:
    """Start Prometheus metrics HTTP server."""
    start_http_server(port)
