import logging
from typing import Dict, List
from datetime import datetime
from ..utils.pool_manager import PoolManager

logger = logging.getLogger(__name__)


class NovelDBWriter:
    """小說數據庫操作類"""

    def __init__(self):
        self.pool_manager = None

    async def init(self):
        """初始化"""
        self.pool_manager = await PoolManager.get_instance()
        await self.ensure_tables()

    async def ensure_tables(self):
        """確保必要的表存在"""
        async with self.pool_manager.db_pool.acquire() as conn:
            try:
                await conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS novels (
                        id SERIAL PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        author <PERSON><PERSON><PERSON><PERSON>(255),
                        description TEXT,
                        source VARCHAR(50),
                        source_url VARCHAR(255) UNIQUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                await conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS chapters (
                        id SERIAL PRIMARY KEY,
                        novel_id VARCHAR(255) REFERENCES novels(source_url),
                        chapter_number INTEGER,
                        chapter_title VARCHAR(255),
                        chapter_content TEXT,
                        chapter_url VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(novel_id, chapter_number)
                    )
                """
                )
                logger.info("數據表檢查/創建完成")
            except Exception:
                logger.exception("創建數據表失敗")
                raise

    async def save_novel(self, novel_info: Dict) -> str:
        """保存小說信息"""
        async with self.pool_manager.db_pool.acquire() as conn:
            try:
                result = await conn.fetchrow(
                    """
                    INSERT INTO novels (title, author, description, source, source_url)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (source_url)
                    DO UPDATE SET
                        title = EXCLUDED.title,
                        author = EXCLUDED.author,
                        description = EXCLUDED.description,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING source_url
                """,
                    novel_info["title"],
                    novel_info["author"],
                    novel_info.get("description", ""),
                    novel_info.get("source", "unknown"),
                    novel_info["source_url"],
                )
                logger.info(f"保存小說信息成功: {novel_info['title']}")
                return result["source_url"]
            except Exception:
                logger.exception(f"保存小說信息失敗: {novel_info['title']}")
                raise

    async def save_chapters(self, novel_id: str, chapters: List[Dict]):
        """批量保存章節"""
        async with self.pool_manager.db_pool.acquire() as conn:
            try:
                # 準備批量插入的數據
                values = [
                    (
                        novel_id,
                        chapter["chapter_number"],
                        chapter["chapter_title"],
                        chapter["chapter_content"],
                        chapter["chapter_url"],
                        datetime.now(),
                        datetime.now(),
                    )
                    for chapter in chapters
                ]

                # 使用 copy_records_to_table 進行批量插入
                await conn.copy_records_to_table(
                    "chapters",
                    records=values,
                    columns=[
                        "novel_id",
                        "chapter_number",
                        "chapter_title",
                        "chapter_content",
                        "chapter_url",
                        "created_at",
                        "updated_at",
                    ],
                    conflict_action=(
                        "DO UPDATE SET chapter_content = EXCLUDED.chapter_content, "
                        "updated_at = CURRENT_TIMESTAMP"
                    ),
                )

                logger.info(f"批量保存章節成功: {len(chapters)} 章")
                return True
            except Exception:
                logger.exception("批量保存章節失敗")
                raise

    async def get_chapter_count(self, novel_id: str) -> int:
        """獲取小說章節數"""
        async with self.pool_manager.db_pool.acquire() as conn:
            try:
                result = await conn.fetchval(
                    "SELECT COUNT(*) FROM chapters WHERE novel_id = $1",
                    novel_id,
                )
                return result
            except Exception:
                logger.exception("獲取章節數失敗")
                raise

    async def get_chapter_stats(self, novel_id: str, limit: int = 5) -> List[Dict]:
        """獲取章節統計信息"""
        async with self.pool_manager.db_pool.acquire() as conn:
            try:
                rows = await conn.fetch(
                    """
                    SELECT
                        chapter_number,
                        length(chapter_content) as content_length,
                        created_at,
                        updated_at
                    FROM chapters
                    WHERE novel_id = $1
                    ORDER BY chapter_number
                    LIMIT $2
                """,
                    novel_id,
                    limit,
                )

                return [dict(row) for row in rows]
            except Exception:
                logger.exception("獲取章節統計失敗")
                raise
