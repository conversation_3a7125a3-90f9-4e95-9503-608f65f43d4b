import asyncio
import json
import logging
import signal
from collections import defaultdict
from typing import Any, Dict, List, Optional

try:  # optional import for default queue name
    from ..config.settings import settings

    DEFAULT_QUEUE = settings.redis_config.get("item_queue", "crawler:item_queue")
except Exception:  # pragma: no cover - fallback when settings module unavailable
    DEFAULT_QUEUE = "crawler:item_queue"

logger = logging.getLogger(__name__)

BATCH_SIZE = 20
FETCH_TIMEOUT = 5


class AsyncPGWorker:
    """Consume items from Redis and write to PostgreSQL using asyncpg."""

    def __init__(
        self,
        *,
        pool_manager: Optional[Any] = None,
        db_writer: Optional[Any] = None,
        queue_name: str = DEFAULT_QUEUE,
        batch_size: int = BATCH_SIZE,
        fetch_timeout: int = FETCH_TIMEOUT,
    ) -> None:
        self.pool_manager = pool_manager
        self.db_writer = db_writer
        self.queue_name = queue_name
        self.batch_size = batch_size
        self.fetch_timeout = fetch_timeout
        self.redis_client = None
        self._shutdown = asyncio.Event()

    async def start(self) -> None:
        """Start consuming items until :meth:`stop` is called."""
        if self.pool_manager is None:
            from ..utils.pool_manager import PoolManager  # lazy import

            self.pool_manager = await PoolManager.get_instance()
        if not await self.pool_manager.ensure_connections():
            logger.error("Failed to establish connections")
            return
        self.redis_client = self.pool_manager.get_redis()
        if not self.redis_client:
            logger.error("Failed to get Redis client from PoolManager.")
            await self.pool_manager.close_pools()
            return

        if self.db_writer is None:
            from ..db.novel_writer import NovelDBWriter  # lazy import

            self.db_writer = NovelDBWriter()
        await self.db_writer.init()

        loop = asyncio.get_running_loop()
        for sig in (signal.SIGINT, signal.SIGTERM):
            try:
                loop.add_signal_handler(sig, self.stop)
            except NotImplementedError:
                pass

        batch: List[Dict] = []
        while not self._shutdown.is_set():
            item = await self.redis_client.blpop(
                self.queue_name, timeout=self.fetch_timeout
            )
            if item:
                _, data = item
                try:
                    batch.append(json.loads(data))
                except json.JSONDecodeError:
                    logger.exception(
                        "Failed to decode JSON data from queue. Raw data: %s", data
                    )
            if batch and (len(batch) >= self.batch_size or item is None):
                try:
                    await self._process_batch(batch)
                    batch = []
                except Exception:
                    logger.exception("Failed to process batch, requeuing items")
                    for failed in batch:
                        try:
                            await self.redis_client.rpush(
                                self.queue_name,
                                json.dumps(failed, ensure_ascii=False),
                            )
                        except Exception:
                            logger.exception(
                                "Failed to requeue item back to Redis: %s", failed
                            )
                    batch = []
            if item is None:
                await asyncio.sleep(1)

        await self.pool_manager.close_pools()

    def stop(self) -> None:
        """Signal the worker loop to exit."""
        self._shutdown.set()

    async def _process_batch(self, items: List[Dict]) -> None:
        novel_infos: Dict[str, Dict] = {}
        chapters_map: Dict[str, List[Dict]] = defaultdict(list)
        for item in items:
            novel_id = item.get("source_url")
            if not novel_id:
                continue
            current = novel_infos.get(novel_id, {})
            novel_infos[novel_id] = {
                "title": current.get("title") or item.get("title"),
                "author": current.get("author") or item.get("author"),
                "description": current.get("description")
                or item.get("description", ""),
                "source": item.get("source", current.get("source", "unknown")),
                "source_url": novel_id,
            }
            if item.get("chapter_number") and item.get("chapter_content"):
                chapters_map[novel_id].append(
                    {
                        "chapter_number": item["chapter_number"],
                        "chapter_title": item.get("chapter_title", ""),
                        "chapter_content": item["chapter_content"],
                        "chapter_url": item.get("chapter_url", ""),
                    }
                )

        if novel_infos:
            await asyncio.gather(
                *(self.db_writer.save_novel(info) for info in novel_infos.values())
            )

        if chapters_map:
            await asyncio.gather(
                *(
                    self.db_writer.save_chapters(n_id, chapters)
                    for n_id, chapters in chapters_map.items()
                )
            )


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    worker = AsyncPGWorker()
    asyncio.run(worker.start())
