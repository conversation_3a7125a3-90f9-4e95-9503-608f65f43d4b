import logging
from typing import Dict, Any
import sys


def setup_logger(config: Dict[str, Any]) -> logging.Logger:
    """設置日誌配置"""
    # 創建日誌記錄器
    logger = logging.getLogger("novel_crawler")
    logger.setLevel(getattr(logging, config["level"]))

    # 清除現有的處理器
    logger.handlers.clear()

    # 創建控制台處理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(config["format"]))
    logger.addHandler(console_handler)

    # 創建文件處理器
    if config.get("filename"):
        file_handler = logging.FileHandler(config["filename"], encoding="utf-8")
        file_handler.setFormatter(logging.Formatter(config["format"]))
        logger.addHandler(file_handler)

    return logger
