import re
from urllib.parse import urlparse


def extract_novel_id(url: str) -> str:
    """
    從不同來源的 URL 中提取小說 ID

    支援的格式:
    - https://www.ttkan.co/novel/chapters/wenxian-heishanlaogui
    - https://www.wa01.com/novel/pagea/wenxian-heishanlaogui_1.html
    - https://www.wa01.com/novel/pagea/bukexueyushou-qingquanliuxiang_1.html

    Args:
        url (str): 小說頁面的 URL

    Returns:
        str: 提取出的小說 ID
    """
    # 解析 URL
    parsed = urlparse(url)

    # 獲取路徑最後一段
    last_part = parsed.path.rsplit("/", 1)[-1]

    # 移除查詢參數
    if "?" in last_part:
        last_part = last_part.split("?", 1)[0]

    # 移除 .html 後綴
    last_part = re.sub(r"\.html$", "", last_part)

    # 移除頁碼後綴 (如 _1, _2 等)
    last_part = re.sub(r"_\d+$", "", last_part)

    return last_part


def get_source_site(url: str) -> str:
    """
    從 URL 判斷來源網站

    Args:
        url (str): 小說頁面的 URL

    Returns:
        str: 來源網站標識 (ttkan, wa01 等)
    """
    domain = urlparse(url).netloc.lower()
    if "ttkan.co" in domain:
        return "ttkan"
    elif "wa01.com" in domain:
        return "wa01"
    else:
        return "unknown"


def build_chapter_url(novel_id: str, chapter_id: int, source: str) -> str:
    """
    根據來源網站構建章節 URL

    Args:
        novel_id (str): 小說 ID
        chapter_id (int): 章節 ID
        source (str): 來源網站

    Returns:
        str: 章節頁面的 URL
    """
    if source == "ttkan":
        return (
            f"https://www.ttkan.co/novel/user/page_direct"
            f"?novel_id={novel_id}&page={chapter_id}"
        )
    elif source == "wa01":
        return f"https://www.wa01.com/novel/pagea/{novel_id}_{chapter_id}.html"
    else:
        raise ValueError(f"不支援的來源網站: {source}")
