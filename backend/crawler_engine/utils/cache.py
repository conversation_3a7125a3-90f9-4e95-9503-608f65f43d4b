import json
import logging
from typing import Dict, Optional
from datetime import datetime
from .pool_manager import PoolManager

logger = logging.getLogger(__name__)


class NovelCache:
    """小說緩存管理類"""

    def __init__(self):
        self.pool_manager = None
        self.redis = None

    async def init(self):
        """初始化"""
        self.pool_manager = await PoolManager.get_instance()
        self.redis = self.pool_manager.get_redis()

    async def get_novel_cache(self, novel_url: str) -> Dict:
        """獲取小說的緩存信息"""
        if not self.redis:
            raise RuntimeError("Redis 客戶端未初始化")

        cache = {
            "is_crawled": await self.redis.sismember("crawled_novels", novel_url),
            "last_crawl_time": None,
            "chapters_info": None,
            "total_chapters": 0,
        }

        if cache["is_crawled"]:
            # 獲取上次爬取時間
            last_crawl = await self.redis.get(f"novel_last_crawl:{novel_url}")
            if last_crawl:
                cache["last_crawl_time"] = datetime.strptime(
                    last_crawl, "%Y-%m-%d %H:%M:%S"
                )

            # 獲取章節信息
            chapters_data = await self.redis.get(f"novel_chapters:{novel_url}")
            if chapters_data:
                try:
                    cache["chapters_info"] = json.loads(chapters_data)
                    cache["total_chapters"] = len(cache["chapters_info"])
                except json.JSONDecodeError:
                    logger.warning(f"解析緩存章節數據失敗: {novel_url}")

        return cache

    async def update_novel_cache(
        self, novel_url: str, chapters: list, expire: int = 86400
    ) -> Optional[list]:
        """更新小說的緩存信息"""
        if not self.redis:
            raise RuntimeError("Redis 客戶端未初始化")

        try:
            current_time = datetime.now()

            # 更新基本信息
            await self.redis.sadd("crawled_novels", novel_url)
            await self.redis.set(
                f"novel_last_crawl:{novel_url}",
                current_time.strftime("%Y-%m-%d %H:%M:%S"),
            )

            # 更新章節信息
            chapters_info = [
                {
                    "number": ch["chapter_number"],
                    "title": ch["chapter_title"],
                    "url": ch["chapter_url"],
                    "updated_at": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                }
                for ch in chapters
            ]

            await self.redis.set(
                f"novel_chapters:{novel_url}", json.dumps(chapters_info), ex=expire
            )

            # 更新章節數量
            await self.redis.set(
                f"novel_chapters_count:{novel_url}", str(len(chapters_info))
            )

            return chapters_info
        except Exception:
            logger.exception(f"更新緩存失敗: {novel_url}")
            return None
