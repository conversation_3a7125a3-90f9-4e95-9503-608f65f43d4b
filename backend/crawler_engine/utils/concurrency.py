import logging
import os
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ConcurrencyConfig:
    """並發與重試設定。

    Attributes:
        max_concurrency: 同時處理的最大協程數
        chunk_size: 每批次處理的章節數
        chunk_delay: 兩批次之間的等待秒數
        timeout: 請求逾時秒數
        max_retries: 失敗時的重試次數
        retry_delay: 重試前的等待秒數
    """

    max_concurrency: int = int(os.getenv("SPIDER_MAX_CONCURRENCY", 5))
    chunk_size: int = 10
    chunk_delay: float = 1.0
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 2.0
