import logging
import asyncio
from typing import Dict, Optional
import redis.asyncio as redis
import asyncpg
from ..settings import DATABASE_CONFIG, REDIS_CONFIG

logger = logging.getLogger(__name__)


class PoolManager:
    """連接池管理器（單例模式）"""

    _instance = None
    _lock = asyncio.Lock()
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.db_pool: Optional[asyncpg.Pool] = None
            self.redis_pool: Optional[redis.Redis] = None
            self._initialized = True

    @classmethod
    async def get_instance(cls) -> "PoolManager":
        """獲取單例實例"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    instance = cls()
                    await instance.init_pools()
                    cls._instance = instance
        return cls._instance

    async def init_pools(self):
        """初始化所有連接池"""
        try:
            # 初始化數據庫連接池
            if not self.db_pool:
                self.db_pool = await asyncpg.create_pool(
                    min_size=DATABASE_CONFIG["min_connections"],
                    max_size=DATABASE_CONFIG["max_connections"],
                    **{
                        k: v
                        for k, v in DATABASE_CONFIG.items()
                        if k not in ["min_connections", "max_connections"]
                    },
                )
                logger.info("數據庫連接池初始化成功")

            # 初始化 Redis 連接池
            if not self.redis_pool:
                self.redis_pool = redis.Redis.from_url(
                    REDIS_CONFIG["url"],
                    encoding=REDIS_CONFIG["encoding"],
                    decode_responses=True,
                    max_connections=REDIS_CONFIG["maxsize"],
                )
                logger.info("Redis 連接池初始化成功")
        except Exception:
            logger.exception("連接池初始化失敗")
            await self.close_pools()
            raise

    async def get_db_conn(self) -> asyncpg.Connection:
        """獲取數據庫連接"""
        if not self.db_pool:
            raise RuntimeError("數據庫連接池未初始化")
        return await self.db_pool.acquire()

    async def release_db_conn(self, conn: asyncpg.Connection):
        """釋放數據庫連接"""
        if self.db_pool:
            await self.db_pool.release(conn)

    def get_redis(self) -> redis.Redis:
        """獲取 Redis 客戶端"""
        if not self.redis_pool:
            raise RuntimeError("Redis 連接池未初始化")
        return self.redis_pool

    async def close_pools(self):
        """關閉所有連接池"""
        if self.db_pool:
            await self.db_pool.close()
            self.db_pool = None
            logger.info("數據庫連接池已關閉")

        if self.redis_pool:
            await self.redis_pool.close()
            self.redis_pool = None
            logger.info("Redis 連接池已關閉")

    async def check_connections(self) -> Dict[str, bool]:
        """檢查連接狀態"""
        status = {"database": False, "redis": False}

        try:
            if self.db_pool:
                async with self.db_pool.acquire() as conn:
                    await conn.execute("SELECT 1")
                    status["database"] = True
        except Exception:
            logger.error("數據庫連接檢查失敗")

        try:
            if self.redis_pool:
                await self.redis_pool.ping()
                status["redis"] = True
        except Exception:
            logger.error("Redis 連接檢查失敗")

        return status

    async def ensure_connections(self) -> bool:
        """確保所有連接可用"""
        status = await self.check_connections()
        if not all(status.values()):
            logger.warning("檢測到連接異常，嘗試重新初始化連接池")
            await self.close_pools()
            await self.init_pools()
            status = await self.check_connections()
        return all(status.values())
