import logging
import asyncio
import os
from abc import ABC, abstractmethod
from typing import Dict, List, Optional
from .types import ErrorConfig, DbConfig, RedisConfig
import redis
import asyncpg

logger = logging.getLogger(__name__)


class BaseCrawler(ABC):
    def __init__(self, config: "CrawlerConfig"):
        self.config = config
        self.db = DatabaseManager(config.db_config)
        self.cache = RedisCache(config.redis_config)
        self.error_handler = ErrorHandler(config.error_config)

    @abstractmethod
    async def crawl(self) -> bool:
        pass

    @abstractmethod
    async def parse(self, content: str) -> Dict:
        pass

    @abstractmethod
    async def save(self, data: Dict) -> bool:
        pass

    async def retry_request(self, url: str, retries: int = 3) -> str:
        for i in range(retries):
            try:
                return await self._make_request(url)
            except Exception as e:
                self.error_handler.log_error(e)
                if i == retries - 1:
                    raise
                await asyncio.sleep(2**i)


class ErrorHandler:
    def __init__(self, config: "ErrorConfig"):
        self.config = config
        self.logger = logging.getLogger(__name__)

    def log_error(self, error: Exception):
        error_type = type(error).__name__
        if error_type in self.config.retryable_errors:
            self._handle_retryable_error(error)
        else:
            self._handle_critical_error(error)

    def _handle_retryable_error(self, error: Exception):
        self.logger.warning(f"Retryable error: {str(error)}")
        # Implement retry logic

    def _handle_critical_error(self, error: Exception):
        self.logger.error(f"Critical error: {str(error)}")
        # Implement alerting and recovery


class DatabaseManager:
    def __init__(self, config: "DbConfig"):
        self.pool = self._create_pool(config)

    async def execute(self, query: str, params: tuple) -> List[Dict]:
        async with self.pool.acquire() as conn:
            return await conn.fetch(query, *params)

    async def batch_insert(self, table: str, data: List[Dict]):
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                await conn.copy_records_to_table(
                    table, records=data, columns=data[0].keys()
                )

    def _create_pool(self, config: "DbConfig"):
        return asyncpg.create_pool(
            host=config["host"],
            port=config["port"],
            database=config["dbname"],
            user=config["user"],
            password=config["password"],
            min_size=1,
            max_size=10,
            timeout=30,
            command_timeout=60,
        )


class RedisCache:
    def __init__(self, config: "RedisConfig"):
        self.client = redis.Redis(**config)

    async def get(self, key: str) -> Optional[str]:
        return self.client.get(key)

    async def set(self, key: str, value: str, expire: int = None):
        self.client.set(key, value, ex=expire)


class CrawlerConfig:
    def __init__(self):
        self.db_config = self._load_db_config()
        self.redis_config = self._load_redis_config()
        self.error_config = self._load_error_config()

    def _load_db_config(self) -> "DbConfig":
        return {
            "host": os.getenv("DB_HOST"),
            "port": int(os.getenv("DB_PORT")),
            "dbname": os.getenv("DB_NAME"),
            "user": os.getenv("DB_USER"),
            "password": os.getenv("DB_PASSWORD"),
        }

    def _load_redis_config(self) -> "RedisConfig":
        return {
            "host": os.getenv("REDIS_HOST"),
            "port": int(os.getenv("REDIS_PORT")),
            "db": int(os.getenv("REDIS_DB", 0)),
            "decode_responses": True,
        }

    def _load_error_config(self) -> "ErrorConfig":
        return {
            "retryable_errors": ["ConnectionError", "TimeoutError"],
            "max_retries": 3,
        }
