from pathlib import Path
from typing import List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

load_dotenv()

# 基礎路徑配置
BASE_DIR = Path(__file__).resolve().parent.parent.parent


class CrawlerSettings(BaseSettings):
    """爬蟲配置設定類 - 使用 pydantic-settings"""

    model_config = SettingsConfigDict(env_prefix="CRAWLER_")

    # ===== 基礎路徑配置 =====
    base_dir: Path = Field(
        default_factory=lambda: Path(__file__).resolve().parent.parent.parent,
        description="專案根目錄路徑",
    )

    # ===== Redis 配置 =====
    redis_host: str = Field(default="localhost", description="Redis 服務器地址")
    redis_port: int = Field(
        default=6379, ge=1, le=65535, description="Redis 服務器端口"
    )
    redis_db: int = Field(default=0, ge=0, le=15, description="Redis 資料庫編號")
    redis_decode_responses: bool = Field(
        default=True, description="是否解碼 Redis 回應為字符串"
    )
    redis_max_connections: int = Field(
        default=20, ge=1, le=100, description="Redis 最大連接數"
    )
    redis_item_queue: str = Field(
        default="crawler:item_queue", description="Redis 隊列名稱"
    )

    # ===== PostgreSQL 配置 =====
    postgres_host: str = Field(default="localhost", description="PostgreSQL 服務器地址")
    postgres_port: int = Field(
        default=5432, ge=1, le=65535, description="PostgreSQL 服務器端口"
    )
    postgres_db: str = Field(default="novel_db", description="PostgreSQL 資料庫名稱")
    postgres_user: str = Field(default="postgres", description="PostgreSQL 用戶名")
    postgres_password: str = Field(default="", description="PostgreSQL 密碼")
    postgres_min_connections: int = Field(
        default=5, ge=1, le=50, description="PostgreSQL 最小連接數"
    )
    postgres_max_connections: int = Field(
        default=20, ge=1, le=100, description="PostgreSQL 最大連接數"
    )

    # ===== Scrapy 配置 =====
    scrapy_concurrent_requests: int = Field(
        default=16, ge=1, le=100, description="Scrapy 並發請求數"
    )
    scrapy_download_delay: float = Field(
        default=0.5, ge=0.0, le=60.0, description="Scrapy 下載延遲（秒）"
    )
    scrapy_user_agent: str = Field(
        default=(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/120.0.0.0 Safari/537.36"
        ),
        description="Scrapy 用戶代理字符串",
    )

    # ===== 爬蟲業務配置 =====
    crawler_max_workers: int = Field(
        default=3, ge=1, le=20, description="爬蟲最大工作進程數"
    )
    crawler_chunk_size: int = Field(default=10, ge=1, le=1000, description="批處理大小")
    crawler_delay: float = Field(
        default=1.0, ge=0.0, le=60.0, description="請求延遲（秒）"
    )
    crawler_timeout: int = Field(
        default=30, ge=1, le=300, description="請求超時時間（秒）"
    )
    crawler_retries: int = Field(default=3, ge=0, le=10, description="重試次數")

    # ===== 日誌配置 =====
    log_level: str = Field(default="INFO", description="日誌級別")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日誌格式",
    )
    log_file: str = Field(default="crawler.log", description="日誌文件名")

    # ===== 緩存配置 =====
    cache_enabled: bool = Field(default=True, description="是否啟用緩存")
    cache_expire: int = Field(default=86400, ge=0, description="緩存過期時間（秒）")
    cache_prefix: str = Field(default="novel_cache:", description="緩存鍵前綴")

    # ===== 錯誤處理配置 =====
    retryable_errors: List[str] = Field(
        default=["ConnectionError", "TimeoutError", "HTTPError"],
        description="可重試的錯誤類型列表",
    )
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重試次數")
    retry_delay: int = Field(default=5, ge=0, le=60, description="重試延遲（秒）")

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        """驗證日誌級別"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"log_level 必須是 {valid_levels} 之一")
        return v.upper()

    @property
    def redis_config(self) -> dict:
        """Redis 配置字典（向後兼容）"""
        return {
            "host": self.redis_host,
            "port": self.redis_port,
            "db": self.redis_db,
            "decode_responses": self.redis_decode_responses,
            "max_connections": self.redis_max_connections,
            "item_queue": self.redis_item_queue,
        }

    @property
    def postgres_config(self) -> dict:
        """PostgreSQL 配置字典（向後兼容）"""
        return {
            "host": self.postgres_host,
            "port": self.postgres_port,
            "database": self.postgres_db,
            "user": self.postgres_user,
            "password": self.postgres_password,
        }

    @property
    def postgres_url(self) -> str:
        """PostgreSQL 連接 URL"""
        return (
            f"postgresql://{self.postgres_user}:{self.postgres_password}"
            f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
        )

    @property
    def redis_url(self) -> str:
        """Redis 連接 URL"""
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"

    def get_redis_url(self) -> str:
        """獲取 Redis 連接 URL（向後兼容）"""
        return self.redis_url

    def get_database_url(self) -> str:
        """獲取資料庫連接 URL（向後兼容）"""
        return self.postgres_url

    @property
    def scrapy_settings(self) -> dict:
        """Scrapy 配置字典"""
        return {
            "CONCURRENT_REQUESTS": self.scrapy_concurrent_requests,
            "DOWNLOAD_DELAY": self.scrapy_download_delay,
            "USER_AGENT": self.scrapy_user_agent,
            "RANDOMIZE_DOWNLOAD_DELAY": True,
            "DOWNLOAD_TIMEOUT": self.crawler_timeout,
            "RETRY_TIMES": self.crawler_retries,
        }


# 全局配置實例
settings = CrawlerSettings()


# ===== 向後兼容的配置字典 =====
# 保持與現有代碼的兼容性

DB_CONFIG = settings.postgres_config
REDIS_CONFIG = settings.redis_config
