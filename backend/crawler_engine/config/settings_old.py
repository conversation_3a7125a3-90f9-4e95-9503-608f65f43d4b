import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# 基礎路徑配置
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 數據庫配置
DB_CONFIG = {
    "database": os.getenv("CRAWLER_DB_NAME", "postgres"),
    "user": os.getenv("CRAWLER_DB_USER", "postgres"),
    "password": os.getenv("CRAWLER_DB_PASSWORD", ""),
    "host": os.getenv("CRAWLER_DB_HOST", "localhost"),
    "port": os.getenv("CRAWLER_DB_PORT", "5432"),
}

# Redis配置
REDIS_CONFIG = {
    "host": os.getenv("CRAWLER_REDIS_HOST", "localhost"),
    "port": int(os.getenv("CRAWLER_REDIS_PORT", "6379")),
    "db": int(os.getenv("CRAWLER_REDIS_DB", "0")),
    "decode_responses": True,
}

# 爬蟲配置
CRAWLER_CONFIG = {
    "max_workers": 3,
    "chunk_size": 10,
    "delay": 1.0,
    "timeout": 30,
    "retries": 3,
    "user_agent": (
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
        "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ),
}

# 日誌配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "crawler.log",
}

# 緩存配置
CACHE_CONFIG = {"enabled": True, "expire": 86400, "prefix": "novel_cache:"}  # 24小時

# 錯誤處理配置
ERROR_CONFIG = {
    "retryable_errors": ["ConnectionError", "TimeoutError", "HTTPError"],
    "max_retries": 3,
    "retry_delay": 5,
}

# 並發配置
CONCURRENCY_CONFIG = {"max_workers": 3, "chunk_size": 10, "delay": 1.0}
