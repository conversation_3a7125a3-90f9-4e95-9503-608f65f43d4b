from __future__ import annotations

import yaml
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiohttp
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)


class BaseAdapter(ABC):
    """可擴展的網站適配器基類,支援外部化選擇器設定."""

    def __init__(self, selector_path: Optional[str] = None) -> None:
        self.selector_file = (
            Path(selector_path)
            if selector_path
            else (
                Path(__file__).resolve().parents[1]
                / "selectors"
                / f"{self.__class__.__name__.replace('Adapter', '').lower()}.yaml"
            )
        )
        self.selectors: Dict[str, str] = self._load_selectors()
        self._session: Optional[aiohttp.ClientSession] = None

    def _load_selectors(self) -> Dict[str, str]:
        if not self.selector_file.exists():
            logger.warning("Selector file %s not found", self.selector_file)
            return {}
        try:
            with self.selector_file.open("r", encoding="utf-8") as f:
                return yaml.safe_load(f) or {}
        except yaml.YAMLError as e:
            logger.error(
                "Error decoding YAML from selector file %s: %s",
                self.selector_file,
                e,
            )
            return {}
        except OSError as e:
            logger.error(
                "Error reading selector file %s: %s",
                self.selector_file,
                e,
            )
            return {}

    def selector(self, key: str) -> str:
        return self.selectors.get(key, "")

    async def fetch(self, url: str) -> str:
        if not self._session:
            self._session = aiohttp.ClientSession()
        async with self._session.get(url) as resp:
            resp.raise_for_status()
            return await resp.text()

    async def close(self) -> None:
        if self._session:
            await self._session.close()
            self._session = None

    @abstractmethod
    async def parse_novel_info(self, html: str) -> Dict[str, Any]:
        """解析小說資訊"""

    @abstractmethod
    async def parse_chapter_list(self, html: str) -> List[Dict[str, Any]]:
        """解析章節列表"""

    @abstractmethod
    async def parse_chapter_content(self, html: str) -> str:
        """解析章節內容"""

    def _soup(self, html: str) -> BeautifulSoup:
        return BeautifulSoup(html, "html.parser")

    def _extract_text(self, soup: BeautifulSoup, key: str) -> str:
        selector = self.selector(key)
        if selector:
            try:
                el = soup.select_one(selector)
                if el and el.text:
                    return el.text.strip()
            except Exception as e:
                logger.error(
                    "Invalid CSS selector '%s' for key '%s': %s", selector, key, e
                )
        return ""

    def _extract_all(self, soup: BeautifulSoup, key: str) -> List[str]:
        selector = self.selector(key)
        if selector:
            try:
                return [el.get_text(strip=True) for el in soup.select(selector)]
            except Exception as e:
                logger.error(
                    "Invalid CSS selector '%s' for key '%s': %s", selector, key, e
                )
        return []

    def validate_url(self, url: str) -> bool:
        """基礎的URL驗證，子類可覆寫以實現更複雜的邏輯。"""
        if not url or not url.startswith("http"):
            return False
        # 可以在這裡加入更多通用驗證規則
        return True

    def default_parse_novel_info(self, html: str) -> Dict[str, Any]:
        soup = self._soup(html)
        return {
            "title": self._extract_text(soup, "title"),
            "author": self._extract_text(soup, "author"),
            "intro": self._extract_text(soup, "intro"),
        }

    def default_parse_chapter_list(self, html: str) -> List[Dict[str, Any]]:
        soup = self._soup(html)
        chapters = []
        selector = self.selector("chapter_list")
        if not selector:
            return chapters
        try:
            for idx, link in enumerate(soup.select(selector), start=1):
                chapter_title = link.get_text(strip=True)
                chapter_url = link.get("href") or ""
                chapters.append(
                    {
                        "chapter_number": idx,
                        "chapter_title": chapter_title,
                        "chapter_url": chapter_url,
                    }
                )
        except Exception as e:
            logger.error(
                "Error parsing chapter list with selector '%s': %s", selector, e
            )
        return chapters

    def default_parse_chapter_content(self, html: str) -> str:
        soup = self._soup(html)
        selector = self.selector("chapter_content")
        if selector:
            try:
                element = soup.select_one(selector)
                if element:
                    return element.get_text(separator="\n", strip=True)
            except Exception as e:
                logger.error(
                    "Error parsing chapter content with selector '%s': %s", selector, e
                )
        return ""


__all__ = ["BaseAdapter"]
