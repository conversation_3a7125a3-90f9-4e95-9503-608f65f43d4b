from typing import Any, Dict, List
from .base_adapter import BaseAdapter


class TTKANAdapter(BaseAdapter):
    async def parse_novel_info(self, html: str) -> Dict[str, Any]:
        return self.default_parse_novel_info(html)

    async def parse_chapter_list(self, html: str) -> List[Dict[str, Any]]:
        return self.default_parse_chapter_list(html)

    async def parse_chapter_content(self, html: str) -> str:
        return self.default_parse_chapter_content(html)


__all__ = ["TTKANAdapter"]
