from prometheus_client import Counter, start_http_server
from scrapy import signals


class PrometheusStatsExtension:
    def __init__(self, port: int) -> None:
        start_http_server(port)
        self.success = Counter(
            "crawler_success_total",
            "Total successful items scraped",
            ["spider"],
        )
        self.failure = Counter(
            "crawler_failure_total",
            "Total crawl errors",
            ["spider"],
        )

    @classmethod
    def from_crawler(cls, crawler):
        ext = cls(crawler.settings.getint("PROMETHEUS_PORT", 9000))
        crawler.signals.connect(ext.item_scraped, signal=signals.item_scraped)
        crawler.signals.connect(ext.spider_error, signal=signals.spider_error)
        return ext

    def item_scraped(self, item, spider):
        self.success.labels(spider.name).inc()

    def spider_error(self, failure, response, spider):
        self.failure.labels(spider.name).inc()
