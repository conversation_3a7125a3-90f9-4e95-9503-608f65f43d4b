import scrapy


class NovelItem(scrapy.Item):
    # 基本信息
    title = scrapy.Field()
    author = scrapy.Field()
    description = scrapy.Field()
    cover_url = scrapy.Field()
    category = scrapy.Field()
    tags = scrapy.Field()
    status = scrapy.Field()

    # 來源信息
    source = scrapy.Field()
    source_id = scrapy.Field()
    source_url = scrapy.Field()

    # 章節信息
    chapter_number = scrapy.Field()
    chapter_title = scrapy.Field()
    chapter_content = scrapy.Field()
    chapter_url = scrapy.Field()


class ChapterItem(scrapy.Item):
    novel_title = scrapy.Field()  # 所屬小說標題
    chapter_title = scrapy.Field()  # 章節標題
    chapter_number = scrapy.Field()  # 章節號
    content = scrapy.Field()  # 章節內容
    chapter_url = scrapy.Field()  # 章節URL
    update_time = scrapy.Field()  # 更新時間
    source = scrapy.Field()  # 來源網站
