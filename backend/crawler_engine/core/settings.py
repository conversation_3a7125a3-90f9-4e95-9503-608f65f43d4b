import os

BOT_NAME = "novel_crawler"

SPIDER_MODULES = ["crawler_engine.spiders"]
NEWSPIDER_MODULE = "crawler_engine.spiders"

# scrapy-redis integration
DUPEFILTER_CLASS = "scrapy_redis.dupefilter.RFPDupeFilter"
SCHEDULER = "scrapy_redis.scheduler.Scheduler"
SCHEDULER_PERSIST = True

# 改進的 Redis 配置 - 使用環境變數避免寫死端口
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = os.getenv("REDIS_PORT", "6379")
REDIS_DB = os.getenv("REDIS_DB", "0")
REDIS_URL = os.getenv("REDIS_URL", f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}")

ITEM_PIPELINES = {
    "crawler_engine.core.pipelines.DefaultRedisPipeline": 300,
}
