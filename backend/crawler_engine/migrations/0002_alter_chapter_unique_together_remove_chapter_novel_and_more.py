# Generated by Django 4.2.22 on 2025-06-12 15:07

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("crawler_engine", "0001_initial"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="chapter",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="chapter",
            name="novel",
        ),
        migrations.AlterUniqueTogether(
            name="novel",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="novel",
            name="category",
        ),
        migrations.RemoveField(
            model_name="novel",
            name="tags",
        ),
        migrations.DeleteModel(
            name="Category",
        ),
        migrations.DeleteModel(
            name="Chapter",
        ),
        migrations.DeleteModel(
            name="Novel",
        ),
        migrations.DeleteModel(
            name="Tag",
        ),
    ]
