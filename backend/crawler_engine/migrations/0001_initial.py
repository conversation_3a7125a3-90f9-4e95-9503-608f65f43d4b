# Generated by Django 5.1.4 on 2024-12-20 17:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100, unique=True)),
            ],
            options={
                "verbose_name_plural": "categories",
            },
        ),
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="Novel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("author", models.CharField(max_length=100)),
                ("description", models.TextField()),
                (
                    "cover",
                    models.ImageField(blank=True, null=True, upload_to="covers/"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("ongoing", "連載中"), ("completed", "已完結")],
                        default="ongoing",
                        max_length=20,
                    ),
                ),
                ("views", models.IntegerField(default=0)),
                ("likes", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("source", models.CharField(max_length=50)),
                ("source_id", models.CharField(max_length=100)),
                ("source_url", models.URLField()),
                (
                    "category",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="crawler_engine.category",
                    ),
                ),
                ("tags", models.ManyToManyField(to="crawler_engine.tag")),
            ],
            options={
                "unique_together": {("source", "source_id")},
            },
        ),
        migrations.CreateModel(
            name="Chapter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField()),
                ("chapter_number", models.IntegerField()),
                ("views", models.IntegerField(default=0)),
                ("is_vip", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("source_url", models.URLField()),
                (
                    "novel",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chapters",
                        to="crawler_engine.novel",
                    ),
                ),
            ],
            options={
                "ordering": ["chapter_number"],
                "unique_together": {("novel", "chapter_number")},
            },
        ),
    ]
