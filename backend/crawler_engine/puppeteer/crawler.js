const axios = require('axios');
const fs = require('fs-extra');
const cheerio = require('cheerio');

class MCPNovelCrawler {
  constructor(url) {
    this.url = url;
    this.results = [];
    this.mcpServerUrl = 'http://localhost:8000/render'; // MCP server URL
  }

  async fetchPageContent(url) {
    try {
      const response = await axios.post(this.mcpServerUrl, { url });
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch page content for ${url}:`, error);
      return null;
    }
  }

  async getNovelInfo() {
    const content = await this.fetchPageContent(this.url);
    if (!content) throw new Error('Failed to fetch novel page');

    const $ = cheerio.load(content);
    const title = $('h1.book-title').text().trim();
    const author = $('div.author').text().replace('作者：', '').trim();
    const description = $('div.intro').text().trim();
    const coverUrl = $('div.book-img img').attr('src');

    return {
      title,
      author,
      description,
      coverUrl,
    };
  }

  async getChapterList() {
    const content = await this.fetchPageContent(this.url);
    if (!content) throw new Error('Failed to fetch chapter list page');

    const $ = cheerio.load(content);
    const chapters = [];
    $('div.chapter-list a, div.chapter a').each((index, element) => {
      const title = $(element).text().trim();
      if (title && /第.*[章|節]|序章|序幕|尾聲|終章/.test(title)) {
        chapters.push({
          url: $(element).attr('href'),
          title,
          number: index + 1,
        });
      }
    });

    return chapters;
  }

  async getChapterContent(chapter) {
    const content = await this.fetchPageContent(chapter.url);
    if (!content) throw new Error(`Failed to fetch content for chapter ${chapter.title}`);

    const $ = cheerio.load(content);
    const text = $('div.chapter-content, div#chapter-content, div.content, article.chapter')
      .text()
      .trim();

    return text;
  }

  async crawl() {
    try {
      console.log('Starting novel crawl...');

      const novelInfo = await this.getNovelInfo();
      const chapters = await this.getChapterList();

      for (const chapter of chapters) {
        const content = await this.getChapterContent(chapter);
        this.results.push({
          ...novelInfo,
          chapterNumber: chapter.number,
          chapterTitle: chapter.title,
          chapterContent: content,
          chapterUrl: chapter.url,
        });

        await this.saveResults();
        await new Promise((resolve) => setTimeout(resolve, 2000)); // Delay between requests
      }

      console.log('Crawl completed.');
    } catch (error) {
      console.error('Crawl failed:', error);
    }
  }

  async saveResults() {
    try {
      await fs.writeJSON('novel_data.json', this.results, { spaces: 2 });
    } catch (error) {
      console.error('Failed to save results:', error);
    }
  }
}

(async () => {
  const novelUrl = 'https://www.ttkan.co/novel/chapters/sumingzhihuan-aiqianshuidewuzei';
  const crawler = new MCPNovelCrawler(novelUrl);
  await crawler.crawl();
})();
