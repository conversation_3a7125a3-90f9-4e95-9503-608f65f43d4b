const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const path = require('path');
const cheerio = require('cheerio');

class TTKanCrawler {
  static RETRY_TIMES = 3;

  static RETRY_DELAY = 5000;

  constructor(url) {
    this.url = url;
    this.novelData = {
      title: '',
      author: '',
      chapters: [],
    };
  }

  async fetchPageContent(url) {
    let retries = TTKanCrawler.RETRY_TIMES;
    while (retries > 0) {
      try {
        const browser = await puppeteer.launch({ headless: true });
        const page = await browser.newPage();
        await page.setExtraHTTPHeaders({
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          Connection: 'keep-alive',
        });
        await page.goto(url, { waitUntil: 'networkidle2' });
        // Wait for dynamic content to load, e.g., a specific element
        await page.waitForSelector('.chapter-content, #chapter-content, .content, article.chapter');
        const content = await page.content();
        await browser.close();
        return content;
      } catch (error) {
        retries--;
        if (retries === 0) throw error;
        console.log(
          `Failed to fetch page content for ${url}, retrying... (${retries} attempts left)`,
        );
        await new Promise((resolve) => setTimeout(resolve, TTKanCrawler.RETRY_DELAY));
      }
    }
  }

  async getNovelInfo(content) {
    const $ = cheerio.load(content);
    this.novelData.title = $('h1.book-title').text().trim();
    this.novelData.author = $('div.author').text().replace('作者：', '').trim();

    console.log(`Novel: ${this.novelData.title} by ${this.novelData.author}`);
  }

  async getAllChapters(content) {
    let retries = TTKanCrawler.RETRY_TIMES;
    while (retries > 0) {
      try {
        const $ = cheerio.load(content);
        const chapters = [];
        $('a[href*="/chapter/"], .chapter-item').each((index, element) => {
          const title = $(element).text().trim();
          const contentEl = $(element).next().find('.chapter-content');
          const content = contentEl.text().trim();
          const url = $(element).attr('href');
          if (title && title.length > 0) {
            chapters.push({
              title,
              content,
              url,
            });
          }
        });

        this.novelData.chapters = chapters.sort((a, b) => {
          const numA = parseInt(a.title.match(/\d+/) || [0]);
          const numB = parseInt(b.title.match(/\d+/) || [0]);
          return numA - numB;
        });

        console.log(`Found ${this.novelData.chapters.length} chapters`);
        break;
      } catch (error) {
        retries--;
        if (retries === 0) throw error;
        console.log(`Failed to get chapters, retrying... (${retries} attempts left)`);
        await new Promise((resolve) => setTimeout(resolve, TTKanCrawler.RETRY_DELAY));
      }
    }
  }

  async getChapterContent(chapter) {
    if (chapter.content) {
      return chapter.content;
    }

    let retries = TTKanCrawler.RETRY_TIMES;
    while (retries > 0) {
      try {
        const content = await this.fetchPageContent(chapter.url);
        const $ = cheerio.load(content);
        const chapterContent = $('.chapter-content, #chapter-content, .content, article.chapter')
          .text()
          .trim();
        if (chapterContent.length < 100) {
          throw new Error('Content too short, might be error page');
        }
        return chapterContent;
      } catch (error) {
        retries--;
        if (retries === 0) throw error;
        console.log(`Retrying chapter ${chapter.title}... (${retries} attempts left)`);
        await new Promise((resolve) => setTimeout(resolve, TTKanCrawler.RETRY_DELAY));
      }
    }
  }

  async crawl() {
    try {
      console.log('Fetching main page content...');
      const content = await this.fetchPageContent(this.url);

      console.log('Getting novel info...');
      await this.getNovelInfo(content);

      console.log('Getting chapter list...');
      await this.getAllChapters(content);

      if (!this.novelData.chapters.length) {
        throw new Error('No chapters found');
      }

      // Create directory for saving chapters
      const saveDir = path.join(__dirname, 'novels', this.novelData.title);
      await fs.ensureDir(saveDir);

      // Save novel info
      await fs.writeJSON(path.join(saveDir, 'info.json'), {
        title: this.novelData.title,
        author: this.novelData.author,
        totalChapters: this.novelData.chapters.length,
        crawledAt: new Date().toISOString(),
      });

      // Create a combined file for all chapters
      const combinedFilePath = path.join(saveDir, 'full_novel.txt');
      await fs.writeFile(
        combinedFilePath,
        `${this.novelData.title}\n作者：${this.novelData.author}\n\n`,
      );

      // Crawl each chapter
      for (let i = 0; i < this.novelData.chapters.length; i++) {
        const chapter = this.novelData.chapters[i];
        console.log(
          `Crawling chapter ${i + 1}/${this.novelData.chapters.length}: ${chapter.title}`,
        );

        try {
          const content = await this.getChapterContent(chapter);

          // Save individual chapter
          const chapterPath = path.join(
            saveDir,
            `chapter_${(i + 1).toString().padStart(4, '0')}.txt`,
          );
          await fs.writeFile(chapterPath, `${chapter.title}\n\n${content}`);

          // Append to combined file
          await fs.appendFile(combinedFilePath, `\n\n${chapter.title}\n\n${content}`);

          // Random delay between requests (3-7 seconds)
          const delay = Math.floor(Math.random() * (7000 - 3000 + 1)) + 3000;
          await new Promise((resolve) => setTimeout(resolve, delay));

          // Log progress
          console.log(`✓ Saved chapter ${i + 1}/${this.novelData.chapters.length}`);
        } catch (error) {
          console.error(`Failed to crawl chapter ${chapter.title}:`, error.message);
          // Save error info
          await fs.appendFile(
            path.join(saveDir, 'errors.log'),
            `Failed to crawl chapter ${chapter.title}: ${error.message}\n`,
          );
        }
      }

      console.log('Crawling completed successfully!');
      console.log(`Novel saved to: ${saveDir}`);
    } catch (error) {
      console.error('Crawling failed:', error);
      throw error;
    }
  }
  // Consider analyzing network requests in Chrome DevTools to see if there are any direct API endpoints that can be used instead of scraping the HTML.
}

// Run the crawler
const novelUrl = 'https://www.ttkan.co/novel/chapters/sumingzhihuan-aiqianshuidewuzei';
const crawler = new TTKanCrawler(novelUrl);
crawler.crawl().catch(console.error);
