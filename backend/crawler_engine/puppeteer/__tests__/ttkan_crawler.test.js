const TTKanCrawler = require('../ttkan_crawler');

describe('TTKanCrawler', () => {
  describe('getNovelInfo', () => {
    it('should extract the title and author from the HTML content', async () => {
      const htmlContent = `
        <h1 class="book-title">Test Novel Title</h1>
        <div class="author">作者：Test Author</div>
      `;
      const crawler = new TTKanCrawler('test-url');
      crawler.getNovelInfo(htmlContent);
      expect(crawler.novelData.title).toBe('Test Novel Title');
      expect(crawler.novelData.author).toBe('Test Author');
    });
  });
});
