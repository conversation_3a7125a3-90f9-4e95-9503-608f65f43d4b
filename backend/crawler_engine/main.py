import asyncio
import arg<PERSON><PERSON>
from datetime import datetime, timedelta
from typing import Dict, Type

from .settings import CRAWLER_CONFIG, LOG_CONFIG
from .utils.logger import setup_logger
from .utils.cache import NovelCache
from .utils.pool_manager import PoolManager
from .db.novel_writer import NovelDBWriter
from .spiders.base_spider import NovelSpider
from .spiders.ttkan_spider import TtkanSpider

# 註冊爬蟲
SPIDERS: Dict[str, Type[NovelSpider]] = {"ttkan": TtkanSpider}


async def should_update_novel(
    cache: Dict, update_interval_hours: int = 24
) -> tuple[bool, str]:
    """判斷是否需要更新小說"""
    if not cache["is_crawled"]:
        return True, "首次爬取"

    if not cache["last_crawl_time"]:
        return True, "無上次爬取時間記錄"

    # 檢查更新時間間隔
    time_diff = datetime.now() - cache["last_crawl_time"]
    if time_diff > timedelta(hours=update_interval_hours):
        return True, f"距離上次更新已超過{update_interval_hours}小時"

    return False, "更新間隔未到"


async def crawl_novel(url: str, spider_type: str = "ttkan"):
    """爬取小說"""
    # 設置日誌
    logger = setup_logger(LOG_CONFIG)
    logger.info(f"開始爬取: {url}")

    # 獲取連接池管理器實例
    pool_manager = await PoolManager.get_instance()

    try:
        # 檢查連接狀態
        if not await pool_manager.ensure_connections():
            logger.error("無法建立數據庫或Redis連接")
            return

        # 初始化緩存管理器
        cache_manager = NovelCache()
        await cache_manager.init()

        # 初始化數據庫寫入器
        db_writer = NovelDBWriter()
        await db_writer.init()

        # 獲取緩存信息
        cache = await cache_manager.get_novel_cache(url)

        # 檢查是否需要更新
        should_update, reason = await should_update_novel(
            cache, CRAWLER_CONFIG["update_interval"]
        )
        logger.info(f"檢查更新: {reason}")

        if not should_update and not CRAWLER_CONFIG["force_update"]:
            logger.info("無需更新，使用緩存數據")
            return

        # 獲取爬蟲類
        spider_class = SPIDERS.get(spider_type)
        if not spider_class:
            logger.error(f"未找到爬蟲類型: {spider_type}")
            return

        # 創建爬蟲實例
        spider = spider_class(url)

        # 開始爬取
        logger.info("開始爬取...")
        if await spider.crawl():
            logger.info("爬取完成")
            logger.info(f"小說信息: {spider.get_novel_info()}")

            # 比較章節變化
            new_chapter_count = len(spider.get_chapters())
            if cache["total_chapters"] > 0:
                chapter_diff = new_chapter_count - cache["total_chapters"]
                if chapter_diff > 0:
                    logger.info(f"新增了 {chapter_diff} 個章節")
                elif chapter_diff < 0:
                    logger.warning(f"章節數減少了 {abs(chapter_diff)} 個")
                else:
                    logger.info("章節數量未變")

            logger.info(f"總章節數: {new_chapter_count}")
            logger.info(f"成功爬取章節數: {len(spider.results)}")

            # 檢查失敗的章節
            failed_chapters = [
                ch
                for ch in spider.get_chapters()
                if ch["chapter_number"]
                not in [r["chapter_number"] for r in spider.results]
            ]
            if failed_chapters:
                logger.warning(f"失敗章節數: {len(failed_chapters)}")
                logger.warning("失敗章節列表:")
                for ch in failed_chapters[:10]:
                    logger.warning(f"- {ch['chapter_title']}")
                if len(failed_chapters) > 10:
                    logger.warning(f"... 還有 {len(failed_chapters)-10} 個失敗章節")

            # 保存小說信息
            novel_id = await db_writer.save_novel(spider.get_novel_info())
            logger.info("小說信息已保存")

            # 保存章節
            await db_writer.save_chapters(novel_id, spider.results)
            logger.info("章節已保存")

            # 更新緩存
            await cache_manager.update_novel_cache(
                url, spider.get_chapters(), CRAWLER_CONFIG["cache_expire"]
            )
            logger.info("緩存已更新")

            # 顯示數據庫統計
            chapter_count = await db_writer.get_chapter_count(novel_id)
            logger.info(f"數據庫中的章節數: {chapter_count}")

            # 檢查章節內容
            chapter_stats = await db_writer.get_chapter_stats(novel_id, 5)
            for stat in chapter_stats:
                logger.info(
                    f"章節 {stat['chapter_number']} "
                    f"內容長度: {stat['content_length']} 字符"
                )
        else:
            logger.error("爬取失敗")
    except Exception:
        logger.exception("執行過程中發生錯誤")
        raise
    finally:
        # 關閉所有連接池
        await pool_manager.close_pools()


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description="小說爬蟲")
    parser.add_argument("url", help="小說URL")
    parser.add_argument(
        "--spider", default="ttkan", choices=SPIDERS.keys(), help="爬蟲類型"
    )
    parser.add_argument("--force", action="store_true", help="強制更新，忽略緩存")

    args = parser.parse_args()

    # 設置強制更新標誌
    if args.force:
        CRAWLER_CONFIG["force_update"] = True

    try:
        asyncio.run(crawl_novel(args.url, args.spider))
    except KeyboardInterrupt:
        print("\n程序被用戶中斷")
    except Exception as e:
        print(f"程序執行出錯: {str(e)}")


if __name__ == "__main__":
    main()
