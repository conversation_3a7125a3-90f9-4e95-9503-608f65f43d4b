"""爬蟲配置"""

import logging
import os
import sys

# 數據庫配置 - 支持CI環境和生產環境
DB_CONFIG = {
    "host": os.getenv("DB_HOST") or os.getenv("POSTGRES_HOST", "localhost"),
    "port": int(os.getenv("DB_PORT") or os.getenv("POSTGRES_PORT", "5432")),
    "user": os.getenv("DB_USER") or os.getenv("POSTGRES_USER", "postgres"),
    "password": os.getenv("DB_PASSWORD") or os.getenv("POSTGRES_PASSWORD", ""),
    "dbname": os.getenv("DB_NAME") or os.getenv("POSTGRES_DB", "postgres"),
}

# 檢查必要的數據庫配置 - 在測試環境或CI環境中跳過
is_testing = (
    "test" in sys.argv
    or os.getenv("DJANGO_SETTINGS_MODULE", "").endswith("test")
    or os.getenv("CI") == "true"
    or os.getenv("DEBUG") == "true"
    or os.getenv("GITHUB_ACTIONS") == "true"  # GitHub Actions環境
    or "unittest" in sys.modules  # unittest模組載入時
    or "pytest" in sys.modules  # pytest模組載入時
)

# 完全跳過數據庫配置檢查在測試/CI環境
if not is_testing:
    # 只在生產環境檢查必要配置
    if not all([DB_CONFIG["host"], DB_CONFIG["user"]]):
        logging.critical(
            "DB_HOST/POSTGRES_HOST, DB_USER/POSTGRES_USER "
            "environment variables must be set"
        )
        sys.exit(1)
    # 密碼可以為空（某些配置下）
    if (
        not DB_CONFIG["password"]
        and os.getenv("REQUIRE_DB_PASSWORD", "true").lower() == "true"
    ):
        logging.warning(
            "DB_PASSWORD/POSTGRES_PASSWORD not set, "
            "proceeding without authentication"
        )

# Redis 配置
REDIS_CONFIG = {"host": "localhost", "port": 6379, "db": 0}

# 爬蟲配置
CRAWLER_CONFIG = {
    "max_workers": 5,
    "chunk_size": 10,
    "delay": 1.0,
    "retry_attempts": 3,
    "timeout": 30,
}

# 日誌配置
LOGGING_CONFIG = {
    "level": logging.INFO,
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "filename": "ttkan_crawler.log",
}

# Headers for request
DEFAULT_HEADERS = {
    "User-Agent": os.getenv(
        "USER_AGENT",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/91.0.4472.124 Safari/537.36",
    ),
    "Accept": (
        "text/html,application/xhtml+xml,application/xml;q=0.9,"
        "image/webp,image/apng,*/*;q=0.8"
    ),
    "Accept-Language": "zh-TW,zh;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
}
