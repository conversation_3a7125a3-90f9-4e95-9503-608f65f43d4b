#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


def apply_cli_config_overrides(argv: list[str]) -> list[str]:
    """Parse --config KEY=VALUE overrides and update environment."""
    args = list(argv)
    i = 0
    while i < len(args):
        if args[i] == "--config" and i + 1 < len(args):
            key, value = args[i + 1].split("=", 1)
            if key.isidentifier():
                os.environ[key] = value
            else:
                print(f"Invalid environment variable name: {key}", file=sys.stderr)
            del args[i : i + 2]
        else:
            i += 1
    return args


def main():
    """Run administrative tasks."""
    # 將 backend/ 設為 Python 路徑的根目錄，使 Django 能找到 novel app
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.django_settings")
    sys.argv = apply_cli_config_overrides(sys.argv)
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == "__main__":
    main()
