"""
增強的 Django 配置完整性檢查模塊
提供全面的配置驗證、性能檢查和安全審計功能
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from urllib.parse import urlparse

from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from django.core.management.utils import get_random_secret_key

from .validation import ConfigurationError, _is_test_environment

logger = logging.getLogger(__name__)


class EnhancedConfigValidator:
    """增強的配置驗證器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.suggestions = []
    
    def validate_all(self) -> Dict[str, Any]:
        """執行所有配置檢查"""
        self.errors.clear()
        self.warnings.clear()
        self.suggestions.clear()
        
        # 執行各項檢查
        self._check_media_and_static()
        self._check_logging_config()
        self._check_performance_settings()
        self._check_security_enhanced()
        self._check_api_configuration()
        self._check_internationalization()
        
        return {
            'is_valid': len(self.errors) == 0,
            'errors': self.errors,
            'warnings': self.warnings,
            'suggestions': self.suggestions,
            'summary': self._generate_summary()
        }
    
    def _check_media_and_static(self):
        """檢查媒體文件和靜態文件配置"""
        try:
            # 檢查靜態文件配置
            if not hasattr(settings, 'STATIC_URL'):
                self.errors.append("STATIC_URL 未設定")
            elif not settings.STATIC_URL:
                self.errors.append("STATIC_URL 不能為空")
            
            if not hasattr(settings, 'STATIC_ROOT'):
                self.warnings.append("STATIC_ROOT 未設定，部署時可能需要")
            elif settings.STATIC_ROOT and not Path(settings.STATIC_ROOT).is_absolute():
                self.warnings.append("STATIC_ROOT 建議使用絕對路徑")
            
            # 檢查媒體文件配置
            if not hasattr(settings, 'MEDIA_URL'):
                self.errors.append("MEDIA_URL 未設定")
            elif not settings.MEDIA_URL:
                self.errors.append("MEDIA_URL 不能為空")
            
            if not hasattr(settings, 'MEDIA_ROOT'):
                self.errors.append("MEDIA_ROOT 未設定")
            elif settings.MEDIA_ROOT and not Path(settings.MEDIA_ROOT).is_absolute():
                self.warnings.append("MEDIA_ROOT 建議使用絕對路徑")
            
            # 檢查靜態文件查找器
            if hasattr(settings, 'STATICFILES_FINDERS'):
                default_finders = [
                    'django.contrib.staticfiles.finders.FileSystemFinder',
                    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
                ]
                if not all(finder in settings.STATICFILES_FINDERS for finder in default_finders):
                    self.warnings.append("缺少預設的靜態文件查找器")
            
        except Exception as e:
            self.errors.append(f"媒體和靜態文件配置檢查失敗: {e}")
    
    def _check_logging_config(self):
        """檢查日誌配置完整性"""
        try:
            if not hasattr(settings, 'LOGGING'):
                self.warnings.append("未設定 LOGGING 配置，將使用 Django 預設配置")
                return
            
            logging_config = settings.LOGGING
            
            # 檢查基本結構
            required_keys = ['version', 'formatters', 'handlers', 'loggers']
            for key in required_keys:
                if key not in logging_config:
                    self.warnings.append(f"日誌配置缺少 '{key}' 設定")
            
            # 檢查根日誌器
            if 'root' not in logging_config.get('loggers', {}):
                self.suggestions.append("建議設定根日誌器 (root logger)")
            
            # 檢查處理器設定
            handlers = logging_config.get('handlers', {})
            if not handlers:
                self.warnings.append("未設定任何日誌處理器")
            
            # 檢查是否有檔案處理器用於生產環境
            if not _is_test_environment() and not any(
                handler.get('class', '').endswith('FileHandler') for handler in handlers.values()
            ):
                self.suggestions.append("生產環境建議使用檔案日誌處理器")
            
        except Exception as e:
            self.errors.append(f"日誌配置檢查失敗: {e}")
    
    def _check_performance_settings(self):
        """檢查性能相關配置"""
        try:
            # 檢查數據庫連接池
            if hasattr(settings, 'DATABASES'):
                for db_name, db_config in settings.DATABASES.items():
                    if 'CONN_MAX_AGE' not in db_config:
                        self.suggestions.append(f"資料庫 '{db_name}' 建議設定 CONN_MAX_AGE 以啟用連接池")
                    elif db_config.get('CONN_MAX_AGE', 0) == 0:
                        self.suggestions.append(f"資料庫 '{db_name}' 的連接池已停用，考慮啟用以提升性能")
            
            # 檢查快取配置
            if hasattr(settings, 'CACHES'):
                default_cache = settings.CACHES.get('default', {})
                if default_cache.get('BACKEND') == 'django.core.cache.backends.dummy.DummyCache':
                    self.warnings.append("使用虛擬快取後端，生產環境建議使用 Redis 或 Memcached")
            
            # 檢查中間件順序
            if hasattr(settings, 'MIDDLEWARE'):
                middleware = settings.MIDDLEWARE
                
                # 安全中間件應該在前面
                security_middleware = 'django.middleware.security.SecurityMiddleware'
                if security_middleware in middleware and middleware.index(security_middleware) > 2:
                    self.warnings.append("SecurityMiddleware 建議放在中間件列表前面")
                
                # 快取中間件順序檢查
                cache_middleware = [m for m in middleware if 'cache' in m.lower()]
                if len(cache_middleware) > 1:
                    self.suggestions.append("檢查快取中間件順序，確保正確的快取行為")
            
            # 檢查模板配置
            if hasattr(settings, 'TEMPLATES'):
                for template_config in settings.TEMPLATES:
                    options = template_config.get('OPTIONS', {})
                    if not options.get('debug') and 'loaders' not in options:
                        self.suggestions.append("生產環境建議配置模板快取載入器")
            
        except Exception as e:
            self.errors.append(f"性能配置檢查失敗: {e}")
    
    def _check_security_enhanced(self):
        """增強的安全配置檢查"""
        try:
            # SSL/TLS 配置檢查
            if not _is_test_environment():
                if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
                    self.warnings.append("生產環境建議啟用 SECURE_SSL_REDIRECT")
                
                if not getattr(settings, 'SECURE_HSTS_SECONDS', 0):
                    self.warnings.append("建議設定 SECURE_HSTS_SECONDS 啟用 HTTPS Strict Transport Security")
                
                if not getattr(settings, 'SECURE_CONTENT_TYPE_NOSNIFF', False):
                    self.warnings.append("建議啟用 SECURE_CONTENT_TYPE_NOSNIFF")
                
                if not getattr(settings, 'SECURE_BROWSER_XSS_FILTER', False):
                    self.warnings.append("建議啟用 SECURE_BROWSER_XSS_FILTER")
            
            # 會話安全檢查
            if not getattr(settings, 'SESSION_COOKIE_SECURE', False) and not _is_test_environment():
                self.warnings.append("生產環境建議啟用 SESSION_COOKIE_SECURE")
            
            if not getattr(settings, 'SESSION_COOKIE_HTTPONLY', True):
                self.warnings.append("建議啟用 SESSION_COOKIE_HTTPONLY 防止 XSS 攻擊")
            
            if getattr(settings, 'SESSION_COOKIE_AGE', 1209600) > 86400:  # 超過1天
                self.suggestions.append("考慮縮短會話過期時間以提高安全性")
            
            # CSRF 保護檢查
            if not getattr(settings, 'CSRF_COOKIE_SECURE', False) and not _is_test_environment():
                self.warnings.append("生產環境建議啟用 CSRF_COOKIE_SECURE")
            
            if not getattr(settings, 'CSRF_COOKIE_HTTPONLY', False):
                self.suggestions.append("考慮啟用 CSRF_COOKIE_HTTPONLY")
            
            # 檔案上傳安全檢查
            max_upload_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 2621440)  # 預設 2.5MB
            if max_upload_size > 10485760:  # 超過 10MB
                self.warnings.append("檔案上傳大小限制較高，注意 DoS 攻擊風險")
            
            # 密碼驗證器檢查
            if hasattr(settings, 'AUTH_PASSWORD_VALIDATORS'):
                validators = settings.AUTH_PASSWORD_VALIDATORS
                if not validators:
                    self.warnings.append("未設定密碼驗證器，建議啟用密碼強度檢查")
                elif len(validators) < 3:
                    self.suggestions.append("建議使用更全面的密碼驗證器")
            
        except Exception as e:
            self.errors.append(f"安全配置檢查失敗: {e}")
    
    def _check_api_configuration(self):
        """檢查 API 相關配置"""
        try:
            # Django REST Framework 配置檢查
            if 'rest_framework' in getattr(settings, 'INSTALLED_APPS', []):
                if not hasattr(settings, 'REST_FRAMEWORK'):
                    self.warnings.append("已安裝 DRF 但未設定 REST_FRAMEWORK 配置")
                else:
                    drf_config = settings.REST_FRAMEWORK
                    
                    # 檢查認證配置
                    if 'DEFAULT_AUTHENTICATION_CLASSES' not in drf_config:
                        self.warnings.append("未設定 DRF 預設認證類別")
                    
                    # 檢查權限配置  
                    if 'DEFAULT_PERMISSION_CLASSES' not in drf_config:
                        self.warnings.append("未設定 DRF 預設權限類別")
                    
                    # 檢查分頁配置
                    if 'DEFAULT_PAGINATION_CLASS' not in drf_config:
                        self.suggestions.append("建議設定 DRF 預設分頁類別")
                    
                    # 檢查限流配置
                    if 'DEFAULT_THROTTLE_CLASSES' not in drf_config:
                        self.suggestions.append("建議設定 API 限流以防止濫用")
                    elif 'DEFAULT_THROTTLE_RATES' not in drf_config:
                        self.warnings.append("已設定限流類別但未設定限流速率")
            
            # CORS 配置檢查
            if 'corsheaders' in getattr(settings, 'INSTALLED_APPS', []):
                if not hasattr(settings, 'CORS_ALLOWED_ORIGINS') and not hasattr(settings, 'CORS_ALLOW_ALL_ORIGINS'):
                    self.warnings.append("已安裝 django-cors-headers 但未設定允許的來源")
                elif getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', False):
                    self.warnings.append("CORS_ALLOW_ALL_ORIGINS=True 在生產環境中不安全")
            
        except Exception as e:
            self.errors.append(f"API 配置檢查失敗: {e}")
    
    def _check_internationalization(self):
        """檢查國際化和時區配置"""
        try:
            # 時區配置檢查
            if not getattr(settings, 'USE_TZ', True):
                self.warnings.append("建議啟用 USE_TZ 以正確處理時區")
            
            time_zone = getattr(settings, 'TIME_ZONE', 'UTC')
            if time_zone == 'UTC' and not _is_test_environment():
                self.suggestions.append("考慮設定適合應用程式的時區")
            
            # 國際化配置檢查
            if getattr(settings, 'USE_I18N', True):
                if not hasattr(settings, 'LANGUAGES'):
                    self.suggestions.append("啟用了國際化但未設定支援的語言")
                
                if not hasattr(settings, 'LOCALE_PATHS'):
                    self.suggestions.append("建議設定 LOCALE_PATHS 指定翻譯檔案位置")
            
            # 檢查語言程式碼
            language_code = getattr(settings, 'LANGUAGE_CODE', 'en-us')
            if language_code == 'en-us' and not _is_test_environment():
                self.suggestions.append("考慮設定適合目標用戶的語言程式碼")
            
        except Exception as e:
            self.errors.append(f"國際化配置檢查失敗: {e}")
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成檢查摘要"""
        return {
            'total_errors': len(self.errors),
            'total_warnings': len(self.warnings),
            'total_suggestions': len(self.suggestions),
            'health_score': self._calculate_health_score(),
            'priority_issues': self._get_priority_issues()
        }
    
    def _calculate_health_score(self) -> int:
        """計算配置健康分數 (0-100)"""
        base_score = 100
        
        # 錯誤扣分較重
        error_penalty = len(self.errors) * 15
        # 警告扣分中等
        warning_penalty = len(self.warnings) * 5
        # 建議扣分較輕
        suggestion_penalty = len(self.suggestions) * 2
        
        score = max(0, base_score - error_penalty - warning_penalty - suggestion_penalty)
        return score
    
    def _get_priority_issues(self) -> List[str]:
        """獲取優先處理的問題"""
        priority_keywords = ['安全', '密碼', 'SECRET_KEY', 'SSL', 'CSRF', 'XSS']
        priority_issues = []
        
        for error in self.errors:
            if any(keyword in error for keyword in priority_keywords):
                priority_issues.append(f"[錯誤] {error}")
        
        for warning in self.warnings:
            if any(keyword in warning for keyword in priority_keywords):
                priority_issues.append(f"[警告] {warning}")
        
        return priority_issues[:5]  # 返回前5個優先問題


def validate_enhanced_configuration() -> Dict[str, Any]:
    """執行增強的配置檢查"""
    validator = EnhancedConfigValidator()
    return validator.validate_all()


def get_configuration_report() -> str:
    """獲取格式化的配置報告"""
    result = validate_enhanced_configuration()
    
    report = []
    report.append("=== Django 配置完整性檢查報告 ===\n")
    
    # 摘要
    summary = result['summary']
    report.append(f"健康分數: {summary['health_score']}/100")
    report.append(f"錯誤: {summary['total_errors']} | 警告: {summary['total_warnings']} | 建議: {summary['total_suggestions']}\n")
    
    # 優先問題
    if summary['priority_issues']:
        report.append("🚨 優先處理問題:")
        for issue in summary['priority_issues']:
            report.append(f"  • {issue}")
        report.append("")
    
    # 錯誤
    if result['errors']:
        report.append("❌ 配置錯誤:")
        for error in result['errors']:
            report.append(f"  • {error}")
        report.append("")
    
    # 警告
    if result['warnings']:
        report.append("⚠️  配置警告:")
        for warning in result['warnings']:
            report.append(f"  • {warning}")
        report.append("")
    
    # 建議
    if result['suggestions']:
        report.append("💡 優化建議:")
        for suggestion in result['suggestions']:
            report.append(f"  • {suggestion}")
        report.append("")
    
    if result['is_valid']:
        report.append("✅ 配置檢查通過！")
    else:
        report.append("❌ 發現配置問題，請檢查上述錯誤。")
    
    return "\n".join(report) 