"""
NovelWebsite 統一配置管理模組

基於 pydantic-settings 的現代化配置管理系統，提供：
- 類型安全的配置載入
- 環境變數自動驗證
- 開發/生產環境分離
- 向後兼容的配置介面
"""

from .validation import validate_config, ConfigurationError
from .base import ENVIRONMENT_KEY


def get_legacy_db_config():
    from .legacy import get_legacy_db_config as _func

    return _func()


def get_legacy_redis_config():
    from .legacy import get_legacy_redis_config as _func

    return _func()


__version__ = "1.0.0"
__all__ = [
    "validate_config",
    "ConfigurationError",
]


# 配置模組自動驗證
def _validate_on_import():
    """在模組導入時進行配置驗證"""
    try:
        validate_config()
    except ConfigurationError as e:
        import sys
        import os

        # 僅在非測試環境和主程序執行時才強制退出
        is_test = any(
            test_indicator in sys.argv[0]
            for test_indicator in ["pytest", "test", "unittest"]
        )
        is_interactive = hasattr(sys, "ps1") or bool(sys.flags.interactive)
        is_development = (
            os.getenv("DJANGO_SETTINGS_MODULE", "").endswith(".test")
            or os.getenv(ENVIRONMENT_KEY) == "test"
        )

        if not (is_test or is_interactive or is_development):
            print(f"❌ 配置驗證失敗: {e}")
            print("🔧 請檢查環境變數設定或參考 .env.example")
            sys.exit(1)
        else:
            # 在測試或開發環境中，僅發出警告而不退出
            import warnings

            warnings.warn(
                f"配置驗證失敗: {e}. 在測試/開發環境中繼續執行。", UserWarning
            )


# 執行驗證 - 暫時禁用以允許測試
# _validate_on_import()
