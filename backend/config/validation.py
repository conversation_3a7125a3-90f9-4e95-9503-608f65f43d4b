"""
配置驗證模組
"""

import os

from .base import ENVIRONMENT_KEY


class ConfigurationError(Exception):
    """配置錯誤異常"""

    pass


def validate_config() -> None:
    """
    驗證配置完整性

    在測試或開發環境中發出警告
    在生產環境中拋出異常
    """
    errors = []
    warnings = []

    # 檢查關鍵環境變數
    critical_vars = [
        "SECRET_KEY",
        "DB_PASSWORD" if os.getenv(ENVIRONMENT_KEY) == "production" else None,
    ]

    for var in critical_vars:
        if var and not os.getenv(var):
            errors.append(f"缺少關鍵環境變數: {var}")

    # 檢查安全設定
    if (
        os.getenv("DEBUG", "").lower() == "true"
        and os.getenv(ENVIRONMENT_KEY) == "production"
    ):
        errors.append("生產環境不應啟用 DEBUG 模式")

    if (
        os.getenv("CORS_ALLOW_ALL_ORIGINS", "").lower() == "true"
        and os.getenv(ENVIRONMENT_KEY) == "production"
    ):
        errors.append("生產環境不應允許所有 CORS 來源")

    # 檢查憑證安全性
    secret_key = os.getenv("SECRET_KEY", "")
    if secret_key == "django-insecure-change-me-in-production":
        if os.getenv(ENVIRONMENT_KEY) == "production":
            errors.append("生產環境必須更換預設的 SECRET_KEY")
        else:
            warnings.append("建議更換預設的 SECRET_KEY")

    # 處理錯誤和警告
    if warnings:
        for warning in warnings:
            print(f"⚠️  配置警告: {warning}")

    if errors:
        error_message = "配置驗證失敗:\n" + "\n".join(f"- {error}" for error in errors)

        # 在測試環境中只發出警告
        if _is_test_environment():
            print(f"⚠️  {error_message}")
            return

        raise ConfigurationError(error_message)


def _is_test_environment() -> bool:
    """檢測是否為測試環境"""
    # 檢查常見的測試環境指標
    test_indicators = [
        os.getenv("DJANGO_SETTINGS_MODULE", "").endswith(".test"),
        "pytest" in os.getenv("_", ""),
        "unittest" in os.getenv("_", ""),
        bool(os.getenv("CI")),  # CI 環境
        "test" in os.getenv(ENVIRONMENT_KEY, "").lower(),
    ]

    return any(test_indicators)
