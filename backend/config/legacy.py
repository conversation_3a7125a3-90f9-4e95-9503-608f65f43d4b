"""
向後兼容的配置支援
"""

from .settings import settings


def get_legacy_db_config() -> dict:
    """
    返回傳統格式的資料庫配置

    用於需要直接存取資料庫配置字典的舊代碼
    """
    return settings.database.postgres_config_dict


def get_legacy_redis_config() -> dict:
    """
    返回傳統格式的 Redis 配置

    用於需要直接存取 Redis 配置的舊代碼
    """
    return {
        "host": settings.cache.redis_host,
        "port": settings.cache.redis_port,
        "db": settings.cache.redis_db,
        "password": settings.cache.redis_password,
        "url": settings.cache.redis_connection_url,
    }


def get_legacy_cache_config() -> dict:
    """
    返回傳統格式的 Django 快取配置
    """
    return settings.cache.django_cache_config


def get_legacy_celery_config() -> dict:
    """
    返回傳統格式的 Celery 配置
    """
    return {
        "broker_url": settings.cache.celery_broker_url_resolved,
        "result_backend": settings.cache.celery_result_backend_resolved,
    }
