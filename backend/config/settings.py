"""
統一配置管理 - 主要設定模組
"""

from functools import lru_cache

from pydantic import Field

from .base import BaseConfig, SecurityConfig, EnvironmentConfig
from .database import DatabaseConfig
from .cache import CacheConfig


class Settings(BaseConfig):
    """
    統一配置管理類別

    整合所有配置類別，提供單一設定介面
    """

    # 環境配置
    environment: EnvironmentConfig = Field(
        default_factory=EnvironmentConfig,
        validation_alias="__ENVIRONMENT__",
    )

    # 安全配置
    security: SecurityConfig = SecurityConfig()

    # 資料庫配置
    database: DatabaseConfig = DatabaseConfig()

    # 快取配置
    cache: CacheConfig = CacheConfig()

    @property
    def is_development(self) -> bool:
        """是否為開發環境"""
        return self.environment.is_development

    @property
    def is_production(self) -> bool:
        """是否為生產環境"""
        return self.environment.is_production

    @property
    def debug(self) -> bool:
        """Debug 模式"""
        return self.security.debug

    @property
    def secret_key(self) -> str:
        """Django Secret Key"""
        return self.security.secret_key


# 全域設定實例
@lru_cache()
def get_settings() -> "Settings":
    """取得全域設定單例"""
    return Settings()


settings = get_settings()
