"""
配置管理基礎類別
"""

from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, field_validator
from typing import List
import os

# Unified key for application environment
ENVIRONMENT_KEY = "ENVIRONMENT"


def _env_files() -> list[str]:
    """計算環境變數檔案載入順序"""
    current = os.getenv(ENVIRONMENT_KEY)
    files = [".env", ".env.local"]
    if current:
        files.append(f".env.{current}")
    return files


class BaseSettingsConfig(BaseSettings):
    """提供統一環境變數載入與驗證的基礎類別"""

    model_config = SettingsConfigDict(
        env_file_encoding="utf-8",
        extra="ignore",
        case_sensitive=False,
        str_strip_whitespace=True,
        env_parse_enums=False,
        enable_decoding=False,
    )

    def __init__(self, **data):
        super().__init__(**data, _env_file=_env_files())

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        """調整設定讀取來源順序，使 .env 優先於環境變數"""
        return (
            dotenv_settings,
            env_settings,
            file_secret_settings,
            init_settings,
        )


class BaseConfig(BaseSettingsConfig):
    """基礎配置類別"""

    @field_validator("*", mode="before")
    @classmethod
    def empty_str_to_none(cls, v):
        """將空字串轉換為 None"""
        if v == "":
            return None
        return v


class SecurityConfig(BaseConfig):
    """安全配置"""

    secret_key: str = Field(
        default="django-insecure-change-me-in-production",
        env="SECRET_KEY",
        description="Django secret key",
    )

    debug: bool = Field(
        default=False, env="DEBUG", description="Debug mode (production-first approach)"
    )

    allowed_hosts: str = Field(
        default="localhost,127.0.0.1,0.0.0.0,backend",
        env="ALLOWED_HOSTS",
        description="Django allowed hosts (comma-separated) - includes Docker service name",
    )

    # CORS 配置
    cors_allow_all_origins: bool = Field(
        default=False,
        env="CORS_ALLOW_ALL_ORIGINS",
        description="Allow all CORS origins (development only)",
    )

    cors_allowed_origins: str = Field(
        default=(
            "http://localhost:8000,http://127.0.0.1:8000,"
            "http://localhost:3000,http://127.0.0.1:3000,"
            "http://localhost:3001,http://127.0.0.1:3001"
        ),
        env="CORS_ALLOWED_ORIGINS",
        description="Allowed CORS origins - includes CRA (3000) and Next.js (3001)",
    )

    csrf_trusted_origins: str = Field(
        default=(
            "http://localhost:8000,http://127.0.0.1:8000,"
            "http://localhost:3000,http://127.0.0.1:3000,"
            "http://localhost:3001,http://127.0.0.1:3001"
        ),
        env="CSRF_TRUSTED_ORIGINS",
        description="CSRF trusted origins - synchronized with CORS",
    )

    # Admin 安全
    admin_url: str = Field(
        default="admin/", env="ADMIN_URL", description="Django admin URL path"
    )

    @property
    def allowed_hosts_list(self) -> List[str]:
        """將 allowed_hosts 字符串轉為列表"""
        return [item.strip() for item in self.allowed_hosts.split(",") if item.strip()]

    @property
    def cors_allowed_origins_list(self) -> List[str]:
        """將 cors_allowed_origins 字符串轉為列表"""
        return [
            item.strip()
            for item in self.cors_allowed_origins.split(",")
            if item.strip()
        ]

    @property
    def csrf_trusted_origins_list(self) -> List[str]:
        """將 csrf_trusted_origins 字符串轉為列表"""
        return [
            item.strip()
            for item in self.csrf_trusted_origins.split(",")
            if item.strip()
        ]

    @field_validator("secret_key")
    @classmethod
    def validate_secret_key(cls, v):
        """驗證 secret key 安全性"""
        if not v or v == "django-insecure-change-me-in-production":
            if os.getenv(ENVIRONMENT_KEY) == "production":
                raise ValueError("生產環境必須設定安全的 SECRET_KEY")
        return v


class EnvironmentConfig(BaseConfig):
    """環境配置"""

    environment: str = Field(
        default="development",
        env=ENVIRONMENT_KEY,
        description="Application environment (development/staging/production)",
    )

    timezone: str = Field(
        default="Asia/Taipei", env="TIME_ZONE", description="Application timezone"
    )

    language_code: str = Field(
        default="zh-hant", env="LANGUAGE_CODE", description="Application language"
    )

    @property
    def is_development(self) -> bool:
        """是否為開發環境"""
        return self.environment == "development"

    @property
    def is_production(self) -> bool:
        """是否為生產環境"""
        return self.environment == "production"

    @property
    def is_staging(self) -> bool:
        """是否為測試環境"""
        return self.environment == "staging"

    @property
    def is_ci(self) -> bool:
        """是否為 CI 環境"""
        return self.environment == "ci"
