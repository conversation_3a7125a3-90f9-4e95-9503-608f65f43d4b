"""
Django核心配置 - 整合現代Pydantic配置與Django標準配置
"""

import os
from pathlib import Path

# 導入現代配置系統
from .settings import settings

# Build paths inside the project like this: BASE_DIR / 'subdir'.
# Adjusted for monorepo structure: point to project root instead of backend/
BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = settings.secret_key

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = settings.debug

ALLOWED_HOSTS = (
    settings.security.allowed_hosts.split(",")
    if settings.security.allowed_hosts
    else []
)

# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Third party apps
    "rest_framework",
    "corsheaders",
    # Local apps - standardized naming
    "apps.catalog",  # Data models & API
    "crawler_engine",  # Independent crawler engine (renamed from novel.crawler)
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

# Database - 使用現代配置系統
DATABASES = settings.database.django_database_config

# Cache - 使用現代配置系統
CACHES = settings.cache.django_cache_config

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation."
        "UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
LANGUAGE_CODE = "zh-hant"
TIME_ZONE = "Asia/Taipei"
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / "backend" / "staticfiles"

MEDIA_URL = "/media/"
MEDIA_ROOT = BASE_DIR / "backend" / "media"

# Default primary key field type
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# CORS settings - 使用現代配置系統
CORS_ALLOW_ALL_ORIGINS = settings.security.cors_allow_all_origins
CORS_ALLOWED_ORIGINS = (
    settings.security.cors_allowed_origins.split(",")
    if settings.security.cors_allowed_origins
    else []
)

# CSRF settings - 與 CORS 同步
CSRF_TRUSTED_ORIGINS = (
    settings.security.csrf_trusted_origins.split(",")
    if settings.security.csrf_trusted_origins
    else []
)

# REST Framework
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticatedOrReadOnly",
    ],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 20,
    "DEFAULT_THROTTLE_RATES": {
        "anon": "100/hour",
        "user": "1000/hour",
        "burst": "20/minute",
    },
}

# Admin URL - 可通過環境變數配置
ADMIN_URL = os.getenv("ADMIN_URL", "admin/")

# Logging
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
        "simple": {
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "INFO",
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "catalog": {
            "handlers": ["console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "crawler_engine": {
            "handlers": ["console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
    },
}

# [FIX] 在非 CI 環境中添加文件日誌
if os.getenv("ENVIRONMENT") != "ci":
    # 確保日誌目錄存在
    logs_dir = BASE_DIR / "logs"
    logs_dir.mkdir(exist_ok=True)

    LOGGING["handlers"]["file"] = {
        "level": "INFO",
        "class": "logging.FileHandler",
        "filename": logs_dir / "django.log",
        "formatter": "verbose",
    }
    # 為所有處理器添加文件日誌
    for logger_config in LOGGING["loggers"].values():
        logger_config["handlers"].append("file")
    LOGGING["root"]["handlers"].append("file")
else:
    # CI 環境：只使用控制台日誌，不創建文件日誌
    pass
