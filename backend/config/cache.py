"""
快取系統配置管理
"""

from pydantic import Field, field_validator
from typing import Optional
from .base import BaseConfig


class CacheConfig(BaseConfig):
    """
    統一快取配置 (Redis)

    支援 REDIS_URL 或分離的環境變數配置
    """

    # Redis 配置
    redis_host: str = Field(
        default="localhost", env="REDIS_HOST", description="Redis host"
    )

    redis_port: int = Field(
        default=6379, ge=1, le=65535, env="REDIS_PORT", description="Redis port"
    )

    redis_db: int = Field(
        default=0, ge=0, le=15, env="REDIS_DB", description="Redis database number"
    )

    redis_password: Optional[str] = Field(
        default=None, env="REDIS_PASSWORD", description="Redis password (optional)"
    )

    # URL 覆蓋選項 (優先使用)
    redis_url: Optional[str] = Field(
        default=None,
        env="REDIS_URL",
        description="Complete Redis URL (overrides individual settings)",
    )

    # 連接池設定
    redis_max_connections: int = Field(
        default=20,
        ge=1,
        le=100,
        env="REDIS_MAX_CONNECTIONS",
        description="Redis connection pool size",
    )

    redis_socket_timeout: float = Field(
        default=5.0,
        gt=0,
        env="REDIS_SOCKET_TIMEOUT",
        description="Redis socket timeout (seconds)",
    )

    # 快取過期時間 (秒)
    cache_default_timeout: int = Field(
        default=3600,  # 1 hour
        ge=0,
        env="CACHE_DEFAULT_TIMEOUT",
        description="Default cache timeout (seconds)",
    )

    # Celery 配置
    celery_broker_url: Optional[str] = Field(
        default=None, env="CELERY_BROKER_URL", description="Celery broker URL"
    )

    celery_result_backend: Optional[str] = Field(
        default=None,
        env="CELERY_RESULT_BACKEND",
        description="Celery result backend URL",
    )

    @property
    def redis_connection_url(self) -> str:
        """
        生成 Redis 連接字串

        優先使用 REDIS_URL，否則從個別環境變數組合
        """
        if self.redis_url:
            return self.redis_url

        # 構建連接字串
        auth = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth}{self.redis_host}:{self.redis_port}/{self.redis_db}"

    @property
    def celery_broker_url_resolved(self) -> str:
        """解析 Celery broker URL，預設使用 Redis"""
        return self.celery_broker_url or self.redis_connection_url

    @property
    def celery_result_backend_resolved(self) -> str:
        """解析 Celery result backend URL，預設使用 Redis"""
        return self.celery_result_backend or self.redis_connection_url

    @property
    def django_cache_config(self) -> dict:
        """
        返回 Django CACHES 配置字典

        使用內建的 LocMemCache 作為開發環境的預設快取
        生產環境可以通過環境變數切換到 Redis
        """
        # 檢查是否有 Redis URL 配置，如果有則嘗試使用 Redis
        if self.redis_url or (self.redis_host != "localhost"):
            try:
                # 嘗試導入 django_redis，如果可用則使用 Redis
                import django_redis  # noqa: F401

                return {
                    "default": {
                        "BACKEND": "django_redis.cache.RedisCache",
                        "LOCATION": self.redis_connection_url,
                        "OPTIONS": {
                            "CLIENT_CLASS": "django_redis.client.DefaultClient",
                            "CONNECTION_POOL_KWARGS": {
                                "max_connections": self.redis_max_connections,
                                "socket_timeout": self.redis_socket_timeout,
                            },
                        },
                        "TIMEOUT": self.cache_default_timeout,
                    }
                }
            except ImportError:
                # 如果 django_redis 不可用，回退到內存快取
                pass

        # 使用 Django 內建的內存快取作為預設
        return {
            "default": {
                "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
                "LOCATION": "unique-snowflake",
                "TIMEOUT": self.cache_default_timeout,
                "OPTIONS": {
                    "MAX_ENTRIES": 1000,
                    "CULL_FREQUENCY": 3,
                },
            }
        }

    @field_validator("redis_url", "celery_broker_url", "celery_result_backend")
    @classmethod
    def validate_redis_url(cls, v):
        """驗證 Redis URL 格式"""
        if v and not v.startswith("redis://"):
            raise ValueError("Redis URL 必須以 'redis://' 開頭")
        return v
