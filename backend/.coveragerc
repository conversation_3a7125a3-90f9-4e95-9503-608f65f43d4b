[run]
source = .
omit =
    */venv/*
    */migrations/*
    */tests/*
    */test_*
    manage.py
    */settings/*
    */wsgi.py
    */asgi.py
    */urls.py
    */admin.py
    */apps.py
    */conftest.py
    */node_modules/*
    */static/*
    */media/*
    */logs/*
    */coverage/*
    */htmlcov/*
    */__pycache__/*
    */.*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[html]
directory = htmlcov
title = Novel Website Coverage Report

[xml]
output = coverage.xml
