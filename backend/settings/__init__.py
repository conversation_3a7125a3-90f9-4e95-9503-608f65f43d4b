"""
Django Settings Module for Monorepo + T3 Stack

This module provides environment-aware Django settings configuration
optimized for monorepo structure with Next.js and CRA frontend integration.

Environment Detection:
- DJANGO_ENV=development -> dev.py
- DJANGO_ENV=production -> prod.py
- DJANGO_ENV=testing -> testing.py
- Default -> dev.py

Usage:
    export DJANGO_SETTINGS_MODULE=settings
    python manage.py runserver
"""

import os

# Detect environment
DJANGO_ENV = os.getenv("DJANGO_ENV", "development").lower()

# Import appropriate settings
if DJANGO_ENV == "production":
    from .prod import *  # noqa: F403,F401
elif DJANGO_ENV == "testing":
    from .testing import *  # noqa: F403,F401
else:
    from .dev import *  # noqa: F403,F401

# Expose current environment
CURRENT_ENV = DJANGO_ENV
