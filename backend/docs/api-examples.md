# Novel List API 使用範例

## 基本用法

### 1. 獲取小說列表 (默認分頁)

```bash
GET /api/novels
```

回應：

```json
{
  "novels": [
    {
      "id": "wenxian",
      "title": "瘟仙",
      "author": "黑山老鬼",
      "description": "一個普通的實習醫生...",
      "cover_url": "/wenxian-cover.jpg",
      "status": "ongoing",
      "total_chapters": 1000,
      "updated_at": "2024-01-01T00:00:00Z",
      "source": "ttkan"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPrevPage": false
  },
  "filters": {
    "status": null,
    "author": null,
    "search": "",
    "sortBy": "updated_at",
    "sortOrder": "DESC"
  }
}
```

### 2. 分頁查詢

```bash
GET /api/novels?page=2&limit=5
```

### 3. 搜索小說

```bash
GET /api/novels?search=瘟仙
```

### 4. 按作者篩選

```bash
GET /api/novels?author=黑山老鬼
```

### 5. 按狀態篩選

```bash
GET /api/novels?status=ongoing
```

### 6. 排序

```bash
# 按標題排序（升序）
GET /api/novels?sortBy=title&sortOrder=asc

# 按更新時間排序（降序）
GET /api/novels?sortBy=updated_at&sortOrder=desc

# 按章節數排序
GET /api/novels?sortBy=total_chapters&sortOrder=desc
```

### 7. 組合查詢

```bash
GET /api/novels?search=仙&status=ongoing&sortBy=updated_at&sortOrder=desc&page=1&limit=20
```

## 可用的排序欄位

- `title` - 標題
- `author` - 作者
- `updated_at` - 更新時間（默認）
- `total_chapters` - 章節數
- `status` - 狀態

## 可用的狀態值

- `ongoing` - 連載中
- `completed` - 已完結
- `hiatus` - 暫停更新

## 錯誤處理

### 無效的分頁參數

```bash
GET /api/novels?page=0
```

回應：

```json
{
  "error": "Page must be greater than 0"
}
```

### 無效的排序欄位

```bash
GET /api/novels?sortBy=invalid_field
```

回應：

```json
{
  "error": "Invalid sortBy parameter",
  "validFields": ["title", "author", "updated_at", "total_chapters", "status"]
}
```

### 限制說明

- `limit` 最大值為 50
- `search` 查詢長度限制為 100 字符
- 搜索會同時匹配標題和描述
