# Development Dependencies for NovelWebsite
# Install with: pip install -r requirements-dev.txt

# Code Quality and Formatting
black==24.3.0
flake8==7.0.0
isort==5.13.2

# Type Checking
mypy==1.9.0
mypy-extensions==1.1.0

# Testing Tools
pytest==8.1.1
pytest-django==4.8.0
pytest-cov==4.0.0
pytest-xdist==3.5.0

# Development Tools
pre-commit==3.7.0
ipython==8.22.2
ipdb==0.13.13

# Documentation
sphinx==7.3.7
sphinx-rtd-theme==2.0.0

# Security
safety==3.1.0
bandit==1.7.8

# Performance Profiling
django-debug-toolbar==4.3.0
memory-profiler==0.61.0
