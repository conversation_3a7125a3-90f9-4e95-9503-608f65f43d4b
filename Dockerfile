# Monorepo Optimized Dockerfile for NovelWebsite
# 支援 pnpm workspace + Turborepo 架構

# ========================================
# 1. Base image
# ========================================
FROM node:18-alpine AS base
WORKDIR /app

# 安裝系統依賴
RUN apk add --no-cache libc6-compat git

# 啟用 corepack 以支援 pnpm
RUN corepack enable

# ========================================
# 2. Install dependencies
# ========================================
FROM base AS deps
WORKDIR /app

# 首先只複製依賴定義文件
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/web/package.json ./apps/web/package.json

# 安裝所有依賴（包括 devDependencies，因為構建時需要）
RUN pnpm install --frozen-lockfile

# ========================================
# 3. Build the application
# ========================================
FROM base AS builder
WORKDIR /app

# 複製依賴
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/web/node_modules ./apps/web/node_modules

# 複製源碼和配置
COPY . .

# 讓 Turborepo 智能地構建 web 應用
RUN pnpm turbo build --filter=@novelwebsite/web

# ========================================
# 4. Final production image
# ========================================
FROM base AS runner
WORKDIR /app

# 創建非 root 用戶
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 複製構建產物
COPY --from=builder /app/apps/web/build ./apps/web/build
COPY --from=builder /app/apps/web/public ./apps/web/public
COPY --from=builder /app/apps/web/package.json ./apps/web/package.json

# 複製根目錄配置
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/pnpm-workspace.yaml ./pnpm-workspace.yaml

# 只安裝生產依賴
COPY --from=deps /app/node_modules ./node_modules

# 設置用戶權限
USER nextjs

# 暴露端口
EXPOSE 3000

# 設置環境變數
ENV NODE_ENV=production
ENV PORT=3000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# 啟動應用
CMD ["npx", "serve", "-s", "apps/web/build", "-l", "3000"]
