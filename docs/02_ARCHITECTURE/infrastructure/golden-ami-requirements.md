# 黃金 AMI 需求規格 (Golden AMI Requirements)

## 📋 概述

基於當前手動配置經驗，制定自動化的「黃金 AMI」需求，目標是實現新 Spot Instance 在 1-2 分鐘內自動上線。

## 🏗️ 基礎系統環境

### 作業系統基礎

- **基礎 AMI**: Amazon Linux 2023.7.20250609
- **架構**: x86_64
- **核心版本**: 6.1.140-154.222.amzn2023
- **實例類型**: t3.medium (4GB RAM, 2 vCPU)
- **儲存**: 8GB EBS gp3

### 必要系統依賴

```bash
# ICU 國際化支援 (修復 .NET Core 6.0 依賴問題)
icu-67.1-7.amzn2023.0.3.x86_64
libicu-67.1-7.amzn2023.0.3.x86_64

# 開發工具
git-2.47.1-1.amzn2023.0.3.x86_64
docker-25.0.8-1.amzn2023.0.4.x86_64
nodejs-18.20.8-1.amzn2023.0.1.x86_64
nodejs-npm-10.8.2-*********.1.amzn2023.0.1.x86_64
```

## 🤖 GitHub Actions Runner 配置

### 預安裝軟體

- **Runner 版本**: v2.325.0
- **安裝路徑**: `/home/<USER>/actions-runner/`
- **執行用戶**: `ec2-user`
- **服務名稱**: `actions.runner.MumuTW-novel-web.ip-172-31-46-95.service`

### 檔案結構

```
/home/<USER>/actions-runner/
├── bin/                    # Runner 執行檔
├── externals/              # 外部依賴 (包含 node20)
├── config.sh               # 配置腳本
├── svc.sh                  # 服務管理腳本
└── actions-runner-linux-x64-2.325.0.tar.gz  # 原始壓縮檔
```

## 🔧 系統配置需求

### 用戶與權限

- **主要用戶**: `ec2-user` (UID: 1000, GID: 1000)
- **sudo 權限**: 已配置免密碼 sudo
- **服務權限**: 可管理 systemd 服務

### 網路配置

- **SSH 配置**: 允許 PEM 金鑰登入
- **防火牆**: 允許 SSH (port 22) 和 HTTP/HTTPS 出站
- **DNS**: 配置 GitHub 和相關服務的解析

### 儲存配置

- **根分區**: 8GB (目前使用 70%)
- **暫存目錄**: `/tmp` (tmpfs, 1.9G)
- **工作目錄**: `/home/<USER>/` 有足夠空間

## 🚀 自動化初始化需求

### User Data 腳本職責

AMI 啟動後，User Data 腳本只需執行以下最小化配置：

1. **動態註冊**: 從 GitHub API 獲取即時 Registration Token
2. **Runner 配置**: 執行 `./config.sh --url https://github.com/MumuTW/novel-web --token <DYNAMIC_TOKEN> --unattended`
3. **服務啟動**: 執行 `sudo ./svc.sh install && sudo ./svc.sh start`
4. **健康檢查**: 確認服務狀態和 GitHub 連接

### 環境變數

```bash
GITHUB_REPO_URL="https://github.com/MumuTW/novel-web"
RUNNER_NAME_PREFIX="ip-$(hostname -I | cut -d' ' -f1 | tr '.' '-')"
RUNNER_WORK_DIR="_work"
```

## 📦 Docker 支援

### 容器化準備

- **Docker Engine**: 25.0.8 已預安裝
- **Docker Compose**: 待確認版本需求
- **用戶群組**: `ec2-user` 加入 `docker` 群組
- **服務狀態**: Docker daemon 開機自啟

### 映像預拉取 (可選)

為加速 CI/CD 流程，可預拉取常用映像：

```bash
docker pull node:18-alpine
docker pull python:3.11-slim
docker pull nginx:alpine
```

## 🔒 安全考量

### 最小權限原則

- **IAM 角色**: 只授予必要的 EC2 和 GitHub API 權限
- **網路存取**: 最小化入站規則，僅允許必要出站流量
- **金鑰管理**: 使用 AWS Secrets Manager 或 Parameter Store

### 監控與日誌

- **CloudWatch Agent**: 收集系統指標和日誌
- **Runner 日誌**: 集中收集到 CloudWatch Logs
- **健康檢查**: 定期檢查服務狀態

## 💡 優化建議

### 效能調優

- **EBS 優化**: 使用 gp3 並調整 IOPS
- **網路效能**: 啟用 Enhanced Networking
- **CPU 效能**: 考慮使用計算優化實例類型

### 成本優化

- **Spot Instance**: 已使用，需考慮中斷處理
- **自動擴縮**: 根據 CI/CD 需求動態調整
- **定期清理**: 自動清理舊的構建快取

## ✅ 驗證清單

### AMI 構建後驗證

- [ ] 系統依賴完整安裝
- [ ] GitHub Actions Runner 軟體就位
- [ ] Docker 服務正常運行
- [ ] 網路連通性測試
- [ ] 磁碟空間充足
- [ ] 用戶權限配置正確

### 自動化測試

- [ ] User Data 腳本執行無誤
- [ ] Runner 成功註冊到 GitHub
- [ ] 服務狀態健康
- [ ] 可執行簡單 CI 任務
- [ ] 日誌收集正常

## 📅 實施計畫

### Phase 1: Packer 腳本開發

- 研究 Packer 最佳實踐
- 編寫自動化構建腳本
- 本地測試和驗證

### Phase 2: User Data 腳本

- 開發動態註冊邏輯
- 實現健康檢查機制
- 錯誤處理和重試邏輯

### Phase 3: 整合與部署

- 整合到現有 IaC (Terraform/CloudFormation)
- 設置自動化構建管道
- 生產環境測試

---

**創建日期**: 2025-06-22
**最後更新**: 2025-06-22
**狀態**: Draft - 待評審
