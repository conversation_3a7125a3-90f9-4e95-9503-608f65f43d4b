# 小說網站專案結構詳細註解 (完整版)

*最後更新: 2025-06-27 - 基於遠端 main 穩定版本同步 (commit: 2033946d7)*

## 📋 專案概覽

### 📁 前端目錄說明
- **apps/web-next/**: Next.js 15 主線版本 (活躍開發)
- **apps/web-sunset/**: CRA 日落版本，等待完全遷移後移除
- **frontend/**: Legacy CRA + Storybook 資料，已 sunset，不納入 Docker build

⚠️ **重要**: Dockerfile 和 CI 僅使用 `apps/web-next/`，`frontend/` 目錄已在 `.dockerignore` 中排除

**重構狀態**: ✅ Phase 1-7 完成，遺留模組清理完成，系統達到企業級穩定性

- **多層次測試策略**: Unit → Integration → E2E → Visual Regression
- **現代化 CI/CD**: Tier 2 架構，1秒極速執行，100% 穩定性
- **Docker 優化**: Multi-stage builds，ECR 映像儲存，故障修復完成
- **依賴優化**: Node.js 從 1.3GB → 769MB (節省 531MB, 87,258 個檔案)
- **Django 架構統一**: 遺留模組已清理，統一使用 config 配置架構
- **統計**: 188 個目錄, 647 個檔案 (Monorepo 架構完成)

### 🎯 重構完成狀態

#### Phase 1: 檔案結構清理 ✅
- **移除**: 重複嵌套 `frontend/frontend/` 目錄
- **清理**: 17個廢棄 .bak 檔案移至 `archive/legacy-code-backup/`
- **整合**: TXT 檔案轉換為結構化 Markdown

#### Phase 2: CI/CD 工作流程優化 ✅
- **刪除**: 6個實驗性 GitHub Actions workflow
- **整合**: lighthouse 和 percy 品質檢查合併至主 CI
- **文檔**: 完整 CI/CD 策略文檔化

#### Phase 3: 程式碼品質提升 ✅
- **DRY 原則**: 使用可重用 Docker Action，減少 35% 重複程式碼
- **效能**: CI 執行時間穩定在 8 秒
- **監控**: 整合 Prometheus 指標和品質檢查

#### Phase 4: Node.js 依賴優化 ✅
- **移除臃腫包**: @mui/icons-material (128MB), @heroicons/react (21MB)
- **清理無用依賴**: vue-template-compiler
- **圖示優化**: 使用內聯 SVG 取代龐大圖示庫
- **效果**: 節省 531MB, 87,258 個檔案 (41% 減少)

## 🏗️ 核心架構

```
├── backend/          Django 後端 (APIs + 爬蟲引擎)
├── frontend/         React 前端 (用戶界面)
├── infra/           基礎設施配置 (Docker + 監控)
├── scripts/         自動化腳本和工具
├── docs/            項目文檔
└── tests/           整合測試
```

## 🧪 測試策略 (多層次)

| 命令 | 說明 |
|------|------|
| `make test-backend` | Django 單元測試 |
| `make test-frontend` | React/Jest 測試 |
| `make test-integration` | 程式碼層面整合測試 |
| `make test-e2e` | Playwright 端到端測試 |
| `make ci-check` | 完整本地 CI 流程 |

## 🔐 安全與品質

- **Pre-commit hooks** 確保代碼品質
- **敏感信息掃描** (scripts/check_sensitive.sh)
- **多種 CI/CD 工作流程** (.github/workflows/)
- **Doppler 密鑰管理整合**

---

## 📁 詳細檔案結構

### 🤖 AI 開發工具配置

```
.claude/                          # Claude AI 配置和設定
├── settings.json                 # Claude 全域設定
└── settings.local.json           # 本地 Claude 設定

.cursor/                          # Cursor 編輯器配置
├── rules/                        # Cursor 規則檔案
│   ├── cursor_rules.mdc
│   ├── dev_workflow.mdc
│   ├── self_improve.mdc
│   └── taskmaster.mdc
├── mcp.json                      # MCP 配置
└── mcp.json.template            # MCP 配置模板
```

### ⚙️ GitHub Actions CI/CD

```
.github/workflows/
├── main-ci.yml                   # 主要 CI 流程 (Tier 2 - 極速 8秒)
├── build-and-push-images.yml     # Docker 映像構建推送
├── tier2-full-test.yml          # Tier 2 完整測試套件
├── docs.yml                     # 文檔生成
├── lighthouse.yml               # 性能測試
├── percy.yml                    # 視覺回歸測試
└── weekly-security-scan.yml     # 定期安全掃描
```

### 🐍 Django 後端應用

```
backend/
├── apps/                        # Django 應用目錄 (標準化架構)
│   └── catalog/                 # 📚 核心業務邏輯 (小說目錄管理)
│       ├── api/                 # REST API 介面
│       │   ├── serializers.py   # 數據序列化
│       │   ├── urls.py          # API 路由
│       │   └── views.py         # API 視圖
│       ├── fixtures/            # 🧪 測試數據 (重要: CI 依賴)
│       │   └── test_data.json   # Django 測試 fixture
│       ├── migrations/          # 數據庫遷移檔案
│       ├── models.py            # 數據模型 (Novel, Chapter, Category)
│       ├── tests.py             # 單元測試
│       └── views.py             # 視圖邏輯
│
├── config/                      # ⚙️ Django 設定模組化管理
│   ├── base.py                  # 基礎設定
│   ├── cache.py                 # 快取配置
│   ├── database.py              # 數據庫配置
│   ├── django_settings.py       # 主要 Django 設定
│   ├── minimal_settings.py      # 🧪 測試用最小設定
│   ├── settings.py              # Pydantic 設定管理
│   └── validation.py            # 設定驗證
│
├── crawler_engine/              # 🕷️ 重構後的爬蟲引擎 (主要爬蟲邏輯)
│   ├── adapters/                # 網站適配器 (不同小說網站的解析器)
│   │   ├── base_adapter.py      # 基礎適配器介面
│   │   ├── novel543_adapter.py
│   │   └── ttkan_adapter.py
│   ├── core/                    # 核心爬蟲邏輯
│   │   ├── pipelines.py         # Scrapy 數據處理管線
│   │   └── settings.py
│   ├── spiders/                 # Scrapy 爬蟲 (大部分已停用/備份)
│   │   ├── hjwzw_spider.py      # 飄天文學網爬蟲
│   │   ├── czbooks_spider.py.disabled
│   │   └── novel_data.json
│   ├── utils/                   # 工具函數
│   │   ├── cache.py
│   │   ├── concurrency.py
│   │   ├── db_utils.py
│   │   └── logger.py
│   └── worker/                  # 工作程序
│       └── asyncpg_worker.py
│

```

### ⚛️ React 前端應用

```
frontend/
├── .storybook/                  # Storybook 配置
├── docs/                        # 前端文檔
│   ├── STORYBOOK_GUIDE.md
│   └── STYLING_GUIDE.md
├── public/                      # 靜態資源
├── src/                         # 🎨 主要原始碼
│   ├── components/              # React 組件
│   │   ├── common/Layout/       # 通用組件
│   │   ├── novel/NovelCard.tsx  # 小說相關組件
│   │   └── reader/ChapterReader/ # 閱讀器組件
│   ├── pages/                   # 頁面組件
│   │   ├── Home.tsx
│   │   ├── NovelDetail.tsx
│   │   ├── Login.tsx
│   │   └── Register.tsx
│   ├── services/                # API 服務
│   │   └── api.ts
│   ├── types/                   # TypeScript 類型定義
│   ├── utils/                   # 工具函數
│   └── App.tsx                  # 主應用組件
├── tests/e2e/                   # E2E 測試
│   ├── chapter.spec.ts
│   ├── home.spec.ts
│   └── search.spec.ts
├── jest.config.js               # Jest 測試配置
├── playwright.config.ts         # Playwright 配置
├── tailwind.config.js           # Tailwind CSS 配置
└── package.json                 # NPM 依賴
```

### 🏗️ 基礎設施配置

```
infra/
├── aws-ecr/                     # AWS ECR 配置
│   ├── github-secrets-setup.md
│   ├── required-permissions.md
│   ├── setup-iam-role-manual.md
│   └── tier2-local-test-results.md
├── docker/                      # Docker 配置
│   ├── backend-tier2.Dockerfile # Tier 2 後端映像
│   ├── frontend-tier2.Dockerfile # Tier 2 前端映像
│   ├── package-ci.json          # CI 專用 package.json
│   └── build-images.sh          # 映像構建腳本
├── templates/                   # 基礎設施模板
└── monitoring/                  # 監控配置
    └── prometheus.yml
```

### 🛠️ 自動化腳本

```
scripts/
├── deployment/                  # 部署腳本
│   ├── docker-act-init.sh
│   └── migrate-to-doppler.sh
├── maintenance/                 # 維護腳本
│   ├── check_sensitive.sh       # 敏感信息檢查
│   ├── security-hotfix.sh
│   └── verify-security-config.py
├── setup/                       # 設置腳本
│   ├── dev-environment-check.sh
│   └── setup-devcontext.sh
└── testing/                     # 🧪 測試腳本
    ├── local-ci.sh              # 本地 CI 流程
    └── test-local-ci.sh
```

### 📚 專案文檔 (層級架構 00-04)

```
docs/
├── 00_SYSTEM_BLUEPRINT/         # 系統藍圖
│   ├── MOSES_TABLETS.md         # 摩西石板 (絕對指令)
│   └── SIBYLLINE_ORACLES.md     # 預言神諭 (AI 戰略框架)
├── 01_PRODUCT_DESIGN/           # 產品設計
│   ├── PRD.md                   # 產品需求文檔 v2.1
│   ├── golden-28-novels.md      # 黃金28精品小說清單
│   └── target-websites.md       # 目標網站清單
├── 02_ARCHITECTURE/             # 架構文檔
│   ├── project-structure.md     # 專案結構 (本檔案的簡潔版)
│   ├── project-structure-annotated.md # 📁 本檔案 (詳細註解)
│   ├── cicd-strategy.md         # CI/CD 策略
│   └── infrastructure/          # 基礎設施文檔
├── 03_DEVELOPMENT_GUIDES/       # 開發指南
│   ├── development-timeline.md  # 開發時程記錄
│   ├── COMMIT_GUIDELINES.md     # Git 提交規範
│   ├── makefile-guide.md        # Makefile 使用指南
│   └── crawler/                 # 爬蟲文檔
├── 04_AI_OPERATIONS/            # AI 操作
│   ├── navigator-mvp/           # Navigator 分析報告
│   └── task-dispatcher/         # Task-Dispatcher 執行報告
└── _archive/                    # 封存文檔
    ├── experiments/             # 實驗性文檔
    ├── setup-guides/            # 已完成設置指南
    └── legacy-taskmaster/       # 舊版任務管理系統
```

---

## 🎯 關鍵檔案標記

| 檔案 | 說明 |
|------|------|
| 🤖 `CLAUDE.md` | AI 開發指南和規範 |
| 🔨 `Makefile` | 統一開發工具入口 |
| 🧪 `test_data.json` | CI 測試數據 (已修復) |
| 🔐 `check_sensitive.sh` | 安全檢查腳本 |
| 🏗️ `minimal_settings.py` | 測試專用設定 |
| ⚙️ `main-ci.yml` | GitHub Actions CI 流程 (Tier 2) |
| 📁 `.gitignore` | 已修復 fixtures 例外規則 |
| 📜 `PRD.md` | 產品需求文檔 v2.1 (更新至 Tier 2 CI/CD) |
| 📝 `development-timeline.md` | 記錄 CI/CD 革命性優化里程碑 |
| 🌟 `golden-28-novels.md` | 黃金28精品小說戰略文檔 |

## 🚀 Tier 2 CI/CD 革命性升級亮點

- **AWS Spot Instance 自託管 Runner** (70% 成本節省)
- **ECR 預構建映像** (消除依賴安裝時間)
- **智能路徑觸發器** (Docker/infra 監控)
- **企業級效能** (5分鐘 → 8秒，98.7% 提升)

## 📊 統計資訊

- **總計**: 119 個目錄, 461 個檔案
- **架構**: 現代化、模組化的全端小說網站專案
- **特色**: 完整的 CI/CD 流程、多層次測試策略和專業的代碼品質管控
- **優勢**: 企業級的基礎設施優化和成本控制能力
- **文檔系統**: 層級化 00-04 架構，支援 AI 驅動的專案管理

---

這個架構代表了一個現代化、模組化的全端小說網站專案，具備完整的 CI/CD 流程、多層次測試策略和專業的代碼品質管控，以及企業級的基礎設施優化和成本控制能力。
