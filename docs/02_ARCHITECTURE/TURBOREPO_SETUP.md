# Turborepo Remote Caching 設置指南

## 🚀 設置 Vercel Remote Caching

### 步驟 1: 註冊並配置 Vercel Remote Caching

1. **本地設置**：
   ```bash
   # 登入 Vercel (如果還沒有帳號會自動註冊)
   npx turbo login

   # 將專案鏈接到 Vercel
   npx turbo link
   ```

2. **獲取 Token 和 Team ID**：
   執行 `npx turbo link` 後，會顯示類似以下資訊：
   ```
   >>> Remote Caching

   Your team's Remote Cache is enabled. Turborepo will cache your builds in the cloud.

   Team: your-team-name (team_xxxxxxxxxx)
   ```

### 步驟 2: 設置 GitHub Secrets

前往 GitHub 倉庫 Settings > Secrets and variables > Actions，添加以下 secrets：

1. **TURBO_TOKEN**：
   - 從 Vercel Dashboard > Settings > Tokens 創建新的 token
   - 或者從 `~/.turbo/config.json` 中獲取

2. **TURBO_TEAM**：
   - 從 `npx turbo link` 輸出中獲取 team ID
   - 格式通常是 `team_xxxxxxxxxx`

### 步驟 3: 驗證設置

```bash
# 測試遠程緩存是否正常工作
TURBO_TOKEN=your_token TURBO_TEAM=your_team pnpm turbo build

# 第二次運行應該顯示 CACHE HIT
pnpm turbo build
```

## 🧪 測試場景

### 場景 A: 基準測試 (無變更)
- 創建空 PR，觀察 CI 是否正確跳過

### 場景 B: Web App 變更
- 修改 `apps/web/src/App.tsx`
- 預期：只有 @novelwebsite/web 重新構建

### 場景 C: 共享包變更 (未來)
- 修改 `packages/ui/` 中的組件
- 預期：ui 包和依賴它的 apps 都重新構建

### 場景 D: 配置變更
- 修改 `turbo.json` 或 `package.json`
- 預期：所有任務重新運行

## 🔍 故障排除

### 常見問題

1. **遠程緩存不工作**：
   - 檢查 TURBO_TOKEN 和 TURBO_TEAM 是否正確設置
   - 確認網路連接正常

2. **構建失敗**：
   - 檢查 workspace 配置是否正確
   - 確認所有依賴都已安裝

3. **緩存過期**：
   - Turborepo 會自動處理緩存失效
   - 可以使用 `--force` 強制重新構建

### 有用的命令

```bash
# 查看 Turborepo 配置
pnpm turbo --help

# 強制重新構建（忽略緩存）
pnpm turbo build --force

# 查看構建計劃（不實際執行）
pnpm turbo build --dry

# 清理緩存
pnpm turbo clean
```
