# CI/CD 自動化策略

*最後更新: 2025-06-22*

本文檔記錄 NovelWebsite 專案的 CI/CD 自動化哲學、架構決策和實施策略。

## 🎯 核心哲學

### 設計原則

1. **速度優先**: 8秒極速 CI 是我們的技術護城河
2. **職責清晰**: 每個 workflow 都有明確的單一職責
3. **成本效益**: 在品質保障和資源消耗間找平衡
4. **開發體驗**: 不阻塞開發流程，提供快速回饋

### 架構策略

**Tier 2 架構核心**:
- AWS Spot Instance 自託管 Runner (70% 成本節省)
- ECR 預構建映像 (消除依賴安裝時間)
- 智能路徑觸發器 (避免不必要的執行)

## 🏗️ Workflows 架構設計

### 主要流程 (Core Workflows)

#### 1. `main-ci.yml` - 完整 PR 品質門檻
**職責**: 所有 PR 相關的檢查集中管理
**觸發**: 每個 PR 和 main 分支推送
**執行環境**: Self-hosted runner
**預期時間**: <30秒 (核心測試 8秒 + 品質檢查)

```yaml
jobs:
  # 變更檢測 (Path Filter)
  detect-changes:            # 使用 dorny/paths-filter 檢測變更
    outputs:
      frontend_changed: frontend/**
      ui_components_changed: frontend/src/components/**, frontend/src/pages/**

  # 核心測試 (Always Run)
  test-frontend-optimized:    # 8秒極速前端測試
  test-backend-optimized:     # 8秒極速後端測試

  # 品質門檻 (Conditional Run)
  lighthouse-performance:     # 前端代碼變更時
    needs: [detect-changes, test-frontend-optimized]
    if: needs.detect-changes.outputs.frontend_changed == 'true'

  percy-visual-regression:    # UI 組件變更時
    needs: [detect-changes, test-frontend-optimized]
    if: needs.detect-changes.outputs.ui_components_changed == 'true'

  # 整合報告
  quality-summary:           # 統一報告所有檢查結果
    needs: [detect-changes, test-frontend-optimized, test-backend-optimized, lighthouse-performance, percy-visual-regression]
```

#### 2. `build-and-push-images.yml` - Docker 映像管理
**職責**: 構建並推送 Docker 映像到 ECR
**觸發**: main 分支代碼變更 + 週期性重建
**執行環境**: Self-hosted runner

### 維護流程 (Maintenance Workflows)

#### 3. `docs.yml` - 文檔生成
**觸發**: docs/ 目錄變更

#### 4. `nightly-cleanup.yml` - 夜間清理
**觸發**: 每日 2:00 UTC
**職責**: 清理 ECR 舊映像、臨時資源

#### 5. `weekly-security-scan.yml` - 安全掃描
**觸發**: 每週日
**職責**: 依賴漏洞掃描、安全基線檢查

### ✅ 已刪除的實驗性流程 (2025-06-22)

#### ❌ 一次性基礎設施設置 (已完成歷史使命)
- ~~`setup-ecr-repositories.yml`~~ - ECR 倉庫創建 ✅ 已完成並刪除
- ~~`test-aws-oidc.yml`~~ - OIDC 認證測試 ✅ 已完成並刪除

#### ❌ Tier 實驗檔案 (成果已固化)
- ~~`tier1-docker-test.yml`~~ - Tier 1 實驗 ✅ 成果已固化到 main-ci.yml 並刪除
- ~~`tier2-full-test.yml`~~ - Tier 2 實驗 ✅ 成果已固化到 main-ci.yml 並刪除

## 🚦 觸發策略詳細說明

### PR 階段品質檢查

| 檢查類型 | 觸發條件 | 執行時間 | 失敗影響 |
|----------|----------|----------|----------|
| **核心測試** | 每個 PR | 8秒 | 阻塞合併 |
| **Lighthouse** | 前端代碼變更 | 20-30秒 | 警告，不阻塞 |
| **Percy** | UI 組件變更 | 20-30秒 | 警告，不阻塞 |

### Main 分支後續流程

| 流程 | 觸發條件 | 執行環境 |
|------|----------|----------|
| **映像重建** | 依賴或 Dockerfile 變更 | Self-hosted |
| **文檔生成** | docs/ 目錄變更 | GitHub-hosted |

### 週期性維護

| 流程 | 頻率 | 職責 |
|------|------|------|
| **夜間清理** | 每日 2:00 UTC | 資源清理 |
| **安全掃描** | 每週日 | 漏洞檢測 |

## 🔧 技術實施細節

### ECR 映像復用策略

**前端映像**: `novel-web-frontend:latest`
- 預安裝所有 NPM 依賴
- 支援 React 測試、Lighthouse、Storybook 構建

**後端映像**: `novel-web-backend:latest`
- 預安裝所有 Python 依賴
- 支援 Django 測試、數據庫遷移

### Self-hosted Runner 最佳化

**資源分配**:
- 核心測試: 最高優先級，專用資源
- 品質檢查: 並行執行，共享資源
- 維護流程: 低優先級，背景執行

**快取策略**:
- ECR 映像本地快取
- Docker layer 快取
- 工作目錄隔離

## 📊 成功指標

### 性能指標

| 指標 | 目標 | 當前狀態 |
|------|------|----------|
| **PR CI 時間** | <30秒 | ✅ 8秒 (核心) + 22秒 (品質) |
| **映像構建時間** | <5分鐘 | ✅ 2分鐘 |
| **開發者等待時間** | <1分鐘 | ✅ 30秒 |

### 品質指標

| 指標 | 目標 | 測量方式 |
|------|------|----------|
| **測試覆蓋率** | >80% | Jest + Django coverage |
| **性能分數** | >90 | Lighthouse |
| **視覺回歸** | 0 unexpected | Percy |

## 🛡️ 成本控制策略

### AWS 資源最佳化

1. **Spot Instance**: 70% 成本節省
2. **ECR 映像生命週期**: 自動清理舊版本
3. **按需擴展**: Auto Scaling Group 配置

### GitHub Actions 額度管理

1. **條件觸發**: 避免不必要的執行
2. **Self-hosted 優先**: 減少 GitHub-hosted 用量
3. **並行最佳化**: 最大化資源利用率

## 🔄 演進計劃

### 短期優化 (Sprint 3)
- [x] 重新啟用 Lighthouse 和 Percy ✅ 2025-06-22
- [x] 整合到 main-ci.yml ✅ 2025-06-22
- [x] 優化觸發條件 (使用 dorny/paths-filter) ✅ 2025-06-22

### 中期發展 (Q2 2025)
- [ ] 增加跨瀏覽器測試
- [ ] 整合安全掃描到 PR 流程
- [ ] 性能趨勢分析

### 長期願景 (Q3 2025)
- [ ] AI 輔助的品質預測
- [ ] 全自動化部署流程
- [ ] 多環境支援 (Staging/Production)

## 📚 團隊指南

### 開發者須知

1. **PR 檢查**: 確保所有檢查通過後再合併
2. **觸發條件**: 了解什麼變更會觸發什麼檢查
3. **失敗處理**: 核心測試失敗必須修復，品質檢查失敗可協商

### 維護人員須知

1. **監控指標**: 定期檢查 CI/CD 性能指標
2. **成本追蹤**: 監控 AWS 和 GitHub Actions 用量
3. **基礎設施**: 保持 self-hosted runner 健康狀態

---

## 🎯 決策記錄

### 2025-06-22: Workflows 合併策略
**決策**: 將 Lighthouse 和 Percy 整合到 main-ci.yml
**理由**: 統一 PR 檢查視圖，復用 ECR 認證，精確控制觸發條件
**影響**: 減少 workflow 檔案數量，提升開發體驗

### 2025-06-22: 品質檢查觸發策略
**決策**: 採用條件觸發，而非每次都執行
**理由**: 保護 8 秒極速 CI 優勢，平衡品質和效率
**影響**: 開發者體驗提升，資源消耗最佳化

### 2025-06-22: 智能變更檢測實施
**決策**: 使用 dorny/paths-filter@v3 action 取代簡單的字串匹配
**理由**: GitHub Actions 原生變更檢測語法限制，需要更可靠的解決方案
**影響**: 精確的條件觸發，避免誤觸發或漏觸發品質檢查

### 2025-06-22: 可重用 Action 安全策略
**決策**: 為 `run-in-docker` Action 添加明確的安全警告和使用限制
**理由**: 該 Action 具有執行任意命令的能力，存在命令注入風險，需要明確安全邊界
**影響**: 提升開發者安全意識，建立安全使用準則，為未來擴展到多人協作奠定基礎

## 🛡️ 安全考量

### 可重用 Actions 安全管理

#### 已識別風險
1. **命令注入風險** (`run-in-docker` Action):
   - `run_command` 參數直接傳遞給 shell 執行
   - `environment_variables` 參數拼接到 docker 命令
   - 潛在攻擊向量: 惡意 PR 修改 workflow 檔案

#### 當前防護措施
1. **文檔化風險**: Action 描述中包含明確安全警告
2. **受信任環境**: 僅在受控的自託管 runner 上執行
3. **單一維護者**: 當前為單人專案，所有變更來自可信源

#### 未來安全增強 (多人協作時)
1. **環境保護規則**: 為關鍵 workflows 設置 GitHub Environments
2. **Pull Request Target**: 使用 `pull_request_target` 替代 `pull_request` 處理外部貢獻
3. **輸入驗證**: 考慮對 Action 輸入進行更嚴格的驗證和清理

### 安全監控
- **定期審查**: 每月檢視 workflow 執行日誌，識別異常模式
- **依賴更新**: 保持所有 GitHub Actions 和 Docker 映像為最新版本
- **權限最小化**: 各 job 僅授予必要的最小權限

---

**文檔維護**: 此文檔應在每次 CI/CD 架構變更時更新
**負責人**: DevOps 工程師
**審查週期**: 每季度檢討並優化策略
