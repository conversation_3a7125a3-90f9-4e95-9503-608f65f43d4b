# NovelWebsite 項目結構

*最後更新: 2025-06-28 - 基於遠端 main 穩定版本 (commit: 5ae3d73ad)*

## 📊 項目統計
- **目錄數**: 286 個
- **檔案數**: 879 個
- **架構**: Monorepo (pnpm workspace + Turborepo)
- **主要技術**: Django + React + Docker

## 🏗️ 頂層結構

```
├── .claude/                    # Claude AI 配置和命令
├── .cursor/                    # Cursor 編輯器配置
├── .github/                    # GitHub Actions CI/CD
├── .turbo/                     # Turborepo 緩存
├── apps/                       # 應用程式 (Monorepo 結構)
│   ├── web-next/               # Next.js 15 App Router 應用
│   └── web-sunset/             # React CRA 應用 (日落版本)
├── archive/                    # 歷史代碼備份
├── backend/                    # Django 後端
├── docs/                       # 項目文檔 (分層 00-04)
├── infra/                      # 基礎設施配置
├── scripts/                    # 自動化腳本
├── Dockerfile                  # 容器配置
├── Makefile                    # 統一開發命令
├── pnpm-workspace.yaml         # pnpm workspace 配置
├── turbo.json                  # Turborepo 管線配置
└── vercel.json                 # Vercel 部署配置
```

## 🔧 主要應用

### Backend (Django)
```
backend/
├── apps/catalog/               # 核心業務邏輯 (小說目錄)
├── config/                     # Django 設定模組化
├── crawler_engine/             # 爬蟲引擎
├── staticfiles/                # 靜態檔案
└── requirements.txt            # Python 依賴
```

### Frontend Applications

#### Next.js 15 (apps/web-next) - 主要應用
```
apps/web-next/
├── app/                        # App Router 目錄
│   ├── layout.tsx              # 根布局
│   ├── page.tsx                # 首頁
│   ├── about/                  # 關於頁面
│   ├── api/                    # API 路由
│   └── novels/                 # 小說相關頁面
├── lib/                        # 共用庫
├── public/                     # 靜態資源
└── package.json                # 依賴配置
```

#### React CRA (apps/web-sunset) - 日落版本
```
apps/web-sunset/
├── src/                        # React 源碼
│   ├── components/             # React 組件
│   ├── pages/                  # 頁面組件
│   ├── services/               # API 服務
│   └── types/                  # TypeScript 類型
├── tests/                      # 前端測試
├── package.json                # Node.js 依賴
└── README.md                   # 前端說明
```

## 🚀 CI/CD 架構 (Tier 2)

### GitHub Actions
```
.github/workflows/
├── main-ci.yml                 # 主 CI 流程 (1-2秒極速)
├── emergency-cleanup.yml       # 緊急清理
├── nightly-cleanup.yml         # 夜間清理
├── runner-maintenance.yml      # Runner 維護
└── weekly-security-scan.yml    # 安全掃描
```

### Docker 基礎設施
```
infra/docker/
├── backend-tier2.Dockerfile    # 後端生產映像
├── frontend-tier2.Dockerfile   # 前端生產映像
├── docker-compose.ci.yml       # CI 專用配置
└── nginx-ci.conf              # Nginx 配置
```

## 📚 文檔系統 (分層架構)

```
docs/
├── 00_SYSTEM_BLUEPRINT/        # 系統藍圖
├── 01_PRODUCT_DESIGN/          # 產品設計
├── 02_ARCHITECTURE/            # 架構文檔
├── 03_DEVELOPMENT_GUIDES/      # 開發指南
├── 04_AI_OPERATIONS/           # AI 操作記錄
└── _archive/                   # 歷史文檔
```

## 🛠️ 開發工具

### 統一命令介面
```bash
# 基本開發命令
make dev                        # 啟動開發環境
make test                       # 執行測試
make ci-check                   # 本地 CI 檢查
make clean                      # 清理環境

# Turborepo 命令
pnpm turbo build               # 構建所有應用
pnpm turbo test                # 執行所有測試
pnpm turbo lint                # 程式碼檢查
```

### 測試策略
- **單元測試**: Django + Jest
- **整合測試**: API 端點測試
- **E2E 測試**: Playwright
- **視覺測試**: Percy (已移除臃腫依賴)

## 🔐 安全與品質

- **Pre-commit hooks**: 程式碼品質檢查
- **敏感信息掃描**: 自動檢測密鑰洩漏
- **依賴安全掃描**: 定期漏洞檢查
- **Docker 安全**: 多階段構建優化

## 📈 性能優化成果

- **CI 執行時間**: 5分鐘 → 1-2秒 (99% 提升)
- **依賴大小**: 1.3GB → 769MB (節省 531MB)
- **文件數量**: 減少 87,258 個不必要文件
- **Docker 映像**: Backend 2.28GB → 596MB (73% 減少)

## 🎯 MVP 戰略

**黃金28小說戰略**: 先完美內容體驗 → 再擴大內容規模
- 主要來源: 黃金屋中文 (hjwzw.com)
- 目標: 28本市場驗證的頂級完本小說
- 優先級: P0 內容爬取 > P1 用戶系統 > P2 SEO 優化

---

*此專案採用現代化 Monorepo 架構，支援 Turborepo 緩存和智能構建，具備企業級 CI/CD 流程和全面的測試覆蓋。*
