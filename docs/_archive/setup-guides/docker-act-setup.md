# 🐳 Docker Socket + act 環境使用指南

## 🎯 概述

本項目已成功建立了安全的 Docker Socket + act 環境，用於在容器內本地執行 GitHub Actions 工作流程。

## 🚀 快速開始

### 1. 啟動環境

```bash
# 啟動完整的 Docker + act 環境
docker-compose -f infra/docker/docker-compose.act.yml up -d

# 檢查服務狀態
docker-compose -f infra/docker/docker-compose.act.yml ps
```

### 2. 進入開發容器

```bash
# 進入主要開發容器
docker exec -it novel-dev-act bash

# 執行初始化腳本
scripts/setup/docker-act-init.sh
```

### 3. 執行 GitHub Actions

```bash
# 列出所有可用的工作流程
act -l

# 執行特定工作流程
act -j security-check

# 乾跑模式 (不實際執行)
act -j build --dryrun

# 執行所有工作流程
act
```

## 🛡️ 安全配置

### 已實施的安全措施

✅ **非特權用戶**: 容器以 UID/GID 1000:1000 運行
✅ **資源限制**: Memory 4GB, CPU 2.0 cores
✅ **網絡隔離**: 自定義 Docker 網絡
✅ **權限限制**: `no-new-privileges:true`
✅ **Socket 控制**: 僅掛載 Docker Socket，無完整 Docker daemon

### 安全檢查工具

```bash
# 執行完整安全檢查
./scripts/maintenance/docker-act-security.sh

# 檢查生成的安全報告
cat security-check-report.txt
```

## 📋 環境結構

```
Docker Socket + act 環境
├── novel-dev-act     # 主要開發容器
│   ├── Docker CLI    # 客戶端工具
│   ├── act v0.2.78   # GitHub Actions 執行器
│   ├── Python 3.11   # 基礎運行環境
│   └── 安全配置       # 非特權用戶 + 資源限制
├── redis-act         # Redis 快取服務
├── postgres-act      # PostgreSQL 測試資料庫
└── 自定義網絡         # novel_ci_network
```

## 🔧 配置文件

### act 配置 (.actrc)

```ini
# 簡潔配置，僅包含經過驗證的選項
-P ubuntu-latest=catthehacker/ubuntu:act-22.04
-P ubuntu-22.04=catthehacker/ubuntu:act-22.04

--reuse                    # 啟用容器重用
--env-file=.env           # 從環境文件加載變數
--secret-file=.secrets    # 從secrets文件加載機密
```

### Docker Compose 關鍵配置

```yaml
services:
  novel-dev-act:
    volumes:
      - ./:/workspace # 項目代碼
      - /var/run/docker.sock:/var/run/docker.sock # Docker Socket
    security_opt:
      - no-new-privileges:true # 禁止特權提升
    user: "1000:1000" # 非root用戶
    deploy:
      resources:
        limits:
          memory: 4G # 記憶體限制
          cpus: "2.0" # CPU限制
```

## 📊 監控和日誌

### 檢查容器狀態

```bash
# 檢查所有服務狀態
docker-compose -f infra/docker/docker-compose.act.yml ps

# 檢查容器資源使用
docker stats

# 檢查網絡連接
docker exec novel-dev-act docker ps
```

### 查看日誌

```bash
# 檢查 act 執行日誌
docker-compose -f infra/docker/docker-compose.act.yml logs novel-dev-act

# 檢查 Redis 日誌
docker-compose -f infra/docker/docker-compose.act.yml logs redis-act

# 實時跟蹤日誌
docker-compose -f infra/docker/docker-compose.act.yml logs -f
```

## 🎭 act 使用指南

### 常用指令

```bash
# 基本指令
act                           # 執行所有工作流程
act -l                        # 列出所有可用的工作流程
act -j <job-name>             # 執行特定工作
act --dryrun                  # 乾跑模式，不實際執行

# 事件觸發
act pull_request              # 模擬PR觸發
act push                      # 模擬push觸發
act workflow_dispatch         # 模擬手動觸發

# 調試選項
act --verbose                 # 詳細輸出
act --env VAR=value          # 設置環境變數
act --secret SECRET=value    # 設置機密變數
```

### 本項目可用的工作流程

1. **CI Pipeline** (`ci.yml`)

   - `security-check`: 安全檢查
   - `backend-tests`: 後端測試
   - `frontend-tests`: 前端測試
   - `integration-tests`: 整合測試
   - `coverage`: 測試覆蓋率

2. **Build Docs** (`docs.yml`)

   - `build`: 文檔構建

3. **Weekly Security Scan** (`weekly-security-scan.yml`)
   - `weekly-security-scan`: 深度安全掃描

## 🔧 故障排除

### 常見問題

#### 1. Docker Socket 權限錯誤

```bash
# 檢查權限
ls -la /var/run/docker.sock

# 解決方案 (macOS)
# Docker Desktop 通常自動處理權限
```

#### 2. 容器啟動失敗

```bash
# 檢查日誌
docker-compose -f infra/docker/docker-compose.act.yml logs

# 重新構建容器
docker-compose -f infra/docker/docker-compose.act.yml build --no-cache
```

#### 3. act 執行失敗

```bash
# 檢查 GitHub Actions 語法
act --dryrun

# 檢查環境變數
act --env-file .env --verbose
```

### 環境重置

```bash
# 停止所有服務
docker-compose -f infra/docker/docker-compose.act.yml down

# 清理數據卷
docker-compose -f infra/docker/docker-compose.act.yml down -v

# 重新啟動
docker-compose -f infra/docker/docker-compose.act.yml up -d
```

## 🎯 最佳實踐

### 安全建議

1. **定期更新**: 保持 act 和 Docker 鏡像最新
2. **資源監控**: 監控容器資源使用情況
3. **日誌審計**: 定期檢查 act 執行日誌
4. **權限最小化**: 僅掛載必要的文件和目錄

### 性能優化

1. **容器重用**: 啟用 `--reuse` 選項提升速度
2. **本地快取**: 使用 Docker 層快取
3. **並行執行**: 合理使用 `--parallel` 選項

### 開發工作流程

1. **本地驗證**: 使用 act 本地測試 GitHub Actions
2. **快速反饋**: 在推送到 GitHub 前本地驗證
3. **調試**: 使用 `--verbose` 和 `--dryrun` 調試工作流程

## 📚 相關文檔

- [act 官方文檔](https://github.com/nektos/act)
- [Docker Compose 參考](https://docs.docker.com/compose/)
- [GitHub Actions 語法](https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions)

## 🆘 支援

如有問題，請：

1. 檢查 [故障排除](#故障排除) 部分
2. 執行安全檢查腳本: `./scripts/deployment/docker-act-security.sh`
3. 查看容器日誌: `docker-compose -f infra/docker/docker-compose.act.yml logs`
4. 提交 Issue 或聯繫開發團隊

---

**🎉 恭喜！您現在擁有一個完全配置的 Docker Socket + act 環境，可以安全地在本地執行 GitHub Actions 工作流程！**
