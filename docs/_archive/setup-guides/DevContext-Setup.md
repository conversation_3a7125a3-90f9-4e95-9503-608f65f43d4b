# DevContext 數據庫設置和維護指南

## 📋 概述

DevContext 是 NovelWebsite 項目的 AI 增強開發工具鏈核心組件，提供持久化項目記憶系統。本指南將幫助您完整設置和維護 DevContext 數據庫。

## 🗃️ 核心表結構一覽

| 表名                   | 字段數 | 作用描述                   | 主要功能         |
| ---------------------- | ------ | -------------------------- | ---------------- |
| `git_commits`          | 6      | Git提交記錄                | 版本控制歷史追蹤 |
| `git_commit_files`     | 4      | Git提交影響的文件          | 文件變更關聯     |
| `code_entities`        | 18     | 代碼實體（函數/類/變數等） | 代碼結構分析     |
| `code_relationships`   | 8      | 代碼實體間的關係           | 依賴關係映射     |
| `entity_keywords`      | 5      | 實體關鍵詞映射             | 語義搜索支持     |
| `project_documents`    | 12     | 項目文檔（如README等）     | 文檔管理與搜索   |
| `conversation_history` | 7      | 對話消息歷史               | AI對話記錄       |
| `conversation_topics`  | 11     | 對話主題分段               | 上下文主題追蹤   |
| `background_ai_jobs`   | 13     | 後台AI任務隊列             | 異步任務管理     |
| `system_metadata`      | 3      | 系統級元數據               | 系統配置和狀態   |

## 🚀 快速開始

### 1. 環境配置

確保在 `.cursor/mcp.json` 中正確配置了 DevContext：

```json
{
  "mcpServers": {
    "devcontext": {
      "command": "npx",
      "args": ["-y", "@turso/devcontext-mcp"],
      "env": {
        "TURSO_DATABASE_URL": "libsql://your-database-url.turso.io",
        "TURSO_AUTH_TOKEN": "your-auth-token-here"
      }
    }
  }
}
```

### 2. 數據庫初始化

#### 方法一：使用管理腳本（推薦）

```bash
# 初始化數據庫結構
python scripts/setup/devcontext-init.py --init

# 驗證表結構
python scripts/setup/devcontext-init.py --validate

# 查看系統信息
python scripts/setup/devcontext-init.py --info
```

#### 方法二：直接執行 SQL

```bash
# 使用 Turso CLI
turso db shell your-database-name < docs/devcontext-schema.sql

# 或使用 SQLite（本地測試）
sqlite3 devcontext.db < docs/devcontext-schema.sql
```

### 3. 測試 DevContext 連接

```bash
# 在 Cursor 中執行
@devcontext initialize_conversation_context {
  "initialQuery": "開發 NovelWebsite 項目",
  "focusHint": {"type": "web_scraping", "identifier": "novel_crawler"},
  "contextDepth": "comprehensive"
}
```

## 🛠️ 管理工具使用

### 數據庫初始化

```bash
# 完整初始化（包含表、索引、視圖）
python scripts/setup/devcontext-init.py --init

# 指定數據庫路徑
python scripts/setup/devcontext-init.py --init --db-path /path/to/database.db
```

### 表結構驗證

```bash
# 驗證所有核心表
python scripts/setup/devcontext-init.py --validate

# 輸出示例：
# ============================================================
# 📊 DevContext 核心表結構驗證報告
# ============================================================
#
# ✅ git_commits
#    字段數量: 6/6
#    記錄數量: 0
#
# ✅ code_entities
#    字段數量: 18/18
#    記錄數量: 0
#
# 📈 總計: 10/10 個表結構正確
```

### 數據維護

```bash
# 清理 30 天前的數據
python scripts/setup/devcontext-init.py --cleanup 30

# 匯出數據庫結構
python scripts/setup/devcontext-init.py --export

# 匯出到指定文件
python scripts/setup/devcontext-init.py --export backup.sql
```

### 系統信息查看

```bash
# 獲取詳細系統信息
python scripts/setup/devcontext-init.py --info

# 輸出示例：
# {
#   "metadata": {
#     "schema_version": "1.0.0",
#     "project_name": "NovelWebsite",
#     "initialized_at": "1703123456"
#   },
#   "table_count": 10,
#   "tables": ["git_commits", "code_entities", ...],
#   "database_path": "libsql://..."
# }
```

## 📊 重要視圖

系統自動創建了以下視圖以便於查詢：

### `code_entities_with_relationships`

```sql
-- 查看代碼實體及其關係
SELECT * FROM code_entities_with_relationships
WHERE entity_type = 'function';
```

### `conversation_topic_stats`

```sql
-- 查看對話主題統計
SELECT * FROM conversation_topic_stats
WHERE intent_category = 'coding';
```

### `file_change_frequency`

```sql
-- 查看文件變更頻率
SELECT * FROM file_change_frequency
ORDER BY change_count DESC LIMIT 10;
```

## 🔧 開發工作流程集成

### 1. 會話初始化（每次對話開始）

```javascript
@devcontext initialize_conversation_context {
  "initialQuery": "開發 ttkan.co 爬蟲適配器",
  "focusHint": {"type": "web_scraping", "identifier": "ttkan_adapter"},
  "contextDepth": "comprehensive",
  "includeArchitecture": true,
  "tokenBudget": 4000
}
```

### 2. 代碼變更記錄

```javascript
@devcontext update_conversation_context {
  "newMessages": [
    {"role": "user", "content": "實現 ttkan 爬蟲"},
    {"role": "assistant", "content": "創建了爬蟲適配器"}
  ],
  "codeChanges": [{
    "filePath": "backend/novel/crawler/spiders/ttkan.py",
    "newContent": "class TtkanSpider(scrapy.Spider): ..."
  }]
}
```

### 3. 上下文檢索

```javascript
@devcontext retrieve_relevant_context {
  "query": "反爬蟲策略實現",
  "constraints": {
    "filePaths": ["backend/novel/crawler/*"],
    "entityTypes": ["function", "class"]
  },
  "tokenBudget": 2000
}
```

### 4. 里程碑記錄

```javascript
@devcontext record_milestone_context {
  "name": "ttkan 適配器完成",
  "description": "成功實現反爬策略和內容解析",
  "milestoneCategory": "feature_completion"
}
```

### 5. 會話結束

```javascript
@devcontext finalize_conversation_context {
  "extractLearnings": true,
  "generateNextSteps": true,
  "outcome": "completed"
}
```

## 🎯 爬蟲開發特定配置

### 關鍵實體類型

在 `code_entities` 表中，爬蟲相關的實體類型包括：

- `spider_class` - 爬蟲主類
- `parse_method` - 解析方法
- `selector_config` - 選擇器配置
- `anti_crawl_strategy` - 反爬策略
- `data_pipeline` - 數據管道

### 重要關係類型

在 `code_relationships` 表中追蹤：

- `calls` - 方法調用關係
- `inherits` - 繼承關係
- `uses` - 依賴關係
- `configures` - 配置關係

## 🔒 安全考量

### 敏感數據處理

- API 密鑰存儲在環境變數中，不記錄到數據庫
- 用戶輸入在儲存前進行清理和驗證
- 定期清理舊的對話記錄

### 訪問控制

- 數據庫連接使用認證令牌
- 限制對敏感表的直接訪問
- 記錄所有管理操作的審計日誌

## 🐛 故障排除

### 常見問題

1. **連接失敗**

   ```bash
   # 檢查環境變數
   echo $TURSO_DATABASE_URL
   echo $TURSO_AUTH_TOKEN

   # 測試連接
   turso db list
   ```

2. **表不存在**

   ```bash
   # 重新初始化
   python scripts/setup/devcontext-init.py --init
   ```

3. **字段數量不匹配**

   ```bash
   # 檢查結構
   python scripts/setup/devcontext-init.py --validate

   # 匯出當前結構進行比較
   python scripts/setup/devcontext-init.py --export current_schema.sql
   ```

### 日誌查看

```bash
# 查看詳細日誌
PYTHONPATH=. python scripts/setup/devcontext-init.py --validate 2>&1 | tee devcontext.log
```

## 📈 性能優化

### 索引策略

數據庫已預配置了關鍵索引：

- 時間戳索引（快速按時間查詢）
- 文件路徑索引（快速文件定位）
- 實體名稱索引（快速代碼搜索）
- 關係類型索引（快速關係查詢）

### 定期維護

```bash
# 每週執行一次清理
python scripts/setup/devcontext-init.py --cleanup 7

# 每月備份結構
python scripts/setup/devcontext-init.py --export monthly_backup_$(date +%Y%m).sql
```

## 🔄 更新和遷移

### 結構版本管理

```sql
-- 檢查當前版本
SELECT value FROM system_metadata WHERE key = 'schema_version';

-- 更新版本
UPDATE system_metadata SET value = '1.1.0' WHERE key = 'schema_version';
```

### 遷移腳本

當需要更新數據庫結構時：

1. 備份當前數據
2. 執行遷移 SQL
3. 驗證新結構
4. 更新版本號

## 📞 支持和維護

如遇到問題，請：

1. 查看本文檔的故障排除部分
2. 檢查日誌文件
3. 驗證環境配置
4. 參考 DevContext 官方文檔

---

**記住：DevContext 是您的 AI 開發助手的長期記憶系統。正確維護它將大大提升開發效率！** 🧠✨
