# Doppler 設置指南

## 已完成的設置

✅ Doppler CLI v3.75.0 已安裝

## 需要手動完成的設置步驟

### 1. 獲取API令牌

請訪問您的Doppler dashboard並創建一個service token：

- 專案: novel-website
- 環境: dev/staging/prd
- URL: https://dashboard.doppler.com/workplace/YOUR_WORKPLACE_ID/projects/novel-website

### 2. 身份驗證選項

#### 選項A: 互動式登入 (推薦)

```bash
doppler login
```

#### 選項B: 使用Service Token (CI/CD)

```bash
export DOPPLER_TOKEN="dp.st.dev.xxxx"  # gitleaks:allow
```

### 3. 專案設置

```bash
# 設置專案配置
doppler setup --project novel-website --config dev

# 驗證設置
doppler configure

# 測試秘密檢索
doppler secrets
```

### 4. 環境整合

#### 開發環境

```bash
# 直接運行
doppler run -- python manage.py runserver

# 或者載入到環境
doppler secrets download --no-file --format env > .env.doppler
```

#### Docker整合

```dockerfile
# 在Dockerfile中
RUN curl -Ls https://cli.doppler.com/install.sh | sh
CMD ["doppler", "run", "--", "python", "manage.py", "runserver"]
```

### 5. 安全最佳實踐

1. **永不提交令牌**: 確保 `.env.doppler` 在 `.gitignore` 中
2. **使用Service Tokens**: 為不同環境創建專用令牌
3. **最小權限**: 只授予必要的秘密訪問權限

### 6. 建議的秘密結構

```
novel-website/
├── dev/
│   ├── DATABASE_URL
│   ├── REDIS_URL
│   ├── SECRET_KEY
│   └── DEBUG=true
├── staging/
│   └── [staging secrets]
└── prd/
    └── [production secrets]
```

## 下一步

1. 請手動執行身份驗證
2. 設置專案配置
3. 移植現有的 `.env` 文件到Doppler
4. 更新部署腳本使用Doppler

## 使用範例

```bash
# 開發模式
doppler run -- make dev

# 後端服務
doppler run -- python manage.py runserver

# 爬蟲服務
doppler run -- scrapy crawl ttkan
```
