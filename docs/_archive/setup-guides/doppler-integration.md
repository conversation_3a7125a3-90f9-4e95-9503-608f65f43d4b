# Doppler 整合指南

## 🔐 Doppler 秘密管理已完成集成

NovelWebsite 項目已成功整合 Doppler CLI v3.75.0 進行統一的秘密管理。

### ✅ 已完成的設置

- **專案**: `novel-website`
- **環境**: `dev` (開發環境)
- **秘密數量**: 36+ 個環境變數
- **CLI 狀態**: 已安裝並認證

### 🚀 可用的 Doppler 命令

所有 Doppler 命令都已集成到 Makefile 中：

```bash
# 🔐 Doppler 開發命令
make doppler-dev         # 使用Doppler運行完整開發環境
make doppler-test        # 使用Doppler運行所有測試
make doppler-migrate     # 使用Doppler執行資料庫遷移
make doppler-shell       # 使用Doppler啟動Django shell

# 🔧 管理命令
make doppler-setup       # 快速設置Doppler並遷移環境變數
make doppler-status      # 檢查Doppler安裝和認證狀態
```

### 📋 已設置的環境變數

#### Django 核心配置

- `SECRET_KEY`: Django 安全密鑰
- `DEBUG`: 開發模式標誌
- `ALLOWED_HOSTS`: 允許的主機列表
- `ADMIN_URL`: Django 管理員路徑

#### 資料庫配置

- `DATABASE_URL`: PostgreSQL 連線 URL
- `POSTGRES_DB`: 資料庫名稱
- `POSTGRES_USER/PASSWORD/HOST/PORT`: 詳細連線資訊

#### Redis 配置

- `REDIS_URL`: Redis 連線 URL
- `CELERY_BROKER_URL`: Celery 訊息佇列
- `CELERY_RESULT_BACKEND`: Celery 結果儲存

#### 爬蟲配置

- `SCRAPY_LOG_LEVEL`: 爬蟲日誌級別
- `DOWNLOAD_DELAY`: 下載延遲
- `CONCURRENT_REQUESTS`: 併發請求數
- `TTKAN_BASE_URL`: ttkan 網站基礎 URL
- `USER_AGENT`: 瀏覽器標識

#### CORS 配置

- `CORS_ALLOW_ALL_ORIGINS`: 跨域允許設定
- `CORS_ALLOWED_ORIGINS`: 允許的來源列表

### 🔄 開發工作流程

#### 標準開發流程

```bash
# 1. 檢查 Doppler 狀態
make doppler-status

# 2. 啟動完整開發環境（後端+前端）
make doppler-dev

# 3. 運行資料庫遷移
make doppler-migrate

# 4. 啟動 Django shell
make doppler-shell
```

#### 直接使用 Doppler CLI

```bash
# 查看所有秘密
doppler secrets

# 運行任意命令
doppler run -- python manage.py runserver
doppler run -- npm start

# 下載環境變數到文件（謹慎使用）
doppler secrets download --no-file --format env > .env.doppler
```

### 🛡️ 安全最佳實踐

#### ✅ 推薦做法

- 使用 `make doppler-*` 命令進行開發
- 透過 Doppler Dashboard 管理生產環境秘密
- 定期輪換敏感的 API 密鑰和密碼
- 為不同環境創建專用的配置（dev/staging/prd）

#### ❌ 避免做法

- 不要將 `.env.doppler` 檔案提交到 Git
- 不要在程式碼中硬編碼秘密
- 不要共享個人的 Doppler token

### 📁 檔案結構說明

```
NovelWebsite/
├── scripts/setup/migrate-to-doppler.sh   # 環境變數遷移腳本 ✅
├── doppler-setup.md                # 設置指南 ✅
├── Makefile                        # 包含 Doppler 命令 ✅
└── docs/doppler-integration.md     # 本文檔 ✅
```

### 🚀 下一步整合

1. **CI/CD 整合**: 為 GitHub Actions 設置 Doppler service token
2. **Docker 整合**: 更新 Dockerfile 以使用 Doppler
3. **生產環境**: 配置 staging 和 prd 環境
4. **監控**: 整合 Doppler 審計日誌

### 🆘 故障排除

#### 常見問題

**問題**: `Doppler CLI未安裝`

```bash
# 解決方案
brew install dopplerhq/cli/doppler
```

**問題**: `Doppler未認證`

```bash
# 解決方案
doppler login
```

**問題**: `You must specify a project`

```bash
# 解決方案
doppler setup --project novel-website --config dev
```

**問題**: 資料庫連線失敗

```bash
# 解決方案：確保 PostgreSQL 運行並創建資料庫
brew services start postgresql@14
createdb novelwebsite_dev
```

### 📞 支援

- **Doppler 文檔**: https://docs.doppler.com/
- **專案配置**: https://dashboard.doppler.com/workplace/YOUR_WORKPLACE_ID/projects/novel-website
- **CLI 幫助**: `doppler --help`

---

🎉 **Doppler 整合完成！** 現在您可以安全地管理所有環境變數，並使用統一的命令進行開發。
