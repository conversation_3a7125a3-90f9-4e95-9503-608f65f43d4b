# 安全檢查調整說明

## 📋 背景

隨著 Doppler CLI 的成功整合，NovelWebsite 專案的安全檢查策略已從「偵測式」調整為「預防式」。

## 🔄 變更內容

### 1. CI/CD 安全檢查調整

**變更前:**

- `check_sensitive.sh` 為阻擋性檢查
- 發現敏感信息時會阻止 PR 合併
- 每次 commit 都必須通過檢查

**變更後:**

- `check_sensitive.sh` 調整為非阻擋性檢查 (`continue-on-error: true`)
- 發現問題時會顯示警告，但不阻擋開發流程
- 主要用於檢測硬編碼秘密，而非 .env 洩漏

### 2. 新增週期性安全掃描

**功能:**

- 每週一自動執行深度安全掃描
- 支援手動觸發 (`workflow_dispatch`)
- 包含依賴漏洞檢查、檔案權限審計
- 自動生成安全報告和儀表板

**檔案位置:**

- `.github/workflows/weekly-security-scan.yml`

## 🎯 調整理由

### 1. Doppler 已解決主要風險

**原始問題**: 防止開發者意外提交 `.env` 檔案中的敏感信息
**Doppler 解決方案**:

- 完全移除 `.env` 檔案依賴
- 秘密統一存儲在 Doppler 雲端
- 從根源消除了洩漏風險

### 2. 過度防禦影響開發效率

**問題**:

- 腳本模板觸發誤報 (如 `postgresql://USER:PASSWORD@HOST:PORT`)
- 開發者需要頻繁處理誤報
- 阻擋正常的開發流程

**解決**:

- 調整為非阻擋性檢查
- 保留對硬編碼秘密的檢測能力
- 提升開發體驗

### 3. 週期性掃描補強安全

**新增價值**:

- 深度歷史掃描
- 依賴漏洞檢查
- 檔案權限審計
- 自動化安全報告

## 🛡️ 新的安全架構

```
【預防層】Doppler 秘密管理
    ↓
【檢測層】CI/CD 非阻擋性檢查 (快速回饋)
    ↓
【深度掃描】週期性安全審計 (全面分析)
    ↓
【回應層】自動化問題報告 & 追蹤
```

## 📊 檢查範圍對比

| 檢查項目      | 原本每次 CI | 現在每次 CI | 週期性掃描  |
| ------------- | ----------- | ----------- | ----------- |
| .env 洩漏檢測 | ✅ 阻擋性   | ⚠️ 警告性   | ✅ 深度掃描 |
| 硬編碼秘密    | ✅ 阻擋性   | ⚠️ 警告性   | ✅ 模式匹配 |
| 依賴漏洞      | ❌          | ❌          | ✅ 完整審計 |
| 檔案權限      | ❌          | ❌          | ✅ 權限檢查 |
| Git 歷史      | ❌          | ❌          | ✅ 歷史掃描 |

## 🔧 使用指南

### 開發者日常工作

1. **正常開發**: CI 不會因敏感信息誤報而阻擋
2. **關注警告**: 如果 CI 顯示敏感信息警告，請檢查是否為真實問題
3. **使用 Doppler**: 所有敏感配置使用 `make doppler-*` 命令

### 安全審核

1. **查看週報**: 每週一檢查安全掃描報告
2. **處理 Issues**: 如有安全問題會自動創建 GitHub Issue
3. **手動掃描**: 可在 Actions 頁面手動觸發深度掃描

### 緊急情況

如需立即阻擋敏感信息，可：

1. **臨時啟用阻擋模式**:

   ```yaml
   # 修改 .github/workflows/ci.yml
   - name: Check for sensitive information
     continue-on-error: false # 改為 false
   ```

2. **執行本地檢查**:
   ```bash
   ./scripts/maintenance/check_sensitive.sh
   ```

## 📈 效益評估

### ✅ 優勢

- **開發效率**: 減少誤報阻擋，提升開發速度
- **安全維持**: 通過 Doppler 和週期掃描維持安全水平
- **深度洞察**: 週期性掃描提供更全面的安全視角
- **自動化**: 減少人工干預，自動化安全流程

### ⚠️ 注意事項

- **依賴 Doppler**: 必須確保 Doppler 配置正確
- **週期性關注**: 需定期查看安全掃描報告
- **團隊教育**: 確保團隊了解新的安全流程

## 🔗 相關文檔

- [Doppler 整合指南](./doppler-integration.md)
- [週期性安全掃描 Workflow](../.github/workflows/weekly-security-scan.yml)
- [CI/CD Pipeline](../.github/workflows/ci.yml)

---

_最後更新: 2025-06-11_
_版本: v2.0 (Doppler 整合後)_
