<context>
# Overview
小說閱讀平台是一個面向小說愛好者的在線閱讀服務。平台從免費小說網站自動爬取小說內容，為用戶提供乾淨、友好的閱讀體驗。解決用戶在原始網站上遇到的廣告干擾、排版混亂、閱讀體驗差等問題，讓小說愛好者能夠專注於閱讀本身。

# Core Features
## 內容爬取系統
### 三階段爬取戰略
基於深度市場調研與技術可行性分析，制定精準的分階段實施戰略：

#### 第一階段 (MVP): 集中精力拿下 CZBooks.net 和 飄天文學網 ⭐⭐⭐⭐⭐
**目標**: 在1-2週內，使用純Scrapy快速搭建起核心爬蟲框架，並獲得一個高質量的、包含數萬本完本小說的數據庫。

**CZBooks.net (czbooks.net)**
   - 技術優勢：完全靜態內容，零JavaScript依賴
   - 反爬策略：溫和的頻率限制，標準User-Agent即可
   - 內容質量：收錄經典小說多，更新穩定
   - 實施複雜度：低，適合快速MVP驗證

**飄天文學網 (piaotian55.net)**
   - 技術特點：傳統HTML結構，解析邏輯清晰
   - 反爬機制：基本的Referer檢查，易於處理
   - 數據質量：內容完整度高，章節結構規範
   - 戰略價值：與CZBooks形成互補，提供內容冗餘

#### 第二階段 (技術升級): 轉向 TTKAN 和 Novel543 ⭐⭐⭐⭐
**目標**: 將Playwright集成到您的Scrapy項目中，實現對動態加載內容的處理。開發可複用的適配器邏輯，並增強爬蟲的錯誤處理能力。

**TTKAN (ttkan.co)**
   - 月訪問量：1.306M (適中負載)
   - 技術挑戰：JavaScript動態內容加載，需要Playwright
   - 內容優勢：更新速度快，熱門小說覆蓋廣
   - 實施策略：Scrapy+Playwright混合架構

**Novel543.com**
   - 月訪問量：12.04M (最高流量)
   - 技術挑戰：分站多，域名跳轉需處理
   - 反爬機制：中等強度，需要會話管理
   - 戰略價值：流量最大，覆蓋範圍最廣

#### 第三階段 (挑戰與完善): 最後挑戰 69書吧 和 UU看書 ⭐⭐
**目標**: 實現代理IP輪換、高級瀏覽器模擬等功能，並建立強大的數據清洗管道，完善您的爬蟲系統，使其成為一個能應對多種複雜場景的工業級工具。

**69書吧 (69shuba.cx)**
   - 月訪問量：10.11M
   - 技術挑戰：CHM/手機版切換，格式複雜
   - 反爬機制：高級檢測，需要指紋偽造
   - 實施考量：需要專門的格式處理器

**UU看書 (uukanshu.cc)**
   - 月訪問量：5.31M
   - 技術挑戰：反爬極嚴 (UA/Referer/IP嚴格檢測)
   - 實施策略：代理池+瀏覽器指紋偽造+行為模擬
   - 戰略意義：技術能力的最終驗證

### 爬取功能實現
- 多目標源自動爬取，提高成功率和內容覆蓋度
- 智能章節解析，處理不同網站的HTML結構差異
- 內容清理與去重，過濾廣告和無效內容
- 定期更新策略，自動檢測新章節並更新
- 故障轉移機制，當主要源失效時自動切換備用源

## 閱讀體驗
- 乾淨的閱讀介面，無廣告干擾
- 響應式設計，支持桌面和移動設備
- 基本閱讀功能：章節導航、閱讀進度記錄
- 小說搜索和瀏覽功能

## 內容管理
- 小說分類和標籤系統
- 小說詳情頁面（簡介、作者、更新狀態）
- 章節列表和內容展示

# User Experience
## 目標用戶
- 小說愛好者，希望獲得更好的在線閱讀體驗
- 希望避免原始網站廣告和界面問題的讀者
- 尋找免費小說資源的用戶

## 核心用戶流程
1. 用戶訪問網站首頁
2. 瀏覽或搜索感興趣的小說
3. 進入小說詳情頁查看簡介
4. 開始閱讀，系統記錄閱讀進度
5. 繼續閱讀或返回選擇其他小說
</context>
<PRD>
# Technical Architecture
## 系統組件
- **前端應用**: React 18 + TypeScript 響應式網頁應用
- **後端API**: Django 5.0 + DRF RESTful API 服務
- **爬蟲服務**: 基於 Scrapy + Playwright 的多源爬蟲系統
- **數據庫**: PostgreSQL 主庫 + Redis 緩存
- **文件存儲**: 本地存儲 + CDN 加速（可選）
- **任務調度**: Celery + Redis 處理爬蟲任務和定時更新

## 數據模型
### 核心表設計
- **小說表**: id, title, author, description, status, source_url, source_site, created_at, updated_at, last_crawl_at
- **章節表**: id, novel_id, chapter_number, title, content, word_count, published_at, source_url
- **分類表**: id, name, description, parent_id (支持多級分類)
- **小說分類關聯表**: novel_id, category_id
- **爬蟲源表**: id, site_name, base_url, selector_config, status, priority
- **爬蟲日誌表**: id, source_id, novel_id, task_type, status, error_message, created_at

### 爬蟲配置表
- **網站配置**: 存儲各目標網站的解析規則
- **反爬策略**: 記錄IP限制、請求間隔等策略參數

## API設計
- GET /api/novels - 獲取小說列表
- GET /api/novels/:id - 獲取小說詳情
- GET /api/novels/:id/chapters - 獲取章節列表
- GET /api/chapters/:id - 獲取章節內容
- GET /api/search?q=keyword - 搜索小說

# Development Roadmap

## 🎯 項目狀態概覽 (截至2024年12月)

**總體完成度**: 75% (21/28 tasks) | 子任務完成度: 88% (22/25)

### ✅ 已完成的里程碑

## Phase 1: MVP核心功能 ✅ **已完成**
- ✅ Django後端API架構搭建，包含基礎模型和視圖
- ✅ PostgreSQL數據庫設計和遷移腳本
- ✅ CZBooks + 飄天文學網雙源爬蟲實現 (已超越原計劃的單源ttkan)
- ✅ 完整前端界面：首頁、小說列表、章節閱讀器
- ✅ 小說內容存儲、展示和搜索API功能
- ✅ 工業級錯誤處理和日誌記錄
- ✅ 完整的CI/CD部署管道
- ✅ pydantic-settings統一配置管理
- ✅ Redis Queue + AsyncPG Worker解耦架構

### 🏗️ 核心架構已完成 (95%)

**數據管道完全運營就緒**:
```
Scrapy Spiders → Redis Queue → AsyncPG Worker → PostgreSQL → Django API
```

**已實現的工業級特性**:
- BaseAdapter可擴展爬蟲架構
- 選擇器外部化配置 (YAML)
- AsyncPG高性能數據庫寫入 (比psycopg2快3倍)
- Prometheus監控集成準備
- 完整的RESTful API (Django DRF)

### 🔄 Phase 2: 增強體驗 (70%完成，進行中)
- ✅ 雙源爬蟲系統 (CZBooks + 飄天文學網)
- ✅ 搜索API後端功能 (全文搜索、過濾器)
- ✅ 基礎分類和標籤系統
- 🔄 搜索前端界面 (Task 14進行中)
- ⏳ 響應式設計優化和PWA支持 (Task 15待完成)
- ⏳ Sentry錯誤監控和Winston日誌系統 (Task 16部分完成)

### ⏳ Phase 3: 擴展功能 (規劃中)
- ⏳ 支持更多爬蟲源 (TTKAN、Novel543規劃中)
- ⏳ 用戶系統（註冊、登錄、收藏）
- ⏳ 閱讀偏好設置（字體、主題）
- ⏳ 評論和評分系統
- ⏳ 後台管理界面

## 🎯 修訂開發優先級 (基於實際進展)

### P0 立即執行 (本週完成)
1. **Task 14**: 完成搜索功能前端界面
2. **Task 15**: 響應式設計移動端適配
3. **Task 25**: 完整Prometheus監控系統集成

### P1 重要執行 (2-3週內)
4. **Task 16**: 完成Sentry+Winston完整監控
5. **Task 17**: AsyncPG性能調優和連接池優化
6. **Task 27**: Playwright E2E測試套件完整實現

### P2 後續執行 (3-4週內)
7. **Task 28**: TTKAN + Novel543高級爬蟲架構
8. 用戶系統和個性化功能
9. 高級搜索和推薦算法

## 🏆 超越原計劃的成就

### 原計劃 vs 實際實現

**原計劃Phase 1**: 單源爬蟲 (ttkan.co)
**實際完成**: 雙源工業級爬蟲 (CZBooks + 飄天文學網) + 解耦架構

**原計劃Phase 2**: 多源爬蟲系統
**實際狀態**: 已有可擴展BaseAdapter架構，新增網站只需配置YAML

**技術架構提升**:
- 從簡單Django ORM → AsyncPG高性能批量寫入
- 從單體架構 → 解耦的Redis Queue架構
- 從硬編碼選擇器 → 外部化YAML配置
- 從基礎監控 → Prometheus工業級監控

# Logical Dependency Chain
## 基礎依賴順序
1. **數據庫設計**: 首先建立核心數據模型
2. **後端API基礎**: 建立基本的CRUD操作
3. **爬蟲服務**: 實現內容獲取和存儲
4. **前端基礎頁面**: 可以展示爬取的內容
5. **搜索和分類**: 提升內容發現能力

## 開發優先級
- 優先實現能夠展示內容的最小可用版本
- 確保爬蟲穩定性，避免被目標網站封鎖
- 前端先實現核心閱讀流程，再優化體驗

# Risks and Mitigations
## 技術風險
- **爬蟲被封鎖**:
  - 實現分佈式IP代理池和User-Agent輪換
  - 智能請求間隔控制（隨機化延遲）
  - 模擬真實用戶行為（JavaScript渲染、鼠標事件）
- **網站結構變化**:
  - 建立配置化的CSS選擇器規則庫
  - 實現自動結構檢測和報警機制
  - 多源備份確保內容獲取穩定性
- **性能問題**:
  - PostgreSQL查詢優化和適當索引設計
  - Redis緩存熱點數據和章節內容
  - CDN加速靜態資源訪問

## 法律風險
- **版權問題**: 明確標注內容來源，只爬取免費公開內容
- **服務條款**: 遵守目標網站的robots.txt和使用條款

## 運營風險
- **雲端成本**: 合理的資源規劃和成本監控
- **內容質量**: 建立內容驗證機制，過濾低質量內容

# Appendix
## 確定技術選型
基於現有項目結構和調研結果：

### 後端技術棧
- **框架**: Django 5.0 + Django REST Framework
- **數據庫**: PostgreSQL 主庫 + Redis 緩存
- **爬蟲**: Scrapy + Playwright (處理JS渲染)
- **任務隊列**: Celery + Redis
- **Web服務器**: Gunicorn + Nginx

### 前端技術棧
- **框架**: React 18 + TypeScript
- **UI庫**: Material-UI (與現有保持一致)
- **狀態管理**: React Context + hooks
- **路由**: React Router
- **HTTP客戶端**: Axios

### 爬蟲技術詳細方案
- **目標網站適配器模式**: 每個網站一個獨立的解析器類
- **反爬策略**:
  - 請求頭偽裝 (User-Agent, Referer, Accept)
  - 隨機延遲 (1-3秒間隔)
  - 會話管理 (保持cookies)
  - 失敗重試機制 (指數退避)

# MVP 架構優化方案 (技術評估補充)

基於專業技術評估報告，對MVP架構進行重要優化：

## 核心架構優化

### 解耦架構設計
```
Scrapy Spider → Redis Queue → AsyncPG Worker → PostgreSQL
                              ↓
                       Prometheus Monitoring
```

### 關鍵優化決策

1. **避免 Django ORM 阻塞問題**
   - Scrapy 專注爬取，使用 AsyncPG 直寫數據庫
   - 性能研究證實：asyncpg 比 psycopg2 快 3 倍以上
   - 避免 `django.setup()` 耦合問題

2. **配置管理統一化**
   - 採用 pydantic-settings 統一配置管理
   - Django 和 Scrapy 共用 .env 文件
   - 支持類型驗證和環境變數自動讀取

3. **選擇器外部化**
   - CSS選擇器存儲在 selectors/czbooks.yaml, selectors/piaotian.yaml
   - SHA-256 緩存版本控制
   - CI 驗證選擇器有效性

4. **監控可觀測性**
   - Scrapy Prometheus Exporter 集成
   - 錯誤分級：RetryableError / FatalError
   - Selector 失效監控：item_missing_fields 立即告警

## MVP 實施優先級 (P0-P2)

### P0 基礎設施 (🔴 立即執行)
- **P0-1**: pydantic-settings + .env 統一管理
- **P0-2**: Redis Queue 骨架實現
- **P0-3**: AsyncPG Worker MVP (連接池 + 批量插入)
- **P0-4**: BaseAdapter + SelectorLoader

### P1 核心功能 (🟡 重要執行)
- **P1-1**: CZBooksAdapter v1
- **P1-2**: PiaotianAdapter v1
- **P1-3**: Prometheus 監控 exporter
- **P1-4**: 端到端測試套件

### P2 完善優化 (🟢 後續執行)
- **P2-1**: ContentCleaner + Deduper
- **P2-2**: 架構文檔和指標說明

## 性能優化建議

### AsyncPG 批量寫入
- MVP 階段：使用 `executemany()` 批量插入
- 後續升級：`copy_records_to_table()` 可再提速 2-3 倍

### 監控告警
- Worker 端實時監控 `item_missing_fields`
- Prometheus `FatalError_total{site=czbooks}` 指標
- 異常升高時自動告警

## 時間線規劃
- **Week 1**: 完成 P0 基礎設施
- **Week 1-2**: 開發 CZBooks/Piaotian 適配器 + 監控
- **Week 2-3**: 清洗去重、文檔和測試，批量抓取 50 本小說
- **內容提取**:
  - CSS選擇器 + XPath雙重定位
  - 正則表達式清理HTML標籤
  - 文本去重和格式化

## 性能目標
- 頁面加載時間 < 2秒
- 支持並發用戶數 > 100
- 爬蟲更新頻率：每日一次
- 系統可用性 > 99%

# AI增強開發工具鏈集成
## DevContext記憶系統
- **數據庫**: Turso (libsql://novelwebsite-mumutw.aws-ap-northeast-1.turso.io)
- **功能**: 跨會話項目記憶、上下文連續性、技術決策追蹤
- **工作流程**: 初始化→更新記錄→檢索上下文→記錄里程碑→結束會話

## Context7文檔檢索
- **用途**: 實時獲取最新Scrapy、Django、Playwright API文檔
- **整合**: 與DevContext形成黃金組合，解決AI知識過時問題

## TaskMaster項目管理
- **任務分解**: 自動將複雜開發任務分解為可執行子任務
- **進度追蹤**: 實時更新任務狀態和開發進度
- **依賴管理**: 智能處理任務依賴關係和執行順序

## Playwright自動化測試
- **E2E測試**: 小說閱讀器功能的端到端自動化測試
- **爬蟲測試**: 目標網站的自動化爬取驗證
- **回歸測試**: UI變更的自動化回歸檢測

## Exa智能搜索
- **技術研究**: 搜索最新的Django/React最佳實踐
- **競品分析**: 研究其他小說網站的技術架構
- **反爬策略**: 查找最新的反反爬蟲技術

# 開發工作流程規範
## AI增強開發流程
1. **會話初始化**: 每次開發開始使用DevContext初始化上下文
2. **技術研究**: 使用Exa Search和Context7查找最佳實踐
3. **任務規劃**: 使用TaskMaster分解和管理開發任務
4. **代碼實施**: 遵循TDD原則，記錄開發決策到DevContext
5. **自動化測試**: 使用Playwright進行功能驗證
6. **里程碑記錄**: 完成重要功能時記錄到DevContext

## 問題解決方法論
- **系統化診斷**: 使用DevContext檢索歷史問題和解決方案
- **最新技術查詢**: 使用Context7確認API用法和最佳實踐
- **智能搜索**: 使用Exa Search查找外部解決方案
- **經驗積累**: 將解決過程和結果記錄到DevContext供未來參考

# CI/CD與品質保證
## 三級驗證制度
1. **本地測試**: 代碼格式化、單元測試、集成測試、構建驗證
2. **act模擬**: 本地模擬GitHub Actions工作流程
3. **GitHub Actions**: 自動化CI/CD流程執行

## 強制執行規則
- 禁止直接push，必須經過完整CI/CD流程
- Actions失敗必須立即回滾和修復
- 代碼變更必須同步更新文檔和測試

## 爬取目標網站詳細分析

### 1. 天天看小說 (ttkan.co) - 優先級：高
**網站特點**:
- 小說分類：玄幻、言情、穿越、都市、仙俠、靈異、歷史、懸疑、青春、競技、遊戲、軍事
- 更新頻率：每日更新，連載小說追更及時
- 內容質量：正版授權內容較多，文字質量較高
- 網站穩定性：運營時間長，服務器穩定

**技術實現要點**:
- 首頁URL: `https://www.ttkan.co/`
- 小說列表: `/novel/class/{category}`
- 章節頁面: `/novel/chapters/{novel-slug}`
- 內容解析: 標準HTML結構，CSS選擇器定位內容
- 反爬策略: 基本的User-Agent檢測，需要模擬瀏覽器請求

**實現策略**:
- 多站點輪換抓取
- 智能間隔控制 (5-10秒)
- 失敗自動切換備用域名
</PRD>
