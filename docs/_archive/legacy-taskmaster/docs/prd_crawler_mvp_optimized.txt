# 小說爬蟲 MVP 重構計劃 - 優化版本
## 產品概述

基於技術評估報告的建議，重構現有小說爬蟲系統為高效、可維護的MVP版本。採用Scrapy + Redis Queue + Worker架構，避免同步ORM阻塞問題，確保系統性能與可擴展性。

## 核心技術架構 (優化後)

### 1. 解耦架構設計
- **抓取層**: Scrapy Spider (專注爬取，異步高效)
- **隊列層**: Redis Queue (緩衝與解耦)
- **處理層**: AsyncPG Worker (數據持久化，避免ORM阻塞)

### 2. 配置管理統一化
- 使用 pydantic-settings 管理配置
- Django 和 Scrapy 共用 .env 文件
- 避免 django.setup() 耦合問題

### 3. 選擇器外部化
- CSS選擇器存儲在 selectors/czbooks.yaml, selectors/piaotian.yaml
- SHA-256 緩存版本控制
- CI 驗證選擇器有效性

### 4. 監控可觀測性
- Scrapy Prometheus Exporter 集成
- 錯誤分級：RetryableError / FatalError
- 詳細統計：成功率、延遲、並發數

## MVP 目標網站 (第一階段)

### 主要目標
1. **CZBooks.net** - 純靜態內容，零反爬風險
2. **飄天文學網** - 傳統HTML，基本反爬策略

### 數據目標
- 50本小說的完整抓取
- 每本小說平均50-100章節
- 數據清洗與去重處理

## 技術實施規範

### 1. 數據流設計
```
Scrapy Spider → Redis Queue → AsyncPG Worker → PostgreSQL
                              ↓
                         監控統計 → Prometheus → Grafana
```

### 2. 反爬策略 (基礎版)
- User-Agent 輪換
- 隨機延遲 (1-3秒)
- 基本Session管理
- 失敗重試機制

### 3. 錯誤處理分級
- **HTTP 429**: 指數退避重試
- **HTML結構變化**: 記錄Fatal錯誤，發送告警
- **網絡超時**: 自動重試3次後失敗

### 4. 測試策略
- **選擇器測試**: 使用 HTML fixture，毫秒級驗證
- **Pipeline測試**: Mock DB/Redis，確保數據流
- **端到端測試**: 一本書3章 → DB的完整流程

## 性能指標

### 目標指標
- 並發爬取: 5-10個並發請求
- 成功率: >95%
- 平均響應時間: <2秒
- 數據完整率: >98%

### 監控指標
- `scrapy_spider_requests_total`
- `scrapy_spider_response_time`
- `scrapy_spider_error_rate`
- `redis_queue_length`
- `worker_processing_time`

## 文件結構設計

```
backend/novel/crawler/
├── adapters/
│   ├── base_adapter.py      # 基礎適配器
│   ├── czbooks_adapter.py   # CZBooks適配器
│   └── piaotian_adapter.py  # 飄天適配器
├── config/
│   ├── settings.py          # pydantic-settings配置
│   └── selectors/
│       ├── czbooks.yaml     # CZBooks選擇器
│       └── piaotian.yaml    # 飄天選擇器
├── queue/
│   ├── redis_queue.py       # Redis隊列管理
│   └── worker.py           # AsyncPG數據處理器
├── monitoring/
│   ├── prometheus_exporter.py  # Prometheus指標
│   └── health_check.py     # 健康檢查
└── tests/
    ├── test_selectors.py   # 選擇器測試
    ├── test_pipeline.py    # 數據流測試
    └── test_e2e.py        # 端到端測試
```

## 開發階段規劃

### Phase 1: 基礎架構 (1-2週)
- pydantic-settings 配置管理
- Redis Queue 基礎設施
- AsyncPG Worker 實現

### Phase 2: 爬蟲適配器 (1-2週)
- CZBooks 和飄天適配器
- 選擇器外部化配置
- 基礎反爬策略

### Phase 3: 監控與測試 (1週)
- Prometheus 監控集成
- 測試套件完善
- 性能調優

## 風險控制

### 技術風險
- Redis 單點故障 → 後期考慮 Redis Cluster
- AsyncPG 連接池管理 → 使用成熟的連接池庫
- 選擇器失效 → 自動驗證與告警機制

### 合規風險
- 嚴格遵循 robots.txt
- 合理請求頻率限制
- 僅用於學習和研究目的

## 後續升級路徑

### Phase 4: JavaScript 支援 (未來)
- Playwright 集成
- TTKAN 等動態網站支援

### Phase 5: 智能調度 (未來)
- Celery 任務調度
- 增量更新機制
- 多站點統一管理

---

**MVP成功標準**:
在4-6週內實現穩定的50本小說爬取，系統可用性>99%，為後續擴展奠定堅實基礎。
