# 本地開發環境指南

## 🎯 Frontend Base Image 架構概覽

我們實現了三層架構的 Docker 優化方案：

1. **Base Image** (`frontend-deps.Dockerfile`) - 純依賴層，推送至 ECR 作為快取基底
2. **Production Images** (`frontend-tier2.Dockerfile`, `frontend-ci-v2.Dockerfile`) - FROM base image
3. **Development Image** (`frontend-dev.Dockerfile`) - 本地開發專用，ARM64 原生

## 🚀 日常開發工作流程

### ✅ 平時開發（推薦工作流程）

```bash
# 1. 檢查本地開發環境是否正常
./scripts/local-dev-test.sh

# 2. 啟動開發環境
docker-compose -f docker-compose.dev.yml up frontend-dev

# 3. 訪問開發伺服器
open http://localhost:3000
```

**特點：**
- 不必每次都推 PR 才看 CI 結果
- 使用 volume 掛載實現熱重載
- ARM64 原生支援，避免跨平台問題
- 與本機架構一致的 Node.js 版本

### ✅ 變更底層構建邏輯時

當修改 Base Image、Dockerfile 或依賴配置時：

```bash
# 1. 先測試構建和快取效果
./scripts/local-build-test.sh

# 2. 確認開發容器正常啟動
./scripts/local-dev-test.sh

# 3. 推送到遠端分支觸發 CI
git push origin feature/your-branch
```

**驗證點：**
- 第二次構建應有明顯的快取命中（CACHED 標記）
- 開發伺服器能在 15 秒內啟動
- 熱重載功能正常

## 📁 檔案結構說明

```
infra/docker/
├── frontend-deps.Dockerfile      # Base Image（依賴層）
├── frontend-dev.Dockerfile       # 本地開發專用
├── frontend-tier2.Dockerfile     # 生產環境
└── frontend-ci-v2.Dockerfile     # CI 環境

scripts/
├── local-build-test.sh           # 構建和快取測試
├── local-dev-test.sh             # 完整開發環境測試
└── ci/
    └── deployment-simulation-test-v2.sh  # CI 部署模擬

docker-compose.dev.yml             # 本地開發環境配置
```

## 🔧 Debug 指令

### 手動構建測試

```bash
# 測試 Base Image
docker build -t my-base -f infra/docker/frontend-deps.Dockerfile .

# 測試生產 Image（需要本地 base 或 ECR 連接）
docker build -t my-prod -f infra/docker/frontend-tier2.Dockerfile .

# 測試開發 Image
docker build -t my-dev -f infra/docker/frontend-dev.Dockerfile .
```

### 檢查快取命中率

```bash
# 第一次構建（建立快取）
docker build -t test1 -f infra/docker/frontend-deps.Dockerfile . | tee build1.log

# 第二次構建（應大量命中快取）
docker build -t test2 -f infra/docker/frontend-deps.Dockerfile . | tee build2.log

# 統計快取命中
grep -c "CACHED" build2.log
```

### 檢查 Base Image 內容

```bash
# 驗證依賴安裝正確
docker run --rm my-base sh -c "ls -la /workspace/node_modules/.bin | head -10"

# 檢查 pnpm 和 turbo 可用
docker run --rm my-base sh -c "pnpm --version && turbo --version"
```

## 🚨 常見問題排解

### 1. pnpm lockfile 過期錯誤

```bash
# 更新 lockfile
pnpm install

# 或在 Docker 中使用非凍結模式（開發環境）
RUN pnpm install --no-frozen-lockfile
```

### 2. Docker 構建失敗

```bash
# 清理 Docker 快取
docker system prune -f

# 重新構建，忽略快取
docker build --no-cache -t test -f infra/docker/frontend-deps.Dockerfile .
```

### 3. 開發伺服器無法啟動

```bash
# 檢查容器日誌
docker-compose -f docker-compose.dev.yml logs frontend-dev

# 進入容器 debug
docker-compose -f docker-compose.dev.yml exec frontend-dev sh
```

### 4. 熱重載不工作

確認 volume 掛載正確：
```yaml
volumes:
  - .:/app:cached  # 整個專案掛載
  - /app/node_modules  # 保留容器內 node_modules
```

## 📊 性能基準

### 預期構建時間

| 場景 | 目標時間 | 說明 |
|------|----------|------|
| Base Image（首次） | < 3 分鐘 | 包含完整依賴安裝 |
| Base Image（快取命中） | < 30 秒 | 大部分層應被快取 |
| 開發環境啟動 | < 15 秒 | 熱重載就緒 |
| 生產構建（基於 Base） | < 2 分鐘 | 僅應用程式建置 |

### 快取命中率目標

- **Base Image 重建**: 95%+ 快取命中率
- **開發環境**: 接近 100% 快取命中
- **CI 環境**: 90%+ 快取命中率（ECR 快取）

## 🏗️ 架構優勢

### 傳統 Multi-stage vs Base Image 模式

```dockerfile
# 傳統模式（每次都安裝依賴）
FROM node:20-alpine AS deps
COPY package*.json ./
RUN pnpm install  # 每個 Dockerfile 都重複這步

# Base Image 模式（依賴層重用）
FROM my-registry/frontend:base AS deps  # 直接繼承，零時間
```

### 效益對比

| 指標 | 傳統模式 | Base Image 模式 | 改善 |
|------|----------|-----------------|------|
| 依賴安裝時間 | 3-5 分鐘 | 0 秒 | 100% |
| 快取命中率 | 60-70% | 95%+ | 35%+ |
| CI 執行時間 | 8-12 分鐘 | 4-6 分鐘 | 50% |
| 開發體驗 | 每次 5 分鐘等待 | 15 秒啟動 | 95% |

---

**最後更新**: 2025-06-28
**適用版本**: Frontend Base Image v3.0+
