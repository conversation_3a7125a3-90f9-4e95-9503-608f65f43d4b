# 目標小說網站清單

## 📚 爬蟲目標網站

以下是小說爬蟲系統的目標網站列表，按優先級和技術難度分類：

### 🎯 第一階段目標 (已實現)

| 網站 | URL | 狀態 | 技術特點 |
|------|-----|------|----------|
| 黃金屋中文 | https://tw.hjwzw.com/index.html | ✅ 已實現 | 靜態HTML，反爬策略溫和 |

### 🔄 第二階段候選網站

| 網站 | URL | 評估狀態 | 備註 |
|------|-----|----------|------|
| CZBooks | 待確認 | 📋 待評估 | 純靜態內容，零JS依賴，移出MVP |
| TT看書 | https://www.ttkan.co/ | 📋 待評估 | 需要分析反爬策略 |
| TW看書 | https://twkan.com/ | 📋 待評估 | 需要分析技術架構 |
| 飄天文學 | https://www.piaotia.com/ | 📋 待評估 | 與主站可能重複 |
| UU看書 | https://uukanshu.cc/ | 📋 待評估 | 需要評估內容質量 |
| 8Book | https://8book.com/ | 📋 待評估 | 需要技術可行性分析 |
| 69書吧 | https://www.69shuba.com/ | 📋 待評估 | 需要反爬分析 |
| TW小王 | https://www.twking.org/ | 📋 待評估 | 需要內容質量評估 |
| STO55 | https://sto55.com/ | 📋 待評估 | 需要技術架構分析 |
| 85小說 | https://www.85novel.com/ | 📋 待評估 | 需要可行性評估 |
| 晉江文學城 | https://www.jjwxc.net/ | 📋 待評估 | 重要目標，需專門分析 |

## 🔍 網站評估標準

### 技術可行性
- ✅ **靜態HTML**: 易於解析，穩定性高
- 🟡 **輕量JS**: 可處理，需額外工具
- 🔴 **重度JS**: 需要瀏覽器自動化，成本高

### 反爬策略
- ✅ **溫和**: 基本的頻率限制
- 🟡 **中等**: IP限制、驗證碼
- 🔴 **嚴格**: 複雜的檢測機制

### 內容質量
- ✅ **高**: 內容豐富，更新及時
- 🟡 **中**: 內容一般，更新不穩定
- 🔴 **低**: 內容稀少或質量差

## 📋 實施計劃

### Phase 1 (已完成) - 黃金28戰略
- [x] 黃金屋中文基礎爬蟲 (hjwzw)
- [x] 黃金28精品小說清單確定
- [x] 數據庫結構設計
- [x] 基礎爬蟲架構

### Phase 2 (開發中)
- [ ] 黃金28爬蟲優化
- [ ] 爬蟲穩定性優化
- [ ] 錯誤處理機制

### Phase 3 (規劃中)
- [ ] 晉江文學城技術評估
- [ ] 選擇2-3個第二階段網站
- [ ] 多源數據整合策略

## 🔄 維護說明

- **更新頻率**: 每季度檢查網站可用性
- **技術評估**: 每半年重新評估反爬策略
- **優先級調整**: 根據爬取成功率和內容質量動態調整

---

**最後更新**: 2025-06-22
**維護者**: 爬蟲開發團隊
