# Issue #173 實作計畫
## Frontend 路徑統一與 Legacy 清理

### 🎯 總體目標
將舊版 frontend/ (CRA + Storybook) 從所有活躍 Dockerfile、CI workflow 及快取邏輯中移除，統一使用 apps/web-next/ 作為主線。

### 📊 依賴關係分析
- **前置條件**: Issue #156 (CI 清理) 需要部分完成
- **並行可能**: 部分任務可以與 #156 並行執行
- **風險控制**: 分階段執行，確保每步可回滾

## Phase A: CI 清理與快取重置 (Issue #156 整合)

### 🔧 A1: CI Workflow 路徑清理
**目標**: 清理 .github/workflows/main-ci.yml 中的 82 處 frontend/ 引用

**具體任務**:
- [ ] 備份當前 main-ci.yml
- [ ] 將所有 frontend/ 路徑替換為 apps/web-next/
- [ ] 更新 cache key 以避免舊快取混淆
- [ ] 移除已廢棄的 frontend 相關 jobs
- [ ] 測試 CI 流程完整性

**驗收標準**:
- [ ] CI 中無任何 frontend/ 路徑引用
- [ ] 所有測試通過
- [ ] 建置時間保持在合理範圍

### 🔧 A2: Docker 快取清理
**目標**: 確保 ECR 和本地快取不含 frontend/ 層

**具體任務**:
- [ ] 清理 ECR registry 中的舊映像層
- [ ] 更新 Docker buildx 快取策略
- [ ] 驗證新的快取鍵不會衝突

## Phase B: 路徑統一執行 (Issue #173 核心)

### 🔧 B1: Dockerfile 路徑統一
**目標**: 確保所有 Dockerfile 使用一致的路徑

**具體任務**:
- [ ] 更新根目錄 Dockerfile: apps/web/ → apps/web-next/
- [ ] 檢查 legacy/frontend-ci.Dockerfile 是否仍在使用
- [ ] 驗證所有 Docker 建置成功

### 🔧 B2: 配置文件路徑更新
**目標**: 統一配置文件中的路徑引用

**具體任務**:
- [ ] 更新 .pre-commit-config.yaml 中的 turbo lint 目標
- [ ] 修正 eslint.config.js 的 pattern 規則
- [ ] 調整 scripts/run-frontend-tests.sh 的檢查邏輯

### 🔧 B3: PR Template 建立
**目標**: 確保未來 PR 的標籤一致性

**具體任務**:
- [ ] 創建 .github/PULL_REQUEST_TEMPLATE.md
- [ ] 加入 "Sunset frontend/" 標籤檢查
- [ ] 加入路徑一致性檢查清單

## Phase C: 最終清理與文檔

### 🔧 C1: apps/web-sunset 處理
**目標**: 安全移除或歸檔 apps/web-sunset

**具體任務**:
- [ ] 確認 apps/web-sunset 已完整備份
- [ ] 移除 pnpm workspace 中的 web-sunset 引用
- [ ] 清理相關的 CI 引用

### 🔧 C2: 文檔更新
**目標**: 記錄變更並更新技術債務

**具體任務**:
- [ ] 更新 TECH_DEBT.md 記錄 frontend/ 日落
- [ ] 更新架構文檔反映新的路徑結構
- [ ] 創建遷移記錄文檔

## 🚦 執行順序與 PR 策略

### PR #1: ci/sunset-frontend-workflow-refactor (P0)
- 內容: A1 - CI Workflow 路徑清理
- 優先級: 最高，阻塞其他工作

### PR #2: docker/root-update-apps-web-next (P1)
- 內容: B1 - Dockerfile 路徑統一
- 依賴: PR #1 合併後

### PR #3: infra/ensure-sunset-label-template (P1)
- 內容: B3 - PR Template 建立
- 可並行: 與 PR #2 並行執行

### PR #4: config/pre-commit-eslint-path-update (P1)
- 內容: B2 - 配置文件路徑更新
- 依賴: PR #1, #2 合併後

### PR #5: cleanup/remove-web-sunset (P2)
- 內容: C1 - apps/web-sunset 處理
- 依賴: 所有前置 PR 合併後

### PR #6: docs/update-tech-debt-sunset-note (P2)
- 內容: C2 - 文檔更新
- 可並行: 與其他 PR 並行執行

## ⚠️ 風險控制

### 回滾策略
- 每個 PR 都包含完整的回滾指令
- 保留所有原始配置的備份
- 分階段合併，確保每步可獨立驗證

### 驗證檢查點
- [ ] 每個 PR 合併前執行完整 CI 測試
- [ ] 本地建置驗證
- [ ] 快取一致性檢查

## 📈 成功指標

### DoD 更新
- [ ] CI pipeline 不再含任何 frontend/ pattern
- [ ] 所有構建路徑僅指向 apps/web-next
- [ ] Legacy apps/web-sunset 安全備份且引用清空
- [ ] 所有 PR 使用一致 Sunset frontend/ 標籤
- [ ] 快取、路徑一致性在 PR Template 中強制核對

### 性能指標
- [ ] CI 執行時間不增加
- [ ] Docker 建置時間保持穩定
- [ ] 快取命中率不下降
