# 🛡️ Validator - AI 質量與戰略驗證引擎

## 📁 目錄結構

```
docs/04_AI_OPERATIONS/validator/
├── README.md                           # 本說明文檔
├── YYYY-MM-DD_Issue-XXX_Validation-Report.html  # 驗證報告
└── strategic-anchor-reports/           # 戰略錨定報告目錄
    └── YYYY-MM-DD_Issue-XXX_strategic-anchor.md
```

## 🎯 角色定位

Validator 是 NovelWebsite 專案的**後合併交付品質驗證引擎**，負責：

- **🔬 行為驗證**: 執行整合測試、端到端測試、安全檢查
- **🏛️ 戰略驗證**: 審計戰略意圖達成度、檢測 AI 團隊創造性漂移
- **📊 報告生成**: 產出綜合驗證報告和戰略錨定建議

## 📋 觸發時機

**代碼成功合併到 main 分支後**，由以下方式觸發：
```bash
/validator.md <ISSUE_NUMBER> [full|behavioral-only|strategic-only]
```

## 📊 驗證報告說明

### 主要驗證報告
- **命名格式**: `YYYY-MM-DD_Issue-XXX_Validation-Report.html`
- **內容**: 兩階段驗證結果、DoD 審核、系統狀態評估
- **用途**: 確認交付品質、標記系統可交付狀態

### 戰略錨定報告
- **位置**: `strategic-anchor-reports/`
- **命名格式**: `YYYY-MM-DD_Issue-XXX_strategic-anchor.md`
- **內容**: AI 團隊創造性漂移分析、戰略校準建議
- **用途**: Navigator 下次規劃循環的強制輸入

## 🔄 與其他 AI 角色的整合

- **Navigator**: 接收戰略錨定報告，調整下次規劃策略
- **Task-Dispatcher**: 驗證並行分派策略的實際執行效果
- **merge-planner**: 確認合併決策的最終品質影響

## 📈 歷史記錄

- **2025-06-26**: 首次執行驗證 (Issue #134 - 安全漏洞修復)
  - 總體裁決: ✅ BEHAVIORAL PASS
  - 戰略達成度: 高 (100% 完成)
  - 可交付狀態: 確認

---

🤖 **版本**: v1.0
📅 **更新時間**: 2025-06-26
🔗 **角色檔案**: `.claude/commands/validator.md`
