# 🛡️ 安全提交指南 - 防範大檔案和敏感信息

## 🎯 黃金提交流程

### ✅ 推薦做法

```bash
# 1. 完成程式碼修改後，先檢查狀態
git status

# 2. 仔細查看 "Untracked files" 列表
# 特別注意：*.tar.gz, *.zip, venv/, node_modules/, *.log

# 3. 精確添加需要的檔案 (永不盲目 git add .)
git add src/specific_file.py
git add docs/new_feature.md

# 4. 再次確認暫存區內容
git status
git diff --staged

# 5. 提交前最後檢查
git commit -m "feat: implement new feature"
```

### ❌ 危險做法

```bash
# 🚫 永遠不要這樣做
git add .              # 盲目添加所有檔案
git add -A             # 同樣危險
git commit -am "..."   # 跳過檢查步驟
```

## 🛡️ 三層防護網

### 第一層：.gitignore 規則防護

- 自動忽略常見的大檔案模式
- 涵蓋實驗檔案、壓縮檔、虛擬環境

### 第二層：習慣流程防護

- 養成精確添加檔案的習慣
- 提交前多次確認暫存區內容

### 第三層：pre-commit 自動防護

- 5MB檔案大小限制
- 敏感信息自動掃描
- 程式碼品質檢查

## 🚨 緊急狀況處理

### 如果意外提交了大檔案

```bash
# 1. 立即停止推送
# 按 Ctrl+C 取消正在進行的 git push

# 2. 移除大檔案並重新提交
git rm --cached large_file.tar.gz
git commit --amend -m "fix: remove accidentally added large file"

# 3. 更新 .gitignore
echo "large_file.tar.gz" >> .gitignore
git add .gitignore
git commit -m "chore: update gitignore to prevent large files"
```

### 如果已經推送到遠端

```bash
# 使用更強力的歷史重寫工具
git filter-branch --tree-filter 'rm -f large_file.tar.gz' HEAD
git push --force-with-lease origin branch_name
```

## 📋 提交前檢查清單

- [ ] 執行 `git status` 查看檔案清單
- [ ] 確認沒有 .tar.gz, .zip 等壓縮檔
- [ ] 確認沒有 venv/, node_modules/ 等大型目錄
- [ ] 確認沒有 _.log, _.db 等資料檔案
- [ ] 使用 `git add <specific_files>` 精確添加
- [ ] 執行 `git diff --staged` 確認變更內容
- [ ] pre-commit hooks 全部通過

## 🎓 最佳實踐建議

1. **實驗檔案管理**

   - 實驗用的壓縮檔放在專案外部
   - 使用 `/tmp` 或專用實驗目錄

2. **大檔案處理**

   - 考慮使用 Git LFS
   - 或將大檔案存儲在雲端（AWS S3, Google Drive）

3. **定期清理**
   ```bash
   # 清理未追蹤的檔案（謹慎使用）
   git clean -n  # 預覽會被刪除的檔案
   git clean -f  # 確認後執行清理
   ```

記住：**預防勝於治療**。養成良好的提交習慣比事後修復更重要！
