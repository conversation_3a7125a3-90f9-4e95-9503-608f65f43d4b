# Makefile 使用指南

## 🎯 設計理念

NovelWebsite的Makefile旨在提供統一、便利的開發命令介面，解決跨專案、跨環境的命令不一致問題。

### 核心原則

- **統一介面**: 一個`make`命令解決所有開發需求
- **自我說明**: 內建完整的help文檔
- **跨平台相容**: 支援macOS、Linux、Windows (WSL)
- **錯誤處理**: 友善的錯誤提示和fallback機制
- **路徑一致**: 所有命令自動在正確目錄執行

## 📋 完整命令清單

### 🚀 整合命令 (最常用)

```bash
make help                   # 📖 查看所有可用命令與說明
make dev                    # 🚀 並行啟動前後端開發伺服器 (最常用)
make test                   # 🧪 執行所有測試 (前端+後端)
make lint                   # 🔍 程式碼品質檢查 (ESLint + flake8)
make ci-check              # 🔄 本地CI/CD完整驗證
make quickstart            # ⚡ 一鍵設置並啟動開發環境
```

### 🔧 後端命令

```bash
make backend-dev           # 🌐 Django開發伺服器
make backend-test          # 🧪 Django測試
make backend-migrate       # 📊 資料庫遷移
make backend-makemigrations # 📝 建立新的遷移檔案
make backend-shell         # 🐚 Django shell
make backend-collectstatic # 📦 收集靜態檔案
```

### ⚛️ 前端命令

```bash
make frontend-dev          # ⚛️ React開發伺服器
make frontend-test         # 🧪 前端測試
make frontend-build        # 📦 建置生產版本
make frontend-lint         # 🔍 ESLint檢查
make frontend-type-check   # 📝 TypeScript類型檢查
```

### 🕷️ 爬蟲命令

```bash
make crawler-ttkan         # 🕷️ 執行TTKAN爬蟲
make crawler-czbooks       # 🕷️ 執行CZBooks爬蟲
make crawler-worker        # ⚙️ 啟動爬蟲worker
```

### 🛠️ 開發工具

```bash
make format               # ✨ 自動格式化所有程式碼
make clean                # 🧹 清理快取和建置檔案
make install-deps         # 📦 安裝所有依賴
```

### 🐳 Docker命令

```bash
make docker-up            # 🐳 啟動Docker services
make docker-down          # 🐳 停止Docker services
```

## 🔧 自訂和擴展

### 添加新的make targets

編輯專案根目錄的`Makefile`，遵循現有模式：

```makefile
# 新的後端命令範例
backend-custom:
	@echo "🎯 執行自訂後端任務..."
	cd backend && python manage.py custom_command

# 新的整合命令範例
custom-workflow: backend-custom frontend-test
	@echo "✅ 自訂工作流程完成"
```

### 錯誤處理模式

```makefile
# 帶有錯誤處理的範例
safe-command:
	@echo "🚀 執行安全命令..."
	cd backend && python manage.py command || echo "⚠️ 命令失敗，但繼續執行"
	@echo "✅ 處理完成"
```

### 條件執行

```makefile
# 檢查工具是否存在
dev-with-concurrently:
	@if command -v concurrently >/dev/null 2>&1; then \
		npx concurrently "make backend-dev" "make frontend-dev"; \
	else \
		echo "⚠️ 需要安裝 concurrently: npm install -g concurrently"; \
		echo "或者分別執行: make backend-dev 和 make frontend-dev"; \
	fi
```

## 🎨 最佳實踐

### 1. 目錄管理

所有命令自動切換到正確目錄，無需手動`cd`：

```makefile
# ✅ 好的做法
backend-command:
	cd backend && python manage.py command

# ❌ 不建議
backend-command:
	python backend/manage.py command  # 可能導致路徑問題
```

### 2. 錯誤提示

使用友善的錯誤提示：

```makefile
check-tool:
	@command -v tool >/dev/null 2>&1 || { \
		echo "❌ 找不到必要工具: tool"; \
		echo "💡 安裝方式: brew install tool"; \
		exit 1; \
	}
```

### 3. 進度顯示

使用表情符號和清晰的訊息：

```makefile
long-task:
	@echo "🚀 開始執行長時間任務..."
	@echo "📊 處理數據..."
	actual-command
	@echo "✅ 任務完成！"
```

## 🔄 與CI/CD整合

### GitHub Actions整合

```yaml
# .github/workflows/ci.yml 中使用
- name: Run tests
  run: make test

- name: Lint code
  run: make lint

- name: Build application
  run: make frontend-build
```

### 本地CI驗證

```bash
# 完整的CI流程
make ci-check

# 等同於手動執行：
make lint
make test
make frontend-build
./scripts/maintenance/check_sensitive.sh
```

## 🆘 故障排除

### 常見問題

**Q: make命令找不到？**

```bash
# macOS
brew install make

# Ubuntu/Debian
sudo apt-get install build-essential

# Windows (使用chocolatey)
choco install make
```

**Q: 權限問題？**

```bash
# 確保Makefile有執行權限
chmod +x Makefile

# 檢查腳本權限
ls -la scripts/
chmod +x scripts/*.sh
```

**Q: 命令執行失敗？**

```bash
# 查看詳細錯誤 (增加verbosity)
make -d target_name

# 檢查環境
make help  # 確認命令存在
which python  # 確認工具路徑
```

### 調試技巧

```bash
# 乾運行 (dry run) - 只顯示命令不執行
make -n target_name

# 顯示變數值
make print-VARIABLE_NAME

# 忽略錯誤繼續執行
make -i target_name
```

## 🔗 相關資源

- [GNU Make Manual](https://www.gnu.org/software/make/manual/make.html)
- [Makefile教學](https://makefiletutorial.com/)
- [專案CLAUDE.md](../CLAUDE.md) - 完整開發指南
- [專案README.md](../README.md) - 快速開始指南

## 💡 提示與技巧

### 自動補全

在`.bashrc`或`.zshrc`中添加：

```bash
# Make target自動補全
complete -W "$(make -qp | awk -F':' '/^[a-zA-Z0-9][^$#\/\t=]*:([^=]|$)/ {split($1,A,/ /);for(i in A)print A[i]}' | sort -u)" make
```

### 別名設置

```bash
# 常用別名
alias mh='make help'
alias md='make dev'
alias mt='make test'
alias mc='make ci-check'
```

### 平行執行

```bash
# 同時執行多個目標
make -j4 frontend-test backend-test  # 使用4個並行任務
```

---

這個Makefile系統讓NovelWebsite的開發變得更加統一和高效。無論您是新手還是進階用戶，都能找到適合的工作方式！
