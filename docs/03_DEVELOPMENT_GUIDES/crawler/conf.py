# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

import os
import sys

sys.path.insert(0, os.path.abspath("../../backend/novel"))

project = "Novel Crawler"
copyright = "2025, Novel Team"
author = "Novel Team"

version = "0.1"
release = "0.1"

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    "sphinx.ext.autodoc",
    "sphinx.ext.napoleon",
    "sphinx_autodoc_typehints",
]

nitpicky = True
autodoc_mock_imports = [
    "redis",
    "scrapy",
    "django",
    "itemadapter",
    "aiohttp",
    "asyncpg",
    "novel",
    "bs4",
    "scrapy_redis",
    "fontTools",
    "aioredis",
    "crawler.main",
    "crawler.spiders.run_ttkan_spider",
]

nitpick_ignore = [
    ("py:class", "scrapy.Item"),
    ("py:class", "django.db.models.Model"),
    ("py:class", "django.db.migrations.Migration"),
    ("py:class", "abc.ABC"),
    ("py:data", "typing.Optional"),
    ("py:class", "scrapy.Spider"),
    ("py:data", "typing.Callable"),
    ("py:data", "typing.Any"),
    ("py:class", "scrapy_redis.spiders.RedisSpider"),
]

suppress_warnings = ["autodoc.*"]

templates_path = ["_templates"]
exclude_patterns = ["_build", "Thumbs.db", ".DS_Store"]


# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = "alabaster"
html_static_path = ["_static"]
