crawler package
===============

Subpackages
-----------

.. toctree::
   :maxdepth: 4

   crawler.migrations
   crawler.spiders

Submodules
----------

crawler.base\_crawler module
----------------------------

.. automodule:: crawler.base_crawler
   :members:
   :show-inheritance:
   :undoc-members:

crawler.config module
---------------------

.. automodule:: crawler.config
   :members:
   :show-inheritance:
   :undoc-members:

crawler.items module
--------------------

.. automodule:: crawler.items
   :members:
   :show-inheritance:
   :undoc-members:

crawler.main module
-------------------

.. automodule:: crawler.main
   :members:
   :show-inheritance:
   :undoc-members:

crawler.models module
---------------------

.. automodule:: crawler.models
   :members:
   :show-inheritance:
   :undoc-members:

crawler.pipelines module
------------------------

.. automodule:: crawler.pipelines
   :members:
   :show-inheritance:
   :undoc-members:

crawler.settings module
-----------------------

.. automodule:: crawler.settings
   :members:
   :show-inheritance:
   :undoc-members:

crawler.types module
--------------------

.. automodule:: crawler.types
   :members:
   :show-inheritance:
   :undoc-members:

Module contents
---------------

.. automodule:: crawler
   :members:
   :show-inheritance:
   :undoc-members:
