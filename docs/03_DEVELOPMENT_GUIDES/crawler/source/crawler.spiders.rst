crawler.spiders package
=======================

Submodules
----------

crawler.spiders.base\_spider module
-----------------------------------

.. automodule:: crawler.spiders.base_spider
   :members:
   :show-inheritance:
   :undoc-members:

crawler.spiders.novel\_spider module
------------------------------------

.. automodule:: crawler.spiders.novel_spider
   :members:
   :show-inheritance:
   :undoc-members:

crawler.spiders.qidian\_finish\_crawler module
----------------------------------------------

.. automodule:: crawler.spiders.qidian_finish_crawler
   :members:
   :show-inheritance:
   :undoc-members:

crawler.spiders.qidian\_yuepiao module
--------------------------------------

.. automodule:: crawler.spiders.qidian_yuepiao
   :members:
   :show-inheritance:
   :undoc-members:

crawler.spiders.run\_ttkan\_spider module
-----------------------------------------

.. automodule:: crawler.spiders.run_ttkan_spider
   :members:
   :show-inheritance:
   :undoc-members:

crawler.spiders.test\_ttkan module
----------------------------------

.. automodule:: crawler.spiders.test_ttkan
   :members:
   :show-inheritance:
   :undoc-members:

crawler.spiders.test\_ttkan\_storage module
-------------------------------------------

.. automodule:: crawler.spiders.test_ttkan_storage
   :members:
   :show-inheritance:
   :undoc-members:

crawler.spiders.ttkan\_novel module
-----------------------------------

.. automodule:: crawler.spiders.ttkan_novel
   :members:
   :show-inheritance:
   :undoc-members:

crawler.spiders.ttkan\_spider module
------------------------------------

.. automodule:: crawler.spiders.ttkan_spider
   :members:
   :show-inheritance:
   :undoc-members:

Module contents
---------------

.. automodule:: crawler.spiders
   :members:
   :show-inheritance:
   :undoc-members:
