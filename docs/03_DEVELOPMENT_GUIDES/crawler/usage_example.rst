Typical Usage
=============

The crawler can be executed directly from the project root. For example::

   python backend/novel/crawler/main.py https://www.ttkan.co/novel/chapters/<novel-id>

By default the ``ttkan`` spider is used. You can specify a different spider with
``--spider``:

   python backend/novel/crawler/main.py https://example.com/novel --spider ttkan

Use ``--force`` to ignore existing cache and crawl again::

   python backend/novel/crawler/main.py https://example.com/novel --force
