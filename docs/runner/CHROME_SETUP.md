# 🌐 Self-hosted Runner Chrome 安裝指南

本文件說明如何在 Amazon Linux self-hosted GitHub Actions runner 上安裝 Chrome 瀏覽器，以支援 Lighthouse 效能測試。

## 🚨 問題背景

**問題**: CI workflow 硬編碼使用 Debian 的 `.deb` 安裝方式：
```bash
# ❌ 僅適用於 Debian/Ubuntu
curl -sSL https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb -o chrome.deb
sudo apt-get install -y ./chrome.deb
```

**現實**: self-hosted runner 運行在 Amazon Linux，需要 `yum`/`dnf` + `.rpm` 方式。

## ✅ 解決方案：預安裝 Chrome

### 方式 1: 自動化腳本 (推薦)

```bash
# 在 runner 機器上執行
./scripts/runner/install-chrome-amazon-linux.sh
```

### 方式 2: 手動安裝

```bash
# 1. 設置 Google Chrome YUM repository
sudo tee /etc/yum.repos.d/google-chrome.repo <<'EOF'
[google-chrome]
name=google-chrome
baseurl=http://dl.google.com/linux/chrome/rpm/stable/x86_64
enabled=1
gpgcheck=1
gpgkey=https://dl.google.com/linux/linux_signing_key.pub
EOF

# 2. 導入 GPG 金鑰
sudo rpm --import https://dl.google.com/linux/linux_signing_key.pub

# 3. 安裝 Chrome
sudo yum install -y google-chrome-stable

# 4. 安裝 Lighthouse 相關依賴
sudo yum install -y \
    liberation-fonts \
    liberation-narrow-fonts \
    liberation-sans-fonts \
    liberation-serif-fonts \
    xorg-x11-server-Xvfb

# 5. 驗證安裝
google-chrome --version
```

## 🔍 CI Workflow 變更

**之前** (每次重複安裝):
- 下載 .deb 文件
- 使用 apt-get 安裝
- 浪費時間，且不兼容 Amazon Linux

**現在** (驗證預安裝):
- 檢查 `google-chrome` 命令是否存在
- 如未安裝，提供清晰的安裝指令
- 快速、安全、可維護

## 📋 故障排除

### 問題 1: Chrome 未安裝
```
❌ Chrome 未在 runner 預安裝，需要系統管理員安裝
```
**解決**: 在 runner 執行安裝腳本或手動安裝命令

### 問題 2: 字體缺失
```
Chrome 啟動失敗，缺少字體
```
**解決**: 安裝 liberation-fonts 套件群組

### 問題 3: X11 相關錯誤
```
Chrome headless 模式失敗
```
**解決**: 安裝 xorg-x11-server-Xvfb

## 🎯 效益分析

### ✅ 預安裝優勢
- **效能**: 跳過每次下載+安裝，節省 30-60 秒
- **可靠性**: 避免網路下載失敗風險
- **安全性**: 不需要在 CI 中執行 sudo 安裝
- **兼容性**: 使用正確的套件管理器

### ⚠️ 維護成本
- **一次性設置**: 每台 runner 需要初始安裝
- **版本更新**: 定期更新 Chrome (yum update)
- **依賴管理**: 確保相關字體和函式庫存在

## 🔄 Chrome 更新

```bash
# 更新 Chrome 到最新版
sudo yum update google-chrome-stable

# 檢查版本
google-chrome --version
```

## 📊 AWS API 整合 (進階)

如使用 AWS Systems Manager 管理多台 runner：

```bash
# 透過 SSM 在所有 runner 執行安裝
aws ssm send-command \
    --document-name "AWS-RunShellScript" \
    --parameters 'commands=["curl -sSL https://raw.githubusercontent.com/your-org/novel-web/main/scripts/runner/install-chrome-amazon-linux.sh | bash"]' \
    --targets "Key=tag:Role,Values=github-runner"
```

---

**最後更新**: 2025-06-28
**維護者**: NovelWebsite DevOps Team
