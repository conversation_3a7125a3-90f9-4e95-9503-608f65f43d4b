#!/bin/bash

# DevContext配置驗證腳本
set -e

echo "🔍 DevContext配置驗證"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 檢查MCP配置文件
echo -e "${BLUE}📋 檢查MCP配置文件...${NC}"
if [ -f ".cursor/mcp.json" ]; then
    echo -e "${GREEN}✅ .cursor/mcp.json 存在${NC}"

    # 檢查DevContext配置
    if grep -q "devcontext" .cursor/mcp.json; then
        echo -e "${GREEN}✅ DevContext配置已找到${NC}"

        # 檢查Turso配置
        if grep -q "TURSO_DATABASE_URL" .cursor/mcp.json; then
            echo -e "${GREEN}✅ Turso數據庫URL已配置${NC}"
        else
            echo -e "${RED}❌ Turso數據庫URL未配置${NC}"
        fi

        if grep -q "TURSO_AUTH_TOKEN" .cursor/mcp.json; then
            echo -e "${GREEN}✅ Turso認證令牌已配置${NC}"
        else
            echo -e "${RED}❌ Turso認證令牌未配置${NC}"
        fi
    else
        echo -e "${RED}❌ DevContext配置未找到${NC}"
    fi
else
    echo -e "${RED}❌ .cursor/mcp.json 不存在${NC}"
fi

# 檢查Turso CLI
echo -e "\n${BLUE}🔧 檢查Turso CLI...${NC}"
if command -v turso &> /dev/null; then
    echo -e "${GREEN}✅ Turso CLI已安裝${NC}"

    # 檢查認證
    if turso auth whoami &> /dev/null; then
        USER=$(turso auth whoami)
        echo -e "${GREEN}✅ Turso已認證，用戶: $USER${NC}"
    else
        echo -e "${RED}❌ Turso未認證${NC}"
    fi

    # 檢查數據庫
    if turso db show novelwebsite &> /dev/null; then
        echo -e "${GREEN}✅ novelwebsite數據庫可訪問${NC}"
    else
        echo -e "${RED}❌ novelwebsite數據庫不可訪問${NC}"
    fi
else
    echo -e "${RED}❌ Turso CLI未安裝${NC}"
fi

# 檢查Node.js和npm
echo -e "\n${BLUE}🟢 檢查Node.js環境...${NC}"
if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js已安裝: $NODE_VERSION${NC}"

    # 檢查版本
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_MAJOR" -ge 18 ]; then
        echo -e "${GREEN}✅ Node.js版本符合要求 (>= 18)${NC}"
    else
        echo -e "${RED}❌ Node.js版本過低，需要 >= 18${NC}"
    fi
else
    echo -e "${RED}❌ Node.js未安裝${NC}"
fi

# 測試DevContext安裝
echo -e "\n${BLUE}📦 測試DevContext安裝...${NC}"
if npx devcontext@latest --version &> /dev/null; then
    VERSION=$(npx devcontext@latest --version 2>/dev/null)
    echo -e "${GREEN}✅ DevContext可用: $VERSION${NC}"
else
    echo -e "${RED}❌ DevContext不可用${NC}"
fi

echo -e "\n${BLUE}📝 配置總結${NC}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "數據庫URL: libsql://novelwebsite-mumutw.aws-ap-northeast-1.turso.io"
echo "用戶: $(turso auth whoami 2>/dev/null || echo 'unknown')"
echo "項目: NovelWebsite爬蟲"

echo -e "\n${GREEN}✅ 配置驗證完成！${NC}"
echo -e "${YELLOW}💡 請重啟Cursor IDE以載入新的MCP配置${NC}"
echo -e "${YELLOW}💡 在新對話中測試: @devcontext initialize_conversation_context${NC}"
