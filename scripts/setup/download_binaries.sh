#!/usr/bin/env bash
# Simple helper script to fetch optional binary dependencies for the project.
# Usage: ./scripts/download_binaries.sh

set -euo pipefail

# directory to place downloaded binaries
BIN_DIR="$(pwd)"

# Download cursor_id_modifier for macOS (amd64)
CURSOR_ID_MODIFIER_URL="https://github.com/your-org/cursor-id-modifier/releases/download/v0.0.6/cursor_id_modifier_0.0.6_darwin_amd64"

if [ ! -f "$BIN_DIR/cursor_id_modifier_0.0.6_darwin_amd64" ]; then
  echo "Downloading cursor_id_modifier..."
  curl -L -o "$BIN_DIR/cursor_id_modifier_0.0.6_darwin_amd64" "$CURSOR_ID_MODIFIER_URL"
  chmod +x "$BIN_DIR/cursor_id_modifier_0.0.6_darwin_amd64"
fi

# Download AWS Session Manager plugin bundle
SESSION_MANAGER_URL="https://s3.amazonaws.com/session-manager-downloads/plugin/latest/ubuntu_64bit/sessionmanager-bundle.zip"

if [ ! -f "$BIN_DIR/sessionmanager-bundle.zip" ]; then
  echo "Downloading sessionmanager-bundle.zip..."
  curl -L -o "$BIN_DIR/sessionmanager-bundle.zip" "$SESSION_MANAGER_URL"
fi

if [ ! -d "$BIN_DIR/sessionmanager-bundle" ]; then
  echo "Extracting sessionmanager-bundle..."
  unzip -q "$BIN_DIR/sessionmanager-bundle.zip" -d "$BIN_DIR/sessionmanager-bundle"
fi

echo "Binaries downloaded successfully."
