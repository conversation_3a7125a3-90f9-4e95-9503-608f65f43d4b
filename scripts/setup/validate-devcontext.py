#!/usr/bin/env python3
"""
DevContext 連接和功能驗證腳本
簡化版驗證工具，用於快速檢查 DevContext 是否正常工作
"""

import os
from pathlib import Path


def check_environment():
    """檢查環境變數配置"""
    print("🔍 檢查環境配置...")

    required_vars = ["TURSO_DATABASE_URL", "TURSO_AUTH_TOKEN"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"  ✅ {var}: 已配置")

    if missing_vars:
        print(f"  ❌ 缺少環境變數: {', '.join(missing_vars)}")
        return False

    return True


def check_mcp_config():
    """檢查 MCP 配置文件"""
    print("\n📄 檢查 MCP 配置...")

    mcp_file = Path.home() / ".cursor" / "mcp.json"
    if not mcp_file.exists():
        print(f"  ❌ MCP 配置文件不存在: {mcp_file}")
        return False

    try:
        import json

        with open(mcp_file) as f:
            config = json.load(f)

        if "mcpServers" not in config:
            print("  ❌ MCP 配置缺少 mcpServers 部分")
            return False

        if "devcontext" not in config["mcpServers"]:
            print("  ❌ MCP 配置缺少 devcontext 服務器")
            return False

        print("  ✅ MCP 配置文件存在並包含 devcontext")
        return True

    except json.JSONDecodeError:
        print("  ❌ MCP 配置文件格式錯誤")
        return False
    except Exception as e:
        print(f"  ❌ 讀取 MCP 配置失敗: {e}")
        return False


def check_schema_files():
    """檢查結構文件是否存在"""
    print("\n📋 檢查結構文件...")

    schema_file = Path(__file__).parent.parent / "docs" / "devcontext-schema.sql"
    setup_guide = Path(__file__).parent.parent / "docs" / "devcontext-setup.md"

    files_to_check = [
        (schema_file, "DevContext 結構文件"),
        (setup_guide, "設置指南文檔"),
    ]

    all_exist = True
    for file_path, description in files_to_check:
        if file_path.exists():
            print(f"  ✅ {description}: {file_path}")
        else:
            print(f"  ❌ {description} 不存在: {file_path}")
            all_exist = False

    return all_exist


def check_database_connectivity():
    """測試數據庫連接"""
    print("\n🔗 測試數據庫連接...")

    db_url = os.getenv("TURSO_DATABASE_URL")
    auth_token = os.getenv("TURSO_AUTH_TOKEN")

    if not db_url or not auth_token:
        print("  ⏩ 跳過連接測試（缺少環境變數）")
        return False

    try:
        if db_url.startswith("libsql://"):
            # 嘗試使用 libsql
            try:
                import libsql_experimental as libsql

                conn = libsql.connect(db_url, auth_token=auth_token)
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                conn.close()

                if result and result[0] == 1:
                    print("  ✅ Turso 數據庫連接成功")
                    return True
                else:
                    print("  ❌ 數據庫連接失敗")
                    return False

            except ImportError:
                print("  ⚠️  需要安裝 libsql_experimental 來測試 Turso 連接")
                print("     pip install libsql_experimental")
                return False
        else:
            # 本地 SQLite 測試
            import sqlite3

            conn = sqlite3.connect(db_url)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0] == 1:
                print("  ✅ 本地數據庫連接成功")
                return True
            else:
                print("  ❌ 本地數據庫連接失敗")
                return False

    except Exception as e:
        print(f"  ❌ 數據庫連接失敗: {e}")
        return False


def generate_test_suggestions():
    """生成測試建議"""
    print("\n💡 測試建議:")
    print("   1. 在 Cursor 中執行以下命令測試 DevContext:")
    print("      @devcontext initialize_conversation_context")
    print("   2. 如果遇到問題，請檢查：")
    print("      - Turso 數據庫狀態")
    print("      - MCP 服務器是否正確安裝")
    print("      - 環境變數是否正確設置")
    print("   3. 使用完整管理腳本進行詳細檢查：")
    print("      python scripts/devcontext-init.py --validate")


def main():
    """主函數"""
    print("🚀 DevContext 快速驗證工具")
    print("=" * 50)

    checks = [
        ("環境變數", check_environment),
        ("MCP 配置", check_mcp_config),
        ("結構文件", check_schema_files),
        ("數據庫連接", check_database_connectivity),
    ]

    passed_checks = 0
    total_checks = len(checks)

    for name, check_func in checks:
        try:
            if check_func():
                passed_checks += 1
        except Exception as e:
            print(f"  ❌ {name} 檢查時出錯: {e}")

    print("\n" + "=" * 50)
    print(f"📊 檢查結果: {passed_checks}/{total_checks} 項通過")

    if passed_checks == total_checks:
        print("🎉 所有檢查通過！DevContext 應該可以正常工作")
    elif passed_checks >= total_checks - 1:
        print("⚠️  大部分檢查通過，可能需要微調配置")
    else:
        print("❌ 多項檢查失敗，需要檢查配置")

    generate_test_suggestions()


if __name__ == "__main__":
    main()
