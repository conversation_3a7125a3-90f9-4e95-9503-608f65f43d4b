#!/bin/bash
# =============================================================================
# 開發環境檢查腳本 - scripts/dev-environment-check.sh
# 用途：確保開發環境配置完整，防止因缺少配置文件導致的問題
# =============================================================================

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 開始開發環境檢查...${NC}"
echo "========================================"

# 檢查計數器
missing_configs=0
total_checks=0

# 檢查函數
check_file_exists() {
    local file_path="$1"
    local description="$2"
    local required="$3"  # true/false

    ((total_checks++))

    if [ -f "$file_path" ]; then
        echo -e "${GREEN}✅ ${description}: ${file_path}${NC}"
        return 0
    else
        if [ "$required" = "true" ]; then
            echo -e "${RED}❌ ${description}: ${file_path} (必須)${NC}"
            ((missing_configs++))
        else
            echo -e "${YELLOW}⚠️  ${description}: ${file_path} (可選)${NC}"
        fi
        return 1
    fi
}

# 檢查目錄是否存在
check_directory() {
    local dir_path="$1"
    local description="$2"

    if [ -d "$dir_path" ]; then
        echo -e "${GREEN}✅ ${description}: ${dir_path}${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  ${description}: ${dir_path} (不存在)${NC}"
        return 1
    fi
}

# 1. 檢查MCP配置文件
echo -e "\n${BLUE}=== 檢查MCP配置文件 ===${NC}"

if check_file_exists ".cursor/mcp.json" "MCP配置文件" "true"; then
    # 檢查MCP配置內容
    if grep -q "\${" .cursor/mcp.json 2>/dev/null; then
        echo -e "${RED}❌ MCP配置文件包含未替換的占位符${NC}"
        echo -e "${YELLOW}   請編輯 .cursor/mcp.json 並替換 \${...} 為實際值${NC}"
        ((missing_configs++))
    else
        echo -e "${GREEN}✅ MCP配置文件格式正確${NC}"
    fi
else
    echo -e "${YELLOW}💡 建議執行以下命令創建MCP配置：${NC}"
    echo -e "   cp .cursor/mcp.json.template .cursor/mcp.json"
    echo -e "   然後編輯 .cursor/mcp.json 填入實際的API憑證"
fi

check_file_exists ".cursor/mcp.json.template" "MCP配置模板" "false"

# 2. 檢查環境變數和敏感文件
echo -e "\n${BLUE}=== 檢查環境和敏感文件 ===${NC}"

# 檢查是否有意外的敏感文件被追蹤
if [ -f ".env.devcontext" ]; then
    echo -e "${RED}❌ 發現 .env.devcontext 文件${NC}"
    echo -e "${YELLOW}   建議檢查是否包含敏感信息並添加到 .gitignore${NC}"
    ((missing_configs++))
fi

# 檢查.gitignore配置
if check_file_exists ".gitignore" "Git忽略配置" "true"; then
    if grep -q "\.cursor/mcp\.json" .gitignore 2>/dev/null; then
        echo -e "${GREEN}✅ .gitignore 已配置忽略 MCP 文件${NC}"
    else
        echo -e "${YELLOW}⚠️  建議在 .gitignore 中添加 .cursor/mcp.json${NC}"
    fi
fi

# 3. 檢查開發工具和依賴
echo -e "\n${BLUE}=== 檢查開發工具依賴 ===${NC}"

# 檢查Node.js和npm
if command -v node >/dev/null 2>&1; then
    node_version=$(node --version)
    echo -e "${GREEN}✅ Node.js: ${node_version}${NC}"
else
    echo -e "${RED}❌ Node.js 未安裝${NC}"
    ((missing_configs++))
fi

if command -v npm >/dev/null 2>&1; then
    npm_version=$(npm --version)
    echo -e "${GREEN}✅ npm: ${npm_version}${NC}"
else
    echo -e "${RED}❌ npm 未安裝${NC}"
    ((missing_configs++))
fi

# 檢查Python和pip
if command -v python3 >/dev/null 2>&1; then
    python_version=$(python3 --version)
    echo -e "${GREEN}✅ Python: ${python_version}${NC}"
else
    echo -e "${RED}❌ Python3 未安裝${NC}"
    ((missing_configs++))
fi

# 檢查uv (用於部分MCP工具)
if command -v uvx >/dev/null 2>&1; then
    echo -e "${GREEN}✅ uv/uvx 已安裝${NC}"
else
    echo -e "${YELLOW}⚠️  uv/uvx 未安裝 (部分MCP工具需要)${NC}"
    echo -e "${YELLOW}   安裝命令: curl -LsSf https://astral.sh/uv/install.sh | sh${NC}"
fi

# 4. 檢查項目結構
echo -e "\n${BLUE}=== 檢查項目結構 ===${NC}"

check_directory "backend" "後端目錄"
check_directory "frontend" "前端目錄"
check_directory "scripts" "腳本目錄"
check_directory ".taskmaster" "TaskMaster配置目錄"

# 檢查重要的腳本文件
check_file_exists "scripts/check_sensitive.sh" "敏感信息檢查腳本" "true"
check_file_exists "scripts/verify-devcontext.sh" "DevContext驗證腳本" "false"

# 5. 檢查package.json和requirements.txt
echo -e "\n${BLUE}=== 檢查依賴文件 ===${NC}"

check_file_exists "package.json" "Node.js依賴文件" "true"
check_file_exists "backend/requirements.txt" "Python依賴文件" "true"

# 6. 運行敏感信息檢查
echo -e "\n${BLUE}=== 運行安全檢查 ===${NC}"

if [ -x "scripts/check_sensitive.sh" ]; then
    echo -e "${BLUE}🔍 執行敏感信息掃描...${NC}"
    if ./scripts/check_sensitive.sh; then
        echo -e "${GREEN}✅ 敏感信息檢查通過${NC}"
    else
        echo -e "${RED}❌ 敏感信息檢查發現問題${NC}"
        ((missing_configs++))
    fi
else
    echo -e "${YELLOW}⚠️  敏感信息檢查腳本不可執行${NC}"
    echo -e "${YELLOW}   執行: chmod +x scripts/check_sensitive.sh${NC}"
fi

# 總結報告
echo -e "\n========================================"
echo -e "${BLUE}📊 開發環境檢查完成${NC}"
echo -e "總檢查項目: ${total_checks}"
echo -e "缺少配置: ${missing_configs}"

if [ $missing_configs -eq 0 ]; then
    echo -e "\n${GREEN}🎉 恭喜！開發環境配置完整，可以開始開發${NC}"
    echo -e "\n${BLUE}🚀 接下來可以：${NC}"
    echo -e "   1. 重啟 Cursor IDE 激活 MCP 工具鏈"
    echo -e "   2. 在對話中使用 @devcontext initialize_conversation_context"
    echo -e "   3. 運行 npm install && pip install -r backend/requirements.txt"
    echo -e "   4. 啟動開發服務器開始工作"
    exit 0
else
    echo -e "\n${RED}⚠️  發現 ${missing_configs} 個配置問題，請處理後再開始開發${NC}"
    echo -e "\n${YELLOW}💡 快速修復建議：${NC}"

    if [ ! -f ".cursor/mcp.json" ]; then
        echo -e "   1. 創建MCP配置: cp .cursor/mcp.json.template .cursor/mcp.json"
        echo -e "   2. 編輯MCP配置: 填入真實的API憑證"
    fi

    if ! grep -q "\.cursor/mcp\.json" .gitignore 2>/dev/null; then
        echo -e "   3. 更新.gitignore: echo '.cursor/mcp.json' >> .gitignore"
    fi

    echo -e "   4. 設置腳本權限: chmod +x scripts/*.sh"
    echo -e "   5. 重新運行此檢查: ./scripts/dev-environment-check.sh"

    exit 1
fi
