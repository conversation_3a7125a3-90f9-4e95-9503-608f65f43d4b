#!/usr/bin/env python3
"""
DevContext 數據庫初始化和維護腳本
NovelWebsite 項目 AI 增強開發工具鏈

功能：
1. 初始化 Turso 數據庫核心表結構
2. 驗證表完整性
3. 提供數據庫維護工具
4. 支持數據遷移和升級
"""

import sqlite3
import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict
from pathlib import Path

# 配置日誌
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DevContextDB:
    """DevContext 數據庫管理器"""

    def __init__(self, db_path: str = None):
        """初始化數據庫連接"""
        self.db_path = db_path or os.getenv("TURSO_DATABASE_URL", "devcontext.db")
        self.auth_token = os.getenv("TURSO_AUTH_TOKEN")

        # 定義核心表結構
        self.core_tables = {
            "git_commits": 6,
            "git_commit_files": 4,
            "code_entities": 18,
            "code_relationships": 8,
            "entity_keywords": 5,
            "project_documents": 12,
            "conversation_history": 7,
            "conversation_topics": 11,
            "background_ai_jobs": 13,
            "system_metadata": 3,
        }

    def get_connection(self) -> sqlite3.Connection:
        """獲取數據庫連接"""
        if self.db_path.startswith("libsql://"):
            # Turso 連接 (需要額外的 libsql 庫)
            try:
                import libsql_experimental as libsql

                return libsql.connect(self.db_path, auth_token=self.auth_token)
            except ImportError:
                logger.error("需要安裝 libsql_experimental 才能連接 Turso 數據庫")
                sys.exit(1)
        else:
            # 本地 SQLite 連接
            return sqlite3.connect(self.db_path)

    def initialize_schema(self) -> bool:
        """初始化數據庫結構"""
        try:
            # 讀取 SQL 結構文件
            schema_file = (
                Path(__file__).parent.parent / "docs" / "devcontext-schema.sql"
            )
            if not schema_file.exists():
                logger.error(f"找不到結構文件: {schema_file}")
                return False

            with open(schema_file, "r", encoding="utf-8") as f:
                schema_sql = f.read()

            # 執行結構創建
            with self.get_connection() as conn:
                conn.executescript(schema_sql)
                conn.commit()
                logger.info("✅ 數據庫結構初始化成功")
                return True

        except Exception as e:
            logger.error(f"❌ 初始化失敗: {e}")
            return False

    def validate_tables(self) -> Dict[str, Dict]:
        """驗證表結構完整性"""
        validation_results = {}

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                for table_name, expected_fields in self.core_tables.items():
                    # 檢查表是否存在
                    cursor.execute(
                        """
                        SELECT name FROM sqlite_master
                        WHERE type='table' AND name=?
                    """,
                        (table_name,),
                    )

                    table_exists = cursor.fetchone() is not None

                    if table_exists:
                        # 檢查字段數量
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        actual_fields = len(cursor.fetchall())

                        # 檢查記錄數量
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        record_count = cursor.fetchone()[0]

                        validation_results[table_name] = {
                            "exists": True,
                            "expected_fields": expected_fields,
                            "actual_fields": actual_fields,
                            "fields_match": actual_fields == expected_fields,
                            "record_count": record_count,
                            "status": "✅" if actual_fields == expected_fields else "⚠️",
                        }
                    else:
                        validation_results[table_name] = {
                            "exists": False,
                            "status": "❌",
                        }

        except Exception as e:
            logger.error(f"驗證過程出錯: {e}")

        return validation_results

    def print_validation_report(self, results: Dict[str, Dict]):
        """打印驗證報告"""
        print("\n" + "=" * 60)
        print("📊 DevContext 核心表結構驗證報告")
        print("=" * 60)

        for table_name, result in results.items():
            status = result.get("status", "❌")
            print(f"\n{status} {table_name}")

            if result.get("exists"):
                expected = result["expected_fields"]
                actual = result["actual_fields"]
                records = result["record_count"]

                print(f"   字段數量: {actual}/{expected}")
                print(f"   記錄數量: {records}")

                if not result["fields_match"]:
                    print("   ⚠️  字段數量不匹配！")
            else:
                print("   ❌ 表不存在")

        # 統計信息
        total_tables = len(results)
        valid_tables = sum(
            1 for r in results.values() if r.get("exists") and r.get("fields_match")
        )

        print(f"\n📈 總計: {valid_tables}/{total_tables} 個表結構正確")
        print("=" * 60)

    def get_system_info(self) -> Dict:
        """獲取系統信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 獲取系統元數據
                cursor.execute("SELECT key, value FROM system_metadata")
                metadata = dict(cursor.fetchall())

                # 獲取表信息
                cursor.execute(
                    """
                    SELECT name, sql FROM sqlite_master
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """
                )
                tables = cursor.fetchall()

                return {
                    "metadata": metadata,
                    "table_count": len(tables),
                    "tables": [table[0] for table in tables],
                    "database_path": self.db_path,
                }

        except Exception as e:
            logger.error(f"獲取系統信息失敗: {e}")
            return {}

    def cleanup_old_data(self, days: int = 30):
        """清理舊數據"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cutoff_timestamp = int((datetime.now().timestamp() - days * 86400))

                # 清理舊的對話記錄
                cursor.execute(
                    """
                    DELETE FROM conversation_history
                    WHERE timestamp < ?
                """,
                    (cutoff_timestamp,),
                )

                # 清理完成的後台任務
                cursor.execute(
                    """
                    DELETE FROM background_ai_jobs
                    WHERE status = 'completed' AND completed_at < ?
                """,
                    (cutoff_timestamp,),
                )

                conn.commit()

                logger.info(f"✅ 清理了 {days} 天前的數據")

        except Exception as e:
            logger.error(f"數據清理失敗: {e}")

    def export_schema(self, output_file: str = None) -> bool:
        """匯出數據庫結構"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = output_file or f"devcontext_schema_backup_{timestamp}.sql"

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 獲取所有表結構
                cursor.execute(
                    """
                    SELECT sql FROM sqlite_master
                    WHERE type IN ('table', 'index', 'view')
                    AND name NOT LIKE 'sqlite_%'
                    ORDER BY type, name
                """
                )

                schemas = cursor.fetchall()

                with open(output_file, "w", encoding="utf-8") as f:
                    f.write("-- DevContext 數據庫結構備份\n")
                    f.write(f"-- 生成時間: {datetime.now().isoformat()}\n\n")

                    for schema in schemas:
                        if schema[0]:  # 跳過空結構
                            f.write(schema[0] + ";\n\n")

                logger.info(f"✅ 結構已匯出到: {output_file}")
                return True

        except Exception as e:
            logger.error(f"匯出失敗: {e}")
            return False


def main():
    """主函數"""
    import argparse

    parser = argparse.ArgumentParser(description="DevContext 數據庫管理工具")
    parser.add_argument("--init", action="store_true", help="初始化數據庫結構")
    parser.add_argument("--validate", action="store_true", help="驗證表結構")
    parser.add_argument("--info", action="store_true", help="顯示系統信息")
    parser.add_argument(
        "--cleanup", type=int, metavar="DAYS", help="清理指定天數前的數據"
    )
    parser.add_argument("--export", nargs="?", const="", help="匯出數據庫結構")
    parser.add_argument("--db-path", help="數據庫路徑")

    args = parser.parse_args()

    # 初始化數據庫管理器
    db = DevContextDB(args.db_path)

    if args.init:
        print("🚀 初始化 DevContext 數據庫結構...")
        if db.initialize_schema():
            print("✅ 初始化完成！")
        else:
            print("❌ 初始化失敗！")
            sys.exit(1)

    if args.validate:
        print("🔍 驗證數據庫表結構...")
        results = db.validate_tables()
        db.print_validation_report(results)

    if args.info:
        print("📊 獲取系統信息...")
        info = db.get_system_info()
        print(json.dumps(info, indent=2, ensure_ascii=False))

    if args.cleanup:
        print(f"🧹 清理 {args.cleanup} 天前的數據...")
        db.cleanup_old_data(args.cleanup)

    if args.export is not None:
        output_file = args.export if args.export else None
        print("📦 匯出數據庫結構...")
        db.export_schema(output_file)

    if not any(
        [args.init, args.validate, args.info, args.cleanup, args.export is not None]
    ):
        # 默認執行驗證
        print("🔍 執行默認驗證...")
        results = db.validate_tables()
        db.print_validation_report(results)


if __name__ == "__main__":
    main()
