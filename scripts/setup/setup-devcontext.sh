#!/bin/bash

# NovelWebsite DevContext + Turso 自動配置腳本
# 用途：快速配置AI記憶系統

set -e

echo "🚀 開始配置NovelWebsite項目AI記憶系統..."

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 檢查必要工具
check_requirements() {
    echo -e "${BLUE}📋 檢查系統要求...${NC}"

    # 檢查Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js未安裝，請先安裝Node.js >= 18.0.0${NC}"
        exit 1
    fi

    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        echo -e "${RED}❌ Node.js版本過低，需要 >= 18.0.0${NC}"
        exit 1
    fi

    # 檢查npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm未安裝${NC}"
        exit 1
    fi

    echo -e "${GREEN}✅ 系統要求檢查通過${NC}"
}

# 檢查Turso CLI
check_turso() {
    echo -e "${BLUE}🔍 檢查Turso CLI...${NC}"

    if ! command -v turso &> /dev/null; then
        echo -e "${YELLOW}⚠️  Turso CLI未安裝，正在安裝...${NC}"
        curl -sSfL https://get.tur.so/install.sh | bash

        # 重新加載PATH
        export PATH="$HOME/.turso:$PATH"

        if ! command -v turso &> /dev/null; then
            echo -e "${RED}❌ Turso CLI安裝失敗，請手動安裝${NC}"
            exit 1
        fi
    fi

    echo -e "${GREEN}✅ Turso CLI已安裝${NC}"
}

# 檢查Turso登錄狀態
check_turso_auth() {
    echo -e "${BLUE}🔐 檢查Turso認證狀態...${NC}"

    if ! turso auth whoami &> /dev/null; then
        echo -e "${YELLOW}⚠️  需要登錄Turso...${NC}"
        echo -e "${BLUE}請在瀏覽器中完成登錄流程${NC}"
        turso auth login
    fi

    echo -e "${GREEN}✅ Turso認證完成${NC}"
}

# 檢查數據庫是否存在
check_database() {
    echo -e "${BLUE}🗄️  檢查novelwebsite數據庫...${NC}"

    if ! turso db show novelwebsite &> /dev/null; then
        echo -e "${YELLOW}⚠️  數據庫不存在，正在創建...${NC}"
        turso db create novelwebsite
        echo -e "${GREEN}✅ 數據庫創建成功${NC}"
    else
        echo -e "${GREEN}✅ 數據庫已存在${NC}"
    fi
}

# 獲取數據庫信息
get_database_info() {
    echo -e "${BLUE}📊 獲取數據庫連接信息...${NC}"

    # 獲取數據庫URL
    DATABASE_URL=$(turso db show novelwebsite --url)
    echo -e "${GREEN}Database URL: ${DATABASE_URL}${NC}"

    # 生成訪問Token
    echo -e "${BLUE}🔑 生成訪問Token...${NC}"
    AUTH_TOKEN=$(turso db tokens create novelwebsite)
    echo -e "${GREEN}Auth Token: ${AUTH_TOKEN:0:20}...${NC}"

    # 將信息保存到環境文件
    cat > .env.devcontext << EOF
# DevContext + Turso 配置
TURSO_DATABASE_URL=${DATABASE_URL}
TURSO_AUTH_TOKEN=${AUTH_TOKEN}
EOF

    echo -e "${GREEN}✅ 數據庫信息已保存到 .env.devcontext${NC}"
}

# 更新MCP配置
update_mcp_config() {
    echo -e "${BLUE}⚙️  更新MCP配置...${NC}"

    # 確保.cursor目錄存在
    mkdir -p .cursor

    # 讀取數據庫信息
    source .env.devcontext

    # 創建或更新mcp.json
    cat > .cursor/mcp.json << EOF
{
    "mcpServers": {
        "taskmaster-ai": {
            "command": "npx",
            "args": ["-y", "--package=task-master-ai", "task-master-ai"],
            "env": {
                "ANTHROPIC_API_KEY": "${ANTHROPIC_API_KEY}"
            }
        },
        "context7": {
            "command": "npx",
            "args": ["-y", "@upstash/context7-mcp@latest"],
            "enabled": true
        },
        "devcontext": {
            "command": "npx",
            "args": ["-y", "devcontext@latest"],
            "enabled": true,
            "env": {
                "TURSO_DATABASE_URL": "${TURSO_DATABASE_URL}",
                "TURSO_AUTH_TOKEN": "${TURSO_AUTH_TOKEN}"
            }
        }
    }
}
EOF

    echo -e "${GREEN}✅ MCP配置已更新${NC}"
}

# 測試MCP連接
test_mcp_connection() {
    echo -e "${BLUE}🧪 測試MCP連接...${NC}"

    # 測試DevContext包是否可用
    if npx devcontext@latest --help &> /dev/null; then
        echo -e "${GREEN}✅ DevContext MCP包可用${NC}"
    else
        echo -e "${YELLOW}⚠️  DevContext包下載中...${NC}"
        npx devcontext@latest --help > /dev/null 2>&1 || true
    fi

    # 測試Context7包是否可用
    if npx @upstash/context7-mcp@latest --help &> /dev/null; then
        echo -e "${GREEN}✅ Context7 MCP包可用${NC}"
    else
        echo -e "${YELLOW}⚠️  Context7包下載中...${NC}"
        npx @upstash/context7-mcp@latest --help > /dev/null 2>&1 || true
    fi
}

# 顯示完成信息
show_completion() {
    echo -e "\n${GREEN}🎉 配置完成！${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${YELLOW}下一步操作：${NC}"
    echo -e "1. ${BLUE}重啟Cursor IDE${NC}"
    echo -e "2. ${BLUE}在新對話中測試連接：${NC}"
    echo -e "   ${GREEN}@devcontext initialize_conversation_context{${NC}"
    echo -e "   ${GREEN}  \"initialQuery\": \"配置NovelWebsite爬蟲項目\",${NC}"
    echo -e "   ${GREEN}  \"focusHint\": \"web_scraping\"${NC}"
    echo -e "   ${GREEN}}${NC}"
    echo -e "3. ${BLUE}開始享受AI記憶增強的開發體驗！${NC}"
    echo -e "\n${YELLOW}配置文件位置：${NC}"
    echo -e "- MCP配置: ${BLUE}.cursor/mcp.json${NC}"
    echo -e "- 環境變數: ${BLUE}.env.devcontext${NC}"
    echo -e "- 使用指南: ${BLUE}docs/DevContext-Setup.md${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
}

# 主執行流程
main() {
    echo -e "${GREEN}🎯 NovelWebsite DevContext + Turso 配置工具${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    check_requirements
    check_turso
    check_turso_auth
    check_database
    get_database_info
    update_mcp_config
    test_mcp_connection
    show_completion
}

# 錯誤處理
trap 'echo -e "${RED}❌ 配置過程中發生錯誤${NC}"; exit 1' ERR

# 執行主函數
main "$@"
