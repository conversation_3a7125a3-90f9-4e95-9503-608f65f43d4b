#!/bin/bash
# Dockerfile 快取效率測試腳本
# 🎯 目標：驗證四階段 Dockerfile 的快取效率
# 🚀 測試策略：模擬不同變更場景，驗證快取命中率

set -e

echo "🧪 開始 Dockerfile 快取效率測試..."
echo "=========================================="

# 配置變數
FRONTEND_TIER2_IMAGE="novel-web-frontend-tier2-test"
FRONTEND_CI_IMAGE="novel-web-frontend-ci-test"
TEST_TAG="cache-test"

# 測試結果記錄
TEST_RESULTS=()

# 記錄測試開始時間
OVERALL_START_TIME=$(date +%s)

# 清理函數
cleanup_test_images() {
    echo "🧹 清理測試映像..."
    docker rmi "${FRONTEND_TIER2_IMAGE}:${TEST_TAG}" 2>/dev/null || true
    docker rmi "${FRONTEND_CI_IMAGE}:${TEST_TAG}" 2>/dev/null || true
    echo "✅ 測試映像清理完成"
}

# 測試函數：測量構建時間
measure_build_time() {
    local dockerfile=$1
    local image_name=$2
    local test_name=$3
    local context_dir=${4:-"."}

    echo "📊 測試: $test_name"
    echo "Dockerfile: $dockerfile"
    echo "映像名稱: $image_name"

    local start_time=$(date +%s)

    # 執行構建並捕獲輸出
    local build_output
    if build_output=$(docker build -f "$dockerfile" -t "$image_name" "$context_dir" 2>&1); then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        # 分析快取命中情況
        local cached_steps=$(echo "$build_output" | grep -c "CACHED" || echo "0")
        local total_steps=$(echo "$build_output" | grep -c "Step [0-9]" || echo "1")
        local cache_hit_rate=$((cached_steps * 100 / total_steps))

        echo "✅ 構建成功"
        echo "⏱️ 構建時間: ${duration} 秒"
        echo "📈 快取命中率: ${cache_hit_rate}% (${cached_steps}/${total_steps})"

        # 記錄結果
        TEST_RESULTS+=("$test_name: ${duration}s, 快取命中率: ${cache_hit_rate}%")

        return 0
    else
        echo "❌ 構建失敗"
        echo "$build_output"
        return 1
    fi
}

# 測試 1: 首次構建 (冷啟動)
test_first_build() {
    echo ""
    echo "🚀 測試 1: 首次構建 (冷啟動)"
    echo "----------------------------------------"

    # 清理所有 Docker 快取
    echo "🧹 清理 Docker 快取..."
    docker builder prune -af 2>/dev/null || true

    # 測試 frontend-tier2
    measure_build_time "infra/docker/frontend-tier2.Dockerfile" \
                      "${FRONTEND_TIER2_IMAGE}:${TEST_TAG}" \
                      "Frontend Tier2 - 首次構建"

    # 測試 frontend-ci
    measure_build_time "infra/docker/frontend-ci.Dockerfile" \
                      "${FRONTEND_CI_IMAGE}:${TEST_TAG}" \
                      "Frontend CI - 首次構建"
}

# 測試 2: 無變更重建 (應該全部快取命中)
test_no_change_rebuild() {
    echo ""
    echo "🚀 測試 2: 無變更重建 (預期: 100% 快取命中)"
    echo "----------------------------------------"

    # 測試 frontend-tier2
    measure_build_time "infra/docker/frontend-tier2.Dockerfile" \
                      "${FRONTEND_TIER2_IMAGE}:${TEST_TAG}-rebuild" \
                      "Frontend Tier2 - 無變更重建"

    # 測試 frontend-ci
    measure_build_time "infra/docker/frontend-ci.Dockerfile" \
                      "${FRONTEND_CI_IMAGE}:${TEST_TAG}-rebuild" \
                      "Frontend CI - 無變更重建"
}

# 測試 3: 僅修改源碼 (應該只重建 builder 和 production 階段)
test_source_code_change() {
    echo ""
    echo "🚀 測試 3: 僅修改源碼 (預期: base 和 dependencies 快取命中)"
    echo "----------------------------------------"

    # 創建臨時源碼變更
    local temp_file="frontend/src/temp-test-file.js"
    echo "// 測試檔案 - $(date)" > "$temp_file"

    # 測試 frontend-tier2
    measure_build_time "infra/docker/frontend-tier2.Dockerfile" \
                      "${FRONTEND_TIER2_IMAGE}:${TEST_TAG}-src-change" \
                      "Frontend Tier2 - 源碼變更"

    # 測試 frontend-ci
    measure_build_time "infra/docker/frontend-ci.Dockerfile" \
                      "${FRONTEND_CI_IMAGE}:${TEST_TAG}-src-change" \
                      "Frontend CI - 源碼變更"

    # 清理臨時檔案
    rm -f "$temp_file"
}

# 測試 4: 修改 package.json (應該重建 dependencies 及後續階段)
test_package_json_change() {
    echo ""
    echo "🚀 測試 4: 修改 package.json (預期: 僅 base 階段快取命中)"
    echo "----------------------------------------"

    # 備份原始 package.json
    local package_json="frontend/package.json"
    local backup_file="${package_json}.backup"
    cp "$package_json" "$backup_file"

    # 修改 package.json (添加註釋)
    echo "// 測試變更 - $(date)" >> "$package_json"

    # 測試 frontend-tier2
    measure_build_time "infra/docker/frontend-tier2.Dockerfile" \
                      "${FRONTEND_TIER2_IMAGE}:${TEST_TAG}-pkg-change" \
                      "Frontend Tier2 - package.json 變更"

    # 測試 frontend-ci
    measure_build_time "infra/docker/frontend-ci.Dockerfile" \
                      "${FRONTEND_CI_IMAGE}:${TEST_TAG}-pkg-change" \
                      "Frontend CI - package.json 變更"

    # 恢復原始 package.json
    mv "$backup_file" "$package_json"
}

# 測試 5: 跨映像快取共享驗證
test_cross_image_cache_sharing() {
    echo ""
    echo "🚀 測試 5: 跨映像快取共享驗證"
    echo "----------------------------------------"

    # 清理映像但保留快取
    docker rmi "${FRONTEND_TIER2_IMAGE}:${TEST_TAG}" 2>/dev/null || true
    docker rmi "${FRONTEND_CI_IMAGE}:${TEST_TAG}" 2>/dev/null || true

    echo "📊 驗證跨映像快取共享效果..."

    # 先構建 tier2，再構建 ci，觀察 ci 是否能利用 tier2 的快取
    measure_build_time "infra/docker/frontend-tier2.Dockerfile" \
                      "${FRONTEND_TIER2_IMAGE}:${TEST_TAG}-cross1" \
                      "Frontend Tier2 - 跨映像測試 1"

    measure_build_time "infra/docker/frontend-ci.Dockerfile" \
                      "${FRONTEND_CI_IMAGE}:${TEST_TAG}-cross2" \
                      "Frontend CI - 跨映像測試 2 (應利用 Tier2 快取)"
}

# 生成測試報告
generate_test_report() {
    local overall_end_time=$(date +%s)
    local total_duration=$((overall_end_time - OVERALL_START_TIME))

    echo ""
    echo "🎉 Dockerfile 快取效率測試完成！"
    echo "=========================================="
    echo "📊 測試結果摘要："
    echo ""

    for result in "${TEST_RESULTS[@]}"; do
        echo "  ✅ $result"
    done

    echo ""
    echo "📈 測試統計："
    echo "- 總測試時間: ${total_duration} 秒"
    echo "- 測試場景數: ${#TEST_RESULTS[@]}"
    echo ""
    echo "💡 優化建議："
    echo "- 無變更重建應該在 10 秒內完成"
    echo "- 源碼變更重建應該在 30 秒內完成"
    echo "- 快取命中率應該達到 75% 以上"
    echo ""
    echo "✅ 測試報告生成完成"
}

# 主測試流程
main() {
    echo "🚀 開始 Dockerfile 快取效率測試套件"
    echo "=========================================="

    # 檢查 Docker 是否可用
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安裝或不可用"
        exit 1
    fi

    # 檢查 Dockerfile 是否存在
    if [[ ! -f "infra/docker/frontend-tier2.Dockerfile" ]]; then
        echo "❌ frontend-tier2.Dockerfile 不存在"
        exit 1
    fi

    if [[ ! -f "infra/docker/frontend-ci.Dockerfile" ]]; then
        echo "❌ frontend-ci.Dockerfile 不存在"
        exit 1
    fi

    # 執行測試
    test_first_build
    test_no_change_rebuild
    test_source_code_change
    test_package_json_change
    test_cross_image_cache_sharing

    # 生成報告
    generate_test_report

    # 清理
    cleanup_test_images
}

# 設置清理陷阱
trap cleanup_test_images EXIT

# 執行主函數
main "$@"
