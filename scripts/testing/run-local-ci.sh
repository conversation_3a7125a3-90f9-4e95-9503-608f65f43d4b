#!/bin/bash
# scripts/run-local-ci.sh

# This script provides a standardized and safe way to run the full CI/CD pipeline locally using 'act'.
# It ensures all prerequisites are met and provides clear feedback.

# Exit immediately if a command exits with a non-zero status.
# -e: exit on error, -u: exit on undefined variable, -o pipefail: exit on pipe failure
set -euo pipefail

# --- Preamble and Checks ---
echo "🚀 Starting Local CI/CD Pipeline Simulation with 'act'..."
echo "=========================================================="

# 1. Check for prerequisites
command -v docker >/dev/null 2>&1 || { echo >&2 "❌ Docker is not installed. Please install Docker and try again."; exit 1; }
command -v act >/dev/null 2>&1 || { echo >&2 "❌ 'act' is not installed. Please install it (e.g., 'brew install act') and try again."; exit 1; }
echo "✅ Prerequisites (<PERSON><PERSON>, act) are installed."

# 2. Check for configuration files
if [ ! -f .actrc ] || [ ! -f .secrets ] || [ ! -f .env ]; then
    echo "⚠️  Configuration files (.actrc, .secrets, .env) are not all present."
    echo "Please ensure you have run the initial setup."
    echo "This script will proceed, but 'act' might fail if configurations are missing."
fi
echo "✅ Configuration files checked."

# 3. Check Docker daemon status
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker daemon is not running. Please start Docker and try again."
    exit 1
fi
echo "✅ Docker daemon is running."

# --- Execution ---
echo ""
echo "🔥 Executing 'act' for the 'push' event..."
echo "   This will simulate the entire CI pipeline defined in .github/workflows/ci.yml"
echo "   'act' will use configurations from .actrc, .env, and .secrets"
echo "----------------------------------------------------------"

# Run 'act' for the 'push' event.
# The '-W' flag specifies the workflow file to use, making it explicit.
# We target the main ci.yml workflow.
act push -W .github/workflows/ci.yml

# --- Completion ---
echo "=========================================================="
echo "🎉 Local CI/CD Pipeline Simulation Finished Successfully!"
echo "=========================================================="
