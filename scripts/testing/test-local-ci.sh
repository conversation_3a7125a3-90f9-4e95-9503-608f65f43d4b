#!/bin/bash
# scripts/test-local-ci.sh

# This script provides a safe, incremental way to test the local CI setup
# It will test each component individually before running the full pipeline

set -e

echo "🧪 Testing Local CI/CD Setup - Incremental Mode"
echo "================================================"

# Check prerequisites
echo "🔍 Step 1: Checking prerequisites..."
command -v docker >/dev/null 2>&1 || { echo >&2 "❌ Docker is not installed."; exit 1; }
command -v act >/dev/null 2>&1 || { echo >&2 "❌ 'act' is not installed."; exit 1; }
echo "✅ Docker and act are available"

# Check Docker daemon
echo "🔍 Step 2: Checking Docker daemon..."
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker daemon is not running."
    exit 1
fi
echo "✅ Docker daemon is running"

# Check configuration files
echo "🔍 Step 3: Checking configuration files..."
missing_files=()
[[ ! -f .actrc ]] && missing_files+=(".actrc")
[[ ! -f .env ]] && missing_files+=(".env")
[[ ! -f .secrets ]] && missing_files+=(".secrets")

if [[ ${#missing_files[@]} -gt 0 ]]; then
    echo "❌ Missing configuration files: ${missing_files[*]}"
    exit 1
fi
echo "✅ All configuration files present"

# Test act configuration
echo "🔍 Step 4: Testing act configuration..."
if ! act --list >/dev/null 2>&1; then
    echo "❌ act configuration is invalid"
    exit 1
fi
echo "✅ act configuration is valid"

# Test individual job (dry run equivalent)
echo "🔍 Step 5: Testing job listing..."
echo "Available jobs:"
act --list

echo ""
echo "🎯 Ready to run specific tests!"
echo "Usage examples:"
echo "  ./scripts/test-local-ci.sh backend     # Test only backend job"
echo "  ./scripts/test-local-ci.sh frontend    # Test only frontend job"
echo "  ./scripts/test-local-ci.sh full        # Test full pipeline"

# Handle command line arguments
case "${1:-help}" in
    "backend")
        echo "🚀 Running backend tests only..."
        act -j backend-tests
        ;;
    "frontend")
        echo "🚀 Running frontend tests only..."
        act -j frontend-tests
        ;;
    "full")
        echo "🚀 Running full CI pipeline..."
        act push
        ;;
    "help"|*)
        echo "📋 Test completed successfully. Use arguments above to run specific tests."
        ;;
esac
