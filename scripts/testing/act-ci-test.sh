#!/bin/bash
# scripts/act-ci-test.sh
#
# 智能CI本地測試腳本 - 結合自動清理和手動清理的最佳實踐

set -euo pipefail

echo "🎭 智能Act CI測試 - 最佳清理實踐"
echo "============================================"

# 檢查Docker環境
echo "🔍 Step 1: 檢查Docker環境..."
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker daemon未運行，請先啟動Docker"
    exit 1
fi

# 顯示測試前的Docker資源狀態
echo "📊 Step 2: 測試前資源狀態..."
echo "   容器數量: $(docker ps -aq | wc -l)"
echo "   映像數量: $(docker images -q | wc -l)"
echo "   總佔用空間: $(docker system df --format 'table {{.Size}}' | tail -n +2 | head -1 || echo '未知')"

# 提供測試選項
echo ""
echo "🎯 選擇測試模式："
echo "  1. frontend-tests (快速，--rm自動清理)"
echo "  2. backend-tests (中等，--rm自動清理，需服務)"
echo "  3. integration-tests (完整，--rm自動清理，耗時長)"
echo "  4. security-check (最快，--rm自動清理)"
echo ""

read -p "請選擇測試模式 (1-4): " choice

case $choice in
    1)
        echo "🚀 執行前端測試 (自動清理模式)..."
        gtimeout 300 act -j frontend-tests --rm || {
            echo "⚠️  前端測試超時或失敗"
            make clean-act  # 備援手動清理
        }
        ;;
    2)
        echo "🚀 執行後端測試 (自動清理模式)..."
        gtimeout 600 act -j backend-tests --rm || {
            echo "⚠️  後端測試超時或失敗"
            make clean-act  # 備援手動清理
        }
        ;;
    3)
        echo "🚀 執行完整集成測試 (自動清理模式)..."
        echo "⚠️  這需要很長時間，建議使用 GitHub Actions"
        read -p "確認繼續？(y/N) " confirm
        if [ "$confirm" = "y" ]; then
            gtimeout 1200 act -j integration-tests --rm || {
                echo "⚠️  集成測試超時或失敗"
                make clean-act  # 備援手動清理
            }
        fi
        ;;
    4)
        echo "🚀 執行安全檢查 (自動清理模式)..."
        gtimeout 120 act -j security-check --rm || {
            echo "⚠️  安全檢查超時或失敗"
            make clean-act  # 備援手動清理
        }
        ;;
    *)
        echo "❌ 無效選擇"
        exit 1
        ;;
esac

# 顯示測試後的資源狀態
echo ""
echo "📊 Step 3: 測試後資源狀態..."
echo "   容器數量: $(docker ps -aq | wc -l)"
echo "   映像數量: $(docker images -q | wc -l)"

# 提供手動清理選項
echo ""
echo "🧹 後續清理建議："
echo "  - act已使用--rm自動清理容器"
echo "  - 如需清理act映像: make clean-act"
echo "  - 如需深度清理: make clean-docker-all"
echo ""

read -p "現在執行深度清理？(y/N) " cleanup_choice
if [ "$cleanup_choice" = "y" ]; then
    make clean-act
    echo "✅ 清理完成"
fi

echo "🎉 Act測試和清理流程完成！"
