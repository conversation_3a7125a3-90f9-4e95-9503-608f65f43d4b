#!/bin/bash
# scripts/final-ci-test.sh
#
# 最終CI配置驗證腳本 - 確保所有組件正確配置並遵循最佳實踐

set -euo pipefail

echo "🎯 NovelWebsite 本地CI/CD配置最終驗證"
echo "=========================================="

# 顏色代碼
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 檢查函數
check_component() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

echo ""
echo "🔍 第一步：基礎環境檢查"
echo "========================"

# 檢查基本工具
docker --version >/dev/null 2>&1
check_component $? "Docker 已安裝並可用"

act --version >/dev/null 2>&1
check_component $? "act 已安裝並可用"

# 檢查Docker守護進程
docker info >/dev/null 2>&1
check_component $? "Docker daemon 運行正常"

echo ""
echo "📋 第二步：配置文件檢查"
echo "======================"

# 檢查配置文件存在性
test -f .actrc
check_component $? ".actrc 配置文件存在"

test -f .env
check_component $? ".env 環境變數文件存在"

test -f .secrets
check_component $? ".secrets 機密文件存在"

# 檢查GitHub Actions工作流
test -f .github/workflows/ci.yml
check_component $? "CI 工作流配置存在"

test -f .github/workflows/docs.yml
check_component $? "文檔構建工作流配置存在"

echo ""
echo "🧪 第三步：act命令測試"
echo "===================="

# 測試act列表命令
act --list >/dev/null 2>&1
check_component $? "act 可以正確列出工作流job"

echo ""
echo "📊 第四步：配置內容驗證"
echo "======================"

echo -e "${YELLOW}🔧 .actrc 配置內容:${NC}"
cat .actrc | head -10

echo ""
echo -e "${YELLOW}🔧 檢測到的工作流jobs:${NC}"
act --list | tail -n +2

echo ""
echo "✅ 驗證結果總結"
echo "==============="
echo -e "${GREEN}🎉 所有基礎配置已正確設置！${NC}"
echo ""
echo "📋 可用的測試命令："
echo "   - 列出所有jobs:     act --list"
echo "   - 乾運行文檔構建:    act -j build --dryrun"
echo "   - 乾運行前端測試:    act -j frontend-tests --dryrun"
echo "   - 乾運行後端測試:    act -j backend-tests --dryrun"
echo ""
echo "⚠️  注意事項："
echo "   - 包含PostgreSQL/Redis服務的job可能需要較長時間初始化"
echo "   - 首次運行需要下載Docker鏡像，請耐心等待"
echo "   - 建議先使用--dryrun測試，然後再執行實際運行"
echo ""
echo "🚀 下一步建議："
echo "   1. 測試簡單job: ./scripts/final-ci-test.sh && act -j build --dryrun"
echo "   2. 逐步測試複雜job，確認服務配置"
echo "   3. 集成到開發工作流中"
