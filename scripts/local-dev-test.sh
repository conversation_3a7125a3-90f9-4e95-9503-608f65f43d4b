#!/bin/bash

# 本地開發環境測試腳本
# 🎯 目標：驗證 Frontend Base Image 架構在本地正常運作
# 🚀 策略：測試 BuildKit Cache + 熱重載 + ARM64 相容性

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查前置條件
check_prerequisites() {
    log_info "🔍 檢查本地開發前置條件..."

    # 檢查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝"
        return 1
    fi

    # 檢查 Docker 服務
    if ! docker info &> /dev/null; then
        log_error "Docker 服務未運行"
        return 1
    fi

    # 檢查 pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm 未安裝，但容器內會使用 pnpm"
    fi

    # 檢查必要檔案
    local required_files=(
        "package.json"
        "pnpm-workspace.yaml"
        "turbo.json"
        "apps/web-next/package.json"
        "infra/docker/frontend-dev.Dockerfile"
    )

    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要檔案: $file"
            return 1
        fi
    done

    log_success "前置條件檢查通過"
    return 0
}

# 測試 Base Image 構建
test_base_image_build() {
    log_info "🏗️ 測試 Frontend Base Image 構建..."

    local base_image_tag="novel-frontend-base:local-test"

    log_info "構建 Base Image（僅依賴層）..."
    if docker build \
        --platform linux/arm64 \
        -t "$base_image_tag" \
        -f infra/docker/frontend-deps.Dockerfile \
        --progress=plain \
        .; then
        log_success "Base Image 構建成功: $base_image_tag"
    else
        log_error "Base Image 構建失敗"
        return 1
    fi

    # 檢查 Base Image 內容
    log_info "檢查 Base Image 內容..."
    if docker run --rm "$base_image_tag" \
        sh -c "ls -la /workspace/node_modules/.bin | head -10 && echo '✅ Base Image 依賴驗證通過'"; then
        log_success "Base Image 依賴層驗證通過"
    else
        log_error "Base Image 依賴層驗證失敗"
        return 1
    fi

    return 0
}

# 測試完整應用構建（使用 Base Image）
test_app_build_with_cache() {
    log_info "🚀 測試完整應用構建（使用 Base Image 快取）..."

    local app_image_tag="novel-frontend-app:local-test"

    # 使用本地 Base Image 作為快取
    log_info "構建應用 Image（使用 Base Image 快取）..."
    if docker build \
        --platform linux/arm64 \
        -t "$app_image_tag" \
        -f infra/docker/frontend-tier2.Dockerfile \
        --build-arg ECR_REGISTRY=novel-frontend-base \
        --progress=plain \
        .; then
        log_success "應用 Image 構建成功: $app_image_tag"
    else
        log_warning "應用 Image 構建失敗（可能因為本地沒有 ECR 映像）"
        log_info "這是正常的，因為我們沒有連接到 ECR"
        return 0
    fi

    return 0
}

# 測試本地開發環境
test_local_dev_environment() {
    log_info "🧪 測試本地開發環境..."

    # 清理可能存在的容器
    log_info "清理舊的開發容器..."
    docker-compose -f docker-compose.dev.yml down -v --remove-orphans 2>/dev/null || true

    # 啟動開發環境
    log_info "啟動本地開發環境..."
    if docker-compose -f docker-compose.dev.yml up -d frontend-dev; then
        log_success "開發環境啟動成功"
    else
        log_error "開發環境啟動失敗"
        return 1
    fi

    # 等待服務就緒
    log_info "等待 Next.js 開發伺服器啟動..."
    local retry_count=0
    while [[ $retry_count -lt 30 ]]; do
        if curl -f -s "http://localhost:3000/" > /dev/null 2>&1; then
            log_success "Next.js 開發伺服器啟動成功"
            break
        fi
        retry_count=$((retry_count + 1))
        log_info "等待開發伺服器... ($retry_count/30)"
        sleep 2
    done

    if [[ $retry_count -ge 30 ]]; then
        log_error "開發伺服器啟動超時"
        docker-compose -f docker-compose.dev.yml logs frontend-dev
        return 1
    fi

    # 測試熱重載（創建測試檔案）
    log_info "測試熱重載功能..."
    local test_file="apps/web-next/test-hot-reload.txt"
    echo "Test hot reload at $(date)" > "$test_file"

    sleep 3  # 等待檔案變更偵測

    if [[ -f "$test_file" ]]; then
        rm "$test_file"
        log_success "熱重載測試完成"
    fi

    return 0
}

# 清理函數
cleanup() {
    log_info "🧹 清理測試環境..."
    docker-compose -f docker-compose.dev.yml down -v --remove-orphans 2>/dev/null || true

    # 清理本地測試映像（可選）
    # docker rmi novel-frontend-base:local-test novel-frontend-app:local-test 2>/dev/null || true

    log_success "清理完成"
}

# 主函數
main() {
    local exit_code=0
    local start_time=$(date +%s)

    log_info "🚀 開始本地開發環境測試..."
    echo ""

    # 設置清理陷阱
    trap cleanup EXIT

    # 檢查前置條件
    if ! check_prerequisites; then
        log_error "前置條件檢查失敗"
        return 1
    fi

    echo ""

    # 測試 Base Image 構建
    if ! test_base_image_build; then
        log_error "Base Image 構建測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試應用構建（允許失敗）
    test_app_build_with_cache

    echo ""

    # 測試本地開發環境
    if [[ $exit_code -eq 0 ]] && ! test_local_dev_environment; then
        log_error "本地開發環境測試失敗"
        exit_code=1
    fi

    echo ""

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 本地開發環境測試全部通過！"
        log_success "⏱️ 執行時間: ${duration} 秒"
        log_info "✨ Frontend Base Image 架構本地驗證成功"
        log_info "🌐 開發伺服器: http://localhost:3000"
    else
        log_error "💥 本地開發環境測試失敗！"
        log_error "⏱️ 執行時間: ${duration} 秒"
    fi

    return $exit_code
}

# 執行主函數
main "$@"
