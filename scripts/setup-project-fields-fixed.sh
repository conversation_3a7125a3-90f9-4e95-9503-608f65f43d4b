#!/bin/bash
# 設置 Monorepo 專案的所有字段值 (修正版)
set -e

echo "🚀 開始設置 Project #3 的所有字段值 (修正版)..."

# 專案 ID
PROJECT_ID="PVT_kwHOAo1lbs4A8hDr"

# 字段 IDs
T3_WEEK_FIELD="PVTSSF_lAHOAo1lbs4A8hDrzgwfkbs"
ARCH_FIELD="PVTSSF_lAHOAo1lbs4A8hDrzgwfkeQ"
COMPLEXITY_FIELD="PVTSSF_lAHOAo1lbs4A8hDrzgwfkeU"
DURATION_FIELD="PVTSSF_lAHOAo1lbs4A8hDrzgwfkeY"

# T3 週次選項 IDs
WEEK1_ID="0b7fd983"     # 第1週:準備期
WEEK2_ID="c8dbf085"     # 第2週:核心遷移
WEEK3_ID="9431810a"     # 第3週:優化整合
WEEK4_ID="e02dace8"     # 第4週:驗證部署
EPIC_ID="07bc04e6"      # 史詩任務
CLEANUP_ID="6b50c9dc"   # 清理/維護

# 架構類別選項 IDs
FRONTEND_ID="a90a6b5a"  # 前端架構
BACKEND_ID="6079ee28"   # 後端架構
CICD_ID="afb89c06"      # CI/CD
TEST_ID="7ba4b6ef"      # 測試框架
DEPS_ID="f5ca5aa1"      # 依賴管理
DOCS_ID="42718166"      # 文檔/清理

# 技術複雜度選項 IDs
SIMPLE_ID="c9061a39"    # 簡單
MEDIUM_ID="95a322da"    # 中等
COMPLEX_ID="20b04e58"   # 複雜
EPIC_COMPLEX_ID="4b28d51f" # 史詩級

# 工期估算選項 IDs
DAYS12_ID="989c527b"    # 1-2天
DAYS35_ID="1468f521"    # 3-5天
WEEK1_DUR_ID="26460336" # 1週
WEEK2_DUR_ID="c6461fca" # 2週
WEEKS34_ID="e8367233"   # 3-4週

# 函數：設置單個 Issue 的字段值
set_issue_fields() {
    local issue_num=$1
    local t3_week=$2
    local arch_category=$3
    local complexity=$4
    local duration=$5
    local description=$6

    echo "🎯 設置 Issue #$issue_num ($description)..."

    # 獲取 item ID
    local item_id=$(gh project item-list 3 --owner=MumuTW --format=json 2>/dev/null | jq -r ".items[] | select(.content.number == $issue_num) | .id" | head -1)

    if [ -z "$item_id" ] || [ "$item_id" = "null" ]; then
        echo "   ⚠️  找不到 Issue #$issue_num 的 item ID，跳過"
        return
    fi

    # 設置 T3 週次
    if gh project item-edit --id "$item_id" --field-id "$T3_WEEK_FIELD" --single-select-option-id "$t3_week" --project-id "$PROJECT_ID" >/dev/null 2>&1; then
        echo "   ✅ T3 週次設置成功"
    else
        echo "   ⚠️ T3 週次設置失敗"
    fi

    # 設置架構類別
    if gh project item-edit --id "$item_id" --field-id "$ARCH_FIELD" --single-select-option-id "$arch_category" --project-id "$PROJECT_ID" >/dev/null 2>&1; then
        echo "   ✅ 架構類別設置成功"
    else
        echo "   ⚠️ 架構類別設置失敗"
    fi

    # 設置技術複雜度
    if gh project item-edit --id "$item_id" --field-id "$COMPLEXITY_FIELD" --single-select-option-id "$complexity" --project-id "$PROJECT_ID" >/dev/null 2>&1; then
        echo "   ✅ 技術複雜度設置成功"
    else
        echo "   ⚠️ 技術複雜度設置失敗"
    fi

    # 設置工期估算
    if gh project item-edit --id "$item_id" --field-id "$DURATION_FIELD" --single-select-option-id "$duration" --project-id "$PROJECT_ID" >/dev/null 2>&1; then
        echo "   ✅ 工期估算設置成功"
    else
        echo "   ⚠️ 工期估算設置失敗"
    fi

    echo "   ✅ Issue #$issue_num 設置完成"
    echo ""
}

echo ""
echo "📋 開始設置所有 Issues 的字段值..."

# 史詩級任務
echo "🏛️ 史詩級任務:"
set_issue_fields 143 "$EPIC_ID" "$FRONTEND_ID" "$EPIC_COMPLEX_ID" "$WEEKS34_ID" "T3 Stack 架構統一戰役"
set_issue_fields 129 "$EPIC_ID" "$FRONTEND_ID" "$EPIC_COMPLEX_ID" "$WEEKS34_ID" "CRA → Next.js 框架遷移"

# 第1週任務
echo "📅 第1週任務:"
set_issue_fields 146 "$WEEK1_ID" "$FRONTEND_ID" "$COMPLEX_ID" "$WEEK1_DUR_ID" "Next.js 15 環境準備"
set_issue_fields 159 "$WEEK1_ID" "$CICD_ID" "$COMPLEX_ID" "$DAYS35_ID" "CI 部署安全網"

# 第2週任務
echo "📅 第2週任務:"
set_issue_fields 147 "$WEEK2_ID" "$BACKEND_ID" "$COMPLEX_ID" "$WEEK1_DUR_ID" "Django 配置重構"
set_issue_fields 148 "$WEEK2_ID" "$FRONTEND_ID" "$COMPLEX_ID" "$WEEK2_DUR_ID" "核心功能遷移"
set_issue_fields 149 "$WEEK2_ID" "$DEPS_ID" "$MEDIUM_ID" "$WEEK1_DUR_ID" "Monorepo 結構"

# 第3週任務
echo "📅 第3週任務:"
set_issue_fields 150 "$WEEK3_ID" "$CICD_ID" "$COMPLEX_ID" "$WEEK1_DUR_ID" "CI/CD 適配"
set_issue_fields 151 "$WEEK3_ID" "$DEPS_ID" "$MEDIUM_ID" "$DAYS35_ID" "依賴清理"
set_issue_fields 152 "$WEEK3_ID" "$TEST_ID" "$COMPLEX_ID" "$WEEK1_DUR_ID" "測試現代化"

# 第4週任務
echo "📅 第4週任務:"
set_issue_fields 153 "$WEEK4_ID" "$CICD_ID" "$COMPLEX_ID" "$WEEK1_DUR_ID" "生產驗證"
set_issue_fields 154 "$WEEK4_ID" "$BACKEND_ID" "$MEDIUM_ID" "$DAYS35_ID" "效能監控"
set_issue_fields 155 "$WEEK4_ID" "$DOCS_ID" "$SIMPLE_ID" "$DAYS12_ID" "文檔更新"

# 清理任務
echo "🧹 清理任務:"
set_issue_fields 156 "$CLEANUP_ID" "$DOCS_ID" "$SIMPLE_ID" "$DAYS12_ID" "Legacy cleanup"

echo "🎉 關鍵任務字段值設置完成！"
echo "📊 請訪問 https://github.com/users/MumuTW/projects/3 查看結果"
echo ""
echo "💡 建議接下來手動創建以下視圖："
echo "   1. T3 週次時間線視圖 (主視圖)"
echo "   2. 當前衝刺視圖 (日常使用)"
echo "   3. 架構類別看板視圖 (技術分類)"
echo "   4. 複雜度分析視圖 (風險管理)"
echo "   5. 進度總覽視圖 (整體追蹤)"
