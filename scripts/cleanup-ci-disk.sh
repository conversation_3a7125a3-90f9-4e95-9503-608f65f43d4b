#!/bin/bash
# CI 磁碟空間清理腳本
# 🎯 目標：清理 Docker 系統和 CI 快取，釋放約 20GB+ 空間

set -e

echo "🧹 開始 CI 磁碟空間清理程序..."
echo "=========================================="

# 1. 顯示清理前的磁碟使用狀況
echo "📊 清理前的磁碟使用狀況："
df -h | grep -E "(Filesystem|/dev/disk)"
echo ""

echo "🐳 清理前的 Docker 使用狀況："
docker system df
echo ""

# 2. 清理 Docker 系統
echo "🚀 階段 1: 清理 Docker 系統資源"
echo "----------------------------------------"

# 清理停止的容器
echo "🗑️ 清理停止的容器..."
docker container prune -f

# 清理未使用的網路
echo "🌐 清理未使用的網路..."
docker network prune -f

# 清理未使用的映像
echo "🖼️ 清理未使用的映像..."
docker image prune -f

# 清理建置快取
echo "🏗️ 清理建置快取..."
docker builder prune -f

# 清理 volumes (保留最近使用的)
echo "💾 清理未使用的 volumes..."
docker volume prune -f

echo "✅ Docker 系統清理完成"
echo ""

# 3. 清理 Node.js 和 pnpm 快取
echo "🚀 階段 2: 清理 Node.js 相關快取"
echo "----------------------------------------"

# 清理 npm 快取
if command -v npm &> /dev/null; then
    echo "📦 清理 npm 快取..."
    npm cache clean --force 2>/dev/null || true
fi

# 清理 pnpm 快取
if command -v pnpm &> /dev/null; then
    echo "📦 清理 pnpm 快取..."
    pnpm store prune 2>/dev/null || true
fi

# 清理 yarn 快取
if command -v yarn &> /dev/null; then
    echo "📦 清理 yarn 快取..."
    yarn cache clean 2>/dev/null || true
fi

echo "✅ Node.js 快取清理完成"
echo ""

# 4. 清理 Python 快取
echo "🚀 階段 3: 清理 Python 快取"
echo "----------------------------------------"

echo "🐍 清理 Python 快取目錄..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# 清理 pip 快取
if command -v pip &> /dev/null; then
    echo "📦 清理 pip 快取..."
    pip cache purge 2>/dev/null || true
fi

echo "✅ Python 快取清理完成"
echo ""

# 5. 清理 GitHub Actions 快取目錄
echo "🚀 階段 4: 清理 GitHub Actions 快取"
echo "----------------------------------------"

# 清理臨時檔案
echo "🗑️ 清理臨時檔案..."
rm -rf /tmp/* 2>/dev/null || true
rm -rf ~/.cache/* 2>/dev/null || true

# 清理 GitHub Actions 相關目錄
echo "🗑️ 清理 GitHub Actions 快取..."
rm -rf ~/.local/share/pnpm/store/v3/tmp-* 2>/dev/null || true
rm -rf .lighthouseci/temp-* 2>/dev/null || true

echo "✅ GitHub Actions 快取清理完成"
echo ""

# 6. 強制系統快取清理 (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🚀 階段 5: macOS 系統快取清理"
    echo "----------------------------------------"

    echo "🗑️ 清理系統快取..."
    sudo dscacheutil -flushcache 2>/dev/null || true
    sudo killall -HUP mDNSResponder 2>/dev/null || true

    echo "✅ macOS 系統快取清理完成"
    echo ""
fi

# 7. 強制 Docker 深度清理（危險操作，慎用）
echo "🚀 階段 6: Docker 深度清理 (可選)"
echo "----------------------------------------"
read -p "⚠️ 是否執行 Docker 深度清理？這將刪除所有未使用的資源 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 執行 Docker 深度清理..."
    docker system prune -a -f --volumes
    echo "✅ Docker 深度清理完成"
else
    echo "⏭️ 跳過 Docker 深度清理"
fi
echo ""

# 8. 顯示清理後的狀況
echo "🎉 磁碟清理完成！"
echo "=========================================="

echo "📊 清理後的磁碟使用狀況："
df -h | grep -E "(Filesystem|/dev/disk)"
echo ""

echo "🐳 清理後的 Docker 使用狀況："
docker system df
echo ""

echo "✅ CI 磁碟空間清理程序完成"
echo "💡 建議定期執行此腳本以維持系統效能"
