#!/bin/bash

# 🌐 Amazon Linux / CentOS / RHEL Runner 安裝 Chrome 腳本
# 用於 self-hosted GitHub Actions runner Chrome 預安裝

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查是否為 root 或有 sudo 權限
check_sudo() {
    if [[ $EUID -eq 0 ]]; then
        log_info "以 root 權限執行"
        SUDO=""
    elif sudo -n true 2>/dev/null; then
        log_info "檢測到 sudo 權限"
        SUDO="sudo"
    else
        log_error "此腳本需要 sudo 權限執行"
        exit 1
    fi
}

# 檢查是否已安裝 Chrome
check_existing_chrome() {
    if command -v google-chrome >/dev/null 2>&1; then
        log_success "Chrome 已安裝"
        google-chrome --version
        log_warning "如需重新安裝，請先執行: $SUDO yum remove -y google-chrome-stable"
        return 0
    else
        log_info "Chrome 未安裝，開始安裝程序..."
        return 1
    fi
}

# 檢測 Linux 發行版
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS_ID=$ID
        OS_VERSION=$VERSION_ID
        log_info "檢測到系統: $NAME $VERSION_ID"
    else
        log_error "無法檢測 Linux 發行版"
        exit 1
    fi
}

# 安裝 Chrome for Amazon Linux / CentOS / RHEL
install_chrome_rhel() {
    log_info "🌐 開始安裝 Google Chrome..."

    # 1. 創建 Google Chrome YUM repository
    log_info "📦 設置 Google Chrome YUM repository..."
    $SUDO tee /etc/yum.repos.d/google-chrome.repo <<'EOF'
[google-chrome]
name=google-chrome
baseurl=http://dl.google.com/linux/chrome/rpm/stable/x86_64
enabled=1
gpgcheck=1
gpgkey=https://dl.google.com/linux/linux_signing_key.pub
EOF

    # 2. 導入 GPG 金鑰
    log_info "🔐 導入 Google Linux 簽名金鑰..."
    $SUDO rpm --import https://dl.google.com/linux/linux_signing_key.pub

    # 3. 安裝 Chrome
    log_info "⬇️ 安裝 Google Chrome Stable..."

    # 檢測套件管理器
    if command -v dnf >/dev/null 2>&1; then
        # Amazon Linux 2023+ / RHEL 8+ / CentOS 8+
        $SUDO dnf install -y google-chrome-stable
    elif command -v yum >/dev/null 2>&1; then
        # Amazon Linux 2 / RHEL 7 / CentOS 7
        $SUDO yum install -y google-chrome-stable
    else
        log_error "未找到 yum 或 dnf 套件管理器"
        exit 1
    fi

    # 4. 驗證安裝
    log_info "✅ 驗證 Chrome 安裝..."
    if command -v google-chrome >/dev/null 2>&1; then
        log_success "Chrome 安裝成功！"
        google-chrome --version
    else
        log_error "Chrome 安裝失敗"
        exit 1
    fi
}

# 安裝額外依賴 (Lighthouse 需要)
install_lighthouse_deps() {
    log_info "📦 安裝 Lighthouse 相關依賴..."

    # 安裝必要的字體和函式庫
    if command -v dnf >/dev/null 2>&1; then
        $SUDO dnf install -y \
            liberation-fonts \
            liberation-narrow-fonts \
            liberation-sans-fonts \
            liberation-serif-fonts \
            xorg-x11-server-Xvfb
    elif command -v yum >/dev/null 2>&1; then
        $SUDO yum install -y \
            liberation-fonts \
            liberation-narrow-fonts \
            liberation-sans-fonts \
            liberation-serif-fonts \
            xorg-x11-server-Xvfb
    fi

    log_success "Lighthouse 依賴安裝完成"
}

# 主函數
main() {
    log_info "🚀 開始 Chrome 安裝程序 (Amazon Linux / RHEL 系列)..."

    # 檢查權限
    check_sudo

    # 檢查現有安裝
    if check_existing_chrome; then
        log_info "Chrome 已存在，跳過安裝"
        return 0
    fi

    # 檢測作業系統
    detect_os

    # 根據系統類型執行安裝
    case $OS_ID in
        "amzn"|"centos"|"rhel"|"rocky"|"almalinux")
            install_chrome_rhel
            install_lighthouse_deps
            ;;
        *)
            log_error "不支援的作業系統: $OS_ID"
            log_error "此腳本僅支援 Amazon Linux / CentOS / RHEL 系列"
            exit 1
            ;;
    esac

    log_success "🎉 Chrome 安裝完成！"
    log_info "💡 現在可以在 GitHub Actions runner 中使用 Lighthouse"
}

# 執行主函數
main "$@"
