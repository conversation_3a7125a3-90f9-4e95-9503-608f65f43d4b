#!/bin/bash

# 🚀 AWS EC2 Runner Chrome 設定腳本
# 透過 AWS Systems Manager 或 SSH 在 runner 上執行

echo "🌐 在 AWS EC2 GitHub Actions Runner 上安裝 Chrome..."

# 1. 設置 Google Chrome YUM repository
echo "📦 設置 Google Chrome repository..."
sudo tee /etc/yum.repos.d/google-chrome.repo <<'EOF'
[google-chrome]
name=google-chrome
baseurl=http://dl.google.com/linux/chrome/rpm/stable/x86_64
enabled=1
gpgcheck=1
gpgkey=https://dl.google.com/linux/linux_signing_key.pub
EOF

# 2. 導入 GPG 金鑰
echo "🔐 導入 Google 簽名金鑰..."
sudo rpm --import https://dl.google.com/linux/linux_signing_key.pub

# 3. 安裝 Chrome
echo "⬇️ 安裝 Google Chrome..."
sudo yum install -y google-chrome-stable

# 4. 安裝 Lighthouse 依賴
echo "📚 安裝 Lighthouse 相關依賴..."
sudo yum install -y \
    liberation-fonts \
    liberation-narrow-fonts \
    liberation-sans-fonts \
    liberation-serif-fonts \
    xorg-x11-server-Xvfb

# 5. 驗證安裝
echo "✅ 驗證安裝..."
google-chrome --version

echo "🎉 Chrome 安裝完成！現在 Lighthouse CI 可以正常運行"
