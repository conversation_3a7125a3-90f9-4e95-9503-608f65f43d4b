#!/bin/bash

# 清理舊版 CI 配置文件腳本
# 移除基於非 Monorepo 架構的舊配置，為新的 v2 配置讓路

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 要移除的舊配置文件列表
LEGACY_FILES=(
    "infra/docker/docker-compose.ci.yml"
    "infra/docker/backend-ci.Dockerfile"
    "infra/docker/frontend-ci.Dockerfile"
    "scripts/ci/deployment-simulation-test.sh"
)

# 要備份的文件列表（以防需要回滾）
BACKUP_DIR="archive/legacy-ci-configs-$(date +%Y%m%d-%H%M%S)"

main() {
    log_info "🧹 開始清理舊版 CI 配置文件..."

    # 創建備份目錄
    mkdir -p "$BACKUP_DIR"
    log_info "📁 創建備份目錄: $BACKUP_DIR"

    # 備份並移除舊文件
    for file in "${LEGACY_FILES[@]}"; do
        if [[ -f "$file" ]]; then
            log_info "📦 備份文件: $file"

            # 創建備份目錄結構
            backup_path="$BACKUP_DIR/$file"
            mkdir -p "$(dirname "$backup_path")"

            # 複製到備份目錄
            cp "$file" "$backup_path"

            # 移除原文件
            rm "$file"
            log_success "✅ 已移除: $file"
        else
            log_warning "⚠️ 文件不存在，跳過: $file"
        fi
    done

    # 創建備份說明文件
    cat > "$BACKUP_DIR/README.md" << EOF
# 舊版 CI 配置文件備份

## 備份時間
$(date)

## 備份原因
遷移到基於 Monorepo 架構的新版 CI 部署模擬系統 v2

## 新版配置文件位置
- \`docker-compose.ci.yml\` (根目錄)
- \`infra/docker/backend-ci-v2.Dockerfile\`
- \`infra/docker/frontend-ci-v2.Dockerfile\`
- \`scripts/ci/deployment-simulation-test-v2.sh\`

## 如何回滾（如果需要）
\`\`\`bash
# 從備份恢復舊配置
cp -r $BACKUP_DIR/infra/docker/* infra/docker/
cp -r $BACKUP_DIR/scripts/ci/* scripts/ci/
\`\`\`

## 新版優勢
1. 基於 Monorepo 架構設計
2. 智能 Docker 層緩存
3. 執行時間從 5 分鐘縮短到 30-60 秒
4. 更清晰的路徑配置
5. 更好的錯誤處理
EOF

    log_success "🎉 舊版 CI 配置文件清理完成！"
    log_info "📋 備份位置: $BACKUP_DIR"
    log_info "🚀 現在可以使用新版 Monorepo CI 部署模擬系統 v2"

    echo ""
    log_info "📝 下一步操作："
    echo "   1. 測試新版部署模擬: ./scripts/ci/deployment-simulation-test-v2.sh"
    echo "   2. 提交更改到 Git"
    echo "   3. 推送到遠程倉庫並觀察 CI 執行"
}

# 執行主函數
main "$@"
