#!/bin/bash
# ECR Cache 初始化腳本 - 防止 buildx cache-from 失敗
#
# 用途：在新 cache key rollout 時執行一次，避免 buildx registry cache importer 錯誤
# 問題：新的 cache key 在 ECR 上不存在時，buildx --cache-from 會失敗
# 解決：預先創建空的 cache manifest，確保 registry 上有可用的 OCI manifest
#
# 使用方式：
#   ./scripts/ci/seed-ecr-cache.sh [ECR_REGISTRY] [CACHE_KEY] [IMAGE_NAME]
#
# 範例：
#   ./scripts/ci/seed-ecr-cache.sh 509399605447.dkr.ecr.ap-northeast-1.amazonaws.com base-cache-v2 novel-web-frontend
#   ./scripts/ci/seed-ecr-cache.sh 509399605447.dkr.ecr.ap-northeast-1.amazonaws.com ci-cache-v2 novel-web-frontend

set -euo pipefail

# 預設值
DEFAULT_ECR_REGISTRY="509399605447.dkr.ecr.ap-northeast-1.amazonaws.com"
DEFAULT_CACHE_KEY="base-cache-v2"
DEFAULT_IMAGE_NAME="novel-web-frontend"

# 參數解析
ECR_REGISTRY="${1:-$DEFAULT_ECR_REGISTRY}"
CACHE_KEY="${2:-$DEFAULT_CACHE_KEY}"
IMAGE_NAME="${3:-$DEFAULT_IMAGE_NAME}"

# 構建完整的 cache reference
CACHE_REF="$ECR_REGISTRY/$IMAGE_NAME:$CACHE_KEY"

echo "🔍 ECR Cache 初始化腳本"
echo "📦 Registry: $ECR_REGISTRY"
echo "🏷️  Cache Key: $CACHE_KEY"
echo "🖼️  Image Name: $IMAGE_NAME"
echo "🔗 Full Reference: $CACHE_REF"
echo ""

# 檢查 Docker 是否已登入 ECR
echo "🔐 檢查 ECR 認證狀態..."
if ! docker info | grep -q "Registry:"; then
    echo "⚠️  警告：Docker 可能未登入 ECR"
    echo "💡 請先執行：aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin $ECR_REGISTRY"
fi

# 檢查 cache manifest 是否已存在
echo "🔍 檢查 ECR cache manifest 是否存在..."
if docker manifest inspect "$CACHE_REF" > /dev/null 2>&1; then
    echo "✅ Cache manifest 已存在：$CACHE_REF"
    echo "ℹ️  無需初始化，腳本結束"
    exit 0
fi

echo "⚠️  Cache manifest 不存在，開始初始化..."

# 檢查 buildx builder 狀態
echo "🔧 檢查 buildx builder 狀態..."
if ! docker buildx ls | awk '{print $1, $2, $3}' | grep -q "^default docker-container running$"; then
    echo "🔨 創建或重置 default buildx builder..."
    # 嘗試移除現有的 default builder，以確保其狀態正確
    docker buildx rm default 2>/dev/null || true
    docker buildx create --name default --use --driver docker-container
fi

# 創建最小的 cache manifest
echo "🚀 創建空 cache manifest..."
echo "📝 使用 FROM scratch 創建最小映像..."

# 使用 heredoc 創建臨時 Dockerfile
cat << 'EOF' | docker buildx build \
    --cache-to="type=registry,ref=$CACHE_REF,mode=max" \
    --push \
    --platform linux/amd64 \
    --tag temp-cache-seed \
    -
FROM scratch
# 空映像，僅用於初始化 ECR cache manifest
# 這確保 buildx --cache-from 不會因為 "not found" 而失敗
LABEL purpose="ECR cache initialization"
LABEL cache-key="$CACHE_KEY"
LABEL created-by="seed-ecr-cache.sh"
EOF

# 驗證 cache manifest 創建成功
echo "✅ 驗證 cache manifest 創建..."
if docker manifest inspect "$CACHE_REF" > /dev/null 2>&1; then
    echo "🎉 ECR cache 初始化完成！"
    echo "📊 Cache Reference: $CACHE_REF"
    echo "🔄 現在可以安全使用 --cache-from type=registry,ref=$CACHE_REF"
else
    echo "❌ Cache manifest 創建失敗"
    exit 1
fi

# 清理臨時映像
docker rmi temp-cache-seed > /dev/null 2>&1 || true # Clean up temporary image

echo ""
echo "📋 使用說明："
echo "   在 CI/CD 中使用此 cache："
echo "   --cache-from type=registry,ref=$CACHE_REF"
echo "   --cache-to type=registry,ref=$CACHE_REF,mode=max"
echo ""
echo "🔧 故障排除："
echo "   如果仍然遇到 cache-from 錯誤，請檢查："
echo "   1. ECR 認證是否正確"
echo "   2. buildx builder 是否一致"
echo "   3. 網路連線是否穩定"
echo ""
echo "✨ ECR Cache 初始化完成！"
