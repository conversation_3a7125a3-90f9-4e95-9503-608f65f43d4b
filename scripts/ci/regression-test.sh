#!/bin/bash
# Regression Test for CI Deployment Safety Nets
# 回歸測試：使用故意錯誤配置驗證防護機制有效性

set -e

echo "🧪 Starting Regression Test for Deployment Safety Nets..."
echo "📂 Working directory: $(pwd)"

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 備份原始文件
backup_files() {
    log_info "📋 備份原始文件..."

    local files_to_backup=(
        "backend/config/wsgi.py"
        "backend/config/django_settings.py"
        ".pre-commit-config.yaml"
    )

    for file in "${files_to_backup[@]}"; do
        if [[ -f "$file" ]]; then
            cp "$file" "$file.regression-backup"
            log_success "已備份: $file"
        fi
    done
}

# 恢復原始文件
restore_files() {
    log_info "🔄 恢復原始文件..."

    local backup_files=(
        "backend/config/wsgi.py.regression-backup"
        "backend/config/django_settings.py.regression-backup"
        ".pre-commit-config.yaml.regression-backup"
    )

    for backup_file in "${backup_files[@]}"; do
        if [[ -f "$backup_file" ]]; then
            local original_file="${backup_file%.regression-backup}"
            mv "$backup_file" "$original_file"
            log_success "已恢復: $original_file"
        fi
    done
}

# 設置清理陷阱
trap restore_files EXIT

# 測試防線一：故意破壞 WSGI 配置
test_line_one_regression() {
    log_info "🔥 測試防線一回歸：故意破壞 WSGI 配置..."

    # 備份原始文件
    cp "backend/config/wsgi.py" "backend/config/wsgi.py.temp-backup"

    # 創建錯誤的 WSGI 配置
    cat > "backend/config/wsgi.py" << 'EOF'
"""
故意錯誤的 WSGI 配置 - 用於回歸測試
"""

import os
from django.core.wsgi import get_wsgi_application

# 故意使用錯誤的設置模組
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "novel.settings")  # 遺留路徑

application = get_wsgi_application()
EOF

    log_info "已創建錯誤的 WSGI 配置（使用遺留路徑）"

    # 執行 WSGI 煙霧測試，應該失敗
    if ./scripts/ci/wsgi-smoke-test.sh 2>/dev/null; then
        log_error "防線一回歸測試失敗：應該檢測到錯誤但沒有"
        # 恢復文件
        mv "backend/config/wsgi.py.temp-backup" "backend/config/wsgi.py"
        return 1
    else
        log_success "防線一回歸測試通過：成功檢測到 WSGI 配置錯誤"
        # 恢復文件
        mv "backend/config/wsgi.py.temp-backup" "backend/config/wsgi.py"
        return 0
    fi
}

# 測試防線二：故意添加遺留路徑引用
test_line_two_regression() {
    log_info "🔍 測試防線二回歸：故意添加遺留路徑引用..."

    # 創建臨時文件包含遺留路徑引用
    local temp_file="temp_legacy_test.py"

    cat > "$temp_file" << 'EOF'
# 故意的遺留路徑引用 - 用於回歸測試
from novel.models import Book  # 這應該被檢測到
import novel.settings as settings  # 這也應該被檢測到

def test_function():
    pass
EOF

    log_info "已創建包含遺留路徑引用的臨時文件"

    # 執行遺留路徑掃描，應該失敗
    if ./scripts/ci/legacy-path-scanner.sh 2>/dev/null; then
        log_error "防線二回歸測試失敗：應該檢測到遺留路徑但沒有"
        rm -f "$temp_file"
        return 1
    else
        log_success "防線二回歸測試通過：成功檢測到遺留路徑引用"
        rm -f "$temp_file"
        return 0
    fi
}

# 測試防線三：故意破壞 Docker 配置
test_line_three_regression() {
    log_info "🚀 測試防線三回歸：故意破壞 Docker 配置..."

    # 備份原始 Docker Compose 配置
    cp "infra/docker/docker-compose.ci.yml" "infra/docker/docker-compose.ci.yml.temp-backup"

    # 創建錯誤的 Docker Compose 配置
    cat > "infra/docker/docker-compose.ci.yml" << 'EOF'
# 故意錯誤的 Docker Compose 配置 - 用於回歸測試
version: '3.8'

services:
  # 故意使用不存在的映像
  backend-ci:
    image: nonexistent-image:latest
    environment:
      - DJANGO_SETTINGS_MODULE=novel.settings  # 遺留路徑
    ports:
      - "8001:8000"
    # 故意缺少必要的依賴
EOF

    log_info "已創建錯誤的 Docker Compose 配置"

    # 檢查 Docker Compose 配置語法
    if docker-compose -f "infra/docker/docker-compose.ci.yml" config > /dev/null 2>&1; then
        log_warning "Docker Compose 配置語法正確，但包含邏輯錯誤"

        # 檢查是否包含遺留路徑
        if grep -q "novel.settings" "infra/docker/docker-compose.ci.yml"; then
            log_success "防線三回歸測試通過：配置中包含遺留路徑引用（會被防線二檢測）"
        else
            log_error "防線三回歸測試失敗：未能創建包含遺留路徑的配置"
            mv "infra/docker/docker-compose.ci.yml.temp-backup" "infra/docker/docker-compose.ci.yml"
            return 1
        fi
    else
        log_success "防線三回歸測試通過：成功檢測到 Docker Compose 配置語法錯誤"
    fi

    # 恢復原始配置
    mv "infra/docker/docker-compose.ci.yml.temp-backup" "infra/docker/docker-compose.ci.yml"
    return 0
}

# 測試 pre-commit hook 回歸
test_precommit_regression() {
    log_info "🔧 測試 pre-commit hook 回歸..."

    # 檢查 pre-commit 是否可用
    if ! command -v pre-commit &> /dev/null; then
        log_warning "pre-commit 不可用，跳過 hook 回歸測試"
        return 0
    fi

    # 創建包含遺留路徑的臨時文件
    local temp_file="temp_precommit_test.py"

    cat > "$temp_file" << 'EOF'
# 故意的遺留路徑引用 - 用於 pre-commit 回歸測試
from novel.models import Book
EOF

    # 添加到 git staging area
    git add "$temp_file" 2>/dev/null || true

    # 執行 pre-commit hook（只針對遺留路徑掃描）
    if pre-commit run legacy-path-scanner --files "$temp_file" 2>/dev/null; then
        log_error "pre-commit 回歸測試失敗：應該檢測到遺留路徑但沒有"
        git reset HEAD "$temp_file" 2>/dev/null || true
        rm -f "$temp_file"
        return 1
    else
        log_success "pre-commit 回歸測試通過：成功檢測到遺留路徑引用"
        git reset HEAD "$temp_file" 2>/dev/null || true
        rm -f "$temp_file"
        return 0
    fi
}

# 測試整合回歸
test_integration_regression() {
    log_info "🔗 測試整合回歸：多重錯誤配置..."

    # 創建多個包含不同類型錯誤的文件
    local temp_files=(
        "temp_wsgi_error.py"
        "temp_legacy_error.py"
        "temp_settings_error.py"
    )

    # WSGI 錯誤
    cat > "${temp_files[0]}" << 'EOF'
# WSGI 配置錯誤
import os
from django.core.wsgi import get_wsgi_application
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "novel.settings")
application = get_wsgi_application()
EOF

    # 遺留路徑錯誤
    cat > "${temp_files[1]}" << 'EOF'
# 遺留路徑錯誤
from novel.models import Book
from novel.views import BookView
import novel.urls
EOF

    # 設置錯誤
    cat > "${temp_files[2]}" << 'EOF'
# 設置錯誤
INSTALLED_APPS = [
    'novel',  # 遺留應用
    'novel.crawler',  # 遺留爬蟲
]
ROOT_URLCONF = 'novel.urls'
WSGI_APPLICATION = 'novel.wsgi.application'
EOF

    log_info "已創建多重錯誤配置文件"

    # 執行遺留路徑掃描，應該檢測到所有錯誤
    if ./scripts/ci/legacy-path-scanner.sh 2>/dev/null; then
        log_error "整合回歸測試失敗：應該檢測到多重錯誤但沒有"
        # 清理文件
        for file in "${temp_files[@]}"; do
            rm -f "$file"
        done
        return 1
    else
        log_success "整合回歸測試通過：成功檢測到多重錯誤配置"
        # 清理文件
        for file in "${temp_files[@]}"; do
            rm -f "$file"
        done
        return 0
    fi
}

# 生成回歸測試報告
generate_regression_report() {
    local exit_code=$1

    log_info "📋 生成回歸測試報告..."

    local report_file="/tmp/regression-test-report-$(date +%Y%m%d-%H%M%S).txt"

    cat > "$report_file" << EOF
# CI 部署安全網回歸測試報告

## 測試時間
$(date)

## 測試目的
驗證三層部署安全網能夠正確檢測和阻止錯誤配置

## 測試結果
$(if [[ $exit_code -eq 0 ]]; then echo "✅ 通過"; else echo "❌ 失敗"; fi)

## 回歸測試項目
- 防線一 WSGI 配置回歸測試
- 防線二遺留路徑掃描回歸測試
- 防線三 Docker 配置回歸測試
- pre-commit hook 回歸測試
- 整合回歸測試

## 測試方法
使用故意的錯誤配置來驗證每個防護機制是否能正確檢測問題：
1. 在 WSGI 配置中使用遺留路徑
2. 在代碼中添加遺留模組引用
3. 在 Docker 配置中使用錯誤設置
4. 測試 pre-commit hook 的檢測能力
5. 組合多種錯誤進行整合測試

## 結論
$(if [[ $exit_code -eq 0 ]]; then
    echo "所有回歸測試通過，部署安全網機制工作正常，能夠有效檢測和阻止錯誤配置。"
else
    echo "部分回歸測試失敗，需要檢查和修復安全網機制。"
fi)
EOF

    log_info "回歸測試報告已保存到: $report_file"
    cat "$report_file"
}

# 主函數
main() {
    local exit_code=0

    log_info "🚀 開始 CI 部署安全網回歸測試..."
    log_warning "注意：此測試會故意創建錯誤配置來驗證防護機制"
    echo ""

    # 備份原始文件
    backup_files

    echo ""

    # 測試防線一回歸
    if ! test_line_one_regression; then
        log_error "防線一回歸測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試防線二回歸
    if [[ $exit_code -eq 0 ]] && ! test_line_two_regression; then
        log_error "防線二回歸測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試防線三回歸
    if [[ $exit_code -eq 0 ]] && ! test_line_three_regression; then
        log_error "防線三回歸測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試 pre-commit 回歸
    if [[ $exit_code -eq 0 ]] && ! test_precommit_regression; then
        log_error "pre-commit 回歸測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試整合回歸
    if [[ $exit_code -eq 0 ]] && ! test_integration_regression; then
        log_error "整合回歸測試失敗"
        exit_code=1
    fi

    echo ""

    # 生成報告
    generate_regression_report $exit_code

    echo ""

    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 CI 部署安全網回歸測試全部通過！"
        log_info "✨ 所有防護機制都能正確檢測錯誤配置"
        log_info "🛡️ 部署安全網驗證完成"
    else
        log_error "💥 CI 部署安全網回歸測試失敗！"
        log_error "🚨 部分防護機制無法正確檢測錯誤配置"
        log_info "📝 請檢查並修復相關機制"
    fi

    return $exit_code
}

# 執行主函數
main "$@"
