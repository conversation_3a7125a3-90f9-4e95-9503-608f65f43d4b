#!/bin/bash
# Integration Safety Test for CI Deployment Safety Nets
# 整合測試：確保三層防護機制協同工作

set -e

echo "🔗 Starting Integration Safety Test..."
echo "📂 Working directory: $(pwd)"

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 檢查腳本是否存在
check_scripts_exist() {
    log_info "檢查安全網腳本是否存在..."

    local scripts=(
        "scripts/ci/wsgi-smoke-test.sh"
        "scripts/ci/legacy-path-scanner.sh"
        "scripts/ci/deployment-simulation-test.sh"
    )

    local missing_scripts=()

    for script in "${scripts[@]}"; do
        if [[ ! -f "$script" ]]; then
            missing_scripts+=("$script")
        else
            log_success "腳本存在: $script"
        fi
    done

    if [[ ${#missing_scripts[@]} -gt 0 ]]; then
        log_error "缺少以下腳本:"
        for script in "${missing_scripts[@]}"; do
            echo "  - $script"
        done
        return 1
    fi

    log_success "所有安全網腳本檢查通過"
    return 0
}

# 檢查腳本執行權限
check_script_permissions() {
    log_info "檢查腳本執行權限..."

    local scripts=(
        "scripts/ci/wsgi-smoke-test.sh"
        "scripts/ci/legacy-path-scanner.sh"
        "scripts/ci/deployment-simulation-test.sh"
    )

    local permission_issues=()

    for script in "${scripts[@]}"; do
        if [[ ! -x "$script" ]]; then
            permission_issues+=("$script")
            # 自動修復權限
            chmod +x "$script"
            log_warning "已修復 $script 的執行權限"
        else
            log_success "權限正確: $script"
        fi
    done

    if [[ ${#permission_issues[@]} -gt 0 ]]; then
        log_warning "已修復 ${#permission_issues[@]} 個腳本的執行權限"
    fi

    log_success "腳本執行權限檢查通過"
    return 0
}

# 測試防線一：WSGI 煙霧測試
test_line_one() {
    log_info "🔥 測試防線一：WSGI 煙霧測試..."

    # 設置環境變數
    export PYTHONPATH=/workspace/backend:$PYTHONPATH
    export DJANGO_SETTINGS_MODULE=config.django_settings

    if ./scripts/ci/wsgi-smoke-test.sh; then
        log_success "防線一測試通過"
        return 0
    else
        log_error "防線一測試失敗"
        return 1
    fi
}

# 測試防線二：遺留路徑掃描
test_line_two() {
    log_info "🔍 測試防線二：遺留路徑掃描..."

    if ./scripts/ci/legacy-path-scanner.sh; then
        log_success "防線二測試通過"
        return 0
    else
        log_error "防線二測試失敗"
        return 1
    fi
}

# 測試防線三：部署模擬測試（簡化版）
test_line_three_lite() {
    log_info "🚀 測試防線三：部署模擬測試（簡化版）..."

    # 檢查 Docker 是否可用
    if ! command -v docker &> /dev/null; then
        log_warning "Docker 不可用，跳過防線三測試"
        return 0
    fi

    if ! docker info &> /dev/null; then
        log_warning "Docker 服務未運行，跳過防線三測試"
        return 0
    fi

    # 檢查 Docker Compose 配置文件
    if [[ ! -f "infra/docker/docker-compose.ci.yml" ]]; then
        log_error "Docker Compose CI 配置文件不存在"
        return 1
    fi

    # 驗證 Docker Compose 配置語法
    if docker-compose -f infra/docker/docker-compose.ci.yml config > /dev/null 2>&1; then
        log_success "Docker Compose 配置語法正確"
    else
        log_error "Docker Compose 配置語法錯誤"
        return 1
    fi

    log_success "防線三配置檢查通過（跳過完整部署測試以節省時間）"
    return 0
}

# 測試 pre-commit hook 配置
test_precommit_config() {
    log_info "🔧 測試 pre-commit hook 配置..."

    # 檢查 .pre-commit-config.yaml 是否存在
    if [[ ! -f ".pre-commit-config.yaml" ]]; then
        log_error "pre-commit 配置文件不存在"
        return 1
    fi

    # 檢查是否包含遺留路徑掃描 hook
    if grep -q "legacy-path-scanner" .pre-commit-config.yaml; then
        log_success "pre-commit 配置包含遺留路徑掃描 hook"
    else
        log_error "pre-commit 配置缺少遺留路徑掃描 hook"
        return 1
    fi

    # 檢查 pre-commit 配置語法（如果 pre-commit 可用）
    if command -v pre-commit &> /dev/null; then
        if pre-commit validate-config; then
            log_success "pre-commit 配置語法正確"
        else
            log_error "pre-commit 配置語法錯誤"
            return 1
        fi
    else
        log_warning "pre-commit 不可用，跳過語法檢查"
    fi

    log_success "pre-commit 配置檢查通過"
    return 0
}

# 測試 CI 工作流程配置
test_ci_workflow_config() {
    log_info "⚙️ 測試 CI 工作流程配置..."

    local workflow_file=".github/workflows/main-ci.yml"

    # 檢查工作流程文件是否存在
    if [[ ! -f "$workflow_file" ]]; then
        log_error "CI 工作流程文件不存在: $workflow_file"
        return 1
    fi

    # 檢查是否包含安全網相關的 job
    local required_jobs=(
        "legacy-path-scanner"
        "deployment-simulation"
        "wsgi-smoke-test"
    )

    local missing_jobs=()

    for job in "${required_jobs[@]}"; do
        if grep -q "$job" "$workflow_file"; then
            log_success "CI 工作流程包含: $job"
        else
            missing_jobs+=("$job")
        fi
    done

    if [[ ${#missing_jobs[@]} -gt 0 ]]; then
        log_warning "CI 工作流程缺少以下 job 或引用:"
        for job in "${missing_jobs[@]}"; do
            echo "  - $job"
        done
        # 不返回錯誤，因為可能是命名不同
    fi

    log_success "CI 工作流程配置檢查通過"
    return 0
}

# 生成整合測試報告
generate_report() {
    local exit_code=$1

    log_info "📋 生成整合測試報告..."

    local report_file="/tmp/integration-safety-test-report-$(date +%Y%m%d-%H%M%S).txt"

    cat > "$report_file" << EOF
# CI 部署安全網整合測試報告

## 測試時間
$(date)

## 測試結果
$(if [[ $exit_code -eq 0 ]]; then echo "✅ 通過"; else echo "❌ 失敗"; fi)

## 三層防護機制狀態
- 防線一：WSGI 煙霧測試 - $(if test_line_one &>/dev/null; then echo "✅ 正常"; else echo "❌ 異常"; fi)
- 防線二：遺留路徑掃描 - $(if test_line_two &>/dev/null; then echo "✅ 正常"; else echo "❌ 異常"; fi)
- 防線三：部署模擬測試 - $(if test_line_three_lite &>/dev/null; then echo "✅ 正常"; else echo "❌ 異常"; fi)

## 配置檢查
- pre-commit 配置 - $(if test_precommit_config &>/dev/null; then echo "✅ 正常"; else echo "❌ 異常"; fi)
- CI 工作流程配置 - $(if test_ci_workflow_config &>/dev/null; then echo "✅ 正常"; else echo "❌ 異常"; fi)

## 建議
$(if [[ $exit_code -eq 0 ]]; then
    echo "所有安全網機制運作正常，可以安全部署。"
else
    echo "發現問題，請檢查上述失敗項目並修復後重新測試。"
fi)
EOF

    log_info "報告已保存到: $report_file"
    cat "$report_file"
}

# 主函數
main() {
    local exit_code=0

    log_info "🚀 開始 CI 部署安全網整合測試..."
    echo ""

    # 檢查腳本是否存在
    if ! check_scripts_exist; then
        log_error "腳本存在性檢查失敗"
        exit_code=1
    fi

    echo ""

    # 檢查腳本執行權限
    if ! check_script_permissions; then
        log_error "腳本權限檢查失敗"
        exit_code=1
    fi

    echo ""

    # 測試防線一
    if [[ $exit_code -eq 0 ]] && ! test_line_one; then
        log_error "防線一測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試防線二
    if [[ $exit_code -eq 0 ]] && ! test_line_two; then
        log_error "防線二測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試防線三（簡化版）
    if [[ $exit_code -eq 0 ]] && ! test_line_three_lite; then
        log_error "防線三測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試 pre-commit 配置
    if [[ $exit_code -eq 0 ]] && ! test_precommit_config; then
        log_error "pre-commit 配置測試失敗"
        exit_code=1
    fi

    echo ""

    # 測試 CI 工作流程配置
    if [[ $exit_code -eq 0 ]] && ! test_ci_workflow_config; then
        log_error "CI 工作流程配置測試失敗"
        exit_code=1
    fi

    echo ""

    # 生成報告
    generate_report $exit_code

    echo ""

    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 CI 部署安全網整合測試全部通過！"
        log_info "✨ 三層防護機制協同工作正常"
        log_info "🛡️ 部署安全網已就緒"
    else
        log_error "💥 CI 部署安全網整合測試失敗！"
        log_error "🚨 請修復上述問題後重新測試"
    fi

    return $exit_code
}

# 執行主函數
main "$@"
