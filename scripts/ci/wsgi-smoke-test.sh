#!/bin/bash
# Simplified WSGI Smoke Test for debugging

set -e

# Auto-detect project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"

echo "🔥 Starting Simplified WSGI Smoke Test..."
echo "📂 Project root: $PROJECT_ROOT"
echo "📂 Backend directory: $BACKEND_DIR"

# Set up environment
export PYTHONPATH="$BACKEND_DIR"
export DJANGO_SETTINGS_MODULE="config.django_settings"

echo "🔧 Environment setup:"
echo "   PYTHONPATH=$PYTHONPATH"
echo "   DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS_MODULE"

# Test 1: Check if backend directory exists
echo ""
echo "📋 Test 1: Check backend directory..."
if [[ -d "$BACKEND_DIR" ]]; then
    echo "✅ Backend directory exists: $BACKEND_DIR"
else
    echo "❌ Backend directory not found: $BACKEND_DIR"
    exit 1
fi

# Test 2: Check if wsgi.py exists
echo ""
echo "📋 Test 2: Check WSGI file..."
WSGI_FILE="$BACKEND_DIR/config/wsgi.py"
if [[ -f "$WSGI_FILE" ]]; then
    echo "✅ WSGI file exists: $WSGI_FILE"
else
    echo "❌ WSGI file not found: $WSGI_FILE"
    exit 1
fi

# Test 3: Check Python version
echo ""
echo "📋 Test 3: Check Python..."
cd "$BACKEND_DIR"
python3 --version
echo "✅ Python is available"

# Test 4: Test Django import
echo ""
echo "📋 Test 4: Test Django import..."
python3 -c "import django; print(f'Django version: {django.get_version()}')"
echo "✅ Django import successful"

# Test 5: Test WSGI application import
echo ""
echo "📋 Test 5: Test WSGI application import..."
python3 -c "
import sys
import os
sys.path.insert(0, '$BACKEND_DIR')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.django_settings')

try:
    from config.wsgi import application
    print('✅ WSGI application imported successfully')
    print(f'📋 Application type: {type(application)}')
    print(f'📋 Application callable: {callable(application)}')
except ImportError as e:
    print(f'❌ Failed to import WSGI application: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ Error loading WSGI application: {e}')
    sys.exit(1)
"

# Test 6: Django check
echo ""
echo "📋 Test 6: Django check..."
python3 manage.py check --settings=config.django_settings
echo "✅ Django check passed"

echo ""
echo "🎉 All WSGI smoke tests passed!"
echo "✨ 部署安全網防線一：WSGI 應用加載驗證 - 正常"
