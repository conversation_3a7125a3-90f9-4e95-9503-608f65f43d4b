#!/bin/bash
# Sensitive Information Check Script
# 檢查代碼中的敏感信息和硬編碼秘密

set -e

echo "🔍 Checking for sensitive information..."

# 定義需要檢查的敏感模式
declare -a SENSITIVE_PATTERNS=(
    "password[[:space:]]*=[[:space:]]*['\"][^'\"]*['\"]"
    "secret[[:space:]]*=[[:space:]]*['\"][^'\"]*['\"]"
    "api_key[[:space:]]*=[[:space:]]*['\"][^'\"]*['\"]"
    "token[[:space:]]*=[[:space:]]*['\"][^'\"]*['\"]"
    "private_key"
    "-----BEGIN.*PRIVATE.*KEY-----"
    "sk-[a-zA-Z0-9]{32,}"
    "ghp_[a-zA-Z0-9]{36}"
    "postgres://[^:]+:[^@]+@"
)

# 定義要忽略的文件和目錄 (與 .pre-commit-config.yaml 保持一致)
IGNORE_DIRS=(
    ".git"
    "node_modules"
    "venv"
    "__pycache__"
    ".pytest_cache"
    "build"
    "dist"
    ".next"
    "coverage"
    "storybook-static"
    "sessionmanager-bundle"
    "staticfiles"
)

# 定義要忽略的文件模式
IGNORE_FILES=(
    "*.log"
    "*.min.js"
    "*.map"
    "*.pyc"
    "*.pyo"
    "package-lock.json"
    "yarn.lock"
    "check_sensitive.sh"          # 排除腳本自己
    "*/check_sensitive.sh"        # 排除維護目錄中的腳本
)

# 構建 grep 忽略參數
GREP_EXCLUDES=""
for dir in "${IGNORE_DIRS[@]}"; do
    GREP_EXCLUDES="$GREP_EXCLUDES --exclude-dir=$dir"
done

for file in "${IGNORE_FILES[@]}"; do
    GREP_EXCLUDES="$GREP_EXCLUDES --exclude=$file"
done

# 檢查函數
check_sensitive_patterns() {
    local found_issues=0

    echo "📋 Scanning for hardcoded secrets..."

    for pattern in "${SENSITIVE_PATTERNS[@]}"; do
        echo "  Checking pattern: $pattern"

        # 使用 grep 搜索敏感模式，但排除包含豁免註釋的行和腳本自己
        # 支援 gitleaks:allow, nosec, noqa 等常見豁免註釋
        if grep -r -i -E "$pattern" . $GREP_EXCLUDES 2>/dev/null | \
           grep -v -E "(gitleaks:allow|nosec|noqa|security:ignore)" | \
           grep -v -E "(scripts/check_sensitive\.sh|scripts/maintenance/check_sensitive\.sh)" > /tmp/sensitive_check_results.tmp; then
            if [ -s /tmp/sensitive_check_results.tmp ]; then
                cat /tmp/sensitive_check_results.tmp
                echo "⚠️  Found potential sensitive information matching: $pattern"
                found_issues=1
            fi
        fi
    done

    # 清理臨時檔案
    rm -f /tmp/sensitive_check_results.tmp

    return $found_issues
}

# 檢查 .env 文件（僅檢查被 Git 追蹤的檔案）
check_env_files() {
    echo "📋 Checking for .env files in repository..."

    # 只檢查被 Git 追蹤或待提交的 .env 檔案，排除範例檔案
    local problematic_envs
    problematic_envs=$(git ls-files | grep -E "\.env$|\.env\." | grep -v -E "\.(example|template|sample)$" 2>/dev/null || true)

    if [[ -n "$problematic_envs" ]]; then
        echo "⚠️  Found .env files that should not be committed:"
        echo "$problematic_envs"
        return 1
    fi

    echo "✅ No problematic .env files found in Git"
    return 0
}

# 檢查大文件（僅檢查被 Git 追蹤的檔案）
check_large_files() {
    echo "📋 Checking for large files (>5MB) in Git repository..."

    local large_files
    # 只檢查被 Git 追蹤的檔案中是否有大文件
    large_files=$(git ls-files | while IFS= read -r file; do
        if [[ -f "$file" ]] && [[ $(stat -f%z "$file" 2>/dev/null || echo 0) -gt 5242880 ]]; then
            echo "$file"
        fi
    done 2>/dev/null || true)

    if [[ -n "$large_files" ]]; then
        echo "⚠️  Found large files in Git repository that might contain sensitive data:"
        echo "$large_files" | while IFS= read -r file; do
            if [[ -f "$file" ]]; then
                ls -lh "$file"
            fi
        done
        return 1
    fi

    echo "✅ No suspicious large files found in Git"
    return 0
}

# 主函數
main() {
    local exit_code=0

    echo "🔐 Starting sensitive information check..."
    echo "📂 Working directory: $(pwd)"
    echo ""

    # 執行各項檢查
    if ! check_sensitive_patterns; then
        echo "❌ Found potential hardcoded secrets"
        exit_code=1
    else
        echo "✅ No hardcoded secrets detected"
    fi

    echo ""

    if ! check_env_files; then
        echo "❌ Found problematic .env files"
        exit_code=1
    fi

    echo ""

    if ! check_large_files; then
        echo "❌ Found suspicious large files"
        exit_code=1
    fi

    echo ""

    if [ $exit_code -eq 0 ]; then
        echo "✅ Sensitive information check completed successfully"
        echo "💡 Note: This is a basic check. Consider using specialized tools like:"
        echo "   - gitleaks"
        echo "   - truffleHog"
        echo "   - git-secrets"
        echo "   - Doppler CLI for secret management"
    else
        echo "⚠️  Sensitive information check found potential issues"
        echo "📝 Please review the findings above and:"
        echo "   1. Remove any hardcoded secrets"
        echo "   2. Use environment variables or secret management tools"
        echo "   3. Consider using Doppler for secret management"
        echo "   4. Add sensitive patterns to .gitignore"
    fi

    return $exit_code
}

# 運行主函數
main "$@"
