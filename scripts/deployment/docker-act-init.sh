#!/bin/bash
# 🐳 Docker Socket + act 環境初始化腳本

set -e

# 🎨 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 📝 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 🚀 主初始化流程
main() {
    echo "🐳 Docker Socket + act 環境初始化開始..."
    echo "========================================"

    # 1️⃣ 檢查Docker連接
    log_step "檢查Docker Socket連接"
    if docker info >/dev/null 2>&1; then
        log_success "Docker Socket連接正常"
        docker_version=$(docker --version)
        log_info "Docker版本: $docker_version"
    else
        log_error "Docker Socket連接失敗，請檢查掛載配置"
        exit 1
    fi

    # 2️⃣ 檢查act安裝
    log_step "檢查act安裝狀態"
    if command -v act >/dev/null 2>&1; then
        log_success "act已安裝"
        act_version=$(act --version)
        log_info "act版本: $act_version"
    else
        log_error "act未安裝，請檢查Dockerfile配置"
        exit 1
    fi

    # 3️⃣ 驗證GitHub Actions工作流程
    log_step "檢查GitHub Actions工作流程"
    if [ -d "/workspace/.github/workflows" ]; then
        workflow_count=$(find /workspace/.github/workflows -name "*.yml" -o -name "*.yaml" | wc -l)
        log_success "發現 $workflow_count 個工作流程檔案"

        # 列出工作流程
        log_info "可用的工作流程:"
        find /workspace/.github/workflows -name "*.yml" -o -name "*.yaml" | while read -r workflow; do
            workflow_name=$(basename "$workflow")
            echo "  📋 $workflow_name"
        done
    else
        log_warning "未找到GitHub Actions工作流程目錄"
    fi

    # 4️⃣ 設置act配置
    log_step "配置act環境"

    # 複製項目的.actrc到用戶目錄
    if [ -f "/workspace/.actrc" ]; then
        cp /workspace/.actrc ~/.actrc
        log_success "已載入項目act配置"
    fi

    # 創建act快取目錄
    mkdir -p ~/.cache/act
    mkdir -p ~/.config/act
    mkdir -p /tmp/act-artifacts

    log_success "act環境配置完成"

    # 5️⃣ 預拉取常用鏡像
    log_step "預拉取GitHub Actions鏡像"

    # 預拉取鏡像（後台執行，不阻塞主流程）
    {
        docker pull catthehacker/ubuntu:act-latest >/dev/null 2>&1 && \
        log_success "已預拉取 ubuntu:act-latest 鏡像" || \
        log_warning "預拉取 ubuntu:act-latest 鏡像失敗"
    } &

    # 6️⃣ 檢查網絡連接
    log_step "檢查服務網絡連接"

    # 檢查Redis連接
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli -h redis-act ping >/dev/null 2>&1; then
            log_success "Redis連接正常"
        else
            log_warning "Redis連接失敗，某些測試可能無法執行"
        fi
    fi

    # 檢查PostgreSQL連接
    if command -v pg_isready >/dev/null 2>&1; then
        if pg_isready -h postgres-act -U test_user >/dev/null 2>&1; then
            log_success "PostgreSQL連接正常"
        else
            log_warning "PostgreSQL連接失敗，資料庫測試可能無法執行"
        fi
    fi

    # 7️⃣ 顯示使用指南
    log_step "環境使用指南"
    echo ""
    echo "🎭 act 使用指南:"
    echo "  act                           # 執行所有工作流程"
    echo "  act -l                        # 列出所有可用的工作流程"
    echo "  act -j <job-name>             # 執行特定工作"
    echo "  act pull_request              # 模擬PR觸發"
    echo "  act push                      # 模擬push觸發"
    echo ""
    echo "🐳 Docker指令:"
    echo "  docker ps                     # 查看運行中的容器"
    echo "  docker images                 # 查看可用鏡像"
    echo "  docker system prune           # 清理未使用的資源"
    echo ""
    echo "🔧 開發指令:"
    echo "  cd /workspace/backend && python manage.py test  # 運行Django測試"
    echo "  cd /workspace/frontend && npm test              # 運行前端測試"
    echo ""

    log_success "🎉 Docker Socket + act 環境初始化完成！"
    echo "========================================"

    # 8️⃣ 進入交互模式或執行指定命令
    if [ $# -eq 0 ]; then
        log_info "進入交互模式，按 Ctrl+C 結束"
        exec bash
    else
        log_info "執行指定命令: $*"
        exec "$@"
    fi
}

# 🚦 錯誤處理
trap 'log_error "初始化過程中發生錯誤，退出碼: $?"' ERR

# 🏁 執行主函數
main "$@"
