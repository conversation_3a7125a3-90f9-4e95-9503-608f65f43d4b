#!/bin/bash
# 🔒 Docker Socket + act 安全最佳實踐配置腳本

set -e

# 🎨 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 📝 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 🔒 安全檢查主函數
main() {
    echo "🔒 Docker Socket + act 安全最佳實踐檢查"
    echo "============================================"

    # 1️⃣ 檢查Docker Socket權限
    log_step "檢查Docker Socket權限設置"

    if [ -S "/var/run/docker.sock" ]; then
        # macOS compatible stat command
        socket_perms=$(stat -f %Mp%Lp /var/run/docker.sock)
        socket_owner=$(stat -f %Su:%Sg /var/run/docker.sock)

        log_info "Docker Socket權限: $socket_perms"
        log_info "Docker Socket擁有者: $socket_owner"

        # 建議的安全權限是660 (rw-rw----)
        if [ "$socket_perms" != "660" ]; then
            log_warning "建議設置Socket權限為660: sudo chmod 660 /var/run/docker.sock"
        else
            log_success "Docker Socket權限設置正確"
        fi
    else
        log_error "Docker Socket不存在或不可訪問"
        exit 1
    fi

    # 2️⃣ 檢查用戶權限
    log_step "檢查當前用戶Docker權限"

    if groups $USER | grep -q "docker"; then
        log_success "用戶 $USER 已在docker組中"
    else
        log_warning "用戶 $USER 不在docker組中"
        log_info "運行: sudo usermod -aG docker $USER"
        log_info "然後重新登錄"
    fi

    # 3️⃣ 檢查容器安全配置
    log_step "檢查容器安全配置"

    # 檢查是否運行為非root用戶
    if [ "$(id -u)" -eq 0 ]; then
        log_warning "正在以root用戶運行，建議使用非特權用戶"
    else
        log_success "正在以非特權用戶運行: $(whoami)"
    fi

    # 4️⃣ 檢查敏感文件掛載
    log_step "檢查敏感文件掛載策略"

    # 檢查是否掛載了敏感目錄
    sensitive_paths=(
        "/etc/passwd"
        "/etc/shadow"
        "/root"
        "/home"
        "/var/lib/docker"
    )

    for path in "${sensitive_paths[@]}"; do
        if mountpoint -q "$path" 2>/dev/null; then
            log_warning "檢測到敏感路徑掛載: $path"
        fi
    done

    # 5️⃣ 檢查Docker資源限制
    log_step "檢查Docker資源限制配置"

    # 這些檢查在主機上運行
    if command -v docker >/dev/null 2>&1; then
        # 檢查容器資源限制
        container_name="novel-dev-act-temp"
        if docker ps -a --format "table {{.Names}}" | grep -q "$container_name"; then
            memory_limit=$(docker inspect "$container_name" --format='{{.HostConfig.Memory}}')
            cpu_limit=$(docker inspect "$container_name" --format='{{.HostConfig.CpuQuota}}')

            if [ "$memory_limit" = "0" ]; then
                log_warning "容器沒有設置記憶體限制"
            else
                log_success "容器記憶體限制: $(($memory_limit/1024/1024))MB"
            fi

            if [ "$cpu_limit" = "0" ]; then
                log_warning "容器沒有設置CPU限制"
            else
                log_success "容器CPU限制已設置"
            fi
        fi
    fi

    # 6️⃣ 網絡安全檢查
    log_step "檢查網絡安全配置"

    # 檢查是否使用自定義網絡
    if docker network ls | grep -q "novel_ci_network"; then
        log_success "使用自定義Docker網絡: novel_ci_network"
    else
        log_warning "建議使用自定義Docker網絡以增強隔離"
    fi

    # 7️⃣ act安全配置檢查
    log_step "檢查act安全配置"

    if [ -f "/workspace/.actrc" ]; then
        log_success "找到act配置文件"

        # 檢查是否啟用了容器重用（可能的安全風險）
        if grep -q "\-\-reuse" /workspace/.actrc; then
            log_warning "啟用了容器重用，注意潛在的狀態污染風險"
        fi

        # 檢查是否有敏感環境變數
        if grep -q "SECRET\|PASSWORD\|TOKEN" /workspace/.actrc; then
            log_error "act配置中可能包含敏感信息，請檢查"
        else
            log_success "act配置未檢測到明顯的敏感信息"
        fi
    else
        log_warning "未找到act配置文件"
    fi

    # 8️⃣ 生成安全報告
    log_step "生成安全檢查報告"

    report_file="./security-check-report.txt"
    {
        echo "Docker Socket + act 安全檢查報告"
        echo "生成時間: $(date)"
        echo "============================================"
        echo "Docker Socket權限: $socket_perms"
        echo "Docker Socket擁有者: $socket_owner"
        echo "當前用戶: $(whoami)"
        echo "用戶組: $(groups)"
        echo "容器運行狀態: $(docker ps --format 'table {{.Names}}\t{{.Status}}' | grep novel)"
        echo "============================================"
        echo "安全建議:"
        echo "1. 定期檢查Docker Socket權限"
        echo "2. 避免以root運行容器"
        echo "3. 使用資源限制防止濫用"
        echo "4. 監控容器網絡活動"
        echo "5. 定期掃描容器鏡像漏洞"
    } > "$report_file"

    log_success "安全檢查報告已生成: $report_file"

    # 9️⃣ 安全強化建議
    echo ""
    echo "🛡️ 安全強化建議:"
    echo "1. 🔐 使用非root用戶運行容器"
    echo "2. 🚧 設置適當的資源限制 (Memory: 4GB, CPU: 2.0)"
    echo "3. 🌐 使用自定義Docker網絡"
    echo "4. 📊 監控Docker Socket訪問"
    echo "5. 🔍 定期掃描容器漏洞"
    echo "6. 📝 審計act執行日誌"
    echo "7. 🔒 限制容器特權 (--user, --no-new-privileges)"
    echo ""

    log_success "🎉 Docker Socket + act 安全檢查完成！"
}

# 🚦 錯誤處理
trap 'log_error "安全檢查過程中發生錯誤，退出碼: $?"' ERR

# 🏁 執行主函數
main "$@"
