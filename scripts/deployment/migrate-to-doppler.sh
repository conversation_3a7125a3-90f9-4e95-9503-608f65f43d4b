#!/bin/bash
# Doppler環境變數遷移腳本

set -euo pipefail

echo "🔐 NovelWebsite Doppler 環境變數遷移腳本"
echo "=========================================="

# 檢查Doppler CLI是否已安裝和配置
if ! command -v doppler &> /dev/null; then
    echo "❌ Doppler CLI未安裝，請先安裝"
    exit 1
fi

if ! doppler me &> /dev/null; then
    echo "❌ Doppler未認證，請先執行 'doppler login'"
    exit 1
fi

echo "✅ Doppler CLI已配置完成"

# 設置專案配置（如果尚未設置）
echo "📋 設置專案配置..."
if ! doppler setup --project novel-website --config dev --no-interactive; then
    echo "❌ Doppler 專案配置失敗"
    exit 1
fi

echo "🔍 檢查現有秘密..."
if ! doppler secrets >/dev/null 2>&1; then
    echo "❌ 無法讀取 Doppler 秘密，請檢查專案配置"
    exit 1
fi

# 顯示當前秘密數量
SECRET_COUNT=$(doppler secrets --json | jq 'length' 2>/dev/null || echo "unknown")
echo "📊 當前已有 ${SECRET_COUNT} 個秘密"

echo ""
echo "📤 開始添加開發環境秘密..."
echo "⚠️  警告：此操作將覆蓋同名的現有秘密"
read -p "是否繼續？ (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 核心Django配置
echo "添加Django核心配置..."
doppler secrets set SECRET_KEY="django-insecure-dev-key-$(date +%s)" --silent

# 資料庫配置
echo "添加資料庫配置..."
echo "⚠️ 注意：請手動設置實際的資料庫連線資訊"
doppler secrets set DATABASE_URL="postgresql://REPLACE_WITH_REAL_VALUES" --silent
doppler secrets set POSTGRES_DB="novelwebsite_dev" --silent
doppler secrets set POSTGRES_USER="postgres" --silent
doppler secrets set POSTGRES_PASSWORD="YOUR_PASSWORD_HERE" --silent  # gitleaks:allow
doppler secrets set POSTGRES_HOST="localhost" --silent
doppler secrets set POSTGRES_PORT="5432" --silent

# Redis配置
echo "添加Redis配置..."
doppler secrets set REDIS_HOST="localhost" --silent
doppler secrets set REDIS_PORT="6379" --silent
doppler secrets set REDIS_DB="0" --silent
doppler secrets set REDIS_URL="redis://localhost:6379/0" --silent
doppler secrets set CELERY_BROKER_URL="redis://localhost:6379/0" --silent
doppler secrets set CELERY_RESULT_BACKEND="redis://localhost:6379/0" --silent

# Django開發配置
echo "添加Django開發配置..."
doppler secrets set DEBUG="True" --silent
doppler secrets set ALLOWED_HOSTS="localhost,127.0.0.1,0.0.0.0" --silent
doppler secrets set ADMIN_URL="admin/" --silent

# 爬蟲配置
echo "添加爬蟲配置..."
doppler secrets set SCRAPY_LOG_LEVEL="INFO" --silent
doppler secrets set DOWNLOAD_DELAY="1" --silent
doppler secrets set RANDOMIZE_DOWNLOAD_DELAY="0.5" --silent
doppler secrets set CONCURRENT_REQUESTS="16" --silent
doppler secrets set TTKAN_BASE_URL="https://ttkan.co" --silent
doppler secrets set USER_AGENT="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36" --silent

# CORS配置（開發環境）
echo "添加CORS配置..."
doppler secrets set CORS_ALLOW_ALL_ORIGINS="True" --silent
doppler secrets set CORS_ALLOWED_ORIGINS="http://localhost:3000,http://localhost:8000" --silent
doppler secrets set CORS_ALLOW_CREDENTIALS="True" --silent

# 其他配置
echo "添加其他配置..."
doppler secrets set TIME_ZONE="Asia/Taipei" --silent
doppler secrets set LANGUAGE_CODE="zh-hant" --silent
doppler secrets set PORT="8000" --silent

# 爬蟲專用配置
echo "添加爬蟲專用配置..."
doppler secrets set CRAWLER_MAX_WORKERS="3" --silent
doppler secrets set CRAWLER_CHUNK_SIZE="10" --silent
doppler secrets set CRAWLER_DELAY="1.0" --silent
doppler secrets set CRAWLER_TIMEOUT="30" --silent
doppler secrets set CRAWLER_RETRIES="3" --silent
doppler secrets set CRAWLER_LOG_LEVEL="INFO" --silent
doppler secrets set CRAWLER_CACHE_ENABLED="true" --silent
doppler secrets set CRAWLER_CACHE_EXPIRE="86400" --silent

echo ""
echo "🎉 開發環境秘密添加完成！"
echo ""
echo "📋 查看所有秘密："
doppler secrets

echo ""
echo "💡 使用說明："
echo "1. 運行開發服務器: doppler run -- python manage.py runserver"
echo "2. 運行前端開發: doppler run -- npm start"
echo "3. 運行爬蟲: doppler run -- scrapy crawl ttkan"
echo "4. 運行完整開發環境: doppler run -- make dev"
echo ""
echo "🔒 重要提醒："
echo "- 請記得更新生產環境的配置（staging/prd）"
echo "- 生產環境請使用強密碼和真實的API密鑰"
echo "- 考慮為CI/CD創建service token"
