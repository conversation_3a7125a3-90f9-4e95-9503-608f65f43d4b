#!/bin/bash
# NovelWebsite Security Hotfix Script
# 🚨 EMERGENCY SECURITY RESPONSE 🚨

set -e

echo "🔥 執行緊急安全修復..."

# 1. 生成新的 Django SECRET_KEY
echo "📝 生成新的 Django SECRET_KEY..."
NEW_SECRET_KEY=$(python3 -c "
import secrets
import string
alphabet = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
print(''.join(secrets.choice(alphabet) for i in range(50)))
")

# 2. 創建安全的 .env 模板
echo "📄 創建安全的環境配置..."
cat > .env << EOF
# ===========================
# 🔒 SECURE ENVIRONMENT CONFIG
# ===========================
# Generated: $(date)

# 🚨 請立即更新這些值！
DATABASE_URL=postgresql://[NEW_USERNAME]:[NEW_PASSWORD]@[NEW_HOST]:5432/[NEW_DATABASE]
POSTGRES_DB=your_new_database_name
POSTGRES_USER=your_new_username
POSTGRES_PASSWORD=your_new_secure_password
POSTGRES_HOST=your_new_database_host
POSTGRES_PORT=5432

# Django 安全配置
SECRET_KEY=${NEW_SECRET_KEY}
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1

# Redis 配置
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=\${REDIS_URL}
CELERY_RESULT_BACKEND=\${REDIS_URL}

# 🔒 安全 CORS 配置 (已修復)
CORS_ALLOW_ALL_ORIGINS=False
CORS_ALLOWED_ORIGINS=http://localhost:8000

# 爬蟲配置
SCRAPY_LOG_LEVEL=INFO
DOWNLOAD_DELAY=1
RANDOMIZE_DOWNLOAD_DELAY=0.5
CONCURRENT_REQUESTS=16

# ttkan.co 爬蟲配置
TTKAN_BASE_URL=https://ttkan.co
USER_AGENT="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# 🚨 請生成新的 API Key！
ANTHROPIC_API_KEY=your_new_anthropic_api_key_here

# 其他設置
TIME_ZONE=Asia/Taipei
LANGUAGE_CODE=zh-hant
PORT=8000
EOF

# 3. 創建 backend/.env
cp .env backend/.env

# 4. 安裝 detect-secrets for pre-commit
echo "🔧 安裝安全掃描工具..."
pip install detect-secrets || echo "⚠️ 請手動安裝: pip install detect-secrets"

# 5. 初始化 detect-secrets
detect-secrets scan --baseline .secrets.baseline || echo "⚠️ 請手動初始化: detect-secrets scan --baseline .secrets.baseline"

echo "✅ 安全修復完成！"
echo ""
echo "🚨 重要後續動作："
echo "1. 立即撤換 PostgreSQL 密碼 (AWS RDS Console)"
echo "2. 立即撤換 Anthropic API Key (console.anthropic.com)"
echo "3. 更新 .env 檔案中的新憑證"
echo "4. 更新 GitHub Secrets"
echo "5. 重新部署應用程式"
echo ""
echo "📝 備份檔案位置："
echo "- .env.backup (原始root .env)"
echo "- backend/.env.backup (原始backend .env)"
