#!/bin/bash
set -e

# Usage: ./create_sub_issue.sh <parent_issue_number> "<child_issue_title>" "<child_issue_body>" "<labels>"

PARENT_ISSUE_NUM=$1
CHILD_TITLE=$2
CHILD_BODY=$3
CHILD_LABELS=$4

if [[ -z "$PARENT_ISSUE_NUM" || -z "$CHILD_TITLE" ]]; then
  echo "Usage: $0 <parent_issue_number> \"<child_title>\" [\"<child_body>\"] [\"<labels>\"]"
  exit 1
fi

echo " Creating child issue..."
# 創建子任務，並獲取其 URL，再從 URL 中提取編號
CHILD_ISSUE_URL=$(gh issue create --title "$CHILD_TITLE" --body "$CHILD_BODY" --label "$CHILD_LABELS" --assignee "@me")
CHILD_ISSUE_NUM=$(echo "$CHILD_ISSUE_URL" | awk -F'/' '{print $NF}')
echo "✅ Child issue #$CHILD_ISSUE_NUM created."

echo " Linking #$CHILD_ISSUE_NUM to parent #$PARENT_ISSUE_NUM..."

# 獲取父 Issue 和子 Issue 的 GraphQL Node ID
PARENT_NODE_ID=$(gh api graphql -f query='{repository(owner:"{",name:"{")issue(number:'$PARENT_ISSUE_NUM'){id}}}' --jq '.data.repository.issue.id')
CHILD_NODE_ID=$(gh api graphql -f query='{repository(owner:"{",name:"{")issue(number:'$CHILD_ISSUE_NUM'){id}}}' --jq '.data.repository.issue.id')

# 使用 GraphQL API 建立父子關係
gh api graphql -f query='
  mutation($parent_id:ID!, $child_id:ID!) {
    addProjectV2ItemById(input: {contentId: $child_id, projectId: $parent_id}) {
      item {
        id
      }
    }
  }' -f parent_id="$PARENT_NODE_ID" -f child_id="$CHILD_NODE_ID" --silent

# 實際建立父子關係的 API 可能是 updateIssue，這取決於 GitHub 的實現細節
# 以下是另一種可能的 GraphQL mutation
# gh api graphql -f query='
#   mutation($childId:ID!, $parentIds:[ID!]) {
#     updateIssue(input: {id: $childId, parentIds: $parentIds}) {
#       issue { id }
#     }
#   }' -f childId="$CHILD_NODE_ID" -f parentIds="[\"$PARENT_NODE_ID\"]" --silent

# 這是針對 Projects V2 的添加，更常見
# 假設你已經有一個 Project ID
# PROJECT_ID="YOUR_PROJECT_NODE_ID"
# gh api graphql -f query='...' -f projectId="$PROJECT_ID" ...

echo "✅ Successfully linked #$CHILD_ISSUE_NUM as a sub-task of #$PARENT_ISSUE_NUM."
