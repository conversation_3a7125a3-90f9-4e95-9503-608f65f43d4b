#!/bin/bash
# Git 安全別名設定
# 使用方法: source scripts/git-safety-aliases.sh

# 危險命令的安全包裝
alias git-reset-hard='echo "⚠️  git reset --hard 會丟棄所有變更!" && echo "請使用: git stash 或 git commit 先保存變更" && echo "確定要繼續嗎? (y/N)" && read -r response && [[ "$response" == "y" ]] && git reset --hard'

alias git-clean-force='echo "⚠️  git clean -fd 會永久刪除未追蹤檔案!" && echo "請先備份重要檔案" && echo "確定要繼續嗎? (y/N)" && read -r response && [[ "$response" == "y" ]] && git clean -fd'

alias git-rebase-abort='echo "⚠️  git rebase --abort 可能丟失變更!" && echo "請先檢查 git status 和 git stash list" && echo "確定要繼續嗎? (y/N)" && read -r response && [[ "$response" == "y" ]] && git rebase --abort'

# 安全的替代命令
alias git-safe-reset='git stash && echo "✅ 變更已保存到 stash，現在執行 reset" && git reset --hard'
alias git-safe-clean='echo "📋 將要刪除的檔案:" && git clean -fd --dry-run && echo "確定刪除以上檔案嗎? (y/N)" && read -r response && [[ "$response" == "y" ]] && git clean -fd'

echo "🛡️  Git 安全別名已載入"
echo "🔧 使用 git-reset-hard, git-clean-force, git-rebase-abort 來執行危險操作"
echo "✅ 使用 git-safe-reset, git-safe-clean 來執行安全操作"
