import asyncio
import time
from pathlib import Path
import psutil

from crawler.spiders.ttkan_spider import Ttkan<PERSON>pider
from crawler.utils.concurrency import ConcurrencyConfig
from crawler.utils import db_utils
from bs4 import BeautifulSoup
import crawler.spiders.ttkan_spider as spider_module

HTML_PATH = Path(__file__).parent / "sample_chapter.html"
HTML_TEXT = HTML_PATH.read_text()


# disable real database operations
async def _noop(*args, **kwargs):
    return 0


db_utils.partial_save_chapters = _noop
spider_module.partial_save_chapters = _noop


class LocalSpider(TtkanSpider):
    async def parse_chapter_content(self, chapter):
        await asyncio.sleep(0.01)
        soup = BeautifulSoup(HTML_TEXT, "html.parser")
        content = soup.select_one(".chapter-content").text.strip()
        return {**chapter, "chapter_content": content, "status": "success"}


async def run_spider(workers: int) -> float:
    config = ConcurrencyConfig(max_concurrency=workers, chunk_size=5, chunk_delay=0.0)
    async with LocalSpider("http://example.com", config) as spider:
        spider.chapters = [
            {"chapter_number": i, "chapter_title": f"chapter {i}", "chapter_url": ""}
            for i in range(20)
        ]
        start = time.monotonic()
        await spider.parse_chapters()
        end = time.monotonic()
        return end - start


if __name__ == "__main__":
    sequential = asyncio.run(run_spider(1))
    parallel = asyncio.run(run_spider(5))
    proc = psutil.Process()
    rss_mb = proc.memory_info().rss / (1024 * 1024)
    open_fds = len(proc.open_files())
    print(f"Sequential duration: {sequential:.2f}s")
    print(f"Parallel duration: {parallel:.2f}s")
    print(f"Peak RSS: {rss_mb:.1f}MB, open files: {open_fds}")
