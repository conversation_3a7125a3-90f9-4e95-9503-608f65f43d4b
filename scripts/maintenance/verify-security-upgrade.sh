#!/bin/bash

# 安全升級驗證腳本
# 用途：快速驗證安全升級是否成功

set -e

echo "🔍 開始驗證安全升級..."

# 檢查當前目錄
if [[ ! -f "backend/requirements.txt" ]]; then
    echo "❌ 請在專案根目錄執行此腳本"
    exit 1
fi

# 1. 檢查 requirements.txt 中的版本
echo "📋 檢查 requirements.txt 中的套件版本..."

DJANGO_VERSION=$(grep "Django==" backend/requirements.txt | cut -d'=' -f3 | cut -d' ' -f1)
SCRAPY_VERSION=$(grep "scrapy==" backend/requirements.txt | cut -d'=' -f3 | cut -d' ' -f1)

echo "Django 版本: $DJANGO_VERSION"
echo "Scrapy 版本: $SCRAPY_VERSION"

# 2. 版本要求檢查
if [[ "$DJANGO_VERSION" == "4.2.23" ]]; then
    echo "✅ Django 版本符合安全要求"
else
    echo "❌ Django 版本不符合要求，需要 4.2.23，當前為 $DJANGO_VERSION"
    exit 1
fi

if [[ "$SCRAPY_VERSION" == "2.13.2" ]]; then
    echo "✅ Scrapy 版本符合安全要求"
else
    echo "❌ Scrapy 版本不符合要求，需要 2.13.2，當前為 $SCRAPY_VERSION"
    exit 1
fi

# 3. 檢查前端 axios 版本 (如果存在)
if [[ -f "frontend/package.json" ]]; then
    echo "📦 檢查前端套件版本..."

    AXIOS_VERSION=$(grep '"axios"' frontend/package.json | cut -d'"' -f4 | sed 's/\^//')
    echo "axios 版本: $AXIOS_VERSION"

    # 簡單的版本比較 (假設版本格式為 x.y.z)
    if [[ "$AXIOS_VERSION" == "1.8.2" ]]; then
        echo "✅ axios 版本符合安全要求"
    else
        echo "⚠️ axios 版本 $AXIOS_VERSION，請確認是否 >= 1.8.2"
    fi
fi

# 4. 檢查是否有虛擬環境
echo "🐍 檢查 Python 環境..."

if [[ -d "backend/venv" ]] || [[ -n "$VIRTUAL_ENV" ]]; then
    echo "✅ 檢測到虛擬環境"
else
    echo "⚠️ 建議使用虛擬環境"
fi

# 5. 建議下一步操作
echo ""
echo "🎯 建議下一步操作："
echo "1. 安裝更新的套件:"
echo "   cd backend && pip install -r requirements.txt"
echo ""
echo "2. 執行相容性測試:"
echo "   python tests/security_compatibility_test.py"
echo ""
echo "3. 執行完整測試套件:"
echo "   python manage.py test"
echo ""
echo "4. 執行 CI 流程:"
echo "   ./scripts/local-ci.sh"
echo ""
echo "5. 提交變更："
echo "   git add -A"
echo "   git commit -m 'security: fix critical vulnerabilities'"

echo "🎉 安全升級驗證完成！"
