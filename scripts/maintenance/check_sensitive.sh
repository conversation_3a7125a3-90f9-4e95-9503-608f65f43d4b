#!/bin/bash
set -euo pipefail
# =============================================================================
# 敏感信息檢查腳本 - scripts/check_sensitive.sh
# 用途：掃描項目中可能存在的敏感信息，防止意外提交
# =============================================================================

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 檢測是否在CI環境
IS_CI=${CI:-false}
if [[ "$IS_CI" == "true" ]]; then
    echo -e "${BLUE}🔍 CI環境：執行嚴格模式敏感信息掃描...${NC}"
else
    echo -e "${BLUE}🔍 本地環境：執行敏感信息掃描...${NC}"
fi

echo "========================================"

# 計數器
sensitive_found=0
warnings=0

# 白名單檔案（本地開發允許，但不應被Git追蹤）
LOCAL_WHITELIST=(
    ".cursor/mcp.json.local"
    ".env.devcontext"
    ".env.local"
    ".env.development.local"
)

# 獲取Git已追蹤的檔案清單
echo -e "\n=== 獲取Git追蹤檔案清單 ==="
if ! command -v git &> /dev/null; then
    echo -e "${RED}❌ Git未安裝${NC}"
    exit 1
fi

# 確保在Git倉庫中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}❌ 當前目錄不是Git倉庫${NC}"
    exit 1
fi

TRACKED_FILES=$(git ls-files)
if [[ -z "$TRACKED_FILES" ]]; then
    echo -e "${YELLOW}⚠️ 沒有找到Git追蹤的檔案${NC}"
    exit 0
fi

echo -e "${GREEN}✅ 找到 $(echo "$TRACKED_FILES" | wc -l) 個已追蹤檔案${NC}"

echo -e "\n=== 檢查 API Keys 和 Tokens ==="

# 定義檢查函數
check_sensitive_pattern() {
    local pattern="$1"
    local description="$2"

    echo -e "\n檢查: $description"

    # 使用 git grep 只搜索已追蹤檔案，但排除 node_modules、測試文件、範例檔案、二進制文件和腳本自身
    if matches=$(git grep -E "$pattern" -- ':!node_modules' ':!*.wasm' ':!*.min.js' ':!scripts/check_sensitive.sh' ':!*test*.py' ':!*test*.js' ':!*test*.ts' ':!*spec*.py' ':!*spec*.js' ':!*spec*.ts' ':!*.example' ':!*example*' ':!*template*' ':!scripts/security-hotfix.sh' ':!sessionmanager-bundle' ':!*.zip' ':!*.bundle' 2>/dev/null); then
        echo -e "${RED}❌ 發現敏感信息: $description${NC}"
        echo "$matches"
        ((sensitive_found++))
    else
        echo -e "${GREEN}✅ 通過: $description${NC}"
    fi
}
# 執行各種敏感模式檢查 (2025年6月更新)
# AI/LLM API Keys - 2025年主流服務
check_sensitive_pattern "sk-ant-api03-[A-Za-z0-9\-_]{93,95}" "Anthropic API Key"
check_sensitive_pattern "sk-proj-[A-Za-z0-9]{48,}" "OpenAI Project API Key"
check_sensitive_pattern "sk-[A-Za-z0-9]{48,51}" "OpenAI Legacy API Key"
check_sensitive_pattern "pplx-[A-Za-z0-9]{64}" "Perplexity API Key"
check_sensitive_pattern "AIza[0-9A-Za-z\-_]{35}" "Google API Key"
check_sensitive_pattern "goog-[A-Za-z0-9\-_]{32,}" "Google Gemini API Key"
check_sensitive_pattern "meta-[A-Za-z0-9\-_]{32,}" "Meta Llama API Key"
check_sensitive_pattern "hf_[A-Za-z0-9]{34}" "Hugging Face Token"
check_sensitive_pattern "xai-[A-Za-z0-9\-_]{32,}" "xAI API Key"

# GitHub 和版控
check_sensitive_pattern "gh[pousr]_[A-Za-z0-9]{36,251}" "GitHub Token"
check_sensitive_pattern "github_pat_[A-Za-z0-9_]{82}" "GitHub Personal Access Token"
check_sensitive_pattern "gho_[A-Za-z0-9]{36}" "GitHub OAuth Token"
check_sensitive_pattern "ghu_[A-Za-z0-9]{36}" "GitHub User Token"

# 雲端平台 - 2025年格式
check_sensitive_pattern "AKIA[0-9A-Z]{16}" "AWS Access Key"
check_sensitive_pattern "ASIA[0-9A-Z]{16}" "AWS Session Token"
check_sensitive_pattern "AROA[0-9A-Z]{16}" "AWS Role Session"
check_sensitive_pattern "AIDA[0-9A-Z]{16}" "AWS User ID"

# JWT 和現代認證
check_sensitive_pattern "eyJ[A-Za-z0-9\-_]+\.eyJ[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+" "JWT Token"
check_sensitive_pattern "ey[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+" "JWT Token (Extended)"
check_sensitive_pattern "Bearer eyJ[A-Za-z0-9\-_]+" "JWT Bearer Token"

# 新型密鑰格式
check_sensitive_pattern "-----BEGIN[A-Z ]+PRIVATE KEY-----" "Private Key"
check_sensitive_pattern "-----BEGIN CERTIFICATE-----" "Certificate"
check_sensitive_pattern "-----BEGIN OPENSSH PRIVATE KEY-----" "SSH Private Key"
check_sensitive_pattern "ssh-rsa AAAA[0-9A-Za-z+/]+" "SSH Public Key"
check_sensitive_pattern "ssh-ed25519 AAAA[0-9A-Za-z+/]+" "Ed25519 SSH Key"

# HTTP 認證和 Token
check_sensitive_pattern "Authorization[:\\s]*Basic\\s+[A-Za-z0-9+/]+=*" "Basic Auth"
check_sensitive_pattern "Bearer [A-Za-z0-9\\-\\._~\\+\\/]+=*" "Bearer Token"
check_sensitive_pattern "token[\\s]*[:=][\\s]*[\"'][A-Za-z0-9\\-\\._~\\+\\/]+=*[\"']" "Generic Token"

# 現代密碼和秘密格式
check_sensitive_pattern "://[^:/@\\[\\]]+:[^:/@\\[\\]]+@" "Password in URL"
check_sensitive_pattern "password[\"']*\\s*[:=]\\s*[\"'][^\"']{8,}[\"']" "Hardcoded Password"
check_sensitive_pattern "secret[_-]?key[\"']*\\s*[:=]\\s*[\"'][^\"']{16,}[\"']" "Secret Key"
check_sensitive_pattern "api[_-]?secret[\"']*\\s*[:=]\\s*[\"'][^\"']{16,}[\"']" "API Secret"
check_sensitive_pattern "client[_-]?secret[\"']*\\s*[:=]\\s*[\"'][^\"']{16,}[\"']" "Client Secret"

# 2025年主流資料庫連線
check_sensitive_pattern "postgres://[^:]*:[^@]*@[^/]*/[^\\s]*" "PostgreSQL URL"
# 排除 crawler settings 文件中的 f-string 模板
if matches=$(git grep -E "postgresql://[^:]*:[^@]*@[^/]*/[^\\s]*" -- ':!node_modules' ':!*.wasm' ':!*.min.js' ':!scripts/check_sensitive.sh' ':!*test*.py' ':!*test*.js' ':!*test*.ts' ':!*spec*.py' ':!*spec*.js' ':!*spec*.ts' ':!*.example' ':!*example*' ':!*template*' ':!scripts/security-hotfix.sh' ':!sessionmanager-bundle' ':!*.zip' ':!*.bundle' ':!backend/novel/crawler/config/settings.py' 2>/dev/null); then
    echo -e "${RED}❌ 發現敏感信息: PostgreSQL URL${NC}"
    echo "$matches"
    ((sensitive_found++))
else
    echo -e "${GREEN}✅ 通過: PostgreSQL URL${NC}"
fi
check_sensitive_pattern "mongodb(\+srv)?://[^:]*:[^@]*@[^/]*/[^\\s]*" "MongoDB URI"
check_sensitive_pattern "redis://[^:]*:[^@]*@[^/]*" "Redis URL"
check_sensitive_pattern "rediss://[^:]*:[^@]*@[^/]*" "Redis TLS URL"

# 2025年新興雲端服務
check_sensitive_pattern "https://[^\\s]*\\.turso\\.io/[^\\s]*" "Turso Database URL"
check_sensitive_pattern "https://[^\\s]*\\.supabase\\.co/[^\\s]*" "Supabase URL"
check_sensitive_pattern "https://[^\\s]*\\.planetscale\\.sh/[^\\s]*" "PlanetScale URL"
check_sensitive_pattern "https://[^\\s]*\\.neon\\.tech/[^\\s]*" "Neon Database URL"
check_sensitive_pattern "https://[^\\s]*\\.xata\\.sh/[^\\s]*" "Xata Database URL"

# Slack 和通訊平台
check_sensitive_pattern "xox[bpoa]-[0-9]{12}-[0-9]{12}-[0-9]{12}-[a-z0-9]{32}" "Slack Token"
check_sensitive_pattern "T[A-Z0-9]{8}/B[A-Z0-9]{8}/[a-zA-Z0-9]{24}" "Slack Webhook"

# AI 相關新服務 (2025)
check_sensitive_pattern "sk-[a-z]{4}-[A-Za-z0-9]{32,}" "Claude API Key (New Format)"
check_sensitive_pattern "claude-[A-Za-z0-9\-_]{32,}" "Claude Service Key"
check_sensitive_pattern "mistral-[A-Za-z0-9\-_]{32,}" "Mistral AI Key"

echo -e "\n=== 檢查特定配置檔案 ==="

# 檢查 .cursor/mcp.json（無副檔名）是否被Git追蹤
echo -e "\n檢查: .cursor/mcp.json 是否被追蹤"
if git ls-files --error-unmatch ".cursor/mcp.json" &>/dev/null; then
    echo -e "${RED}❌ .cursor/mcp.json 被Git追蹤！應該移除並使用 .cursor/mcp.json.local${NC}"
    ((sensitive_found++))
else
    echo -e "${GREEN}✅ .cursor/mcp.json 未被Git追蹤${NC}"
fi

# 檢查白名單檔案的狀態
echo -e "\n檢查: 白名單檔案狀態"
for file in "${LOCAL_WHITELIST[@]}"; do
    if [[ -f "$file" ]]; then
        if git ls-files --error-unmatch "$file" &>/dev/null; then
            echo -e "${RED}❌ $file 被Git追蹤！應該添加到 .gitignore${NC}"
            ((sensitive_found++))
        else
            if [[ "$IS_CI" == "true" ]]; then
                echo -e "${YELLOW}⚠️ CI環境發現本地檔案: $file${NC}"
                ((warnings++))
            else
                echo -e "${GREEN}✅ $file 存在且未被追蹤（本地開發正常）${NC}"
            fi
        fi
    fi
done

echo -e "\n=== 檢查 .gitignore 配置 ==="

# 必要的 .gitignore 模式
required_patterns=(
    ".cursor/mcp.json"
    ".cursor/mcp.json.local"
    ".env.devcontext"
    ".env.local"
    "*.log"
    "__pycache__/"
    "node_modules/"
)

missing_patterns=()
if [[ -f ".gitignore" ]]; then
    for pattern in "${required_patterns[@]}"; do
        # 使用更精確的匹配，避免誤判
        if ! grep -F "$pattern" .gitignore > /dev/null 2>&1; then
            missing_patterns+=("$pattern")
        fi
    done

    if [[ ${#missing_patterns[@]} -gt 0 ]]; then
        echo -e "${RED}❌ .gitignore 缺少必要模式:${NC}"
        printf '  %s\n' "${missing_patterns[@]}"
        ((sensitive_found++))
    else
        echo -e "${GREEN}✅ .gitignore 配置完整${NC}"
    fi
else
    echo -e "${RED}❌ .gitignore 檔案不存在${NC}"
    ((sensitive_found++))
fi

echo -e "\n=== 檢查環境變數檔案 ==="

# 檢查是否有環境變數檔案被意外追蹤
env_files=(".env" ".env.local" ".env.production" ".env.development")
for env_file in "${env_files[@]}"; do
    if git ls-files --error-unmatch "$env_file" &>/dev/null; then
        echo -e "${RED}❌ $env_file 被Git追蹤！環境變數檔案不應提交${NC}"
        ((sensitive_found++))
    fi
done

echo -e "\n========================================"
echo -e "掃描結果總結:"

if [[ $sensitive_found -gt 0 ]]; then
    echo -e "${RED}❌ 發現 $sensitive_found 個敏感信息問題${NC}"

    echo -e "\n🛠 修復建議:"
    echo -e "1. 移除所有敏感信息"
    echo -e "2. 檢查 .gitignore 配置"
    echo -e "3. 使用環境變數或本地配置檔案"
    echo -e "4. 確保 .cursor/mcp.json.local 未被追蹤"

    if [[ "$IS_CI" == "true" ]]; then
        echo -e "\n${RED}🚫 CI環境不允許敏感信息，終止流程${NC}"
        exit 1
    else
        echo -e "\n${YELLOW}⚠️ 本地環境發現問題，建議修復後再提交${NC}"
        exit 1
    fi
elif [[ $warnings -gt 0 ]]; then
    echo -e "${YELLOW}⚠️ $warnings 個警告，但無阻塞問題${NC}"
    if [[ "$IS_CI" == "true" ]]; then
        echo -e "${GREEN}✅ CI環境安全檢查通過${NC}"
    else
        echo -e "${GREEN}✅ 本地環境安全檢查通過${NC}"
    fi
    exit 0
else
    echo -e "${GREEN}✅ 未發現敏感信息${NC}"
    if [[ "$IS_CI" == "true" ]]; then
        echo -e "${GREEN}🎉 CI環境完全通過安全檢查${NC}"
    else
        echo -e "${GREEN}🎉 本地環境完全通過安全檢查${NC}"
    fi
    exit 0
fi
