#!/bin/bash

# 安全漏洞修復腳本
# 用途：升級有安全漏洞的套件並進行驗證

set -e

echo "🚨 開始執行安全漏洞修復..."

# 1. 建立備份分支
echo "📌 建立安全修復分支..."
git checkout -b security/fix-critical-vulnerabilities
git push -u origin security/fix-critical-vulnerabilities

# 2. 升級 Python 套件
echo "🐍 升級後端套件..."
cd backend
pip install Django==4.2.23
pip install scrapy==2.13.2

# 3. 更新 requirements.txt
echo "📝 更新 requirements.txt..."
sed -i 's/Django==4.2.22/Django==4.2.23/' requirements.txt
sed -i 's/scrapy==2.11.2/scrapy==2.13.2/' requirements.txt

# 4. 驗證版本
echo "🔍 驗證套件版本..."
python -c "import django; print(f'Django: {django.VERSION}')"
python -c "import scrapy; print(f'Scrapy: {scrapy.__version__}')"

# 5. 執行測試
echo "🧪 執行相容性測試..."
python manage.py test --parallel --keepdb

# 6. 執行爬蟲測試
echo "🕷️ 測試爬蟲功能..."
cd novel/crawler
python -m pytest tests/ -v

# 7. 檢查 linting
echo "📏 檢查代碼品質..."
flake8 --config=../../.flake8 .
black --check .

# 8. 提交變更
echo "✅ 提交安全修復..."
cd ../..
git add -A
git commit -m "security: fix critical vulnerabilities

- Django 4.2.22 → 4.2.23 (SQL injection fix)
- Scrapy 2.11.2 → 2.13.2 (DoS vulnerability fix)

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"

git push origin security/fix-critical-vulnerabilities

echo "🎉 安全修復完成！"
echo "📋 下一步：創建 PR 並運行完整 CI 測試"
