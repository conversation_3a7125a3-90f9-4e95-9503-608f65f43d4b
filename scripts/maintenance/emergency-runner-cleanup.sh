#!/bin/bash

# 🚨 緊急 AWS Runner 清理腳本
# 需要您先配置 SSH 連接到 Runner

set -euo pipefail

echo "🚨 緊急 AWS Runner 磁碟清理"
echo "=========================="

# 配置您的 Runner 連接信息
RUNNER_HOST="${RUNNER_HOST:-your-runner-ip}"
SSH_KEY="${SSH_KEY:-~/.ssh/your-key.pem}"
SSH_USER="${SSH_USER:-ec2-user}"

echo "🔍 連接信息："
echo "  Host: $RUNNER_HOST"
echo "  User: $SSH_USER"
echo "  Key: $SSH_KEY"
echo ""

# 檢查 SSH 金鑰是否存在
if [[ ! -f "$SSH_KEY" ]]; then
    echo "❌ SSH 金鑰不存在: $SSH_KEY"
    echo "請設置環境變數 SSH_KEY 指向您的私鑰文件"
    exit 1
fi

echo "⚡ 開始遠端清理..."

# 執行遠端清理命令
ssh -i "$SSH_KEY" "$SSH_USER@$RUNNER_HOST" << 'EOF'
    echo "🧹 在 Runner 上執行緊急清理..."

    # 檢查當前磁碟狀態
    echo "清理前磁碟狀態："
    df -h /
    echo ""

    # 停止所有容器
    echo "停止所有容器..."
    docker ps -q | xargs -r docker stop 2>/dev/null || true

    # 刪除所有容器
    echo "刪除所有容器..."
    docker ps -aq | xargs -r docker rm -f 2>/dev/null || true

    # 系統級清理
    echo "執行系統級清理..."
    docker system prune -a -f --volumes 2>/dev/null || true

    # 清理建構快取
    echo "清理建構快取..."
    docker builder prune -a -f 2>/dev/null || true

    # 清理 buildx 建構器
    echo "清理 buildx 建構器..."
    docker buildx ls | grep -v default | awk '{print $1}' | xargs -r docker buildx rm 2>/dev/null || true

    # 清理系統臨時文件
    echo "清理臨時文件..."
    sudo find /tmp -type f -atime +1 -delete 2>/dev/null || true

    # 檢查清理後狀態
    echo ""
    echo "✅ 清理完成！清理後磁碟狀態："
    df -h /

    # 測試 Docker 基本功能
    echo ""
    echo "🧪 測試 Docker 功能..."
    if docker version >/dev/null 2>&1; then
        echo "✅ Docker 服務正常"
    else
        echo "❌ Docker 服務需要重啟"
    fi
EOF

echo ""
echo "🎉 遠端清理完成！"
echo ""
echo "📋 下一步："
echo "1. 重新運行失敗的 GitHub Actions 工作流"
echo "2. 監控 Buildx 映像拉取是否成功"
echo "3. 驗證整個 Tier 2 構建流程"
