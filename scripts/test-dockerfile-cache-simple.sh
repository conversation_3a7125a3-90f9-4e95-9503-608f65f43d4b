#!/bin/bash
# 簡化的 Dockerfile 快取效率測試腳本
# 🎯 目標：驗證四階段 Dockerfile 的快取效率 (不依賴網路)
# 🚀 測試策略：使用本地基礎映像，專注於快取層次驗證

set -e

echo "🧪 開始簡化 Dockerfile 快取效率測試..."
echo "=========================================="

# 創建測試用的簡化 Dockerfile
create_test_dockerfile() {
    local dockerfile_name=$1
    local test_dir="test-docker-cache"

    mkdir -p "$test_dir"

    cat > "$test_dir/$dockerfile_name" << 'EOF'
# 測試用四階段 Dockerfile
FROM node:20-alpine AS base
LABEL stage="base-environment"
RUN echo "✅ 基礎環境準備完成 - 此層將被永久快取"
WORKDIR /app

FROM base AS dependencies
LABEL stage="dependencies"
COPY package.json package-lock.json* ./
RUN echo "📦 模擬依賴安裝..." && \
    echo "node_modules installed" > node_modules.txt && \
    echo "✅ 依賴安裝完成 - 快取已最佳化"

FROM dependencies AS builder
LABEL stage="application-builder"
COPY src/ ./src/
COPY public/ ./public/
RUN echo "🏗️ 模擬應用程式建置..." && \
    echo "build completed" > build.txt && \
    echo "✅ 應用程式建置完成"

FROM base AS production
LABEL stage="production"
COPY --from=dependencies /app/node_modules.txt ./
COPY --from=builder /app/build.txt ./
RUN echo "🚀 生產環境準備完成"
CMD ["echo", "Production ready!"]
EOF

    # 創建測試用的檔案結構
    mkdir -p "$test_dir/src" "$test_dir/public"
    echo '{"name": "test-app", "version": "1.0.0"}' > "$test_dir/package.json"
    echo 'console.log("Hello World");' > "$test_dir/src/index.js"
    echo '<html><body>Test</body></html>' > "$test_dir/public/index.html"

    echo "✅ 測試 Dockerfile 和檔案結構已創建"
}

# 測試函數：測量構建時間和快取命中率
measure_build_performance() {
    local dockerfile=$1
    local image_name=$2
    local test_name=$3
    local context_dir=$4

    echo ""
    echo "📊 測試: $test_name"
    echo "----------------------------------------"

    local start_time=$(date +%s)

    # 執行構建並捕獲輸出
    local build_output
    if build_output=$(docker build -f "$dockerfile" -t "$image_name" "$context_dir" 2>&1); then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        # 分析快取命中情況
        local cached_steps
        local total_steps
        local cache_hit_rate=0

        cached_steps=$(echo "$build_output" | grep -c "CACHED" 2>/dev/null || echo "0")
        total_steps=$(echo "$build_output" | grep -c "Step [0-9]" 2>/dev/null || echo "1")

        # 確保變數是數字
        cached_steps=${cached_steps//[^0-9]/}
        total_steps=${total_steps//[^0-9]/}

        if [[ -n "$cached_steps" && -n "$total_steps" && $total_steps -gt 0 ]]; then
            cache_hit_rate=$((cached_steps * 100 / total_steps))
        fi

        echo "✅ 構建成功"
        echo "⏱️ 構建時間: ${duration} 秒"
        echo "📈 快取命中率: ${cache_hit_rate}% (${cached_steps}/${total_steps})"

        # 顯示詳細的快取資訊
        echo ""
        echo "🔍 快取詳情:"
        echo "$build_output" | grep -E "(Step [0-9]+|CACHED|RUN|COPY)" | head -20

        return 0
    else
        echo "❌ 構建失敗"
        echo "$build_output"
        return 1
    fi
}

# 清理函數
cleanup_test_environment() {
    echo ""
    echo "🧹 清理測試環境..."
    rm -rf test-docker-cache
    docker rmi test-cache-app:v1 test-cache-app:v2 test-cache-app:v3 test-cache-app:v4 2>/dev/null || true
    echo "✅ 測試環境清理完成"
}

# 主測試流程
main() {
    echo "🚀 開始簡化 Dockerfile 快取效率測試套件"
    echo "=========================================="

    # 創建測試環境
    create_test_dockerfile "Dockerfile.test"

    local test_dir="test-docker-cache"
    local dockerfile="$test_dir/Dockerfile.test"

    # 測試 1: 首次構建 (冷啟動)
    echo ""
    echo "🚀 測試 1: 首次構建 (冷啟動)"
    echo "預期: 所有步驟都會執行，無快取命中"
    docker builder prune -af 2>/dev/null || true
    measure_build_performance "$dockerfile" "test-cache-app:v1" "首次構建" "$test_dir"

    # 測試 2: 無變更重建 (應該全部快取命中)
    echo ""
    echo "🚀 測試 2: 無變更重建"
    echo "預期: 100% 快取命中，構建時間 < 5 秒"
    measure_build_performance "$dockerfile" "test-cache-app:v2" "無變更重建" "$test_dir"

    # 測試 3: 僅修改源碼 (應該只重建 builder 和 production 階段)
    echo ""
    echo "🚀 測試 3: 僅修改源碼"
    echo "預期: base 和 dependencies 階段快取命中"
    echo 'console.log("Hello World - Modified!");' > "$test_dir/src/index.js"
    measure_build_performance "$dockerfile" "test-cache-app:v3" "源碼變更" "$test_dir"

    # 測試 4: 修改 package.json (應該重建 dependencies 及後續階段)
    echo ""
    echo "🚀 測試 4: 修改 package.json"
    echo "預期: 僅 base 階段快取命中"
    echo '{"name": "test-app", "version": "1.0.1"}' > "$test_dir/package.json"
    measure_build_performance "$dockerfile" "test-cache-app:v4" "package.json 變更" "$test_dir"

    echo ""
    echo "🎉 簡化 Dockerfile 快取效率測試完成！"
    echo "=========================================="
    echo ""
    echo "📊 測試結果分析："
    echo "✅ 首次構建: 建立基準線"
    echo "✅ 無變更重建: 驗證完整快取效率"
    echo "✅ 源碼變更: 驗證層次分離效果"
    echo "✅ 依賴變更: 驗證快取失效機制"
    echo ""
    echo "💡 優化效果："
    echo "- 四階段分層設計確保最大化快取重用"
    echo "- 程式碼變更不會影響依賴層快取"
    echo "- 依賴變更時僅重建必要的層"
    echo ""
    echo "✅ 快取策略驗證完成"
}

# 設置清理陷阱
trap cleanup_test_environment EXIT

# 執行主函數
main "$@"
