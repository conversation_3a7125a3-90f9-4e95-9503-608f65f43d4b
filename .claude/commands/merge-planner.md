---
allowed-tools: Ba<PERSON>(gh: *), Ba<PERSON>(git: *), Read, Edit, View, ToolCall, Write # 確保 Claude 能夠使用 GitHub CLI (gh)、Git 命令、讀取/編輯檔案以及通用工具調用
description: NovelWebsite專案PR全面分析，包含CI結果與審核意見，並生成修復行動方案和HTML報告。
---

## NovelWebsite PR 全面分析 - AI 技術夥伴級別的智能審查

### 核心理念升級
**從「自動化 Linter」到「AI 技術夥伴」**：本系統不僅檢查程式碼錯誤，更具備**上下文感知**和**工程智慧**，能夠理解原始任務目標、進行多維度建設性反饋，並基於風險等級動態調整審查深度。

**三大核心突破**：
1. **🧠 雙重上下文感知**：同時理解「實現」(PR) 與「意圖」(原始開發簡報)
2. **📊 結構化多維反饋**：提供 BLOCKER、SUGGESTION、QUESTION、PRAISE 等建設性意見
3. **⚖️ 適應性審查標準**：基於風險評級動態調整審查深度

---

## 🔍 Stage 0：狀態驗證協議 (Pre-Action State Validation)

### 🛡️ 強制前置檢查 - 防止狀態不一致

**AI 指令**: 在執行任何 PR 分析之前，必須先驗證 PR 的當前狀態和可分析性：

```bash
#!/bin/bash

# 接收 PR 編號
PR_NUMBER=$ARGUMENTS

if [[ -z "$PR_NUMBER" ]]; then
    echo "❌ 錯誤：請提供 PR 編號"
    echo "使用方式: /merge-planner.md <PR_NUMBER>"
    exit 1
fi

echo "🤖 merge-planner 啟動 - NovelWebsite 專案智能審查"
echo "🎯 目標 PR：#$PR_NUMBER"

# === 🔍 狀態驗證協議 ===
echo "🔍 執行 Stage 0：PR 狀態驗證..."

# 檢查 PR 是否存在
if ! gh pr view $PR_NUMBER >/dev/null 2>&1; then
    echo "❌ 狀態驗證失敗：PR #$PR_NUMBER 不存在"
    echo "🔍 請確認 PR 編號是否正確"
    exit 1
fi

# 檢查 PR 當前狀態
CURRENT_STATE=$(gh pr view $PR_NUMBER --json state --jq '.state')
IS_DRAFT=$(gh pr view $PR_NUMBER --json isDraft --jq '.isDraft')

if [ "$CURRENT_STATE" != "OPEN" ]; then
    echo "❌ 狀態驗證失敗：PR #$PR_NUMBER 狀態為 $CURRENT_STATE，與預期 OPEN 不符"
    echo "🔍 可能原因：已被手動處理 (合併/關閉)"
    echo "💡 建議：重新評估任務清單或檢查 PR 狀態"
    exit 1
fi

# 檢查是否有待執行的 CI 檢查
CI_STATUS=$(gh pr view $PR_NUMBER --json statusCheckRollup --jq '.statusCheckRollup[0].state // "PENDING"')
if [ "$CI_STATUS" = "PENDING" ]; then
    echo "⚠️ 注意：PR #$PR_NUMBER 的 CI 檢查仍在執行中"
    echo "🔍 建議：等待 CI 完成後再進行分析，或基於當前狀態進行初步分析"
fi

echo "✅ 狀態驗證通過：PR #$PR_NUMBER 處於可分析狀態"
echo "📊 PR 詳情：狀態=$CURRENT_STATE, Draft=$IS_DRAFT, CI狀態=$CI_STATUS"
echo ""
```

---

#### 🎯 第一階段 Alpha：PR 風險評級 (PR Risk Assessment)
**指令：智能化審查深度調控**

在狀態驗證通過後，對此 PR 進行**多維度風險評級**，以決定後續審查的深度和重點。

**風險評估維度**：
```bash
# 1. 變更範圍分析
FILES_CHANGED=$(gh pr diff $ARGUMENTS --name-only | wc -l)
LINES_CHANGED=$(gh pr diff $ARGUMENTS --numstat | awk '{sum += $1 + $2} END {print sum+0}')

# 2. 核心系統觸及度檢查
CORE_FILES_HIT=$(gh pr diff $ARGUMENTS --name-only | grep -E "(auth/|payment/|adapters/base\.py|config/django_settings\.py|package\.json|requirements.*\.txt)" | wc -l)

# 3. 依賴變更檢測
DEP_CHANGES=$(gh pr diff $ARGUMENTS --name-only | grep -E "(package\.json|requirements.*\.txt|Pipfile|yarn\.lock|pnpm-lock\.yaml)" | wc -l)

# 4. 測試覆蓋率評估
NEW_CODE_LINES=$(gh pr diff $ARGUMENTS --numstat | grep -v test | awk '{sum += $1} END {print sum+0}')
NEW_TEST_LINES=$(gh pr diff $ARGUMENTS --numstat | grep test | awk '{sum += $1} END {print sum+0}')
TEST_RATIO=$(echo "scale=2; $NEW_TEST_LINES / ($NEW_CODE_LINES + 1)" | bc)
```

**綜合風險評級邏輯**：
- **🟢 LOW**: 檔案≤5, 無核心觸及, 測試比例>0.3
- **🟡 MEDIUM**: 檔案6-15, 部分核心觸及, 測試比例0.1-0.3
- **🔴 HIGH**: 檔案>15, 大量核心觸及, 測試比例<0.1

**適應性審查策略**：
- **LOW風險**: 關注邏輯正確性，跳過風格細節
- **MEDIUM風險**: 平衡功能審查與架構建議
- **HIGH風險**: 深度架構審查，啟用子代理分析

#### 🧠 第一階段：三重上下文獲取 + 熔斷檢測 (Tri-Context Ingestion + Circuit Breaker)
**指令：消除「失憶式」審查，整合團隊智慧，防止死循環**

**熔斷機制前置檢查**：
```bash
# 0. 熔斷機制：檢測是否陷入修復循環
echo "🔍 執行熔斷檢測：檢查是否存在修復循環風險..."

# 統計 merge-planner 對此 PR 的審查次數
REVIEW_COUNT=$(gh pr view $ARGUMENTS --comments --json author,body,createdAt | \
    jq -r '.[] | select(.body | contains("🤖 Merge-Planner 審查與合併計劃")) | .createdAt' | wc -l | tr -d ' ')

echo "📊 檢測結果：merge-planner 已審查此 PR $REVIEW_COUNT 次"

# 熔斷條件：如果審查次數 >= 3，啟動熔斷機制
if [[ $REVIEW_COUNT -ge 3 ]]; then
    echo "🚨 熔斷機制觸發：檢測到潛在的修復循環風險"
    trigger_circuit_breaker $ARGUMENTS $REVIEW_COUNT
    exit 1
fi

# 如果審查次數 = 2，發出警告但繼續執行
if [[ $REVIEW_COUNT -eq 2 ]]; then
    echo "⚠️ 循環風險警告：這是第三次審查，請格外謹慎避免循環修復"
    LOOP_RISK_WARNING=true
fi
```

**三重獲取流程**：
1. **獲取實現 (The Implementation)**：
   ```bash
   # 完整 PR 詳情
   gh pr view $ARGUMENTS --json title,body,labels,comments,files
   # 程式碼差異
   gh pr diff $ARGUMENTS
   # CI 檢查狀態（絕對優先）
   gh pr checks $ARGUMENTS
   ```

2. **獲取意圖 (The Intent)**：
   ```bash
   # 追溯原始開發簡報 Issue
   CODING_BRIEF_ISSUE=$(gh pr view $ARGUMENTS --json body --jq '.body' | grep -oE '#[0-9]+' | head -1 | sed 's/#//')
   if [[ -n "$CODING_BRIEF_ISSUE" ]]; then
       gh issue view $CODING_BRIEF_ISSUE --json title,body
   else
       echo "⚠️ 警告：無法找到原始開發簡報，將基於 PR 描述推理意圖"
   fi
   ```

3. **獲取對話 (The Conversation)**：
   ```bash
   # 獲取 PR 頁面上所有的評論和審查意見
   gh pr view $ARGUMENTS --comments
   # 檢查是否有其他 AI 助手的建議（如 gemini-code-assist）
   gh api repos/:owner/:repo/pulls/$ARGUMENTS/reviews
   ```

**AI 三重上下文關聯指令**：
> 你的所有審查，都必須基於「**實現**」、「**意圖**」和「**已有對話**」進行。你必須：
> 1. 評估實現與原始意圖的契合度
> 2. **明確引用並整合**已有的審查意見（如 gemini-code-assist 的建議）
> 3. 避免重複建議，而是在現有對話基礎上提供增值分析
> 4. 示例：「`gemini-code-assist` 提出了關於 `eslint.config.js` 的可維護性建議，我同意此觀點，並將其納入 `SUGGESTION` 類別的修復計劃中。」

#### 🔍 第二階段：PR 意圖分類與風險定性 (PR Intent Classification & Risk Profiling)
**指令：**
在進行任何操作前，你的首要任務是識別此 PR 的核心**戰略意圖**，避免用「成本」衡量「無限價值」的戰略升級。

1.  **收集意圖信號 (Gather Intent Signals):**
    *   **標題與標籤分析：** 使用 `gh pr view $ARGUMENTS --json title,labels` 檢視關鍵詞：
        *   `refactor`, `upgrade`, `migration`, `🔧`, `🚀` → 重構信號
        *   `feat`, `feature`, `implement`, `✨` → 功能開發信號
        *   `fix`, `bug`, `🐛`, `hotfix` → 錯誤修復信號
        *   `chore`, `deps`, `dependency`, `📦` → 維護信號
    *   **描述內容分析：** 閱讀 PR 描述，尋找戰略性陳述：
        *   「為了升級框架」、「技術債清理」、「架構重構」→ 核心重構
        *   「5分鐘用戶旅程」、「新增搜索功能」、「用戶體驗改善」→ 功能開發
        *   「修復CI失敗」、「安全漏洞修復」、「性能問題」→ 錯誤修復
    *   **程式碼變更性質：** 使用 `gh pr diff $ARGUMENTS --name-only` 分析檔案類型：
        *   大量 `package.json`, `requirements*.txt`, `docker-compose*.yml` → 框架升級
        *   `config/` 目錄大幅變動 → 架構重構
        *   新增 `frontend/src/pages/*.tsx`, `backend/apps/*/views.py` → 功能開發

2.  **進行意圖分類 (Assign Intent Category):**
    *   **A. 核心重構 (Core Refactor)** - 戰略價值無限大：
        *   Django/React/Next.js 框架升級
        *   CI/CD 流程重構
        *   Docker 架構調整
        *   資料庫遷移和模型重構
        *   **特徵**：非做不可的變更，長期效益 > 短期成本
    *   **B. 功能開發 (Feature Development)** - 戰術價值：
        *   搜索功能實現
        *   用戶認證系統
        *   小說閱讀器改進
        *   響應式設計優化
    *   **C. 錯誤修復 (Bug Fix)** - 戰術價值：
        *   CI 測試失敗修復
        *   性能優化
        *   安全性修復
        *   部署問題解決

3.  **進行深度集成風險評估 (In-depth Integration Risk Assessment):**
    *   **任務**：模擬資深架構師，通過三個維度的直接證據評估真實集成風險

    **維度1：直接合併衝突檢測 (Score: 0 or 50)**
    ```bash
    # 合併預演 - 檢測直接衝突
    git fetch origin $baseRefName
    git checkout $headRefName
    git merge --no-commit --no-ff origin/$baseRefName
    # 成功=0分，失敗=50分(一票否決)
    git merge --abort  # 清理狀態
    ```

    **維度2：NovelWebsite核心檔案變更分析 (Score: 0-30)**
    *   **核心檔案清單**：
        *   **CI/CD核心** (20分): `.github/workflows/*`, `Makefile`, `scripts/*`
        *   **後端核心** (15分): `config/django_settings.py`, `config/urls.py`, `requirements*.txt`, `backend/apps/catalog/*`
        *   **前端核心** (15分): `package.json`, `pnpm-lock.yaml`, `frontend/package.json`, `tsconfig.json`
        *   **Docker基礎設施** (15分): `infra/docker/*`, `docker-compose*.yml`, `infra/aws-ecr/*`
        *   **配置檔案** (10分): `.env*`, `backend/config/*`, `.pre-commit-config.yaml`, `lighthouserc.js`
    *   **評分邏輯**: 檢查 `gh pr diff $ARGUMENTS --name-only` 觸及的核心檔案
    *   **最高30分封頂**

    **維度3：演進路徑重疊分析 (Score: 0-20)**
    ```bash
    # 計算檔案重疊度
    PR_FILES=$(gh pr diff $ARGUMENTS --name-only)
    MERGE_BASE=$(git merge-base origin/$baseRefName $headRefName)
    MAIN_CHANGES=$(git diff --name-only $MERGE_BASE...origin/$baseRefName)
    OVERLAP_COUNT=$(comm -12 <(echo "$PR_FILES" | sort) <(echo "$MAIN_CHANGES" | sort) | wc -l)
    # 重疊分數 = OVERLAP_COUNT * 2 (最高20分)
    ```

    **綜合風險評定:**
    *   **總風險分 = 衝突分數(0-50) + 核心分數(0-30) + 重疊分數(0-20)**
    *   **風險等級分類**：
        *   **低風險 (0-15)**：幾乎無衝突，未觸及核心
        *   **中風險 (16-35)**：部分核心變更或路徑交集
        *   **高風險 (36-60)**：觸及關鍵核心且有重疊
        *   **極高風險 (>60)**：存在直接衝突

4.  **輸出精準診斷:**
    *   「診斷：[意圖分類] - [戰略價值] | 集成風險：**[分數:等級]** (衝突:X + 核心:Y + 重疊:Z)」

#### 1. 策略決策門 (Strategic Decision Gate)
**指令：**
**根據步驟 0 的「意圖分類」**，執行以下戰略決策邏輯：

### **分支A：核心重構路徑**
*   **IF (`意圖` == `A. 核心重構`) THEN:**
    *   **決策原則**：**「放棄並重建」選項被禁用**
    *   **核心認知**：此類 PR 具備戰略價值無限大，必須被合併
    *   **任務重新定義**：從「是否修復」轉為「**如何安全地合併**」
    *   **跳過成本效益分析**，直接進入「**分階段降風險策略**」規劃：
        *   **PR 拆分建議**：能否將大型重構拆分為更小、可獨立驗證的 PR？
        *   **功能開關策略**：是否可用 feature flag 將新舊架構隔離，確保安全回滾？
        *   **詳細測試計畫**：除 CI 外，還需哪些手動測試驗證重構？
        *   **分階段合併計畫**：包含溝通、監控和回滾預案的詳細流程

### **分支B：戰術價值路徑**
*   **IF (`意圖` == `B. 功能開發` OR `C. 錯誤修復`) THEN:**
    *   **決策原則**：**啟用「修復 vs. 重建」成本效益分析**
    *   **評估修復成本：**
        *   **低** - 簡單路徑修正、小衝突
        *   **中** - 部分邏輯重寫、中等架構調整
        *   **高** - 大規模衝突、多重核心邏輯修改
    *   **重建成本基準**：恆定為「低」(~30分鐘)
    *   **決策邏輯：**
        ```
        IF (修復成本 == 高 AND 集成風險分數 >= 36)
        OR (修復成本 == 中 AND 集成風險分數 >= 61)
        OR (存在直接衝突 AND 修復成本 >= 中)
        THEN 推薦「放棄並重建」
        ELSE 推薦「繼續修復」
        ```

#### 🎯 第三階段：深度情境理解 (Enhanced Contextual Understanding)
**指令：基於雙重上下文的深度分析**

**結合實現與意圖的分析**：

1. **意圖-實現對齊檢查**：
   - 比較原始開發簡報的「核心目標」與實際 PR 變更
   - 識別是否存在**範圍蔓延**（scope creep）或**欠實現**（under-implementation）
   - 評估解決方案的**優雅程度**和**效率**

2. **工程智慧評估**：
   - 是否存在更簡單的實現路徑？
   - 架構設計是否適當，還是過度工程化？
   - 是否遵循了專案的既有模式和慣例？

3. **技術複雜度與影響範圍分析**：
   - **啟用 ultrathink 模式**進行深度技術分析
   - 評估變更對系統其他部分的潛在影響
   - 識別可能的邊緣情況和風險點

#### 3. CI 結果分析 (CI Result Analysis) - 技術修復重點
**指令：**
*   **檢查 CI 狀態：** 透過 GitHub CLI (`gh`) 檢查此 PR 的所有 CI 檢查結果。
*   **失敗定位：** 如果有任何 CI 檢查失敗，**立刻定位並提取相關的錯誤日誌**。
*   **[調用子代理進行深入 CI 分析]**：
    *   **子代理角色：** "你是一位資深軟體除錯專家。**啟用 ultrathink 模式**進行深度技術分析。"
    *   **子代理任務：** "你的唯一任務是分析以下 CI 錯誤日誌和相關的程式碼變更。**使用 ultrathink 模式**深入探討失敗的根本原因，考慮多種可能的成因，並提出 1-2 個具體且經過深思熟慮的修復方案。以簡潔的 Markdown 格式回報你的發現。"
    *   **子代理輸入：** [失敗檢查的名稱]、[完整的錯誤日誌]、[程式碼差異]
*   **主代理整合：** 接收子代理的分析結果，並將其整合到最終的行動方案中。

#### 4. 審核意見分類與關聯性分析 (Comment Categorization & Correlation Analysis)
**指令：**
*   **分析並分類所有審核意見：** **啟用 ultrathink 模式**，對於每一個審核意見，根據其性質將其分類為以下之一：
    *   `邏輯錯誤 (Logic Flaw)`：程式碼的根本性功能性問題。
    *   `程式碼風格 (Style Nitpick)`：格式、命名或其他非功能性問題。
    *   `提問與建議 (Question/Suggestion)`：需要開發者進一步澄清或可以考慮的優化。
    *   `安全性疑慮 (Security Concern)`：潛在的安全漏洞。
    *   `架構建議 (Architecture Suggestion)`：涉及重構、設計模式或架構改進的建議。
*   **建立關聯：**
    *   將 CI 的失敗日誌與其在程式碼變更中的**特定關聯部分**建立起來。
    *   同時，將每一個審核意見與其**對應的程式碼片段**關聯起來。

#### 📊 第五階段：生成結構化審查意見 (Generate Structured Review Feedback)
**指令：從二元判斷到建設性對話**

**AI 絕對優先級指令**：在你的審查流程中，**第一步永遠是分析 CI 檢查結果**。如果存在任何失敗的、非可選的檢查，你必須立即生成一個**唯一的、最高優先級的 `BLOCKER`，其 `impact_level` 為 `CRITICAL`**。所有其他的審查意見（風格、架構等）都可以繼續分析，但都必須排在這個 CI `BLOCKER` 之後。你的最終結論必須是 `REQUEST_CHANGES`，直到所有 CI 檢查通過。

**結構化反饋格式 (JSON)**：
```json
{
  "review_summary": {
    "overall_verdict": "APPROVE | REQUEST_CHANGES | NEEDS_DISCUSSION",
    "risk_level": "LOW | MEDIUM | HIGH",
    "intent_alignment_score": "1-10",
    "complexity_appropriateness": "UNDER_ENGINEERED | APPROPRIATE | OVER_ENGINEERED",
    "ci_status": {
      "all_checks_passed": "boolean",
      "failed_checks": ["check_name_1", "check_name_2"],
      "critical_blockers": "integer"
    }
  },
  "structured_feedback": [
    {
      "id": "blocker-1 | suggestion-1 | question-1 | praise-1",
      "file_path": "string | null",
      "line_number": "integer | null",
      "feedback_type": "BLOCKER | SUGGESTION | QUESTION | PRAISE",
      "category": "CI | LOGIC | SECURITY | ARCHITECTURE | PERFORMANCE | STYLE | TESTING",
      "title": "簡短標題",
      "comment": "詳細評論內容和修改建議",
      "code_snippet": "相關的程式碼片段 (如適用)",
      "suggested_fix": "具體的修復建議或程式碼範例 (如適用)",
      "impact_level": "CRITICAL | HIGH | MEDIUM | LOW",
      "effort_estimate": "TRIVIAL | EASY | MODERATE | COMPLEX",
      "references": "引用的已有評論或建議 (如適用)"
    }
  ]
}
```

**反饋類型定義**：
- **🚫 BLOCKER**: 嚴重錯誤，必須在合併前修復
- **💡 SUGGESTION**: 推薦的改進，可提升程式碼質量但非強制
- **❓ QUESTION**: 對程式碼意圖或實現有疑問，需要開發者澄清
- **👏 PRAISE**: 對寫得好的程式碼給予肯定和鼓勵

**子代理啟用條件**：
- **HIGH風險 PR**: 自動啟用**架構分析子代理**
- **BLOCKER類型問題**: 啟用**除錯專家子代理**
- **ARCHITECTURE類別問題**: 啟用**技術債務評估子代理**

**子代理協作框架**：
```markdown
### [調用架構分析子代理 - 僅限HIGH風險或ARCHITECTURE問題]
**子代理角色**: "你是一位擁有10年經驗的軟體架構師和技術Lead。**啟用 ultrathink 模式**。"
**子代理任務**: "基於以下上下文，提供深度架構分析：
1. 評估當前實現的架構合理性
2. 提出至少2種替代實現方案
3. 分析技術債務、可維護性、性能影響
4. 給出推薦方案及詳細理由"
**子代理輸入**: [原始任務目標] + [當前實現] + [具體問題點]
```

**適應性審查深度**：
- **LOW風險**: 重點關注邏輯正確性，簡化風格建議
- **MEDIUM風險**: 平衡功能審查與架構建議，適度使用子代理
- **HIGH風險**: 全面深度審查，強制啟用多個子代理並行分析

**優先級自動排序**：
1. **BLOCKER + CRITICAL**: 立即阻塞合併
2. **BLOCKER + HIGH**: 強烈建議修復
3. **SUGGESTION + HIGH**: 重要改進建議
4. **QUESTION**: 需要澄清的疑問
5. **SUGGESTION + MEDIUM/LOW**: 次要改進
6. **PRAISE**: 正面鼓勵

### 📊 期望輸出格式 - 結構化智能審查報告

**核心突破**：所有輸出都基於**結構化JSON反饋** + **適應性Markdown報告**。根據風險等級和意圖分類動態調整內容深度。

**通用輸出框架**：
```markdown
# 🤖 AI 技術夥伴審查報告 - PR #{PR_NUMBER}

## 📊 審查總覽 (Review Summary)
- **風險等級**: [LOW|MEDIUM|HIGH] 🟢🟡🔴
- **意圖分類**: [A.核心重構|B.功能開發|C.錯誤修復]
- **整體判定**: [APPROVE|REQUEST_CHANGES|NEEDS_DISCUSSION]
- **意圖對齊度**: X/10 (基於原始開發簡報比較)
- **架構合理性**: [UNDER_ENGINEERED|適度|OVER_ENGINEERED]

## 🧠 雙重上下文分析
### 原始意圖 vs 實際實現
[基於開發簡報與PR的比較分析]

## 📝 結構化審查意見
[基於JSON格式的分類反饋，以下為示例]
```

## 🟢 格式 A：LOW風險 - 輕量結構化審查

```json
{
  "review_summary": {
    "overall_verdict": "APPROVE",
    "risk_level": "LOW",
    "intent_alignment_score": "8-10",
    "complexity_appropriateness": "APPROPRIATE"
  },
  "structured_feedback": [
    {
      "feedback_type": "PRAISE",
      "title": "程式碼質量優異",
      "comment": "簡潔且符合原始目標",
      "impact_level": "LOW"
    },
    {
      "feedback_type": "SUGGESTION",
      "category": "STYLE",
      "title": "小幅風格優化",
      "effort_estimate": "TRIVIAL"
    }
  ]
}
```

### 🚀 快速通過建議
- **立即可合併**: 功能正常，風險極低
- **主要優點**: [基於審查結果的簡短描述]
- **次要建議**: [可選的改進點，但不阻塞合併]

---

## 🟡 格式 B：MEDIUM風險 - 詳細結構化反饋

```json
{
  "review_summary": {
    "overall_verdict": "REQUEST_CHANGES",
    "risk_level": "MEDIUM",
    "intent_alignment_score": "6-7",
    "complexity_appropriateness": "SLIGHTLY_OVER_ENGINEERED"
  },
  "structured_feedback": [
    {
      "file_path": "backend/novel/adapters/hjwzw.py",
      "line_number": 88,
      "feedback_type": "BLOCKER",
      "category": "LOGIC",
      "title": "異常處理不完整",
      "comment": "如果 requests.get 拋出 Timeout 異常，整個爬蟲進程會崩潰",
      "suggested_fix": "try...except 捕獲此特定異常",
      "impact_level": "HIGH",
      "effort_estimate": "EASY"
    },
    {
      "feedback_type": "SUGGESTION",
      "category": "ARCHITECTURE",
      "title": "正則表達式可讀性改進",
      "impact_level": "MEDIUM"
    },
    {
      "feedback_type": "PRAISE",
      "title": "基類設計優異",
      "comment": "get_content 和 get_chapters 接口清晰"
    }
  ]
}
```

### 🔧 修復行動計畫
1. **立即處理 BLOCKER**: [具體修復步驟]
2. **考慮 SUGGESTION**: [非強制但推薦的改進]
3. **保持 PRAISE**: [繼續維持優秀實踐]

---

## 🔴 格式 C：HIGH風險 - 深度子代理分析 + 核心重構策略

### 🤖 子代理協作深度分析

**自動啟用架構分析子代理** (僅限HIGH風險):
```markdown
#### [架構分析子代理報告]
**任務**: 深度架構合理性評估
**發現**: [具體架構問題和風險點]
**建議**: [替代實現方案和重構建議]
**影響評估**: [技術債務、可維護性、性能影響分析]
```

**核心重構策略** (僅限A類意圖):
- **放棄重建選項被禁用**: 戰略價值無限大
- **分階段降風險策略**: feature flag + 測試計畫 + 監控
- **安全合併路徑**: 48小時監控 + 回滾預案

**戰術價值策略** (B/C類意圖):
- **成本效益分析**: 修復成本 vs 30分鐘重建
- **繼續修復**: 低-中風險 + 可控成本
- **重建建議**: 高成本 + 高風險

```json
{
  "review_summary": {
    "overall_verdict": "NEEDS_DISCUSSION",
    "risk_level": "HIGH",
    "intent_alignment_score": "4-5",
    "complexity_appropriateness": "OVER_ENGINEERED",
    "subagent_analyses": {
      "architecture_analysis": {
        "activated": true,
        "findings": "[架構子代理的深度分析結果]"
      },
      "tech_debt_assessment": {
        "activated": true,
        "findings": "[技術債務評估結果]"
      }
    }
  },
  "structured_feedback": [
    {
      "feedback_type": "BLOCKER",
      "category": "ARCHITECTURE",
      "title": "架構過度設計",
      "comment": "[子代理深度分析的具體結果]",
      "suggested_fix": "[替代實現方案]",
      "impact_level": "CRITICAL",
      "effort_estimate": "COMPLEX"
    }
  ]
}
```

### 🚨 緊急處理建議
1. **立即暫停合併**: 存在架構性風險
2. **啟動子代理團隊**: 深度分析和重設計
3. **考慮重建**: 高成本但更安全的選項
4. **戰略性討論**: 必要時升級為跨部門決策

---

### 🎆 核心價值實現

**從「自動化 Linter」到「AI 技術夥伴」的轉型**:

1. **🧠 上下文感知**: 理解「為什麼寫」，不只是「寫了什麼」
2. **📊 多維度反饋**: 從二元判斷到建設性對話
3. **⚖️ 適應性智慧**: 根據風險和情境動態調整審查深度
4. **🤖 子代理協作**: 高風險情況下自動啟用專家級分析

**最終效果**: 一個真正理解您的代碼意圖、能提供建設性反饋、並根據實際風險調整行為的智能技術夥伴。

---

## 🚨 熔斷機制：防止 AI 修復循環 (Circuit Breaker for AI Repair Loops)

### trigger_circuit_breaker 函數實現

```bash
function trigger_circuit_breaker() {
    local pr_number=$1
    local review_count=$2

    echo "🚨 CIRCUIT BREAKER ACTIVATED - PR #$pr_number"
    echo "📊 審查循環檢測：已進行 $review_count 次審查"

    # 1. 為 PR 添加循環檢測標籤
    gh pr edit $pr_number --add-label "endless-loop-detected,needs-human-intervention"

    # 2. 分析循環模式
    local loop_analysis=$(analyze_review_loop_pattern $pr_number)

    # 3. 生成熔斷報告
    local circuit_breaker_report="## 🚨 CIRCUIT BREAKER - 修復循環檢測

**PR**: #$pr_number
**檢測時間**: $(date +'%Y-%m-%d %H:%M:%S')
**審查次數**: $review_count 次
**狀態**: ⛔ 自動修復已停止

### 🔄 檢測到的循環模式
$loop_analysis

### 🚨 熔斷原因
merge-planner 檢測到此 PR 可能陷入了「審查 → 修復 → 新問題 → 再審查」的無限循環。為了避免資源浪費和代碼品質下降，已自動觸發熔斷機制。

### 🤝 需要人類仲裁
**@MumuTW** 此 PR 需要人工仲裁，可能的解決方案：

1. **🔍 深度分析**: 檢查是否存在根本性架構問題
2. **🔄 重新開始**: 考慮從 main 分支重新創建該功能的 PR
3. **📋 手動修復**: 人工解決剩餘的複雜問題
4. **⚖️ 戰略調整**: 重新評估該功能的實現策略

### 🔧 重啟修復流程
如果問題已解決，可以通過以下方式重啟自動修復：
\`\`\`bash
# 移除循環標籤並重新觸發 merge-planner
gh pr edit $pr_number --remove-label endless-loop-detected
/merge-planner $pr_number
\`\`\`

---
🤖 自動生成 by merge-planner Circuit Breaker | $(date +'%Y-%m-%d %H:%M:%S')"

    # 4. 發布熔斷報告到 PR
    gh pr comment $pr_number --body "$circuit_breaker_report"

    # 5. 記錄到日誌
    echo "$(date): Circuit breaker triggered for PR #$pr_number after $review_count reviews" >> \
        "docs/merge-planner/circuit_breaker.log"

    echo "🛑 熔斷機制已啟動，已停止自動修復並通知人類仲裁者"
}

function analyze_review_loop_pattern() {
    local pr_number=$1

    # 獲取最近3次審查的 BLOCKER 列表，分析循環模式
    local recent_reviews=$(gh pr view $pr_number --comments --json body,createdAt | \
        jq -r '.[] | select(.body | contains("🤖 Merge-Planner 審查與合併計劃")) | "\(.createdAt)|\(.body)"' | \
        sort -r | head -3)

    # 提取每次審查的 BLOCKER 類型
    local pattern_analysis="#### 📋 歷史審查模式分析\n\n"
    local review_index=1

    while IFS='|' read -r timestamp review_body; do
        local blockers=$(echo "$review_body" | grep -E "^- \[ \] \*\*BLOCKER" | sed 's/^- \[ \] \*\*//' | sed 's/\*\*:.*//')

        pattern_analysis="${pattern_analysis}**第 $review_index 次審查** ($(echo $timestamp | cut -d'T' -f1)):\n"
        if [[ -n "$blockers" ]]; then
            echo "$blockers" | while read -r blocker; do
                pattern_analysis="${pattern_analysis}- $blocker\n"
            done
        else
            pattern_analysis="${pattern_analysis}- (無 BLOCKER)\n"
        fi
        pattern_analysis="${pattern_analysis}\n"

        ((review_index++))
    done <<< "$recent_reviews"

    # 檢測是否有重複的 BLOCKER 類型
    local unique_blockers=$(echo "$recent_reviews" | grep -E "BLOCKER.*CI 失敗\|BLOCKER.*測試失敗\|BLOCKER.*構建失敗" | sort | uniq -c | sort -nr)

    if [[ -n "$unique_blockers" ]]; then
        pattern_analysis="${pattern_analysis}#### 🔄 檢測到的重複模式\n"
        echo "$unique_blockers" | while read -r count pattern; do
            if [[ $count -gt 1 ]]; then
                pattern_analysis="${pattern_analysis}- **重複 $count 次**: $pattern\n"
            fi
        done
    fi

    echo -e "$pattern_analysis"
}
```

### 循環風險警告機制

當審查次數達到2次時，merge-planner 將在生成清單時添加額外的警告和限制：

```bash
# 在生成合併就緒清單時添加循環風險警告
if [[ "$LOOP_RISK_WARNING" == "true" ]]; then
    echo "
### ⚠️ 循環風險警告

**注意**: 這是第三次審查此 PR。為避免修復循環，請注意：

1. **🎯 專注核心問題**: 只修復最關鍵的 BLOCKER，避免過度修復
2. **🔒 最小化變更**: 每次修復只改動最少必要的代碼
3. **🧪 充分驗證**: 修復前進行更徹底的本地測試
4. **🤝 考慮人工介入**: 如果問題複雜，建議直接尋求人類工程師協助

**下次審查將觸發熔斷機制！**" >> 合併就緒清單
fi
```

---

## 🎯 終極目標：為 merge-executor 生成「合併就緒清單」

**核心突破**：merge-planner 不只是分析報告，更是 merge-executor 的**指揮官**，發出最清晰、最無歧義的執行指令。

### 📋 在 PR 頁面生成合併就緒清單

**執行指令**：完成所有分析後，merge-planner 必須在 PR 頁面發表以下格式的評論：

```bash
# 在 PR 頁面發表合併就緒清單
gh pr comment $ARGUMENTS --body "$(cat <<'EOF'
## 🤖 Merge-Planner 審查與合併計劃

**PR**: #$ARGUMENTS
**風險評級**: [🟢 LOW | 🟡 MEDIUM | 🔴 HIGH]
**意圖分類**: [A. 核心重構 | B. 功能開發 | C. 錯誤修復]
**結論**: **[APPROVE | REQUEST_CHANGES | NEEDS_DISCUSSION]**

---

### 📝 合併就緒檢查清單 (Merge-Ready Checklist)

**`merge-executor` 請注意：只有當以下所有 BLOCKER 項目都被解決後，才能執行合併操作。**

- [ ] **BLOCKER-1 (CI 失敗)**: \`Build Frontend Image\` 檢查必須通過
  - **診斷**: CI 日誌顯示在 \`RUN pnpm build\` 步驟中發生錯誤
  - **建議行動**: 請 \`Coder Agent\` 檢查 \`infra/docker/frontend-tier2.Dockerfile\` 並修復構建錯誤

- [ ] **SUGGESTION-1 (代碼風格)**: 採納 \`gemini-code-assist\` 的建議
  - **參考**: gemini-code-assist 建議將 \`eslint.config.js\` 中的文件路徑定義為常量
  - **建議行動**: (可選) 請 \`Coder Agent\` 進行此項重構以提升可讀性

---

### 🔄 重新審查觸發條件

**當以下事件發生時，merge-planner 將自動重新審查：**
1. 新的 commit 推送到此 PR
2. CI 檢查狀態發生變化
3. 手動觸發重新審查指令

**@MumuTW 請審閱此計劃。一旦修復完成，merge-planner 將重新評估合併就緒狀態。**

---
🔗 **merge-executor 集成接口**：
- **檢查方式**: \`gh pr view $ARGUMENTS --comments | grep "合併就緒檢查清單"\`
- **合併條件**: 所有 BLOCKER 項目都標記為 \`[x]\` 或不存在未完成的 BLOCKER
- **自動觸發**: 檢測到 PR 更新時重新運行 merge-planner
EOF
)"
```

### 🔧 merge-executor 簡化邏輯

有了這個清單，merge-executor 的邏輯變得極其簡單：

```bash
# merge-executor 的核心邏輯
function check_merge_readiness() {
    local pr_number=$1

    # 1. 獲取最新的 merge-planner 評論
    local latest_review=$(gh pr view $pr_number --comments | grep -A 50 "🤖 Merge-Planner 審查與合併計劃" | tail -1)

    # 2. 檢查是否存在未解決的 BLOCKER
    local pending_blockers=$(echo "$latest_review" | grep -c "- \[ \] \*\*BLOCKER")

    # 3. 檢查最終結論
    local verdict=$(echo "$latest_review" | grep "結論:" | grep -o "APPROVE\|REQUEST_CHANGES\|NEEDS_DISCUSSION")

    if [[ $pending_blockers -eq 0 && "$verdict" == "APPROVE" ]]; then
        echo "✅ 合併就緒：所有 BLOCKER 已解決"
        return 0
    else
        echo "⏸️ 等待修復：發現 $pending_blockers 個待解決的 BLOCKER"
        return 1
    fi
}
```

### 🎯 價值實現

**對人類的價值**：
- 一眼就能看明白 PR 狀態和需要做什麼
- 清晰的行動指令和優先級
- 團隊協作的透明度

**對 AI 的價值**：
- merge-executor 邏輯極大簡化
- 從「理解複雜報告」變為「檢查簡單清單」
- 明確的觸發條件和集成接口

**對整個流水線的價值**：
- 無歧義的指令傳遞
- 自動化決策的可追蹤性
- 人機協作的完美結合

#### 🔄 風險監控
**基於量化指標的持續監控**：
- **衝突檢測**：定期執行合併預演，監控直接衝突
- **核心檔案變更**：追蹤CI/CD、依賴、配置檔案的變更頻率
- **演進路徑分析**：監控與主幹的檔案重疊度變化
- **整體風險趨勢**：追蹤風險分數的變化軌跡

## 💾 HTML報告生成

**完成分析後，立即生成HTML格式報告：**

1. **HTML報告內容**：將上述完整分析結果轉換為專業HTML文檔
2. **文件命名**：`YYYY-MM-DD_PR{$ARGUMENTS}修復分析報告.html`
3. **保存路徑**：`./docs/pr-fix-plan/`
4. **技術要素**：
   - 響應式設計，支援桌面和移動設備
   - Mermaid圖表視覺化風險評估和執行流程
   - 互動式風險分數展示
   - 專業CSS樣式設計

```html
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovelWebsite PR{$ARGUMENTS} 修復分析報告 - {當前日期}</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        /* NovelWebsite專案專用樣式 */
        :root {
            --primary-color: #2563eb;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --bg-color: #f8fafc;
        }
        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-color);
            line-height: 1.6;
        }
        .risk-score {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 8px 0;
        }
        .risk-low { background: #dcfce7; color: var(--success-color); }
        .risk-medium { background: #fef3c7; color: var(--warning-color); }
        .risk-high { background: #fee2e2; color: var(--danger-color); }
        .risk-extreme { background: #fecaca; color: var(--danger-color); border: 2px solid var(--danger-color); }

        .intent-core { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; }
        .intent-feature { background: linear-gradient(135deg, #059669, #047857); color: white; }
        .intent-bug { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; }

        .mermaid { margin: 20px 0; }
        .checklist { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <!-- 動態插入分析結果內容 -->
    <!-- Mermaid風險評估圖表 -->
    <div class="mermaid">
        graph TB
            A[PR分析] --> B{意圖分類}
            B -->|核心重構| C[戰略價值無限大]
            B -->|功能開發| D[戰術價值評估]
            B -->|錯誤修復| E[戰術價值評估]

            C --> F[分階段降風險策略]
            D --> G{成本效益分析}
            E --> G

            G -->|高成本高風險| H[建議重建]
            G -->|可控成本風險| I[繼續修復]
    </div>

    <!-- 風險分數視覺化 -->
    <div class="mermaid">
        pie title 集成風險分析
            "直接衝突" : {衝突分數}
            "核心檔案" : {核心分數}
            "路徑重疊" : {重疊分數}
    </div>

    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
```

### 使用範例
您只需要在您的終端機中，導航到 NovelWebsite 專案目錄，然後輸入以下指令：
```bash
/project:pr-fix-plan 104
```
注意：此指令將會讀取專案的 `.claude/commands/pr-fix-plan.md` 檔案，並將 104 作為 PR 號碼傳遞給 Claude Code 進行處理，最後生成完整的HTML報告。

### NovelWebsite專案戰略優勢

#### 🧠 **核心突破：「意圖 vs 成本」智慧**
專為NovelWebsite專案定制：
- **框架升級**：Django/React/Next.js 升級自動識別為核心重構
- **搜索功能**：5分鐘用戶旅程相關功能優先處理
- **CI重構**：Tier 2架構調整視為戰略必要性

#### 🎯 **專案定制化風險評估**
- **後端風險**：Django配置、Catalog API、爬蟲引擎變更
- **前端風險**：React組件、搜索功能、響應式設計變更
- **基礎設施風險**：Docker、CI/CD、AWS部署配置變更

#### 🕰️ 最後更新日誌

**版本**: v3.0 - AI 技術夥伴 + merge-executor 指揮官
**更新日期**: 2025-06-26
**重大升級**:
- 🧠 **三重上下文感知**: 實現 + 意圖 + 已有對話整合
- 🚨 **CI 絕對優先級**: 失敗的 CI 檢查強制成為最高優先 BLOCKER
- 📋 **合併就緒清單**: 在 PR 頁面生成可執行的合併指令
- 🔗 **merge-executor 集成**: 提供簡化的清單檢查邏輯
- 🤖 **團隊智慧整合**: 自動引用和整合 gemini-code-assist 等其他 AI 建議

**革命性突破**:
1. **消除失憶式審查**: 完整理解項目歷史和團隊對話
2. **CI-First 原則**: 確保 CI 問題永遠是第一優先級
3. **指揮官角色**: 從分析師升級為 merge-executor 的明確指揮官

---

---

## 📋 實戰示例：處理 PR #125 (Next.js Migration)

**模擬 merge-planner 處理 PR #125 的完整流程**：

### 輸入命令
```bash
/merge-planner 125
```

### 三重上下文獲取結果
1. **實現**: 12 個文件變更，包含 package.json, Dockerfile, CI 工作流
2. **意圖**: 追溯到開發簡報 - Next.js 框架遷移（核心重構）
3. **對話**: 發現 gemini-code-assist 的 eslint.config.js 可維護性建議

### 生成的合併就緒清單
```markdown
## 🤖 Merge-Planner 審查與合併計劃

**PR**: #125
**風險評級**: 🔴 HIGH
**意圖分類**: A. 核心重構 (Next.js Migration)
**結論**: **REQUEST_CHANGES** - CI 失敗阻塞合併

---

### 📝 合併就緒檢查清單 (Merge-Ready Checklist)

**`merge-executor` 請注意：只有當以下所有 BLOCKER 項目都被解決後，才能執行合併操作。**

- [ ] **BLOCKER-1 (CI 失敗)**: `Build Frontend Image` 檢查必須通過
  - **診斷**: CI 日誌顯示在 `RUN pnpm build` 步驟中發生錯誤，可能是 workspace 配置問題
  - **建議行動**: 請 `Coder Agent` 檢查 `infra/docker/frontend-tier2.Dockerfile` 中的 PNPM workspace 設置

- [ ] **SUGGESTION-1 (代碼風格)**: 採納 `gemini-code-assist` 的建議
  - **參考**: gemini-code-assist 建議將 `eslint.config.js` 中的文件路徑定義為常量以提高可維護性
  - **建議行動**: (可選) 重構 eslint 配置提升可讀性

### 🚀 核心重構特殊處理
由於此 PR 屬於「A. 核心重構」類別，啟用分階段降風險策略：
- ✅ 已確認為戰略價值無限大，禁用「放棄重建」選項
- 🔍 建議分階段合併：基礎配置 → 構建流程 → 應用遷移
- 📊 需要額外的手動測試驗證 Next.js 功能正常性

**@MumuTW 請審閱此計劃。CI 修復完成後，將重新評估合併就緒狀態。**
```

### merge-executor 檢查邏輯
```bash
# merge-executor 運行時會執行：
check_merge_readiness 125
# 返回：⏸️ 等待修復：發現 1 個待解決的 BLOCKER (CI 失敗)
```

**完美的無縫協作**：merge-planner 分析 → 生成清單 → merge-executor 執行 → 自動化決策

---

**這個版本完全針對NovelWebsite專案的技術棧和業務邏輯進行了定制化，同時實現了從「自動化 Linter」到「AI 技術夥伴 + 指揮官」的革命性升級！**
