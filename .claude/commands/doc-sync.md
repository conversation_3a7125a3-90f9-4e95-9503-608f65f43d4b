---
allowed-tools: Ba<PERSON>(git: *), <PERSON><PERSON>(gh: *), Read, Edit, Write, Glob, Gre<PERSON>, Task # 確保能訪問代碼庫、GitHub和文件操作
description: 智能文檔同步系統 - 基於三層式更新策略，分析代碼庫和PR狀態，自動更新項目文檔
---

## 🔄 智能文檔同步系統 (Intelligent Documentation Sync)

### 任務說明
作為一個智能文檔同步代理，你需要分析整個代碼庫狀態、最近的PR變更和commit歷史，然後根據**三層式更新策略**來更新項目文檔，確保文檔與代碼庫的實際狀態保持同步。

#### 🎯 三層式更新策略 (Three-Tiered Update Strategy)

### **第一層：全自動化更新 (Tier 1: Fully Automated)**
**信賴度：高 | 操作模式：直接更新**

這一層包含固定格式、可由工具確定性生成的文件。AI可以大膽操作，出錯機率極低。

**目標文件：**
- `docs/02_ARCHITECTURE/project-structure.md` - 項目目錄結構
- `docs/02_ARCHITECTURE/project-structure-annotated.md` - 帶註解的項目結構
- `docs/03_DEVELOPMENT_GUIDES/development-timeline.md` - 開發時間線和PR歷史

**更新邏輯：**
```bash
# 生成最新目錄結構
tree -I 'node_modules|venv|__pycache__|.git|build|dist' > temp-structure.txt

# 獲取PR和commit歷史
gh pr list --state all --limit 50 --json number,title,createdAt,state,mergedAt

# 自動更新，無需確認
```

### **第二層：AI驅動的草稿更新 (Tier 2: AI-Driven Drafts)**
**信賴度：中 | 操作模式：生成草稿 + 用戶確認**

包含需要人類判斷但AI可以提供高質量草稿的重要文件。

**目標文件：**
- `README.md` - 項目門面，必須反映最新狀態
- `TECH_DEBT.md` - 技術債務追蹤

**README.md 更新策略：**
- 分析新功能和架構變更，更新功能描述
- 檢查依賴和安裝步驟是否過時
- 確保新開發者能通過README理解項目最新狀態
- **重點關注**：對開發者可見的變化

**TECH_DEBT.md 更新策略：**
- 對比最近變更，識別已償還的技術債
- 標記已完成項目為 `✅ RESOLVED`
- 發現新的技術債務模式，提出討論
- 分析代碼複雜度和維護性問題

### **第三層：須人類審核的建議 (Tier 3: Human-Reviewed Suggestions)**
**信賴度：低 | 操作模式：分析 + 建議 + 請示**

涉及核心產品策略的文件，AI僅提供建議而非主動修改。

**目標文件：**
- `docs/01_PRODUCT_DESIGN/PRD.md` - 產品需求文檔
- `docs/01_PRODUCT_DESIGN/MVP_NOVEL_LIST.md` - MVP小說清單
- 其他戰略性文檔

**分析策略：**
- 檢查PRD是否與當前開發方向一致
- 識別功能實現與需求文檔的偏差
- 提出更新建議，等待用戶確認

---

## 📋 執行流程 (Execution Workflow)

### 階段 1：環境準備和基準線建立

**指令：**

1. **確保基於遠端 main 穩定版本**
   ```bash
   # 獲取遠端最新狀態
   git fetch origin

   # 檢查當前分支狀態
   git branch --show-current
   git status --porcelain

   # 如果不在 main 分支，切換到 main 並同步到最新
   if [ "$(git branch --show-current)" != "main" ]; then
     echo "⚠️ 當前不在 main 分支，切換到 main"
     git stash push -m "doc-sync: 暫存當前工作" 2>/dev/null || true
     git checkout main
   fi

   # 同步到遠端 main 最新版本
   git pull origin main
   echo "✅ 已同步到遠端 main 穩定版本: $(git rev-parse --short HEAD)"
   ```

2. **基準線狀態分析**
   ```bash
   # 獲取 main 分支最近的穩定commit
   git log --oneline -10

   # 確認工作目錄清潔
   if [ -n "$(git status --porcelain)" ]; then
     echo "⚠️ 工作目錄有未提交變更，文檔同步將基於遠端穩定版本"
   fi
   ```

3. **基於穩定版本的 PR 狀態分析**
   ```bash
   # 獲取最近合併到 main 的PR狀態（穩定版本基準）
   gh pr list --state merged --base main --limit 20 --json number,title,state,createdAt,mergedAt,body

   # 獲取最近一週合併到 main 的穩定PR
   gh pr list --state merged --base main --limit 15 --json number,title,mergedAt,body,author

   # 確保分析的是已經穩定的、合併到 main 的變更
   echo "📊 分析基準：遠端 main 分支穩定版本"
   ```

4. **基於穩定版本的項目結構掃描**
   - 使用 `Glob` 工具掃描當前 main 分支的完整目錄結構
   - 識別相對於上次文檔更新的結構變化
   - 分析穩定版本中的 `package.json`, `requirements.txt` 等依賴狀態
   - **重要**：僅基於已合併到 main 的穩定變更進行分析

### 階段 2：第一層 - 全自動化更新

**指令：**

#### 更新 `docs/02_ARCHITECTURE/project-structure.md`
```bash
# 生成完整目錄結構
tree -I 'node_modules|venv|__pycache__|.git|build|dist|coverage|.pytest_cache' -a
```

#### 更新 `docs/02_ARCHITECTURE/project-structure-annotated.md`
- 使用 `Read` 工具讀取現有註解
- 保留人工註解，更新結構部分
- 自動識別新目錄的用途

#### 更新 `docs/03_DEVELOPMENT_GUIDES/development-timeline.md`
```bash
# 獲取基於 main 分支的穩定PR歷史
gh pr list --state merged --base main --limit 50 --json number,title,createdAt,mergedAt,state,author,body | jq

# 確保時間線反映的是穩定的開發歷程
echo "📈 更新開發時間線：基於 main 分支穩定合併記錄"
```

**執行模式：** 直接更新文件，完成後報告變更摘要

### 階段 3：第二層 - AI驅動的草稿更新

**指令：**

#### README.md 草稿生成
1. **功能變更分析**：
   - 掃描最近合併的PR，識別新功能
   - 檢查 `frontend/src/`, `backend/apps/` 的變化
   - 分析API端點變更

2. **技術棧更新檢查**：
   - 比較 `package.json`, `requirements.txt` 的版本變化
   - 檢查Docker配置和CI流程變更
   - 驗證安裝步驟的有效性

3. **草稿生成策略**：
   - 保留現有結構，更新內容
   - 突出新功能和重要變更
   - 確保新開發者入門流暢

#### TECH_DEBT.md 草稿生成
1. **基於穩定版本的債務償還檢測**：
   ```bash
   # 搜索 main 分支中的相關commit信息
   git log origin/main --grep="refactor" --grep="clean" --grep="debt" --oneline -20

   # 確保分析的是穩定版本的技術債務狀況
   echo "🔍 技術債務分析基準：遠端 main 穩定版本"
   ```

2. **基於穩定代碼庫的新債務識別**：
   - 使用 `Grep` 在 main 分支代碼中搜索 `TODO`, `FIXME`, `HACK` 標記
   - 分析穩定版本的代碼複雜度和重複模式
   - 基於當前穩定狀態檢查測試覆蓋率缺口

3. **更新建議生成**：
   - 標記已解決項目
   - 提出新發現的技術債務（需討論）
   - 重新評估優先級

**執行模式：** 生成草稿 → 展示給用戶 → 等待確認 → 應用更新 → 推送到遠端

### 階段 4：第三層 - 人類審核建議

**指令：**

#### docs/01_PRODUCT_DESIGN/PRD.md 分析
1. **基於穩定版本的需求實現對比**：
   - 讀取現有PRD內容
   - 對比 main 分支穩定版本中已實現功能與PRD描述
   - 識別穩定版本與PRD之間的功能偏差或新增需求

2. **基於穩定狀態的產品策略分析**：
   - 檢查MVP目標在穩定版本中的實際完成狀態
   - 分析已穩定部署的用戶體驗改進實現情況
   - 評估當前穩定技術選擇與產品目標的匹配度

3. **更新建議**：
   - 提出具體的文檔更新建議
   - 解釋每個建議的理由
   - 等待用戶決策

**執行模式：** 分析 → 生成建議報告 → 請示用戶 → 根據指示操作 → 推送到遠端

### 階段 5：文檔更新推送

**指令：**

用戶審核完文檔更新後，自動執行以下推送流程：

1. **暫存文檔更新**：
   ```bash
   git add docs/ README.md TECH_DEBT.md
   ```

2. **生成結構化提交信息**：
   ```bash
   git commit -m "docs: 基於遠端main穩定版本同步文檔更新

   🔄 基於穩定版本 (commit: $(git rev-parse --short HEAD))
   - 全部基於遠端 main 分支穩定狀態進行文檔同步
   - 反映最新安全修復狀態和CI/CD穩定性

   📊 更新文檔清單:
   [自動生成更新文檔列表]

   ✅ 關鍵狀態更新:
   [自動生成關鍵變更摘要]

   🤖 Generated with [Claude Code](https://claude.ai/code)

   Co-Authored-By: Claude <<EMAIL>>"
   ```

3. **推送到遠端**：
   ```bash
   git push origin main
   ```

4. **確認推送結果**：
   - 檢查推送是否成功
   - 記錄新的commit hash
   - 報告推送狀態

**執行模式：** 用戶確認 → 自動暫存 → 結構化提交 → 推送遠端 → 狀態報告

---

## 📊 輸出格式

### 🎯 執行摘要
```
📋 基於遠端 main 穩定版本的文檔同步完成報告
🔗 基準版本: origin/main (commit: abc1234)

🟢 第一層 (全自動化更新)：
✅ docs/02_ARCHITECTURE/project-structure.md - 基於穩定版本更新了15個新目錄
✅ docs/03_DEVELOPMENT_GUIDES/development-timeline.md - 添加了3個已合併到main的穩定PR記錄
✅ docs/02_ARCHITECTURE/project-structure-annotated.md - 同步穩定版本的目錄結構

🟡 第二層 (草稿更新)：
📝 README.md - 基於穩定功能生成草稿，等待確認
📝 TECH_DEBT.md - 發現2個已解決項目，1個新技術債務

🔵 第三層 (人類審核)：
💭 docs/01_PRODUCT_DESIGN/PRD.md - 基於穩定版本發現功能實現與需求偏差，已生成更新建議

🚀 第四層 (自動推送)：
✅ 文檔推送到遠端 main 分支 (commit: abc1234)
✅ 推送狀態：成功，無衝突
```

### 🔍 詳細分析報告

#### README.md 草稿變更（基於穩定版本）
```markdown
## 主要更新內容：
1. **穩定新功能**：添加了搜索功能描述 (基於已合併到main的PR #123)
2. **穩定依賴**：更新了Node.js版本要求至18+ (基於main分支package.json)
3. **穩定API**：新增了認證端點說明 (已在main分支部署)

## 建議確認：
- 是否採用基於穩定版本的功能描述？
- 穩定版本的安裝步驟是否需要進一步調整？

📌 更新基準：遠端 main 分支穩定版本
```

#### TECH_DEBT.md 更新建議（基於穩定版本）
```markdown
## 基於 main 穩定版本已解決技術債務：
✅ Docker多階段構建優化 (PR #104已合併到main)
✅ CI性能優化 (Tier 2架構已穩定部署)

## 基於穩定代碼庫新發現技術債務：
🔍 frontend/src/components/ 中發現重複組件邏輯（main分支現狀）
📝 建議：重構為共用Hook模式

## 討論項目：
❓ 是否將此項添加到技術債務清單？

📌 分析基準：遠端 main 分支穩定版本代碼
```

#### docs/01_PRODUCT_DESIGN/PRD.md 更新建議（基於穩定版本）
```markdown
## 基於 main 穩定版本分析結果：
🎯 MVP目標：28本小說已完成爬取集成（main分支穩定實現）
📊 用戶功能：搜索、收藏、閱讀歷史已穩定實現
⚠️  偏差發現：PRD中提到的"評論系統"在穩定版本中尚未開發

## 基於穩定狀態的更新建議：
1. 更新MVP完成狀態為85%（基於main分支實際功能）
2. 調整功能優先級，將評論系統移至Phase 2
3. 添加已在main分支穩定實現的搜索優化功能描述

## 請示：是否按基於穩定版本的建議更新PRD？

📌 分析基準：遠端 main 分支穩定部署狀態
```

---

## ⚡ 快速執行模式

### 基於穩定版本的增量同步 (Stable Delta Sync)
```bash
/user:doc-sync --mode=delta --since=7days
```
僅分析最近7天合併到main的穩定變更，快速更新

### 基於穩定版本的完整同步 (Stable Full Sync)
```bash
/user:doc-sync --mode=full
```
完整分析遠端main分支穩定代碼庫，深度更新所有文檔

### 特定文檔穩定版本同步 (Targeted Stable Sync)
```bash
/user:doc-sync --files=README.md,TECH_DEBT.md
```
僅基於main分支穩定狀態同步指定文檔

### 強制同步到穩定版本 (Force Stable Sync)
```bash
/user:doc-sync --force-stable
```
確保切換到main分支並同步到最新穩定版本後執行文檔更新

---

## 🧠 智能特色

### 🎯 **基於穩定版本的上下文感知更新**
- 理解項目性質（NovelWebsite = 小說網站）
- 識別main分支穩定版本中的關鍵業務邏輯變更（爬蟲、搜索、用戶系統）
- 基於穩定部署狀態保持文檔的業務相關性

### 📈 **穩定版本變更影響分析**
- 評估每個已合併到main的穩定PR對不同文檔的影響程度
- 優先更新已穩定部署的用戶可見變化
- 識別main分支中跨模塊的穩定架構調整

### 🛡️ **穩定性導向的安全保障**
- 基準保證：始終基於遠端main穩定版本
- 第一層：可逆操作，Git可追蹤，穩定版本可信
- 第二層：草稿模式，基於穩定功能，用戶確認
- 第三層：僅建議，基於穩定狀態分析，不主動修改

### 📊 **穩定版本質量監控**
- 確保文檔反映main分支穩定狀態的可讀性和準確性
- 維持既有的寫作風格和格式，避免不穩定功能描述
- 基於穩定版本防止信息過時或矛盾

---

## 使用範例

```bash
# 基於遠端main穩定版本的標準完整同步（包含自動推送）
/user:doc-sync

# 基於穩定版本的快速增量同步（僅最近穩定變更）
/user:doc-sync --mode=delta

# 強制同步到main穩定版本後更新文檔
/user:doc-sync --force-stable

# 針對已合併到main的特定PR進行文檔更新
/user:doc-sync --pr=123

# 僅基於穩定版本更新README和技術債務（包含推送）
/user:doc-sync --files=README.md,TECH_DEBT.md

# 僅生成草稿，不自動推送（用於測試）
/user:doc-sync --draft-only
```

**這個系統將成為您的智能文檔管理助手，確保項目文檔始終與遠端main分支的穩定代碼現實保持同步，並自動推送更新！**

### 🔄 **自動推送工作流**

1. **文檔同步** → 生成基於穩定版本的更新
2. **用戶審核** → 確認草稿內容和建議
3. **自動提交** → 生成結構化commit信息
4. **推送遠端** → 更新遠端main分支
5. **狀態報告** → 確認推送結果和新commit hash

### 🌟 **穩定版本基準的核心優勢**

✅ **可靠性保證**：所有文檔更新基於已驗證的穩定代碼
✅ **一致性確保**：避免基於開發中功能的不準確描述
✅ **部署同步**：文檔狀態與實際部署狀態保持一致
✅ **團隊協作**：所有成員基於相同的穩定基準線工作
