#!/bin/bash
# create-issue.md - 智能 Issue 創建助手
# 用途：建立 GitHub Issue 並與用戶確認優先級和具體內容
# 使用方式：/create-issue.md [標題] [--priority=P0|P1|P2|P3|S級] [--description="詳細描述"]

# 參數初始化
ISSUE_TITLE=""
ISSUE_PRIORITY=""
ISSUE_DESCRIPTION=""
ISSUE_CONTEXT=""
AUTO_CONFIRM=false
VERBOSE=false

# 顯示使用說明
function show_usage() {
    cat <<EOF
📋 智能 Issue 創建助手

使用方式:
  /create-issue.md "Issue 標題" [選項]

選項:
  --priority=P0|P1|P2|P3|S級    設定優先級 (必填)
  --description="描述"          詳細描述 (可選)
  --context="上下文"            額外上下文資訊 (可選)
  --auto-confirm               跳過確認，直接創建 (可選)
  --verbose                    顯示詳細執行過程 (可選)

優先級說明:
  P0 : 阻塞性問題，需要立即處理
  P1 : 核心功能，高優先級
  P2 : 一般改進，中等優先級
  P3 : 可延遲的功能
  S級: 戰略性任務，複雜度高

範例:
  /create-issue.md "修復後端安全漏洞" --priority=P1 --description="修復 requests, sentry-sdk, djangorestframework 安全問題"
  /create-issue.md "實作用戶註冊功能" --priority=P2 --context="支援 email 驗證和社交登入"
  /create-issue.md "重構爬蟲引擎" --priority=S級 --auto-confirm

現有標籤系統:
  優先級: P0:阻塞, P1:核心, P2:一般, P3:可延遲, S級:戰略
  MVP分類: MVP:黃金28內容, MVP:用戶體驗, MVP:技術基礎, MVP:架構調整
  工作量: effort:small, effort:medium, effort:large
  特殊: Navigator, navigator-tracked, security, enhancement
EOF
}

# 解析命令行參數
function parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_usage
                exit 0
                ;;
            --priority=*)
                ISSUE_PRIORITY="${1#*=}"
                ;;
            --description=*)
                ISSUE_DESCRIPTION="${1#*=}"
                ;;
            --context=*)
                ISSUE_CONTEXT="${1#*=}"
                ;;
            --auto-confirm)
                AUTO_CONFIRM=true
                ;;
            --verbose)
                VERBOSE=true
                ;;
            -*)
                echo "❌ 未知選項: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$ISSUE_TITLE" ]]; then
                    ISSUE_TITLE="$1"
                else
                    echo "❌ 錯誤：只能指定一個標題"
                    show_usage
                    exit 1
                fi
                ;;
        esac
        shift
    done
}

# 驗證優先級
function validate_priority() {
    case "$ISSUE_PRIORITY" in
        "P0"|"P1"|"P2"|"P3"|"S級")
            return 0
            ;;
        *)
            echo "❌ 無效的優先級: '$ISSUE_PRIORITY'"
            echo "有效值: P0, P1, P2, P3, S級"
            return 1
            ;;
    esac
}

# 獲取優先級名稱
function get_priority_name() {
    case "$1" in
        "P0") echo "阻塞" ;;
        "P1") echo "核心" ;;
        "P2") echo "一般" ;;
        "P3") echo "可延遲" ;;
        "S級") echo "戰略" ;;
        *) echo "未知" ;;
    esac
}

# AI 智能分析 - 推斷 MVP 類別
function infer_mvp_category() {
    local title="$1"
    local description="$2"
    local context="$3"
    local full_text="$title $description $context"

    if [[ "$full_text" =~ (爬蟲|crawler|spider|網站|hjwzw|ttkan|黃金28|小說內容) ]]; then
        echo "MVP:黃金28內容"
    elif [[ "$full_text" =~ (UI|前端|frontend|介面|設計|用戶體驗|UX|註冊|登入|書架) ]]; then
        echo "MVP:用戶體驗"
    elif [[ "$full_text" =~ (後端|backend|API|database|資料庫|安全|security|漏洞) ]]; then
        echo "MVP:技術基礎"
    elif [[ "$full_text" =~ (CI|CD|docker|deploy|部署|測試|test|infrastructure|基礎設施) ]]; then
        echo "MVP:技術基礎"
    elif [[ "$full_text" =~ (架構|architecture|重構|refactor|engine|引擎|系統) ]]; then
        echo "MVP:架構調整"
    else
        echo "MVP:技術基礎"  # 預設值
    fi
}

# AI 工作量評估
function estimate_effort() {
    local priority="$1"
    local title="$2"
    local description="$3"
    local full_text="$title $description"

    # 基於優先級的基礎評估
    case "$priority" in
        "P0"|"S級")
            if [[ "$full_text" =~ (修復|fix|bug|漏洞|安全) ]]; then
                echo "medium"
            else
                echo "large"
            fi
            ;;
        "P1")
            if [[ "$full_text" =~ (小修改|調整|配置|設定) ]]; then
                echo "small"
            elif [[ "$full_text" =~ (重構|新功能|系統|架構) ]]; then
                echo "large"
            else
                echo "medium"
            fi
            ;;
        "P2"|"P3")
            if [[ "$full_text" =~ (重構|新系統|完整功能) ]]; then
                echo "large"
            elif [[ "$full_text" =~ (小功能|優化|改進) ]]; then
                echo "small"
            else
                echo "medium"
            fi
            ;;
        *)
            echo "medium"
            ;;
    esac
}

# 獲取工作量描述
function get_effort_description() {
    case "$1" in
        "small") echo "(1-4小時，簡單修改)" ;;
        "medium") echo "(4-16小時，中等複雜度)" ;;
        "large") echo "(16+小時，複雜任務)" ;;
        *) echo "(未知工作量)" ;;
    esac
}

# 顯示 Issue 預覽
function show_issue_preview() {
    local mvp_category="$1"
    local effort="$2"
    local priority_name=$(get_priority_name "$ISSUE_PRIORITY")
    local effort_desc=$(get_effort_description "$effort")

    echo ""
    echo "📋 Issue 預覽"
    echo "════════════════════════════════════════"
    echo "🏷️  標題: [$ISSUE_PRIORITY] $ISSUE_TITLE"
    echo "📊 優先級: $ISSUE_PRIORITY ($priority_name)"
    echo "🎯 分類: $mvp_category (AI 自動推斷)"
    echo "⚙️  工作量: effort:$effort $effort_desc"
    echo ""

    if [[ -n "$ISSUE_DESCRIPTION" ]]; then
        echo "📝 描述: $ISSUE_DESCRIPTION"
        echo ""
    fi

    if [[ -n "$ISSUE_CONTEXT" ]]; then
        echo "🔍 上下文: $ISSUE_CONTEXT"
        echo ""
    fi

    echo "🏷️  標籤: ${ISSUE_PRIORITY}:${priority_name}, ${mvp_category}, effort:${effort}, Navigator"
    echo "════════════════════════════════════════"
}

# 用戶確認
function confirm_with_user() {
    if [[ "$AUTO_CONFIRM" == "true" ]]; then
        echo "✅ 自動確認模式，跳過用戶確認"
        return 0
    fi

    echo ""
    echo "請確認以上資訊是否正確："
    echo "  ✅ 確認並創建 Issue (按 Enter 或輸入 'y')"
    echo "  📝 修改描述 (輸入 'd')"
    echo "  🏷️  修改優先級 (輸入 'p')"
    echo "  ❌ 取消 (輸入 'n' 或 'q')"
    echo ""
    read -p "請選擇 [Y/d/p/n]: " choice

    choice=$(echo "$choice" | tr '[:upper:]' '[:lower:]')
    case "$choice" in
        ""|"y"|"yes")
            return 0
            ;;
        "d"|"desc")
            echo "請輸入新的描述:"
            read -p "> " ISSUE_DESCRIPTION
            return 2  # 需要重新顯示預覽
            ;;
        "p"|"priority")
            echo "請輸入新的優先級 (P0/P1/P2/P3/S級):"
            read -p "> " ISSUE_PRIORITY
            if ! validate_priority; then
                echo "優先級無效，保持原值"
            fi
            return 2  # 需要重新顯示預覽
            ;;
        "n"|"no"|"q"|"quit")
            echo "❌ 已取消創建 Issue"
            exit 0
            ;;
        *)
            echo "無效選擇，請重新輸入"
            return 1  # 重新詢問
            ;;
    esac
}

# 生成 Issue 內容
function generate_issue_body() {
    local mvp_category="$1"
    local effort="$2"
    local priority_name=$(get_priority_name "$ISSUE_PRIORITY")
    local effort_desc=$(get_effort_description "$effort")

    cat <<EOF
## 🎯 Issue 描述

${ISSUE_DESCRIPTION:-"（請補充詳細描述）"}

$(if [[ -n "$ISSUE_CONTEXT" ]]; then
    echo "### 📋 上下文資訊"
    echo "$ISSUE_CONTEXT"
    echo ""
fi)

## 🏷️ 分類與優先級

- **優先級**: \`$ISSUE_PRIORITY\` ($priority_name)
- **MVP 類別**: \`$mvp_category\` (AI 自動推斷)
- **工作量評估**: \`effort:$effort\` $effort_desc
- **創建方式**: 使用者透過 create-issue 指令創建

## 🚀 建議執行流程

1. **📊 任務分析**: 運行 \`/task-dispatcher.md <issue_number>\` 進行詳細任務分解
2. **🎯 開發執行**: 根據 Task-Dispatcher 的工作包進行開發
3. **✅ 品質驗證**: 執行 \`make ci-check\` 確保程式碼品質
4. **🔄 進度回報**: 更新 Issue 狀態和完成情況

## 📊 Success Metrics (驗收標準)

- [ ] 需求明確定義和範圍確認
- [ ] 技術方案設計和評審完成
- [ ] 程式碼實作完成且符合規範
- [ ] 測試覆蓋率達標 (單元測試 + 整合測試)
- [ ] CI/CD 流程通過，所有檢查通過
- [ ] 文檔更新完成 (如適用)
- [ ] 使用者驗收測試通過

## 💡 相關資源

- 📖 參考 [CLAUDE.md](./CLAUDE.md) 了解開發原則
- 🏗️ 查看 [專案架構文檔](./docs/02_ARCHITECTURE/) 了解系統架構
- 🔧 使用 [Navigator](./docs/04_AI_OPERATIONS/navigator-mvp/) 進行進度追蹤

---
🤖 Generated by create-issue command
📅 Created: $(date +'%Y-%m-%d %H:%M:%S')
🔗 Priority: $ISSUE_PRIORITY | Category: $mvp_category | Effort: $effort
EOF
}

# 創建 GitHub Issue
function create_github_issue() {
    local mvp_category="$1"
    local effort="$2"
    local priority_name=$(get_priority_name "$ISSUE_PRIORITY")
    local labels="${ISSUE_PRIORITY}:${priority_name},${mvp_category},effort:${effort},Navigator"

    # 添加特殊標籤
    if [[ "$ISSUE_TITLE $ISSUE_DESCRIPTION" =~ (安全|security|漏洞|vulnerability) ]]; then
        labels="${labels},security"
    fi

    if [[ "$ISSUE_TITLE $ISSUE_DESCRIPTION" =~ (enhancement|改進|優化|新功能) ]]; then
        labels="${labels},enhancement"
    fi

    local issue_body=$(generate_issue_body "$mvp_category" "$effort")

    if [[ "$VERBOSE" == "true" ]]; then
        echo "🔧 創建 Issue 參數:"
        echo "  標題: [$ISSUE_PRIORITY] $ISSUE_TITLE"
        echo "  標籤: $labels"
        echo "  內容長度: $(echo "$issue_body" | wc -c) 字符"
    fi

    local issue_url=$(gh issue create \
        --title "[$ISSUE_PRIORITY] $ISSUE_TITLE" \
        --body "$issue_body" \
        --label "$labels" \
        --assignee "@me" 2>&1)

    if [[ $? -eq 0 ]] && [[ "$issue_url" =~ ^https:// ]]; then
        echo "✅ Issue 創建成功!"
        echo "🔗 URL: $issue_url"

        # 提取 Issue 編號
        local issue_number=$(echo "$issue_url" | grep -o '[0-9]*$')
        echo "📋 Issue #$issue_number 已創建"

        echo ""
        echo "💡 後續建議:"
        echo "   📊 運行任務分析: /task-dispatcher.md $issue_number"
        echo "   🎯 檢視 Navigator 報告: /navigator-mvp.md"
        echo "   📋 查看 Issue: $issue_url"

        return 0
    else
        echo "❌ Issue 創建失敗"
        echo "錯誤訊息: $issue_url"
        return 1
    fi
}

# 主要執行流程
function main() {
    echo "📋 智能 Issue 創建助手啟動..."

    # 解析參數
    parse_arguments "$@"

    # 檢查必要參數
    if [[ -z "$ISSUE_TITLE" ]]; then
        echo "❌ 錯誤：請提供 Issue 標題"
        show_usage
        exit 1
    fi

    if [[ -z "$ISSUE_PRIORITY" ]]; then
        echo "❌ 錯誤：請提供優先級 --priority=P0|P1|P2|P3|S級"
        show_usage
        exit 1
    fi

    # 驗證優先級
    if ! validate_priority; then
        exit 1
    fi

    # AI 智能分析
    echo "🤖 AI 分析中..."
    local mvp_category=$(infer_mvp_category "$ISSUE_TITLE" "$ISSUE_DESCRIPTION" "$ISSUE_CONTEXT")
    local effort=$(estimate_effort "$ISSUE_PRIORITY" "$ISSUE_TITLE" "$ISSUE_DESCRIPTION")

    # 顯示預覽並確認
    local confirmed=false
    while [[ "$confirmed" == "false" ]]; do
        show_issue_preview "$mvp_category" "$effort"

        case $(confirm_with_user; echo $?) in
            0)
                confirmed=true
                ;;
            1)
                continue
                ;;
            2)
                # 重新計算分析結果
                mvp_category=$(infer_mvp_category "$ISSUE_TITLE" "$ISSUE_DESCRIPTION" "$ISSUE_CONTEXT")
                effort=$(estimate_effort "$ISSUE_PRIORITY" "$ISSUE_TITLE" "$ISSUE_DESCRIPTION")
                continue
                ;;
        esac
    done

    # 創建 Issue
    echo ""
    echo "🚀 正在創建 GitHub Issue..."
    create_github_issue "$mvp_category" "$effort"
}

# 檢查必要工具
if ! command -v gh &> /dev/null; then
    echo "❌ 錯誤：需要安裝 GitHub CLI (gh)"
    echo "安裝指令: brew install gh"
    exit 1
fi

# 檢查 GitHub 認證
if ! gh auth status &> /dev/null; then
    echo "❌ 錯誤：請先登入 GitHub CLI"
    echo "登入指令: gh auth login"
    exit 1
fi

# 執行主流程
main "$@"
