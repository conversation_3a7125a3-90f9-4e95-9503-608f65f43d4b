---
allowed-tools: Read, Grep, Glob, Task, Bash(git: log|status|diff|checkout|show), Ba<PERSON>(make: test|lint|ci-check), Bash(gh: api|issue|pr|run), WebFetch
description: Validator 是 NovelWebsite 專案的 AI 質量保證與戰略審計官。它負責驗證已交付的工作是否完全符合 Navigator 最初設定的戰略目標和驗收標準，確保自動化開發流程的最終產出質量。
---

## 🛡️ Validator - AI 質量與戰略驗證引擎

### 核心理念
Validator 的角色是**「交付品質的最終守門員」**和**「戰略完整性的審計官」**。它在代碼成功合併到 main 分支後被激活，執行最高層級的整合驗證，確保整個系統在真實環境中符合預期，並從戰略層面審計是否達成了 Navigator 的原始意圖。

**觸發時機**: 📍 代碼成功合併到 main 分支後
**驗證層級**: 🎯 整合測試 + 端到端測試 + 戰略審計
**核心使命**: 🛡️ 確保可交付的穩定狀態

**NovelWebsite 兩階段驗證模型**:
1. **🔬 行為驗證 (Behavioral Verification)**: 在最新 main 分支上執行完整的整合和端到端測試
2. **🏛️ 戰略驗證 (Strategic Verification)**: 基於系統行為正確的前提下，深度審計戰略意圖達成度

**專案特定關注點**:
- 「黃金28」小說內容完整性的端到端驗證
- 5分鐘用戶旅程的完整流程測試
- SEO 和 Next.js 遷移的整合驗證
- 爬蟲系統在生產環境下的穩定性測試

---

## 📥 第一階段：環境準備與上下文重建 (Environment Setup & Context Reconstruction)

Validator 在代碼合併到 main 分支後被激活。它的首要任務是確保在最新的 main 分支上準備驗證環境，並重建任務的完整上下文。

### 1. 任務接收與數據鏈追溯
```bash
#!/bin/bash

# 初始化驗證環境
PARENT_ISSUE_NUM=$1
VALIDATION_MODE=${2:-"full"}  # full, behavioral-only, strategic-only

if [[ -z "$PARENT_ISSUE_NUM" ]]; then
    echo "❌ 錯誤：請提供父任務 Issue 編號"
    echo "使用方式: /validator.md 127 [full|behavioral-only|strategic-only]"
    exit 1
fi

echo "🛡️ Validator 啟動 - 後合併交付品質驗證"
echo "🎯 審計目標：父任務 Issue #$PARENT_ISSUE_NUM"
echo "🔍 驗證模式：$VALIDATION_MODE"
echo "📍 觸發條件：代碼已成功合併到 main 分支"

# 設置報告路徑 - 遵循 AI Operations 目錄結構
VALIDATION_DATE=$(date +'%Y-%m-%d')
REPORT_DIR="docs/04_AI_OPERATIONS/validator"
VALIDATION_REPORT="$REPORT_DIR/${VALIDATION_DATE}_Issue-${PARENT_ISSUE_NUM}_Validation-Report.html"
mkdir -p "$REPORT_DIR"

# 1. 獲取父任務的完整信息，這是所有驗證的「真相之源」
echo "📜 正在讀取父任務 #$PARENT_ISSUE_NUM 的原始意圖與 DoD..."
PARENT_ISSUE_JSON=$(gh issue view $PARENT_ISSUE_NUM --json title,body,labels,comments,closedAt)
PARENT_ISSUE_TITLE=$(echo "$PARENT_ISSUE_JSON" | jq -r .title)
PARENT_ISSUE_BODY=$(echo "$PARENT_ISSUE_JSON" | jq -r .body)
PARENT_ISSUE_LABELS=$(echo "$PARENT_ISSUE_JSON" | jq -r '.labels[].name' | tr '\n' ',' | sed 's/,$//')

# 檢查 Issue 狀態
ISSUE_STATE=$(gh issue view $PARENT_ISSUE_NUM --json state --jq .state)
if [[ "$ISSUE_STATE" != "CLOSED" ]]; then
    echo "⚠️  警告：父任務 #$PARENT_ISSUE_NUM 尚未關閉，但將繼續驗證"
fi

# 提取 DoD 區塊
extract_parent_dod

# 2. 追溯所有相關的子任務和 PR
echo "🔗 正在追溯所有相關的子任務和 Pull Requests..."
trace_related_work_items

# 3. 準備最新 main 分支驗證環境
echo "🔬 正在準備最新 main 分支驗證環境..."
prepare_main_branch_environment
```

### 2. DoD 提取與工作項目追溯
```bash
function extract_parent_dod() {
    echo "📋 提取父任務的驗收標準..."

    # 提取 DoD 區塊
    PARENT_DOD_SECTION=$(echo "$PARENT_ISSUE_BODY" | sed -n '/### ✅ 驗收標準/,/###/p' | head -n -1)

    if [[ -z "$PARENT_DOD_SECTION" ]]; then
        echo "⚠️  未找到明確的 DoD，將檢查 Navigator 動態生成的 DoD..."
        # 在評論中尋找 Navigator 生成的 DoD
        PARENT_DOD_SECTION=$(echo "$PARENT_ISSUE_JSON" | jq -r '.comments[].body' | grep -A 20 "### ✅ 驗收標準" | head -20)

        if [[ -z "$PARENT_DOD_SECTION" ]]; then
            echo "❌ 無法找到驗收標準，將根據 Issue 描述推理 DoD"
            infer_dod_from_issue_content
        fi
    fi

    echo "✅ DoD 提取完成"
    echo "📊 DoD 條目數量：$(echo "$PARENT_DOD_SECTION" | grep -c "^- \[" || echo "0")"
}

function trace_related_work_items() {
    echo "🔍 追溯相關工作項目..."

    # 從父任務的評論中找到 Task-Dispatcher 生成的子任務列表
    CHILD_ISSUES=$(echo "$PARENT_ISSUE_JSON" | jq -r '.comments[].body' | grep -o 'Issue #[0-9]*' | cut -d'#' -f2 | sort -u | tr '\n' ' ')

    # 直接搜索相關的 PR（基於標題或標籤）
    RELATED_PRS=$(gh pr list --state merged --search "\"#$PARENT_ISSUE_NUM\"" --json number,title,mergeCommit --jq '.[].number')

    declare -g -a MERGE_COMMITS=()
    declare -g -a VERIFICATION_COMMITS=()

    # 收集所有相關的合併 commit
    for issue_num in $CHILD_ISSUES; do
        if [[ -n "$issue_num" ]]; then
            echo "  📌 追溯子任務 #$issue_num..."
            LINKED_PRS=$(gh issue view $issue_num --json timelineItems --jq '.timelineItems[] | select(.type == "CONNECTED_EVENT") | .source.number' 2>/dev/null || echo "")
            for pr_num in $LINKED_PRS; do
                if [[ -n "$pr_num" ]]; then
                    PR_JSON=$(gh pr view $pr_num --json state,mergeCommit,mergeable 2>/dev/null || echo "{}")
                    if [[ $(echo "$PR_JSON" | jq -r .state) == "MERGED" ]]; then
                        local merge_commit=$(echo "$PR_JSON" | jq -r .mergeCommit.oid)
                        if [[ "$merge_commit" != "null" && -n "$merge_commit" ]]; then
                            MERGE_COMMITS+=("$merge_commit")
                        fi
                    fi
                fi
            done
        fi
    done

    # 處理直接相關的 PR
    for pr_num in $RELATED_PRS; do
        if [[ -n "$pr_num" ]]; then
            echo "  📌 追溯相關 PR #$pr_num..."
            PR_JSON=$(gh pr view $pr_num --json mergeCommit 2>/dev/null || echo "{}")
            local merge_commit=$(echo "$PR_JSON" | jq -r .mergeCommit.oid)
            if [[ "$merge_commit" != "null" && -n "$merge_commit" ]]; then
                MERGE_COMMITS+=("$merge_commit")
            fi
        fi
    done

    # 去重並獲取驗證範圍
    if [[ ${#MERGE_COMMITS[@]} -gt 0 ]]; then
        VERIFICATION_COMMITS=($(printf "%s\n" "${MERGE_COMMITS[@]}" | sort -u))
        LATEST_MERGE_COMMIT=$(printf "%s\n" "${VERIFICATION_COMMITS[@]}" | tail -n 1)
        echo "✅ 找到 ${#VERIFICATION_COMMITS[@]} 個相關 commit"
        echo "🎯 最新合併 commit: $LATEST_MERGE_COMMIT"
    else
        echo "⚠️  未找到相關的合併 commit，將檢查最近的變更"
        LATEST_MERGE_COMMIT=$(git log -1 --format="%H")
    fi
}

function prepare_main_branch_environment() {
    echo "🏗️  準備最新 main 分支環境..."

    # 確保處於最新的 main 分支
    git fetch origin

    # 檢查工作目錄是否乾淨
    if [[ -n $(git status --porcelain) ]]; then
        echo "⚠️  工作目錄有未提交的變更，將暫存"
        git stash push -m "Validator: 暫存變更以進行後合併驗證"
        STASHED_CHANGES=true
    fi

    # 切換到最新的 main 分支
    echo "🔄 切換到最新 main 分支進行後合併驗證..."
    git checkout main --quiet
    git pull origin main --quiet

    CURRENT_MAIN_COMMIT=$(git rev-parse HEAD)
    echo "📍 當前 main commit: $CURRENT_MAIN_COMMIT"

    # 確保依賴安裝正確
    echo "📦 安裝最新依賴..."
    if [[ -f "package.json" ]]; then
        pnpm install --frozen-lockfile > /dev/null 2>&1
    fi
    if [[ -f "backend/requirements.txt" ]]; then
        pip install -r backend/requirements.txt > /dev/null 2>&1
    fi

    # 確保環境變數和配置正確
    echo "⚙️  驗證環境配置..."
    if [[ -f ".env.example" ]]; then
        if [[ ! -f ".env" ]]; then
            cp .env.example .env
            echo "  📋 已從 .env.example 創建 .env"
        fi
    fi

    echo "✅ main 分支驗證環境準備完成"
}

function infer_dod_from_issue_content() {
    echo "🧠 基於 Issue 內容推理 DoD..."

    # 基於 Issue 標題和內容推理可能的驗收標準
    INFERRED_DOD="### ✅ 推理的驗收標準\n"

    # 針對 NovelWebsite 專案的常見模式
    if [[ "$PARENT_ISSUE_TITLE" =~ "爬蟲" || "$PARENT_ISSUE_BODY" =~ "hjwzw" ]]; then
        INFERRED_DOD="${INFERRED_DOD}- [ ] 爬蟲能夠成功獲取目標網站內容\n"
        INFERRED_DOD="${INFERRED_DOD}- [ ] 爬取的數據格式正確且完整\n"
        INFERRED_DOD="${INFERRED_DOD}- [ ] 爬蟲遵循 robots.txt 和反爬策略\n"
    fi

    if [[ "$PARENT_ISSUE_TITLE" =~ "搜索" || "$PARENT_ISSUE_BODY" =~ "search" ]]; then
        INFERRED_DOD="${INFERRED_DOD}- [ ] 搜索功能能夠返回準確結果\n"
        INFERRED_DOD="${INFERRED_DOD}- [ ] 前端搜索界面完整且響應式\n"
        INFERRED_DOD="${INFERRED_DOD}- [ ] 搜索性能符合預期（<2秒）\n"
    fi

    if [[ "$PARENT_ISSUE_TITLE" =~ "Next.js" || "$PARENT_ISSUE_BODY" =~ "遷移" ]]; then
        INFERRED_DOD="${INFERRED_DOD}- [ ] Next.js 環境配置完成且可運行\n"
        INFERRED_DOD="${INFERRED_DOD}- [ ] 核心頁面已成功遷移\n"
        INFERRED_DOD="${INFERRED_DOD}- [ ] SEO 相關功能正常工作\n"
    fi

    PARENT_DOD_SECTION="$INFERRED_DOD"
    echo "✅ DoD 推理完成"
}
```

---

## 🔍 第二階段：兩階段驗證執行 (Two-Phase Verification)

Validator 執行兩階段驗證：首先確保系統行為正確，然後基於此進行戰略審計。

### 🔬 階段 1: 行為驗證 (Behavioral Verification)

**核心理念**: 在最新的 main 分支上執行完整的整合和端到端測試，確保系統在真實環境中符合預期行為。如果此階段失敗，立即中止並報告回歸或集成錯誤。

```bash
function execute_behavioral_verification() {
    echo "🔬 啟動行為驗證階段..."
    echo "📍 目標：確保系統在 main 分支上正常運行"

    declare -g BEHAVIORAL_VERIFICATION_PASSED=false
    declare -g -a BEHAVIORAL_FAILURES=()

    # 1. 執行整合測試
    execute_integration_tests

    # 2. 執行端到端測試
    execute_end_to_end_tests

    # 3. 執行 NovelWebsite 專案特定的行為測試
    execute_novelwebsite_behavioral_tests

    # 4. 評估行為驗證結果
    evaluate_behavioral_results

    if [[ "$BEHAVIORAL_VERIFICATION_PASSED" == "true" ]]; then
        echo "✅ 行為驗證階段通過"
        return 0
    else
        echo "❌ 行為驗證階段失敗，中止後續驗證"
        generate_behavioral_failure_report
        return 1
    fi
}

function execute_integration_tests() {
    echo "  🔗 執行整合測試..."

    # 檢查是否有整合測試命令
    if [[ -f "Makefile" ]] && grep -q "test-integration\|test-int" Makefile; then
        echo "    執行 make test-integration..."
        local integration_output=$(make test-integration 2>&1)
        local integration_exit_code=$?

        if [[ $integration_exit_code -eq 0 ]]; then
            echo "    ✅ 整合測試通過"
        else
            echo "    ❌ 整合測試失敗"
            BEHAVIORAL_FAILURES+=("整合測試失敗：$integration_output")
        fi
    elif [[ -f "backend/manage.py" ]]; then
        echo "    執行 Django 整合測試..."
        cd backend
        local django_test_output=$(python manage.py test --settings=settings.test 2>&1)
        local django_exit_code=$?
        cd ..

        if [[ $django_exit_code -eq 0 ]]; then
            echo "    ✅ Django 整合測試通過"
        else
            echo "    ❌ Django 整合測試失敗"
            BEHAVIORAL_FAILURES+=("Django 整合測試失敗：$django_test_output")
        fi
    else
        echo "    ⚠️  未找到整合測試配置"
        BEHAVIORAL_FAILURES+=("未找到整合測試配置，無法驗證系統整合狀態")
    fi
}

function execute_end_to_end_tests() {
    echo "  🌐 執行端到端測試..."

    # 檢查 E2E 測試配置
    if [[ -f "Makefile" ]] && grep -q "test-e2e\|e2e" Makefile; then
        echo "    執行 make test-e2e..."
        local e2e_output=$(make test-e2e 2>&1)
        local e2e_exit_code=$?

        if [[ $e2e_exit_code -eq 0 ]]; then
            echo "    ✅ 端到端測試通過"
        else
            echo "    ❌ 端到端測試失敗"
            BEHAVIORAL_FAILURES+=("端到端測試失敗：$e2e_output")
        fi
    elif [[ -d "e2e" || -d "tests/e2e" || -d "cypress" ]]; then
        echo "    檢測到 E2E 測試目錄，嘗試執行..."

        # 嘗試 Cypress
        if [[ -f "cypress.config.js" || -f "cypress.json" ]]; then
            echo "    執行 Cypress E2E 測試..."
            local cypress_output=$(npx cypress run --headless 2>&1)
            local cypress_exit_code=$?

            if [[ $cypress_exit_code -eq 0 ]]; then
                echo "    ✅ Cypress E2E 測試通過"
            else
                echo "    ❌ Cypress E2E 測試失敗"
                BEHAVIORAL_FAILURES+=("Cypress E2E 測試失敗：$cypress_output")
            fi
        # 嘗試 Playwright
        elif [[ -f "playwright.config.js" || -f "playwright.config.ts" ]]; then
            echo "    執行 Playwright E2E 測試..."
            local playwright_output=$(npx playwright test 2>&1)
            local playwright_exit_code=$?

            if [[ $playwright_exit_code -eq 0 ]]; then
                echo "    ✅ Playwright E2E 測試通過"
            else
                echo "    ❌ Playwright E2E 測試失敗"
                BEHAVIORAL_FAILURES+=("Playwright E2E 測試失敗：$playwright_output")
            fi
        else
            echo "    ⚠️  找到 E2E 目錄但無法識別測試框架"
            BEHAVIORAL_FAILURES+=("找到 E2E 測試目錄但無法執行測試")
        fi
    else
        echo "    ⚠️  未找到端到端測試配置"
        BEHAVIORAL_FAILURES+=("未找到端到端測試配置，無法驗證完整用戶流程")
    fi
}

function execute_novelwebsite_behavioral_tests() {
    echo "  📚 執行 NovelWebsite 專案特定行為測試..."

    # 1. 測試黃金28小說內容可讀性
    test_golden_28_novels_accessibility

    # 2. 測試5分鐘用戶旅程
    test_five_minute_user_journey

    # 3. 測試爬蟲系統穩定性
    test_crawler_system_stability

    # 4. 測試搜索功能端到端流程
    test_search_functionality_e2e
}

function test_golden_28_novels_accessibility() {
    echo "    📖 測試黃金28小說內容可讀性..."

    # 檢查是否有小說數據
    local novel_count=0
    if [[ -d "backend" ]]; then
        # 嘗試查詢數據庫中的小說數量
        novel_count=$(python -c "
import sys, os
sys.path.append('backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.local')
try:
    import django
    django.setup()
    from novel.models import Novel
    print(Novel.objects.count())
except Exception as e:
    print('0')
" 2>/dev/null || echo "0")
    fi

    if [[ $novel_count -gt 0 ]]; then
        echo "      ✅ 發現 $novel_count 本小說，內容可訪問"
    else
        echo "      ❌ 未發現小說內容"
        BEHAVIORAL_FAILURES+=("黃金28小說內容：數據庫中未找到小說數據")
    fi
}

function test_five_minute_user_journey() {
    echo "    👤 測試5分鐘用戶旅程..."

    # 檢查關鍵頁面是否可訪問（靜態檢查）
    local journey_steps=0
    local journey_passed=0

    # 1. 首頁
    if [[ -f "frontend/src/pages/index.tsx" || -f "frontend/src/app/page.tsx" ]]; then
        ((journey_steps++))
        ((journey_passed++))
    fi

    # 2. 小說列表頁
    if [[ -f "frontend/src/pages/novels.tsx" || -f "frontend/src/app/novels/page.tsx" ]]; then
        ((journey_steps++))
        ((journey_passed++))
    fi

    # 3. 搜索功能
    if find frontend/src -name "*search*" -o -name "*Search*" | grep -q .; then
        ((journey_steps++))
        ((journey_passed++))
    fi

    # 4. 閱讀器頁面
    if find frontend/src -name "*reader*" -o -name "*Reader*" | grep -q .; then
        ((journey_steps++))
        ((journey_passed++))
    fi

    journey_steps=4  # 總共4個關鍵步驟

    if [[ $journey_passed -eq $journey_steps ]]; then
        echo "      ✅ 5分鐘用戶旅程關鍵頁面完整 ($journey_passed/$journey_steps)"
    else
        echo "      ❌ 5分鐘用戶旅程頁面不完整 ($journey_passed/$journey_steps)"
        BEHAVIORAL_FAILURES+=("5分鐘用戶旅程：關鍵頁面缺失 ($journey_passed/$journey_steps)")
    fi
}

function test_crawler_system_stability() {
    echo "    🕷️  測試爬蟲系統穩定性..."

    # 檢查爬蟲適配器是否可正常導入
    if [[ -d "backend/novel/adapters" ]]; then
        local adapter_test_result=$(python -c "
import sys
sys.path.append('backend')
try:
    from novel.adapters.base_adapter import BaseAdapter
    print('BaseAdapter 導入成功')
except Exception as e:
    print(f'BaseAdapter 導入失敗: {e}')
" 2>&1)

        if [[ "$adapter_test_result" =~ "成功" ]]; then
            echo "      ✅ 爬蟲基礎架構正常"
        else
            echo "      ❌ 爬蟲基礎架構異常"
            BEHAVIORAL_FAILURES+=("爬蟲系統穩定性：$adapter_test_result")
        fi
    else
        echo "      ❌ 爬蟲適配器目錄不存在"
        BEHAVIORAL_FAILURES+=("爬蟲系統穩定性：backend/novel/adapters 目錄不存在")
    fi
}

function test_search_functionality_e2e() {
    echo "    🔍 測試搜索功能端到端流程..."

    # 檢查搜索 API 端點
    local search_api_exists=false
    if [[ -f "backend/apps/catalog/api/views.py" ]]; then
        if grep -q "search\|Search" "backend/apps/catalog/api/views.py"; then
            search_api_exists=true
        fi
    fi

    # 檢查前端搜索組件
    local search_frontend_exists=false
    if find frontend/src -name "*search*" -o -name "*Search*" | grep -q .; then
        search_frontend_exists=true
    fi

    if [[ "$search_api_exists" == "true" && "$search_frontend_exists" == "true" ]]; then
        echo "      ✅ 搜索功能前後端整合完整"
    else
        echo "      ❌ 搜索功能整合不完整"
        BEHAVIORAL_FAILURES+=("搜索功能端到端：前端($search_frontend_exists) 後端($search_api_exists)")
    fi
}

function evaluate_behavioral_results() {
    echo "  📊 評估行為驗證結果..."

    local failure_count=${#BEHAVIORAL_FAILURES[@]}

    if [[ $failure_count -eq 0 ]]; then
        BEHAVIORAL_VERIFICATION_PASSED=true
        echo "    ✅ 所有行為測試通過"
    else
        BEHAVIORAL_VERIFICATION_PASSED=false
        echo "    ❌ 發現 $failure_count 個行為問題"
    fi
}

function generate_behavioral_failure_report() {
    echo "🚨 生成行為驗證失敗報告..."

    local failure_report="docs/04_AI_OPERATIONS/validator/${VALIDATION_DATE}_Issue-${PARENT_ISSUE_NUM}_Behavioral-Failures.md"

    # 執行 AI 根本原因分析
    local root_cause_analysis=$(perform_ai_root_cause_analysis)

    cat > "$failure_report" <<EOF
# 行為驗證失敗報告

**父任務**: Issue #$PARENT_ISSUE_NUM
**驗證時間**: $(date +'%Y-%m-%d %H:%M:%S')
**main 分支 commit**: $CURRENT_MAIN_COMMIT

## 🚨 發現的行為問題

$(for failure in "${BEHAVIORAL_FAILURES[@]}"; do
    echo "### ❌ $failure"
    echo ""
done)

## 🔍 AI 根本原因分析 (Root Cause Analysis)

$root_cause_analysis

## 🔧 建議修復行動

1. **立即回滾**: 考慮回滾到上一個穩定的 main commit
2. **緊急修復**: 優先修復整合和 E2E 測試失敗問題
3. **重新驗證**: 修復後重新運行 Validator 進行驗證

---
🛡️ Generated by Validator Behavioral Verification with AI Root Cause Analysis
EOF

    echo "📋 行為失敗報告已生成：$failure_report"
}

function perform_ai_root_cause_analysis() {
    echo "🧠 執行 AI 根本原因分析..." >&2

    # 收集所有失敗信息
    local all_failures=""
    for failure in "${BEHAVIORAL_FAILURES[@]}"; do
        all_failures="${all_failures}\n- $failure"
    done

    # AI 推理指令：根本原因分析
    local ai_analysis=$(cat <<EOF
### 🧠 AI 診斷報告

**診斷時間**: $(date +'%Y-%m-%d %H:%M:%S')
**診斷專家**: Validator SRE Intelligence Agent

#### 錯誤分類與模式識別

$(analyze_failure_patterns "$all_failures")

#### 關鍵日誌提取

$(extract_critical_logs "$all_failures")

#### 初步診斷結論

$(generate_root_cause_hypothesis "$all_failures")

#### 建議的調查方向

$(suggest_investigation_paths "$all_failures")

---
*此診斷基於失敗日誌的模式識別和歷史經驗推理*
EOF
    )

    echo "$ai_analysis"
}

function analyze_failure_patterns() {
    local failures="$1"

    # 模擬 AI 分析失敗模式
    if [[ "$failures" =~ "502" || "$failures" =~ "503" || "$failures" =~ "Gateway" ]]; then
        echo "**錯誤類型**: 後端服務不可用 (Backend Service Unavailability)"
        echo "**置信度**: 高 (90%)"
        echo "**模式**: 檢測到 HTTP 5xx 錯誤，表明後端服務可能未正確啟動或崩潰"
    elif [[ "$failures" =~ "timeout" || "$failures" =~ "Timeout" || "$failures" =~ "timed out" ]]; then
        echo "**錯誤類型**: 性能退化或死鎖 (Performance Degradation / Deadlock)"
        echo "**置信度**: 高 (85%)"
        echo "**模式**: 多個超時錯誤表明系統響應時間超過預期"
    elif [[ "$failures" =~ "Cannot find module" || "$failures" =~ "ModuleNotFoundError" ]]; then
        echo "**錯誤類型**: 依賴缺失 (Missing Dependencies)"
        echo "**置信度**: 高 (95%)"
        echo "**模式**: 模組導入失敗，可能是依賴未正確安裝或版本不匹配"
    elif [[ "$failures" =~ "ECONNREFUSED" || "$failures" =~ "connection refused" ]]; then
        echo "**錯誤類型**: 服務連接失敗 (Service Connection Failure)"
        echo "**置信度**: 高 (88%)"
        echo "**模式**: 無法連接到必要的服務，可能是服務未啟動或端口配置錯誤"
    else
        echo "**錯誤類型**: 未分類錯誤 (Unclassified Error)"
        echo "**置信度**: 低 (40%)"
        echo "**模式**: 錯誤模式不明確，需要進一步分析"
    fi
}

function extract_critical_logs() {
    local failures="$1"

    # 提取關鍵錯誤信息
    echo '```'
    echo "$failures" | grep -E "(Error|Failed|Exception|refused|timeout)" | head -3
    echo '```'

    echo ""
    echo "**關鍵指標**:"
    echo "- 失敗數量: ${#BEHAVIORAL_FAILURES[@]} 個"
    echo "- 影響範圍: $(estimate_impact_scope)"
}

function generate_root_cause_hypothesis() {
    local failures="$1"

    # 基於模式生成假設
    if [[ "$failures" =~ "hjwzw" && "$failures" =~ "403" ]]; then
        echo "**最可能的根本原因**:"
        echo "1. **外部依賴變更**: hjwzw.com 網站加強了反爬蟲策略，導致爬蟲適配器失效"
        echo "2. **影響鏈**: 爬蟲失敗 → 內容獲取失敗 → E2E 測試無法找到預期內容 → 測試失敗"
        echo "3. **證據**: HTTP 403 錯誤碼明確表示訪問被拒絕"
    elif [[ "$failures" =~ "Django" && "$failures" =~ "migration" ]]; then
        echo "**最可能的根本原因**:"
        echo "1. **數據庫遷移未執行**: 新代碼需要的數據庫結構變更未應用到測試環境"
        echo "2. **影響鏈**: 模型變更 → 遷移文件生成 → 遷移未執行 → ORM 查詢失敗"
        echo "3. **證據**: Django ORM 相關錯誤通常指向數據庫模式不匹配"
    elif [[ "$failures" =~ "search" && "$failures" =~ "undefined" ]]; then
        echo "**最可能的根本原因**:"
        echo "1. **前後端接口不一致**: 搜索 API 的響應格式與前端預期不符"
        echo "2. **影響鏈**: API 變更 → 前端未同步更新 → JavaScript 錯誤 → UI 功能失效"
        echo "3. **證據**: 'undefined' 錯誤通常表示對象屬性訪問失敗"
    else
        echo "**初步分析**:"
        echo "1. **多重因素**: 錯誤模式顯示可能有多個獨立的問題同時發生"
        echo "2. **建議**: 需要逐個檢查失敗的測試，確定是否有共同的根本原因"
        echo "3. **優先級**: 建議優先解決阻塞最多測試的問題"
    fi
}

function suggest_investigation_paths() {
    local failures="$1"

    echo "1. **立即檢查**:"
    echo "   - 最近合併的 PR 是否包含破壞性變更"
    echo "   - CI/CD 流程是否有警告被忽略"
    echo "   - 依賴版本是否有自動更新"
    echo ""
    echo "2. **深入調查**:"
    echo "   - 使用 \`git bisect\` 找到引入問題的具體 commit"
    echo "   - 檢查測試環境與生產環境的配置差異"
    echo "   - 審查相關服務的健康狀態和日誌"
}

function estimate_impact_scope() {
    local behavioral_count=${#BEHAVIORAL_FAILURES[@]}

    if [[ $behavioral_count -gt 5 ]]; then
        echo "系統性故障 (Systemic Failure)"
    elif [[ $behavioral_count -gt 2 ]]; then
        echo "模組級故障 (Module-level Failure)"
    else
        echo "局部故障 (Localized Failure)"
    fi
}
```

### 🏛️ 階段 2: 戰略驗證 + 漂移檢測 (Strategic Verification + Drift Detection)

**核心理念**: 在確認系統行為符合預期後，激活三個專家子代理從概念層面深度審計交付成果是否 100% 符合 Navigator 的原始戰略意圖。**新增核心功能**：檢測 AI 團隊的「創造性漂移」並提供校準建議。

**戰略錨點使命**：Validator 不僅是流水線的最後一步，更是整個 AI 團隊的**戰略校準器**。它的驗證報告將成為 Navigator 下一次規劃循環的強制性輸入，形成持續的戰略對齊閉環。

```bash
function execute_strategic_verification() {
    echo "🏛️ 啟動戰略驗證階段..."
    echo "📍 目標：審計戰略意圖達成度與交付品質"

    if [[ "$BEHAVIORAL_VERIFICATION_PASSED" != "true" ]]; then
        echo "❌ 行為驗證未通過，跳過戰略驗證"
        return 1
    fi

    declare -g STRATEGIC_VERIFICATION_PASSED=false
    declare -g -a STRATEGIC_ISSUES=()

    # 子代理 1: DoD 概念驗證
    execute_dod_conceptual_verification

    # 子代理 2: 戰略意圖深度審計
    execute_strategic_intent_deep_audit

    # 子代理 3: 質量與債務巡檢
    execute_quality_debt_inspection

    # 評估戰略驗證結果
    evaluate_strategic_results

    if [[ "$STRATEGIC_VERIFICATION_PASSED" == "true" ]]; then
        echo "✅ 戰略驗證階段通過"
        return 0
    else
        echo "⚠️  戰略驗證發現問題，但系統功能正常"
        return 0  # 不中止，因為行為已驗證通過
    fi
}

function execute_dod_conceptual_verification() {
    echo "  ✅ 執行 DoD 概念驗證..."
    echo "    📍 基於行為測試通過的信心，進行概念層面的 DoD 審計"

    declare -g -a DOD_CONCEPTUAL_RESULTS=()
    declare -g DOD_CONCEPTUAL_PASS_COUNT=0
    declare -g DOD_CONCEPTUAL_FAIL_COUNT=0

    # 解析 DoD 項目
    local dod_items=$(echo "$PARENT_DOD_SECTION" | grep "^- \[" | sed 's/^- \[ \] //')

    while IFS= read -r dod_item; do
        if [[ -n "$dod_item" ]]; then
            verify_dod_conceptually "$dod_item"
        fi
    done <<< "$dod_items"

    echo "    📊 DoD 概念驗證：通過 $DOD_CONCEPTUAL_PASS_COUNT 項，需關注 $DOD_CONCEPTUAL_FAIL_COUNT 項"
}

function verify_dod_conceptually() {
    local dod_item="$1"
    local conceptual_status=""
    local reasoning=""

    echo "    🔍 概念驗證：$dod_item"

    # 基於系統已通過行為測試的前提，進行概念層面的檢查
    if [[ "$dod_item" =~ "爬蟲" || "$dod_item" =~ "hjwzw" ]]; then
        # 如果行為測試通過，概念上爬蟲功能已實現
        conceptual_status="✅"
        reasoning="行為測試確認爬蟲系統可正常運行"
        ((DOD_CONCEPTUAL_PASS_COUNT++))
    elif [[ "$dod_item" =~ "搜索" || "$dod_item" =~ "search" ]]; then
        conceptual_status="✅"
        reasoning="端到端測試確認搜索功能整合完成"
        ((DOD_CONCEPTUAL_PASS_COUNT++))
    elif [[ "$dod_item" =~ "測試" || "$dod_item" =~ "CI" ]]; then
        conceptual_status="✅"
        reasoning="整合測試和 E2E 測試執行成功"
        ((DOD_CONCEPTUAL_PASS_COUNT++))
    elif [[ "$dod_item" =~ "用戶" || "$dod_item" =~ "界面" || "$dod_item" =~ "前端" ]]; then
        conceptual_status="✅"
        reasoning="用戶旅程測試確認前端功能可用"
        ((DOD_CONCEPTUAL_PASS_COUNT++))
    else
        # 對於無法通過行為測試驗證的概念性需求，進行靜態檢查
        local git_changes=$(git log --oneline -10 --grep="$dod_item" 2>/dev/null | wc -l)
        if [[ $git_changes -gt 0 ]]; then
            conceptual_status="✅"
            reasoning="Git 歷史顯示相關開發活動"
            ((DOD_CONCEPTUAL_PASS_COUNT++))
        else
            conceptual_status="⚠️"
            reasoning="無法通過行為測試或代碼變更確認此需求"
            ((DOD_CONCEPTUAL_FAIL_COUNT++))
            STRATEGIC_ISSUES+=("DoD 概念驗證：$dod_item - $reasoning")
        fi
    fi

    DOD_CONCEPTUAL_RESULTS+=("$conceptual_status|$dod_item|$reasoning")
    echo "      $conceptual_status $reasoning"
}

function analyze_strategic_intent() {
    echo "    🎯 分析戰略意圖和變更差異..."

    # 準備差異分析
    prepare_diff_analysis

    # 基於 Issue 內容和 Git 差異推理戰略意圖
    declare -g STRATEGIC_INTENT_TYPE=""
    declare -g STRATEGIC_INTENT_SCORE=""
    declare -g STRATEGIC_EVIDENCE=""
    declare -g STRATEGIC_REASONING=""
    declare -g STRATEGIC_DIFF_INSIGHTS=""

    # 分析父 Issue 的戰略意圖
    if [[ "$PARENT_ISSUE_TITLE" =~ "爬蟲" || "$PARENT_ISSUE_TITLE" =~ "hjwzw" || "$PARENT_ISSUE_TITLE" =~ "內容" ]]; then
        STRATEGIC_INTENT_TYPE="內容戰略"
    elif [[ "$PARENT_ISSUE_TITLE" =~ "SEO" || "$PARENT_ISSUE_TITLE" =~ "Next.js" || "$PARENT_ISSUE_TITLE" =~ "搜索引擎" ]]; then
        STRATEGIC_INTENT_TYPE="SEO戰略"
    elif [[ "$PARENT_ISSUE_TITLE" =~ "搜索" || "$PARENT_ISSUE_TITLE" =~ "用戶" || "$PARENT_ISSUE_TITLE" =~ "界面" ]]; then
        STRATEGIC_INTENT_TYPE="用戶體驗戰略"
    elif [[ "$PARENT_ISSUE_TITLE" =~ "重構" || "$PARENT_ISSUE_TITLE" =~ "技術債" || "$PARENT_ISSUE_TITLE" =~ "優化" ]]; then
        STRATEGIC_INTENT_TYPE="技術債務戰略"
    elif [[ "$PARENT_ISSUE_TITLE" =~ "CI" || "$PARENT_ISSUE_TITLE" =~ "工作流" || "$PARENT_ISSUE_TITLE" =~ "開發" ]]; then
        STRATEGIC_INTENT_TYPE="開發效率戰略"
    else
        STRATEGIC_INTENT_TYPE="通用戰略"
    fi

    echo "      🏷️ 戰略意圖分類：$STRATEGIC_INTENT_TYPE"
    echo "      📊 差異分析結果：$STRATEGIC_DIFF_INSIGHTS"
}

function prepare_diff_analysis() {
    echo "      🔍 準備差異分析 (HEAD vs HEAD~1)..."

    # 獲取當前 commit 和上一個 commit 的差異
    local current_commit=$(git rev-parse HEAD)
    local previous_commit=$(git rev-parse HEAD~1)

    # 檢查是否有上一個 commit
    if ! git rev-parse HEAD~1 >/dev/null 2>&1; then
        STRATEGIC_DIFF_INSIGHTS="首次提交，無法進行差異比較"
        return 0
    fi

    # 獲取變更統計
    local files_changed=$(git diff --name-only HEAD~1 HEAD | wc -l)
    local lines_added=$(git diff --numstat HEAD~1 HEAD | awk '{sum += $1} END {print sum+0}')
    local lines_deleted=$(git diff --numstat HEAD~1 HEAD | awk '{sum += $2} END {print sum+0}')

    # 分析變更的檔案類型
    local frontend_changes=$(git diff --name-only HEAD~1 HEAD | grep -E "\.(tsx?|jsx?|css|scss)$" | wc -l)
    local backend_changes=$(git diff --name-only HEAD~1 HEAD | grep -E "\.(py|html)$" | wc -l)
    local config_changes=$(git diff --name-only HEAD~1 HEAD | grep -E "(package\.json|requirements.*\.txt|\.yml|\.yaml|Dockerfile)$" | wc -l)
    local ci_changes=$(git diff --name-only HEAD~1 HEAD | grep -E "\.github/" | wc -l)

    # 檢查關鍵功能變更
    local search_changes=$(git diff HEAD~1 HEAD | grep -i "search" | wc -l)
    local crawler_changes=$(git diff HEAD~1 HEAD | grep -i "crawl\|spider\|hjwzw" | wc -l)
    local seo_changes=$(git diff HEAD~1 HEAD | grep -i "seo\|meta\|title\|next" | wc -l)

    # 組合差異洞察
    local change_scope=""
    if [[ $files_changed -gt 20 ]]; then
        change_scope="大規模變更"
    elif [[ $files_changed -gt 5 ]]; then
        change_scope="中等規模變更"
    else
        change_scope="小規模變更"
    fi

    local change_focus=""
    if [[ $frontend_changes -gt $backend_changes ]] && [[ $frontend_changes -gt $config_changes ]]; then
        change_focus="前端為主"
    elif [[ $backend_changes -gt $frontend_changes ]] && [[ $backend_changes -gt $config_changes ]]; then
        change_focus="後端為主"
    elif [[ $config_changes -gt 0 ]]; then
        change_focus="配置/基礎設施"
    else
        change_focus="混合變更"
    fi

    local functional_impact=""
    if [[ $search_changes -gt 0 ]]; then
        functional_impact="$functional_impact 搜索功能,"
    fi
    if [[ $crawler_changes -gt 0 ]]; then
        functional_impact="$functional_impact 爬蟲系統,"
    fi
    if [[ $seo_changes -gt 0 ]]; then
        functional_impact="$functional_impact SEO優化,"
    fi
    if [[ $ci_changes -gt 0 ]]; then
        functional_impact="$functional_impact CI/CD,"
    fi

    # 移除尾端的逗號
    functional_impact=$(echo "$functional_impact" | sed 's/,$//')

    STRATEGIC_DIFF_INSIGHTS="$change_scope ($files_changed 檔案, +$lines_added/-$lines_deleted), $change_focus"
    if [[ -n "$functional_impact" ]]; then
        STRATEGIC_DIFF_INSIGHTS="$STRATEGIC_DIFF_INSIGHTS, 影響:$functional_impact"
    fi

    echo "        📈 變更規模: $files_changed 檔案, +$lines_added/-$lines_deleted 行"
    echo "        🎯 變更重點: $change_focus"
    if [[ -n "$functional_impact" ]]; then
        echo "        🔧 功能影響: $functional_impact"
    fi
}

function execute_strategic_intent_deep_audit() {
    echo "  🏛️ 執行戰略意圖深度審計..."
    echo "    📍 基於系統功能正常，深度審計戰略目標達成度"

    # 重用並強化原有的戰略意圖分析
    analyze_strategic_intent
    execute_intent_specific_deep_audit

    echo "    📊 戰略意圖審計完成，達成度：$STRATEGIC_INTENT_SCORE"
}

function execute_intent_specific_deep_audit() {
    echo "    🕵️ 執行意圖特定深度審計..."

    case "$STRATEGIC_INTENT_TYPE" in
        "內容戰略")
            audit_content_strategy_deep
            ;;
        "SEO戰略")
            audit_seo_strategy_deep
            ;;
        "用戶體驗戰略")
            audit_ux_strategy_deep
            ;;
        "技術債務戰略")
            audit_tech_debt_strategy_deep
            ;;
        "開發效率戰略")
            audit_dev_efficiency_strategy_deep
            ;;
        *)
            audit_generic_strategy_deep
            ;;
    esac
}

function audit_content_strategy_deep() {
    echo "      📚 深度審計內容戰略執行..."

    local content_evidence=""
    local score_factors=0
    local total_factors=0

    # 由於行為測試已確認系統正常，重點關注戰略完整性

    # 檢查黃金28戰略執行程度
    local novel_count=$(python -c "
import sys, os
sys.path.append('backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.local')
try:
    import django
    django.setup()
    from novel.models import Novel
    print(Novel.objects.count())
except:
    print('0')
" 2>/dev/null || echo "0")

    if [[ $novel_count -ge 20 ]]; then
        content_evidence="${content_evidence}✅ 內容數量達到戰略目標 (${novel_count}/28)\n"
        ((score_factors+=2))
    elif [[ $novel_count -ge 10 ]]; then
        content_evidence="${content_evidence}⚠️ 內容數量部分達成 (${novel_count}/28)\n"
        ((score_factors++))
    else
        content_evidence="${content_evidence}❌ 內容數量未達戰略目標 (${novel_count}/28)\n"
    fi
    ((total_factors+=2))

    # 檢查內容品質相關的代碼改進
    local content_quality_commits=$(git log --oneline -20 --grep="content\|novel\|quality\|清理" | wc -l)
    if [[ $content_quality_commits -gt 0 ]]; then
        content_evidence="${content_evidence}✅ 內容品質改進活動：$content_quality_commits 個相關提交\n"
        ((score_factors++))
    fi
    ((total_factors++))

    # 計算戰略達成度
    local score_percentage=$(( score_factors * 100 / total_factors ))

    if [[ $score_percentage -ge 80 ]]; then
        STRATEGIC_INTENT_SCORE="高"
    elif [[ $score_percentage -ge 60 ]]; then
        STRATEGIC_INTENT_SCORE="中"
    else
        STRATEGIC_INTENT_SCORE="低"
        STRATEGIC_ISSUES+=("內容戰略達成度偏低：$score_percentage%")
    fi

    STRATEGIC_EVIDENCE="$content_evidence"
    STRATEGIC_REASONING="內容戰略深度評估基於實際數據量、品質改進活動。得分：$score_factors/$total_factors ($score_percentage%)"
}

function audit_seo_strategy_deep() {
    echo "      🔍 深度審計 SEO 戰略執行..."

    local seo_evidence=""
    local score_factors=0
    local total_factors=0

    # 檢查 Next.js 遷移的實際進展
    if [[ -f "next.config.js" || -f "next.config.ts" ]]; then
        seo_evidence="${seo_evidence}✅ Next.js 配置檔案存在\n"
        ((score_factors++))
    fi
    ((total_factors++))

    # 檢查 SEO 相關的實際實現
    if [[ -f "public/sitemap.xml" ]]; then
        seo_evidence="${seo_evidence}✅ Sitemap 已實現\n"
        ((score_factors++))
    fi
    if [[ -f "public/robots.txt" ]]; then
        seo_evidence="${seo_evidence}✅ Robots.txt 已配置\n"
        ((score_factors++))
    fi
    ((total_factors+=2))

    # 檢查 meta 標籤動態生成
    local meta_implementation=$(find frontend/src -name "*.tsx" -o -name "*.ts" | xargs grep -l "meta\|Head\|title" | wc -l)
    if [[ $meta_implementation -gt 0 ]]; then
        seo_evidence="${seo_evidence}✅ Meta 標籤實現：$meta_implementation 個檔案\n"
        ((score_factors++))
    fi
    ((total_factors++))

    local score_percentage=$(( score_factors * 100 / total_factors ))

    if [[ $score_percentage -ge 75 ]]; then
        STRATEGIC_INTENT_SCORE="高"
    elif [[ $score_percentage -ge 50 ]]; then
        STRATEGIC_INTENT_SCORE="中"
    else
        STRATEGIC_INTENT_SCORE="低"
        STRATEGIC_ISSUES+=("SEO 戰略執行不完整：$score_percentage%")
    fi

    STRATEGIC_EVIDENCE="$seo_evidence"
    STRATEGIC_REASONING="SEO 戰略深度評估基於實際配置檔案、SEO 功能實現。得分：$score_factors/$total_factors ($score_percentage%)"
}

function audit_ux_strategy_deep() {
    echo "      👤 深度審計用戶體驗戰略執行..."

    local ux_evidence=""
    local score_factors=0
    local total_factors=0

    # 基於行為測試通過，重點審計 UX 戰略完整性

    # 檢查 5分鐘用戶旅程的完整實現
    local journey_completeness=$(( journey_passed * 100 / 4 ))  # 使用之前的測試結果
    if [[ $journey_completeness -eq 100 ]]; then
        ux_evidence="${ux_evidence}✅ 5分鐘用戶旅程完整實現\n"
        ((score_factors+=2))
    elif [[ $journey_completeness -ge 75 ]]; then
        ux_evidence="${ux_evidence}⚠️ 5分鐘用戶旅程大部分實現 ($journey_completeness%)\n"
        ((score_factors++))
    fi
    ((total_factors+=2))

    # 檢查響應式設計實現
    local responsive_files=$(find frontend/src -name "*.css" -o -name "*.scss" -o -name "*.tsx" | xargs grep -l "@media\|responsive\|mobile" | wc -l)
    if [[ $responsive_files -gt 0 ]]; then
        ux_evidence="${ux_evidence}✅ 響應式設計實現：$responsive_files 個檔案\n"
        ((score_factors++))
    fi
    ((total_factors++))

    local score_percentage=$(( score_factors * 100 / total_factors ))

    if [[ $score_percentage -ge 80 ]]; then
        STRATEGIC_INTENT_SCORE="高"
    elif [[ $score_percentage -ge 60 ]]; then
        STRATEGIC_INTENT_SCORE="中"
    else
        STRATEGIC_INTENT_SCORE="低"
        STRATEGIC_ISSUES+=("用戶體驗戰略執行不完整：$score_percentage%")
    fi

    STRATEGIC_EVIDENCE="$ux_evidence"
    STRATEGIC_REASONING="用戶體驗戰略深度評估基於旅程完整性、響應式實現。得分：$score_factors/$total_factors ($score_percentage%)"
}

function audit_tech_debt_strategy_deep() {
    echo "      🔧 深度審計技術債務戰略執行..."

    local debt_evidence=""
    local score_factors=0
    local total_factors=0

    # 檢查代碼品質改進
    local code_quality_score=$(make ci-check > /dev/null 2>&1 && echo "通過" || echo "失敗")
    if [[ "$code_quality_score" == "通過" ]]; then
        debt_evidence="${debt_evidence}✅ CI 品質檢查通過\n"
        ((score_factors++))
    else
        debt_evidence="${debt_evidence}❌ CI 品質檢查失敗\n"
        STRATEGIC_ISSUES+=("技術債務：CI 品質檢查未通過")
    fi
    ((total_factors++))

    # 檢查技術債務清理活動
    local refactor_commits=$(git log --oneline -20 --grep="refactor\|cleanup\|debt\|重構" | wc -l)
    if [[ $refactor_commits -gt 0 ]]; then
        debt_evidence="${debt_evidence}✅ 技術債務清理活動：$refactor_commits 個提交\n"
        ((score_factors++))
    fi
    ((total_factors++))

    local score_percentage=$(( score_factors * 100 / total_factors ))

    if [[ $score_percentage -ge 80 ]]; then
        STRATEGIC_INTENT_SCORE="高"
    elif [[ $score_percentage -ge 50 ]]; then
        STRATEGIC_INTENT_SCORE="中"
    else
        STRATEGIC_INTENT_SCORE="低"
        STRATEGIC_ISSUES+=("技術債務戰略執行不充分：$score_percentage%")
    fi

    STRATEGIC_EVIDENCE="$debt_evidence"
    STRATEGIC_REASONING="技術債務戰略深度評估基於代碼品質、重構活動。得分：$score_factors/$total_factors ($score_percentage%)"
}

function audit_dev_efficiency_strategy_deep() {
    echo "      ⚡ 深度審計開發效率戰略執行..."

    local efficiency_evidence=""
    local score_factors=0
    local total_factors=0

    # 檢查 CI/CD 效率改進
    local ci_config_updates=$(git log --oneline -10 --grep="ci\|workflow\|github" -- .github/ | wc -l)
    if [[ $ci_config_updates -gt 0 ]]; then
        efficiency_evidence="${efficiency_evidence}✅ CI/CD 效率改進：$ci_config_updates 個更新\n"
        ((score_factors++))
    fi
    ((total_factors++))

    # 檢查開發工具改進
    local dev_tool_updates=$(git log --oneline -10 --name-only | grep -E "(Makefile|package\.json|requirements\.txt)" | wc -l)
    if [[ $dev_tool_updates -gt 0 ]]; then
        efficiency_evidence="${efficiency_evidence}✅ 開發工具改進活動檢測\n"
        ((score_factors++))
    fi
    ((total_factors++))

    local score_percentage=$(( score_factors * 100 / total_factors ))

    if [[ $score_percentage -ge 80 ]]; then
        STRATEGIC_INTENT_SCORE="高"
    elif [[ $score_percentage -ge 50 ]]; then
        STRATEGIC_INTENT_SCORE="中"
    else
        STRATEGIC_INTENT_SCORE="低"
        STRATEGIC_ISSUES+=("開發效率戰略執行不明顯：$score_percentage%")
    fi

    STRATEGIC_EVIDENCE="$efficiency_evidence"
    STRATEGIC_REASONING="開發效率戰略深度評估基於 CI/CD 改進、開發工具更新。得分：$score_factors/$total_factors ($score_percentage%)"
}

function audit_generic_strategy_deep() {
    echo "      📝 深度審計通用戰略執行..."

    # 基於行為測試通過，給予中等評分
    local changes_count=$(git log --oneline -10 | wc -l)

    if [[ $changes_count -gt 5 ]]; then
        STRATEGIC_INTENT_SCORE="中"
        STRATEGIC_EVIDENCE="✅ 檢測到充分的開發活動 ($changes_count 個最近提交)"
        STRATEGIC_REASONING="基於開發活動頻率和行為測試通過的通用評估"
    else
        STRATEGIC_INTENT_SCORE="低"
        STRATEGIC_EVIDENCE="⚠️ 開發活動較少 ($changes_count 個最近提交)"
        STRATEGIC_REASONING="開發活動頻率偏低"
        STRATEGIC_ISSUES+=("通用戰略執行：開發活動頻率偏低")
    fi
}

function execute_quality_debt_inspection() {
    echo "  🔬 執行質量與債務巡檢..."
    echo "    📍 基於系統運行正常，檢查潛在的質量債務"

    declare -g -a QUALITY_DEBT_ISSUES=()
    declare -g -a CREATIVE_DRIFT_FINDINGS=()

    # 檢查新引入的技術債務
    inspect_new_technical_debt

    # 檢查代碼品質趨勢
    inspect_code_quality_trends

    # 檢查依賴和安全問題
    inspect_dependencies_and_security

    # 🎯 新增：AI 團隊創造性漂移檢測
    detect_creative_drift

    echo "    📊 質量債務巡檢：發現 ${#QUALITY_DEBT_ISSUES[@]} 個潛在問題"
    echo "    🎯 創造性漂移檢測：發現 ${#CREATIVE_DRIFT_FINDINGS[@]} 個漂移指標"
}

function inspect_new_technical_debt() {
    echo "      🔍 檢查新引入的技術債務..."

    # 檢查新增的 TODO/FIXME
    local new_todos=$(git diff HEAD~1 HEAD | grep "^+" | grep -E "(TODO|FIXME|HACK|XXX)" | wc -l)
    if [[ $new_todos -gt 0 ]]; then
        QUALITY_DEBT_ISSUES+=("新增了 $new_todos 個 TODO/FIXME 標記")
    fi

    # 檢查大檔案
    local large_files=$(git diff --name-only HEAD~1 HEAD | while read file; do
        if [[ -f "$file" ]] && [[ $(wc -l < "$file") -gt 500 ]]; then
            echo "$file ($(wc -l < "$file") 行)"
        fi
    done)

    if [[ -n "$large_files" ]]; then
        QUALITY_DEBT_ISSUES+=("發現大檔案：$large_files")
    fi
}

function inspect_code_quality_trends() {
    echo "      📈 檢查代碼品質趨勢..."

    # 檢查最近的 CI 狀態
    local recent_ci_status=$(gh run list --limit 5 --json conclusion --jq '.[].conclusion' | head -1)
    if [[ "$recent_ci_status" != "success" ]]; then
        QUALITY_DEBT_ISSUES+=("最近的 CI 運行狀態：$recent_ci_status")
    fi

    # 檢查測試覆蓋率趨勢（如果有工具）
    if [[ -f "coverage.xml" || -f ".coverage" ]]; then
        echo "        📊 檢測到測試覆蓋率檔案"
        # 這裡可以添加更詳細的覆蓋率分析
    fi
}

function inspect_dependencies_and_security() {
    echo "      🔒 檢查依賴和安全問題..."

    # 檢查 npm 安全漏洞
    if [[ -f "package.json" ]]; then
        local npm_audit_result=$(npm audit --audit-level moderate --json 2>/dev/null | jq '.metadata.vulnerabilities.total' 2>/dev/null || echo "0")
        if [[ $npm_audit_result -gt 0 ]]; then
            QUALITY_DEBT_ISSUES+=("NPM 安全漏洞：$npm_audit_result 個")
        fi
    fi

    # 檢查 Python 依賴問題
    if [[ -f "backend/requirements.txt" ]]; then
        local outdated_packages=$(cd backend && pip list --outdated 2>/dev/null | wc -l)
        if [[ $outdated_packages -gt 5 ]]; then
            QUALITY_DEBT_ISSUES+=("過時的 Python 套件：$outdated_packages 個")
        fi
    fi
}

function detect_creative_drift() {
    echo "      🎯 檢測 AI 團隊創造性漂移..."
    echo "        📍 分析 AI 開發成果與原始戰略意圖的偏差程度"

    declare -g DRIFT_SEVERITY_SCORE=0
    declare -g DRIFT_IMPACT_ASSESSMENT=""
    declare -g STRATEGIC_ANCHOR_REPORT=""

    # 1. 原始戰略意圖重建
    reconstruct_original_strategic_intent

    # 2. 實際交付結果分析
    analyze_actual_delivery_outcomes

    # 3. 漂移模式識別
    identify_drift_patterns

    # 4. 戰略校準建議生成
    generate_strategic_calibration_recommendations

    # 5. 生成戰略錨定報告 (Navigator 下次規劃必讀)
    generate_strategic_anchor_report
}

function reconstruct_original_strategic_intent() {
    echo "        🧠 重建原始戰略意圖..."

    declare -g ORIGINAL_STRATEGIC_CONTEXT=""
    declare -g INTENDED_VALUE_PROPOSITION=""
    declare -g PLANNED_USER_IMPACT=""

    # 從父任務 Issue 提取戰略意圖
    ORIGINAL_STRATEGIC_CONTEXT=$(echo "$PARENT_ISSUE_BODY" | sed -n '/### 🎯 戰略背景/,/###/p' | head -n -1)
    if [[ -z "$ORIGINAL_STRATEGIC_CONTEXT" ]]; then
        # 從 Navigator 評論中尋找戰略背景
        ORIGINAL_STRATEGIC_CONTEXT=$(echo "$PARENT_ISSUE_JSON" | jq -r '.comments[].body' | grep -A 10 "戰略背景" | head -10)
    fi

    # 推理原始價值主張
    if [[ "$PARENT_ISSUE_TITLE" =~ "黃金28" || "$PARENT_ISSUE_BODY" =~ "MVP" ]]; then
        INTENDED_VALUE_PROPOSITION="內容戰略：快速建立頂級小說內容庫，吸引核心用戶群"
        PLANNED_USER_IMPACT="用戶能夠立即訪問28本市場驗證的頂級完本小說"
    elif [[ "$PARENT_ISSUE_TITLE" =~ "搜索" || "$PARENT_ISSUE_BODY" =~ "SEO" ]]; then
        INTENDED_VALUE_PROPOSITION="流量戰略：提升搜索引擎可見度，擴大用戶獲取"
        PLANNED_USER_IMPACT="用戶能夠通過搜索引擎發現並訪問平台"
    elif [[ "$PARENT_ISSUE_TITLE" =~ "用戶" || "$PARENT_ISSUE_BODY" =~ "註冊" ]]; then
        INTENDED_VALUE_PROPOSITION="用戶戰略：建立用戶留存機制，提升平台黏性"
        PLANNED_USER_IMPACT="用戶能夠註冊賬號、建立書架、追蹤閱讀進度"
    else
        INTENDED_VALUE_PROPOSITION="技術戰略：提升平台穩定性和開發效率"
        PLANNED_USER_IMPACT="改善系統性能和開發者體驗"
    fi

    echo "          📋 原始價值主張：$INTENDED_VALUE_PROPOSITION"
    echo "          👥 計劃用戶影響：$PLANNED_USER_IMPACT"
}

function analyze_actual_delivery_outcomes() {
    echo "        📊 分析實際交付結果..."

    declare -g ACTUAL_FUNCTIONALITY=""
    declare -g IMPLEMENTATION_APPROACH=""
    declare -g TECHNICAL_DECISIONS=""

    # 分析實際實現的功能
    local code_changes=$(git diff --name-only HEAD~5 HEAD 2>/dev/null | head -20)
    local frontend_changes=$(echo "$code_changes" | grep -E "\.(tsx?|jsx?|css|scss)$" | wc -l)
    local backend_changes=$(echo "$code_changes" | grep -E "\.(py|html)$" | wc -l)
    local crawler_changes=$(echo "$code_changes" | grep -E "adapters|crawl|spider" | wc -l)
    local config_changes=$(echo "$code_changes" | grep -E "(Dockerfile|docker|\.yml|\.yaml)" | wc -l)

    # 推理實際功能實現
    if [[ $crawler_changes -gt 0 ]]; then
        ACTUAL_FUNCTIONALITY="$ACTUAL_FUNCTIONALITY 爬蟲系統開發,"
    fi
    if [[ $frontend_changes -gt $backend_changes ]]; then
        ACTUAL_FUNCTIONALITY="$ACTUAL_FUNCTIONALITY 前端界面改進,"
    elif [[ $backend_changes -gt 0 ]]; then
        ACTUAL_FUNCTIONALITY="$ACTUAL_FUNCTIONALITY 後端邏輯實現,"
    fi
    if [[ $config_changes -gt 0 ]]; then
        ACTUAL_FUNCTIONALITY="$ACTUAL_FUNCTIONALITY 基礎設施配置,"
    fi

    # 分析技術決策模式
    local commit_messages=$(git log --oneline -10 --grep="feat\|fix\|refactor" | head -5)
    if echo "$commit_messages" | grep -q "refactor"; then
        TECHNICAL_DECISIONS="$TECHNICAL_DECISIONS 重構導向,"
    fi
    if echo "$commit_messages" | grep -q "feat"; then
        TECHNICAL_DECISIONS="$TECHNICAL_DECISIONS 功能導向,"
    fi
    if echo "$commit_messages" | grep -q "fix"; then
        TECHNICAL_DECISIONS="$TECHNICAL_DECISIONS 問題修復導向,"
    fi

    ACTUAL_FUNCTIONALITY=$(echo "$ACTUAL_FUNCTIONALITY" | sed 's/,$//')
    TECHNICAL_DECISIONS=$(echo "$TECHNICAL_DECISIONS" | sed 's/,$//')

    echo "          🔧 實際功能實現：$ACTUAL_FUNCTIONALITY"
    echo "          ⚡ 技術決策模式：$TECHNICAL_DECISIONS"
}

function identify_drift_patterns() {
    echo "        🔍 識別漂移模式..."

    # 戰略 vs 實際的差異分析
    local strategic_alignment=true
    local drift_indicators=()

    # 檢測功能漂移
    if [[ "$INTENDED_VALUE_PROPOSITION" =~ "內容戰略" ]] && [[ ! "$ACTUAL_FUNCTIONALITY" =~ "爬蟲" ]]; then
        drift_indicators+=("功能漂移：內容戰略任務未實現爬蟲相關功能")
        strategic_alignment=false
        ((DRIFT_SEVERITY_SCORE += 3))
    fi

    if [[ "$INTENDED_VALUE_PROPOSITION" =~ "流量戰略" ]] && [[ ! "$ACTUAL_FUNCTIONALITY" =~ "前端" ]]; then
        drift_indicators+=("功能漂移：SEO/搜索戰略未體現在前端改進上")
        strategic_alignment=false
        ((DRIFT_SEVERITY_SCORE += 2))
    fi

    # 檢測技術決策漂移
    if [[ "$INTENDED_VALUE_PROPOSITION" =~ "內容戰略" ]] && [[ "$TECHNICAL_DECISIONS" =~ "重構導向" ]]; then
        drift_indicators+=("決策漂移：內容戰略期間過度關注重構，偏離MVP交付")
        strategic_alignment=false
        ((DRIFT_SEVERITY_SCORE += 2))
    fi

    # 檢測複雜度漂移
    local complex_patterns=$(git diff HEAD~3 HEAD | grep -E "(async|await|Promise|class|interface)" | wc -l)
    if [[ $complex_patterns -gt 50 ]] && [[ "$INTENDED_VALUE_PROPOSITION" =~ "MVP" ]]; then
        drift_indicators+=("複雜度漂移：MVP階段引入了過度複雜的技術模式")
        strategic_alignment=false
        ((DRIFT_SEVERITY_SCORE += 1))
    fi

    # 檢測依賴漂移
    local new_dependencies=$(git diff HEAD~2 HEAD package.json requirements.txt 2>/dev/null | grep "^+" | grep -E "\".*\":" | wc -l)
    if [[ $new_dependencies -gt 3 ]] && [[ "$INTENDED_VALUE_PROPOSITION" =~ "MVP" ]]; then
        drift_indicators+=("依賴漂移：MVP階段添加了過多新依賴")
        strategic_alignment=false
        ((DRIFT_SEVERITY_SCORE += 1))
    fi

    # 更新全局變數
    if [[ ${#drift_indicators[@]} -gt 0 ]]; then
        CREATIVE_DRIFT_FINDINGS+=("${drift_indicators[@]}")
    fi

    # 評估影響等級
    if [[ $DRIFT_SEVERITY_SCORE -ge 5 ]]; then
        DRIFT_IMPACT_ASSESSMENT="🚨 高風險：AI團隊明顯偏離戰略意圖，需要立即校準"
    elif [[ $DRIFT_SEVERITY_SCORE -ge 3 ]]; then
        DRIFT_IMPACT_ASSESSMENT="⚠️ 中風險：存在一定程度的戰略漂移，建議關注"
    elif [[ $DRIFT_SEVERITY_SCORE -ge 1 ]]; then
        DRIFT_IMPACT_ASSESSMENT="📍 低風險：微小漂移，可通過下次規劃調整"
    else
        DRIFT_IMPACT_ASSESSMENT="✅ 無風險：AI團隊完美執行了戰略意圖"
    fi

    echo "          📊 漂移嚴重程度評分：$DRIFT_SEVERITY_SCORE"
    echo "          🎯 影響評估：$DRIFT_IMPACT_ASSESSMENT"
}

function generate_strategic_calibration_recommendations() {
    echo "        📋 生成戰略校準建議..."

    declare -g -a CALIBRATION_RECOMMENDATIONS=()

    # 基於漂移模式生成針對性建議
    if [[ $DRIFT_SEVERITY_SCORE -ge 3 ]]; then
        if [[ "${CREATIVE_DRIFT_FINDINGS[*]}" =~ "功能漂移" ]]; then
            CALIBRATION_RECOMMENDATIONS+=("🎯 Navigator下次規劃：明確功能邊界，避免AI團隊偏離核心MVP目標")
            CALIBRATION_RECOMMENDATIONS+=("📋 Task-Dispatcher改進：在任務分解時更明確功能優先級和戰略關聯性")
        fi

        if [[ "${CREATIVE_DRIFT_FINDINGS[*]}" =~ "決策漂移" ]]; then
            CALIBRATION_RECOMMENDATIONS+=("⚖️ merge-planner強化：增加戰略對齊度檢查，防止過度重構")
            CALIBRATION_RECOMMENDATIONS+=("🎯 Coder Agent指導：強調MVP期間的實用主義原則")
        fi

        if [[ "${CREATIVE_DRIFT_FINDINGS[*]}" =~ "複雜度漂移" ]]; then
            CALIBRATION_RECOMMENDATIONS+=("🔧 技術決策約束：MVP階段限制新技術模式的引入")
            CALIBRATION_RECOMMENDATIONS+=("📊 Code Review標準：增加複雜度評估指標")
        fi

        if [[ "${CREATIVE_DRIFT_FINDINGS[*]}" =~ "依賴漂移" ]]; then
            CALIBRATION_RECOMMENDATIONS+=("📦 依賴管理政策：MVP期間實施「零新依賴」原則")
            CALIBRATION_RECOMMENDATIONS+=("🔍 merge-planner檢查：將依賴變更納入BLOCKER評估")
        fi
    else
        CALIBRATION_RECOMMENDATIONS+=("✅ 當前AI團隊執行良好，建議保持現有開發節奏")
    fi

    # 生成前瞻性建議
    CALIBRATION_RECOMMENDATIONS+=("🔄 建立定期戰略對齊檢查點：每5個PR進行一次漂移評估")
    CALIBRATION_RECOMMENDATIONS+=("📚 更新AI團隊指導原則：基於此次漂移分析優化開發指南")

    echo "          📝 生成 ${#CALIBRATION_RECOMMENDATIONS[@]} 條校準建議"
}

function generate_strategic_anchor_report() {
    echo "        ⚓ 生成戰略錨定報告..."
    echo "          📍 此報告將成為Navigator下次規劃循環的強制輸入"

    # 生成詳細的戰略錨定報告
    STRATEGIC_ANCHOR_REPORT="# 🎯 戰略錨定報告 - Issue #$PARENT_ISSUE_NUM

## 📊 執行摘要
- **原始戰略意圖**: $INTENDED_VALUE_PROPOSITION
- **實際交付成果**: $ACTUAL_FUNCTIONALITY
- **戰略對齊度**: $(if [[ $DRIFT_SEVERITY_SCORE -le 1 ]]; then echo "高度對齊 ✅"; elif [[ $DRIFT_SEVERITY_SCORE -le 3 ]]; then echo "基本對齊 ⚠️"; else echo "需要校準 🚨"; fi)
- **漂移風險等級**: $DRIFT_IMPACT_ASSESSMENT

## 🔍 漂移分析結果
### 檢測到的漂移模式：
$(printf '- %s\n' \"${CREATIVE_DRIFT_FINDINGS[@]}\")

### 漂移嚴重程度評分：$DRIFT_SEVERITY_SCORE/10

## 📋 Navigator 下次規劃必讀指南

### 🎯 戰略校準建議
$(printf '- %s\n' \"${CALIBRATION_RECOMMENDATIONS[@]}\")

### 🔄 AI團隊行為模式觀察
- **技術決策偏好**: $TECHNICAL_DECISIONS
- **實施方法特徵**: $ACTUAL_FUNCTIONALITY
- **品質債務傾向**: ${#QUALITY_DEBT_ISSUES[@]} 個新問題

### ⚠️ 風險預警
$(if [[ $DRIFT_SEVERITY_SCORE -ge 3 ]]; then
echo \"- 🚨 **高風險警告**: AI團隊存在明顯的戰略偏離傾向
- 📋 **建議行動**: Navigator應在下次規劃時重新強調戰略約束
- 🎯 **重點關注**: 確保AI團隊理解MVP的戰略邊界\"
else
echo \"- ✅ **風險可控**: AI團隊基本按照戰略意圖執行
- 📈 **持續改進**: 建議維持當前開發模式並微調\"
fi)

## 📌 Navigator 行動檢查清單
- [ ] 審閱本次AI團隊的漂移模式，更新開發指導原則
- [ ] 基於漂移分析結果，調整下次任務的戰略描述精確度
- [ ] 檢查是否需要加強AI子代理之間的戰略對齊機制
- [ ] 評估當前MVP進度是否需要戰略調整

---
**生成時間**: $(date +'%Y-%m-%d %H:%M:%S')
**驗證範圍**: main分支 commit $CURRENT_MAIN_COMMIT
**下次使用**: Navigator規劃新一輪開發時必須參考此報告"

    # 將報告保存到指定文件
    local anchor_report_file="docs/04_AI_OPERATIONS/validator/strategic-anchor-reports/$(date +'%Y-%m-%d')_Issue-${PARENT_ISSUE_NUM}_strategic-anchor.md"
    mkdir -p "docs/04_AI_OPERATIONS/validator/strategic-anchor-reports"
    echo "$STRATEGIC_ANCHOR_REPORT" > "$anchor_report_file"

    echo "          💾 戰略錨定報告已保存: $anchor_report_file"
    echo "          🔗 Navigator下次規劃時必須讀取此報告"
}

function evaluate_strategic_results() {
    echo "  📊 評估戰略驗證結果..."

    local issue_count=${#STRATEGIC_ISSUES[@]}
    local quality_issue_count=${#QUALITY_DEBT_ISSUES[@]}
    local total_issues=$((issue_count + quality_issue_count))

    if [[ $total_issues -eq 0 && "$STRATEGIC_INTENT_SCORE" == "高" ]]; then
        STRATEGIC_VERIFICATION_PASSED=true
        echo "    ✅ 戰略驗證完全通過"
    elif [[ $total_issues -le 2 && "$STRATEGIC_INTENT_SCORE" != "低" ]]; then
        STRATEGIC_VERIFICATION_PASSED=true
        echo "    ⚠️  戰略驗證基本通過，有少量改進空間"
    else
        STRATEGIC_VERIFICATION_PASSED=false
        echo "    ⚠️  戰略驗證發現問題，需要關注"
    fi
}
```

---

## 📈 第三階段：綜合評估與報告 (Comprehensive Assessment & Reporting)

**核心理念**: 基於兩階段驗證結果，生成綜合評估報告，並根據結果執行相應的後續行動。

```bash
function calculate_final_verdict() {
    echo "⚖️ 計算總體裁決..."

    if [[ "$BEHAVIORAL_VERIFICATION_PASSED" == "true" && "$STRATEGIC_VERIFICATION_PASSED" == "true" ]]; then
        FINAL_VERDICT="✅ PASS"
        VERDICT_SUMMARY="系統行為正常且戰略目標完全達成"
    elif [[ "$BEHAVIORAL_VERIFICATION_PASSED" == "true" ]]; then
        FINAL_VERDICT="⚠️ BEHAVIORAL PASS"
        VERDICT_SUMMARY="系統功能正常，戰略執行有改進空間"
    else
        FINAL_VERDICT="❌ FAIL"
        VERDICT_SUMMARY="系統存在功能性問題，需要立即修復"
    fi

    echo "  🏆 總體裁決：$FINAL_VERDICT"
    echo "  📝 裁決摘要：$VERDICT_SUMMARY"
}

function generate_comprehensive_validation_report() {
    echo "📊 生成綜合驗證報告..."

    cat > "$VALIDATION_REPORT" <<EOF
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validator 後合併驗證報告 - Issue #$PARENT_ISSUE_NUM</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; max-width: 1400px; margin: 0 auto; padding: 20px; background: #f8fafc; }
        .header { text-align: center; background: linear-gradient(135deg, #1f2937, #374151, #4b5563); color: white; padding: 40px; border-radius: 16px; margin-bottom: 30px; box-shadow: 0 10px 25px rgba(31, 41, 55, 0.3); }
        .validator-badge { background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin-top: 10px; display: inline-block; }
        .section { background: white; margin: 20px 0; padding: 24px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); border: 1px solid #e5e7eb; }
        .phase-header { background: linear-gradient(135deg, #6366f1, #8b5cf6); color: white; padding: 20px; border-radius: 8px; margin: 16px 0; }
        .verdict-pass { background: linear-gradient(135deg, #f0fdf4, #dcfce7); border-left: 4px solid #16a34a; padding: 20px; border-radius: 8px; margin: 16px 0; }
        .verdict-warning { background: linear-gradient(135deg, #fefce8, #fef3c7); border-left: 4px solid #eab308; padding: 20px; border-radius: 8px; margin: 16px 0; }
        .verdict-fail { background: linear-gradient(135deg, #fef2f2, #fee2e2); border-left: 4px solid #dc2626; padding: 20px; border-radius: 8px; margin: 16px 0; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; }
        .metric-card { background: #fafafa; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #e5e7eb; }
        .metric-number { font-size: 32px; font-weight: bold; margin-bottom: 8px; }
        .metric-label { color: #6b7280; font-size: 14px; }
        .test-result { background: white; border: 1px solid #e5e7eb; padding: 12px; border-radius: 6px; margin: 8px 0; }
        .evidence-box { background: #f8fafc; border: 1px solid #d1d5db; padding: 14px; border-radius: 6px; margin: 8px 0; font-family: monospace; font-size: 12px; }
        .final-verdict { font-size: 48px; text-align: center; margin: 20px 0; padding: 30px; border-radius: 12px; }
        .pass { background: linear-gradient(135deg, #16a34a, #22c55e); color: white; }
        .warning { background: linear-gradient(135deg, #eab308, #facc15); color: white; }
        .fail { background: linear-gradient(135deg, #dc2626, #ef4444); color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Validator 後合併驗證報告</h1>
        <div class="validator-badge">🔬 兩階段驗證引擎</div>
        <p><strong>驗證目標</strong>: Issue #$PARENT_ISSUE_NUM - $PARENT_ISSUE_TITLE</p>
        <p><strong>驗證時間</strong>: $(date +'%Y-%m-%d %H:%M:%S')</p>
        <p><strong>驗證分支</strong>: main (commit: $CURRENT_MAIN_COMMIT)</p>
        <p><strong>驗證模式</strong>: $VALIDATION_MODE</p>
        <p><strong>觸發條件</strong>: 代碼成功合併到 main 分支後</p>
    </div>

    <!-- 總體裁決 -->
    <div class="final-verdict $([ "$FINAL_VERDICT" = "✅ PASS" ] && echo "pass" || ([ "$FINAL_VERDICT" = "⚠️ BEHAVIORAL PASS" ] && echo "warning" || echo "fail"))">
        <h2>🏆 總體裁決</h2>
        <div style="font-size: 64px; margin: 20px 0;">$FINAL_VERDICT</div>
        <p>$VERDICT_SUMMARY</p>
    </div>

    <!-- 驗證概覽 -->
    <div class="section">
        <h2>📊 兩階段驗證概覽</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-number" style="color: $([ "$BEHAVIORAL_VERIFICATION_PASSED" = "true" ] && echo "#16a34a" || echo "#dc2626");">${BEHAVIORAL_VERIFICATION_PASSED/true/PASS}</div>
                <div class="metric-label">行為驗證結果</div>
            </div>
            <div class="metric-card">
                <div class="metric-number" style="color: $([ "$STRATEGIC_VERIFICATION_PASSED" = "true" ] && echo "#16a34a" || echo "#dc2626");">${STRATEGIC_VERIFICATION_PASSED/true/PASS}</div>
                <div class="metric-label">戰略驗證結果</div>
            </div>
            <div class="metric-card">
                <div class="metric-number" style="color: #8b5cf6;">$STRATEGIC_INTENT_SCORE</div>
                <div class="metric-label">戰略意圖達成度</div>
            </div>
            <div class="metric-card">
                <div class="metric-number" style="color: #ea580c;">${#BEHAVIORAL_FAILURES[@]}</div>
                <div class="metric-label">行為問題數量</div>
            </div>
            <div class="metric-card">
                <div class="metric-number" style="color: $([ $DRIFT_SEVERITY_SCORE -le 1 ] && echo "#16a34a" || ([ $DRIFT_SEVERITY_SCORE -le 3 ] && echo "#eab308" || echo "#dc2626"));">$DRIFT_SEVERITY_SCORE</div>
                <div class="metric-label">漂移嚴重程度</div>
            </div>
            <div class="metric-card">
                <div class="metric-number" style="color: #6366f1;">${#CREATIVE_DRIFT_FINDINGS[@]}</div>
                <div class="metric-label">漂移指標數量</div>
            </div>
        </div>
    </div>

    <!-- 階段 1: 行為驗證 -->
    <div class="section">
        <div class="phase-header">
            <h2>🔬 階段 1: 行為驗證 (Behavioral Verification)</h2>
            <p>在最新 main 分支上執行整合測試和端到端測試</p>
        </div>

        <div class="$([ "$BEHAVIORAL_VERIFICATION_PASSED" = "true" ] && echo "verdict-pass" || echo "verdict-fail")">
            <h3>裁決：$([ "$BEHAVIORAL_VERIFICATION_PASSED" = "true" ] && echo "✅ PASS" || echo "❌ FAIL")</h3>
            <p>$([ "$BEHAVIORAL_VERIFICATION_PASSED" = "true" ] && echo "所有行為測試通過，系統在 main 分支上運行正常" || echo "發現 ${#BEHAVIORAL_FAILURES[@]} 個行為問題，系統存在功能性問題")</p>
        </div>

        $(if [[ ${#BEHAVIORAL_FAILURES[@]} -gt 0 ]]; then
            echo "<h4>發現的行為問題：</h4>"
            for failure in "${BEHAVIORAL_FAILURES[@]}"; do
                echo "<div class='test-result' style='border-left: 4px solid #dc2626;'>❌ $failure</div>"
            done
        else
            echo "<div class='test-result' style='border-left: 4px solid #16a34a;'>✅ 所有行為測試通過</div>"
        fi)
    </div>

    <!-- 階段 2: 戰略驗證 -->
    $(if [[ "$BEHAVIORAL_VERIFICATION_PASSED" == "true" ]]; then
        echo "<div class='section'>"
        echo "    <div class='phase-header'>"
        echo "        <h2>🏛️ 階段 2: 戰略驗證 + 漂移檢測 (Strategic Verification + Drift Detection)</h2>"
        echo "        <p>基於系統行為正常，深度審計戰略意圖達成度並檢測AI團隊創造性漂移</p>"
        echo "    </div>"
        echo "    "
        echo "    <div class=\"$([ "$STRATEGIC_VERIFICATION_PASSED" = "true" ] && echo "verdict-pass" || echo "verdict-warning")\">"
        echo "        <h3>裁決：$([ "$STRATEGIC_VERIFICATION_PASSED" = "true" ] && echo "✅ PASS" || echo "⚠️ NEEDS ATTENTION")</h3>"
        echo "        <p><strong>戰略類型</strong>：$STRATEGIC_INTENT_TYPE</p>"
        echo "        <p><strong>達成度</strong>：$STRATEGIC_INTENT_SCORE</p>"
        echo "    </div>"
        echo "    "
        echo "    <div class='evidence-box'>"
        echo "        <h4>戰略審計證據：</h4>"
        echo "        <pre>$STRATEGIC_EVIDENCE</pre>"
        echo "    </div>"
        echo "    "
        echo "    <div class='evidence-box'>"
        echo "        <h4>推理過程：</h4>"
        echo "        <pre>$STRATEGIC_REASONING</pre>"
        echo "    </div>"
        echo "    "
        echo "    <!-- 漂移檢測詳細結果 -->"
        echo "    <div style='background: #f1f5f9; border: 2px solid #3b82f6; padding: 20px; border-radius: 8px; margin: 16px 0;'>"
        echo "        <h4 style='color: #1e40af; margin-top: 0;'>🎯 AI團隊創造性漂移分析</h4>"
        echo "        <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin: 16px 0;'>"
        echo "            <div style='background: white; padding: 12px; border-radius: 6px;'>"
        echo "                <strong style='color: #1f2937;'>原始戰略意圖:</strong><br>"
        echo "                <span style='color: #6b7280; font-size: 14px;'>$INTENDED_VALUE_PROPOSITION</span>"
        echo "            </div>"
        echo "            <div style='background: white; padding: 12px; border-radius: 6px;'>"
        echo "                <strong style='color: #1f2937;'>實際交付成果:</strong><br>"
        echo "                <span style='color: #6b7280; font-size: 14px;'>$ACTUAL_FUNCTIONALITY</span>"
        echo "            </div>"
        echo "        </div>"
        echo "        <div style='background: white; padding: 12px; border-radius: 6px; margin: 12px 0;'>"
        echo "            <strong style='color: #1f2937;'>漂移嚴重程度評分:</strong> "
        echo "            <span style='background: $([ $DRIFT_SEVERITY_SCORE -le 1 ] && echo "#dcfce7" || ([ $DRIFT_SEVERITY_SCORE -le 3 ] && echo "#fef3c7" || echo "#fee2e2")); color: $([ $DRIFT_SEVERITY_SCORE -le 1 ] && echo "#16a34a" || ([ $DRIFT_SEVERITY_SCORE -le 3 ] && echo "#eab308" || echo "#dc2626")); padding: 6px 12px; border-radius: 20px; font-weight: bold; font-size: 16px;'>$DRIFT_SEVERITY_SCORE/10</span>"
        echo "            <br><small style='color: #6b7280;'>$DRIFT_IMPACT_ASSESSMENT</small>"
        echo "        </div>"
        if [[ \${#CREATIVE_DRIFT_FINDINGS[@]} -gt 0 ]]; then
            echo "        <div style='background: white; padding: 12px; border-radius: 6px; margin: 12px 0;'>"
            echo "            <h5 style='color: #dc2626; margin-top: 0;'>🔍 檢測到的漂移模式:</h5>"
            echo "            <ul style='margin: 8px 0; padding-left: 20px;'>"
            for finding in \"\${CREATIVE_DRIFT_FINDINGS[@]}\"; do
                echo "                <li style='color: \$([ \$DRIFT_SEVERITY_SCORE -ge 3 ] && echo \"#dc2626\" || echo \"#eab308\"); margin: 4px 0;'>\$finding</li>"
            done
            echo "            </ul>"
            echo "        </div>"
        else
            echo "        <div style='background: white; padding: 12px; border-radius: 6px; margin: 12px 0;'>"
            echo "            <p style='color: #16a34a; margin: 0;'>✅ 未檢測到顯著的戰略漂移</p>"
            echo "        </div>"
        fi
        if [[ \${#CALIBRATION_RECOMMENDATIONS[@]} -gt 0 ]]; then
            echo "        <div style='background: white; padding: 12px; border-radius: 6px; margin: 12px 0;'>"
            echo "            <h5 style='color: #7c3aed; margin-top: 0;'>📋 戰略校準建議:</h5>"
            echo "            <ul style='margin: 8px 0; padding-left: 20px;'>"
            for rec in \"\${CALIBRATION_RECOMMENDATIONS[@]}\"; do
                echo "                <li style='color: #374151; margin: 4px 0; font-size: 14px;'>\$rec</li>"
            done
            echo "            </ul>"
            echo "        </div>"
        fi
        echo "        <div style='background: #fef3c7; border: 1px solid #f59e0b; padding: 12px; border-radius: 6px; margin: 12px 0;'>"
        echo "            <h5 style='color: #92400e; margin-top: 0;'>⚓ 戰略錨定報告</h5>"
        echo "            <p style='color: #92400e; margin: 0; font-size: 14px;'>詳細的戰略校準建議已保存至獨立報告，Navigator下次規劃時必須參考。</p>"
        echo "            <p style='color: #92400e; margin: 8px 0 0 0; font-size: 12px;'><strong>報告位置:</strong> docs/04_AI_OPERATIONS/validator/strategic-anchor-reports/\$(date +'%Y-%m-%d')_Issue-\${PARENT_ISSUE_NUM}_strategic-anchor.md</p>"
        echo "        </div>"
        echo "    </div>"
        echo "    "
        if [[ ${#STRATEGIC_ISSUES[@]} -gt 0 ]]; then
            echo "    <h4>戰略執行問題：</h4>"
            for issue in "${STRATEGIC_ISSUES[@]}"; do
                echo "    <div class='test-result' style='border-left: 4px solid #eab308;'>⚠️ $issue</div>"
            done
        fi
        echo "</div>"
    else
        echo "<div class='section'>"
        echo "    <div class='phase-header' style='background: linear-gradient(135deg, #6b7280, #9ca3af);'>"
        echo "        <h2>🏛️ 階段 2: 戰略驗證 (跳過)</h2>"
        echo "        <p>由於行為驗證失敗，跳過戰略驗證階段</p>"
        echo "    </div>"
        echo "    <div class='verdict-fail'>"
        echo "        <h3>❌ 已跳過</h3>"
        echo "        <p>行為驗證未通過，必須先修復功能性問題</p>"
        echo "    </div>"
        echo "</div>"
    fi)

    <!-- DoD 概念驗證結果 -->
    $(if [[ "$BEHAVIORAL_VERIFICATION_PASSED" == "true" && ${#DOD_CONCEPTUAL_RESULTS[@]} -gt 0 ]]; then
        echo "<div class='section'>"
        echo "    <h2>✅ DoD 概念驗證結果</h2>"
        echo "    <p>基於行為測試通過的信心，進行概念層面的 DoD 審計</p>"
        echo "    <div class='metrics-grid'>"
        echo "        <div class='metric-card'>"
        echo "            <div class='metric-number' style='color: #16a34a;'>$DOD_CONCEPTUAL_PASS_COUNT</div>"
        echo "            <div class='metric-label'>概念驗證通過</div>"
        echo "        </div>"
        echo "        <div class='metric-card'>"
        echo "            <div class='metric-number' style='color: #eab308;'>$DOD_CONCEPTUAL_FAIL_COUNT</div>"
        echo "            <div class='metric-label'>需要關注</div>"
        echo "        </div>"
        echo "    </div>"
        echo "    "
        echo "    <h4>詳細概念驗證結果：</h4>"
        for result in "${DOD_CONCEPTUAL_RESULTS[@]}"; do
            local status=$(echo "$result" | cut -d'|' -f1)
            local item=$(echo "$result" | cut -d'|' -f2)
            local reasoning=$(echo "$result" | cut -d'|' -f3)
            echo "    <div class='test-result'>"
            echo "        <strong>$status $item</strong>"
            echo "        <div style='font-size: 12px; color: #6b7280; margin-top: 4px;'>$reasoning</div>"
            echo "    </div>"
        done
        echo "</div>"
    fi)

    <!-- 質量債務巡檢 -->
    $(if [[ "$BEHAVIORAL_VERIFICATION_PASSED" == "true" && ${#QUALITY_DEBT_ISSUES[@]} -gt 0 ]]; then
        echo "<div class='section'>"
        echo "    <h2>🔬 質量債務巡檢結果</h2>"
        echo "    <p>發現 ${#QUALITY_DEBT_ISSUES[@]} 個潛在的質量債務問題</p>"
        for issue in "${QUALITY_DEBT_ISSUES[@]}"; do
            echo "    <div class='test-result' style='border-left: 4px solid #eab308;'>⚠️ $issue</div>"
        done
        echo "</div>"
    fi)

    <!-- Validator 系統信息 -->
    <div class="section">
        <h2>🤖 Validator 系統信息</h2>
        <div class="evidence-box">
            <h4>執行環境：</h4>
            <pre>驗證時間: $(date +'%Y-%m-%d %H:%M:%S')
main 分支 commit: $CURRENT_MAIN_COMMIT
工作目錄: $(pwd)
Python 版本: $(python --version 2>&1 || echo "未安裝")
Node.js 版本: $(node --version 2>&1 || echo "未安裝")
驗證模式: $VALIDATION_MODE
觸發條件: 代碼成功合併到 main 分支後</pre>
        </div>

        <div class="evidence-box">
            <h4>兩階段驗證流程：</h4>
            <pre>1. 🔬 行為驗證 (Behavioral Verification)
   - 整合測試執行
   - 端到端測試執行
   - NovelWebsite 專案特定行為測試
   - 結果：$([ "$BEHAVIORAL_VERIFICATION_PASSED" = "true" ] && echo "通過" || echo "失敗")

2. 🏛️ 戰略驗證 (Strategic Verification)
   - DoD 概念驗證
   - 戰略意圖深度審計
   - 質量與債務巡檢
   - 結果：$([ "$BEHAVIORAL_VERIFICATION_PASSED" = "true" ] && echo "$([ "$STRATEGIC_VERIFICATION_PASSED" = "true" ] && echo "通過" || echo "部分通過")" || echo "已跳過")</pre>
        </div>

        <p style="text-align: center; color: #6b7280; font-style: italic; margin-top: 30px;">
            🛡️ Generated by Validator - 後合併交付品質驗證引擎<br>
            🔬 兩階段驗證：行為驗證 → 戰略驗證<br>
            ⚖️ 確保可交付的穩定狀態
        </p>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'base',
            themeVariables: {
                primaryColor: '#1f2937',
                primaryTextColor: '#f9fafb',
                primaryBorderColor: '#374151',
                lineColor: '#6b7280'
            }
        });
    </script>
</body>
</html>
EOF

    echo "✅ 綜合驗證報告已生成: $VALIDATION_REPORT"
}

function handle_validation_results() {
    echo "🔄 處理驗證結果..."

    case "$FINAL_VERDICT" in
        "✅ PASS")
            handle_full_pass_result
            ;;
        "⚠️ BEHAVIORAL PASS")
            handle_behavioral_pass_result
            ;;
        "❌ FAIL")
            handle_fail_result
            ;;
    esac
}

function handle_full_pass_result() {
    echo "  🎉 完全通過，確認可交付狀態..."

    local comment_body="🛡️ **Validator 後合併驗證報告**

✅ **總體裁決：FULL PASS**

此任務已通過 Validator 的兩階段後合併驗證：
- ✅ **行為驗證**：整合測試和端到端測試全部通過
- ✅ **戰略驗證**：戰略意圖達成度 $STRATEGIC_INTENT_SCORE，無重大問題

🎯 **確認可交付狀態**
系統在 main 分支上運行正常，戰略目標已達成，可進入交付/部署階段。

📊 **完整驗證報告**: [查看詳細報告]($VALIDATION_REPORT)

---
🤖 Generated by Validator | $(date +'%Y-%m-%d %H:%M:%S')"

    gh issue comment $PARENT_ISSUE_NUM --body "$comment_body"

    # 為 Issue 添加 validated-ready-for-delivery 標籤
    gh issue edit $PARENT_ISSUE_NUM --add-label "validated-ready-for-delivery"

    echo "  ✅ 確認可交付狀態，已添加交付就緒標籤"
}

function handle_behavioral_pass_result() {
    echo "  ⚠️  功能正常但戰略執行有改進空間..."

    local comment_body="🛡️ **Validator 後合併驗證報告**

⚠️ **總體裁決：BEHAVIORAL PASS**

此任務的功能性驗證通過，但戰略執行需要關注：
- ✅ **行為驗證**：整合測試和端到端測試全部通過
- ⚠️ **戰略驗證**：戰略意圖達成度 $STRATEGIC_INTENT_SCORE，有改進空間

🎯 **可交付但建議優化**
系統功能正常可以交付，但建議在下個迭代中改進戰略執行。

📊 **完整驗證報告**: [查看詳細報告]($VALIDATION_REPORT)

$(if [[ ${#STRATEGIC_ISSUES[@]} -gt 0 ]]; then
    echo "**主要改進建議**:"
    for issue in "${STRATEGIC_ISSUES[@]}"; do
        echo "- $issue"
    done
fi)

---
🤖 Generated by Validator | $(date +'%Y-%m-%d %H:%M:%S')"

    gh issue comment $PARENT_ISSUE_NUM --body "$comment_body"

    # 為 Issue 添加 validated-with-improvements 標籤
    gh issue edit $PARENT_ISSUE_NUM --add-label "validated-with-improvements"
}

function handle_fail_result() {
    echo "  ❌ 驗證失敗，系統存在功能性問題..."

    create_emergency_rollback_issue

    local comment_body="🛡️ **Validator 後合併驗證報告**

❌ **總體裁決：FAIL**

此任務的合併導致系統功能性問題：
- ❌ **行為驗證失敗**：發現 ${#BEHAVIORAL_FAILURES[@]} 個功能性問題
- ⏸️ **戰略驗證已跳過**：需先修復功能問題

🚨 **緊急狀態 - 需要立即處理**
系統在 main 分支上存在功能性問題，建議立即回滾或緊急修復。

📋 **緊急處理任務**: 已創建 Issue #$EMERGENCY_ISSUE

📊 **詳細失敗報告**: [查看完整報告]($VALIDATION_REPORT)

**主要功能性問題**:
$(for failure in "${BEHAVIORAL_FAILURES[@]}"; do
    echo "- $failure"
done)

---
🤖 Generated by Validator | $(date +'%Y-%m-%d %H:%M:%S')"

    gh issue comment $PARENT_ISSUE_NUM --body "$comment_body"

    # 為 Issue 添加 validation-failed-emergency 標籤並重新開啟
    gh issue edit $PARENT_ISSUE_NUM --add-label "validation-failed-emergency,urgent"
    if [[ "$ISSUE_STATE" == "CLOSED" ]]; then
        gh issue reopen $PARENT_ISSUE_NUM
    fi
}

function create_emergency_rollback_issue() {
    echo "    🚨 創建緊急處理任務..."

    local emergency_body="## 🚨 Validator 緊急處理任務

此 Issue 由 Validator 自動創建，用於處理後合併驗證中發現的功能性問題。

### 📋 問題概述
- **父任務**: #$PARENT_ISSUE_NUM - $PARENT_ISSUE_TITLE
- **驗證時間**: $(date +'%Y-%m-%d %H:%M:%S')
- **問題 commit**: $CURRENT_MAIN_COMMIT
- **驗證結果**: ❌ 行為驗證失敗

### 🚨 發現的功能性問題

$(for failure in "${BEHAVIORAL_FAILURES[@]}"; do
    echo "#### ❌ $failure"
    echo ""
done)

### 🔧 緊急處理選項

#### 選項 1: 立即回滾 (推薦)
\`\`\`bash
# 回滾到上一個穩定 commit
git revert $CURRENT_MAIN_COMMIT
git push origin main
\`\`\`

#### 選項 2: 緊急修復
- [ ] 分析並修復每個功能性問題
- [ ] 確保所有測試通過
- [ ] 重新運行 Validator 驗證：\`/validator.md $PARENT_ISSUE_NUM\`

### 📊 完整驗證報告

詳細的驗證失敗報告可在此查看：[Validation Report]($VALIDATION_REPORT)

### ✅ 完成標準

- [ ] 功能性問題全部解決
- [ ] 整合測試和 E2E 測試通過
- [ ] Validator 重新驗證通過

---
🚨 **自動創建**: 由 Validator 緊急檢測系統生成
🔗 **父任務**: #$PARENT_ISSUE_NUM
🛡️ **目標**: 恢復 main 分支穩定狀態"

    local emergency_title="[EMERGENCY] 修復 Issue #$PARENT_ISSUE_NUM 合併後的功能性問題"

    gh issue create \
        --title "$emergency_title" \
        --body "$emergency_body" \
        --label "emergency,validation-failure,rollback-candidate,urgent" \
        --assignee "@me"

    EMERGENCY_ISSUE=$(gh issue list --limit 1 --json number --jq '.[0].number')
    echo "    ✅ 緊急處理任務已創建: Issue #$EMERGENCY_ISSUE"
}

function cleanup_validation_environment() {
    echo "🧹 清理驗證環境..."

    # 如果之前暫存了變更，恢復它們
    if [[ "$STASHED_CHANGES" == "true" ]]; then
        git stash pop
        echo "  ✅ 已恢復暫存的變更"
    fi

    echo "  ✅ 驗證環境清理完成"
}

# 主執行流程
main() {
    echo "🛡️ Validator 啟動 - 後合併兩階段驗證開始"

    # 第一階段：環境準備與上下文重建
    extract_parent_dod
    trace_related_work_items
    prepare_main_branch_environment

    # 第二階段：兩階段驗證執行

    # 階段 1: 行為驗證（必須先通過）
    if [[ "$VALIDATION_MODE" == "full" || "$VALIDATION_MODE" == "behavioral-only" ]]; then
        if ! execute_behavioral_verification; then
            echo "❌ 行為驗證失敗，生成失敗報告並退出"
            calculate_final_verdict
            generate_comprehensive_validation_report
            handle_validation_results
            cleanup_validation_environment
            return 1
        fi
    else
        echo "⏸️ 跳過行為驗證（僅戰略模式）"
        BEHAVIORAL_VERIFICATION_PASSED=true
    fi

    # 階段 2: 戰略驗證（基於行為驗證通過）
    if [[ "$VALIDATION_MODE" == "full" || "$VALIDATION_MODE" == "strategic-only" ]]; then
        execute_strategic_verification
    fi

    # 第三階段：綜合評估與報告
    calculate_final_verdict
    generate_comprehensive_validation_report
    handle_validation_results

    # 清理環境
    cleanup_validation_environment

    echo "🏆 Validator 後合併驗證完成"
    echo "📊 總體裁決：$FINAL_VERDICT"
    echo "📋 詳細報告：$VALIDATION_REPORT"
}

# 執行主流程
main
