---
allowed-tools: <PERSON><PERSON>(gh: *), <PERSON><PERSON>(git: *), Read, Edit, View, ToolCall, Write # 確保 Claude 能夠使用 GitHub CLI (gh)、Git 命令、讀取/編輯檔案以及通用工具調用
description: NovelWebsite專案PR全面分析，包含CI結果與審核意見，並生成修復行動方案和HTML報告。
---

## NovelWebsite PR 全面分析、意圖識別與策略建議

### 任務說明
作為一個智能代理，請對提供的 NovelWebsite 專案 GitHub Pull Request (PR) 進行全面分析。**核心突破**：在任何技術分析前，必須先理解 PR 的戰略意圖，區分「戰術修復」與「戰略必要性」，避免用成本衡量無限價值的技術升級。

#### 0. PR 意圖分類與風險定性 (PR Intent Classification & Risk Profiling)
**指令：**
在進行任何操作前，你的首要任務是識別此 PR 的核心**戰略意圖**，避免用「成本」衡量「無限價值」的戰略升級。

1.  **收集意圖信號 (Gather Intent Signals):**
    *   **標題與標籤分析：** 使用 `gh pr view $ARGUMENTS --json title,labels` 檢視關鍵詞：
        *   `refactor`, `upgrade`, `migration`, `🔧`, `🚀` → 重構信號
        *   `feat`, `feature`, `implement`, `✨` → 功能開發信號
        *   `fix`, `bug`, `🐛`, `hotfix` → 錯誤修復信號
        *   `chore`, `deps`, `dependency`, `📦` → 維護信號
    *   **描述內容分析：** 閱讀 PR 描述，尋找戰略性陳述：
        *   「為了升級框架」、「技術債清理」、「架構重構」→ 核心重構
        *   「5分鐘用戶旅程」、「新增搜索功能」、「用戶體驗改善」→ 功能開發
        *   「修復CI失敗」、「安全漏洞修復」、「性能問題」→ 錯誤修復
    *   **程式碼變更性質：** 使用 `gh pr diff $ARGUMENTS --name-only` 分析檔案類型：
        *   大量 `package.json`, `requirements*.txt`, `docker-compose*.yml` → 框架升級
        *   `config/` 目錄大幅變動 → 架構重構
        *   新增 `frontend/src/pages/*.tsx`, `backend/apps/*/views.py` → 功能開發

2.  **進行意圖分類 (Assign Intent Category):**
    *   **A. 核心重構 (Core Refactor)** - 戰略價值無限大：
        *   Django/React/Next.js 框架升級
        *   CI/CD 流程重構
        *   Docker 架構調整
        *   資料庫遷移和模型重構
        *   **特徵**：非做不可的變更，長期效益 > 短期成本
    *   **B. 功能開發 (Feature Development)** - 戰術價值：
        *   搜索功能實現
        *   用戶認證系統
        *   小說閱讀器改進
        *   響應式設計優化
    *   **C. 錯誤修復 (Bug Fix)** - 戰術價值：
        *   CI 測試失敗修復
        *   性能優化
        *   安全性修復
        *   部署問題解決

3.  **進行深度集成風險評估 (In-depth Integration Risk Assessment):**
    *   **任務**：模擬資深架構師，通過三個維度的直接證據評估真實集成風險

    **維度1：直接合併衝突檢測 (Score: 0 or 50)**
    ```bash
    # 合併預演 - 檢測直接衝突
    git fetch origin $baseRefName
    git checkout $headRefName
    git merge --no-commit --no-ff origin/$baseRefName
    # 成功=0分，失敗=50分(一票否決)
    git merge --abort  # 清理狀態
    ```

    **維度2：NovelWebsite核心檔案變更分析 (Score: 0-30)**
    *   **核心檔案清單**：
        *   **CI/CD核心** (20分): `.github/workflows/*`, `Makefile`, `scripts/*`
        *   **後端核心** (15分): `config/django_settings.py`, `config/urls.py`, `requirements*.txt`, `backend/apps/catalog/*`
        *   **前端核心** (15分): `package.json`, `pnpm-lock.yaml`, `frontend/package.json`, `tsconfig.json`
        *   **Docker基礎設施** (15分): `infra/docker/*`, `docker-compose*.yml`, `infra/aws-ecr/*`
        *   **配置檔案** (10分): `.env*`, `backend/config/*`, `.pre-commit-config.yaml`, `lighthouserc.js`
    *   **評分邏輯**: 檢查 `gh pr diff $ARGUMENTS --name-only` 觸及的核心檔案
    *   **最高30分封頂**

    **維度3：演進路徑重疊分析 (Score: 0-20)**
    ```bash
    # 計算檔案重疊度
    PR_FILES=$(gh pr diff $ARGUMENTS --name-only)
    MERGE_BASE=$(git merge-base origin/$baseRefName $headRefName)
    MAIN_CHANGES=$(git diff --name-only $MERGE_BASE...origin/$baseRefName)
    OVERLAP_COUNT=$(comm -12 <(echo "$PR_FILES" | sort) <(echo "$MAIN_CHANGES" | sort) | wc -l)
    # 重疊分數 = OVERLAP_COUNT * 2 (最高20分)
    ```

    **綜合風險評定:**
    *   **總風險分 = 衝突分數(0-50) + 核心分數(0-30) + 重疊分數(0-20)**
    *   **風險等級分類**：
        *   **低風險 (0-15)**：幾乎無衝突，未觸及核心
        *   **中風險 (16-35)**：部分核心變更或路徑交集
        *   **高風險 (36-60)**：觸及關鍵核心且有重疊
        *   **極高風險 (>60)**：存在直接衝突

4.  **輸出精準診斷:**
    *   「診斷：[意圖分類] - [戰略價值] | 集成風險：**[分數:等級]** (衝突:X + 核心:Y + 重疊:Z)」

#### 1. 策略決策門 (Strategic Decision Gate)
**指令：**
**根據步驟 0 的「意圖分類」**，執行以下戰略決策邏輯：

### **分支A：核心重構路徑**
*   **IF (`意圖` == `A. 核心重構`) THEN:**
    *   **決策原則**：**「放棄並重建」選項被禁用**
    *   **核心認知**：此類 PR 具備戰略價值無限大，必須被合併
    *   **任務重新定義**：從「是否修復」轉為「**如何安全地合併**」
    *   **跳過成本效益分析**，直接進入「**分階段降風險策略**」規劃：
        *   **PR 拆分建議**：能否將大型重構拆分為更小、可獨立驗證的 PR？
        *   **功能開關策略**：是否可用 feature flag 將新舊架構隔離，確保安全回滾？
        *   **詳細測試計畫**：除 CI 外，還需哪些手動測試驗證重構？
        *   **分階段合併計畫**：包含溝通、監控和回滾預案的詳細流程

### **分支B：戰術價值路徑**
*   **IF (`意圖` == `B. 功能開發` OR `C. 錯誤修復`) THEN:**
    *   **決策原則**：**啟用「修復 vs. 重建」成本效益分析**
    *   **評估修復成本：**
        *   **低** - 簡單路徑修正、小衝突
        *   **中** - 部分邏輯重寫、中等架構調整
        *   **高** - 大規模衝突、多重核心邏輯修改
    *   **重建成本基準**：恆定為「低」(~30分鐘)
    *   **決策邏輯：**
        ```
        IF (修復成本 == 高 AND 集成風險分數 >= 36)
        OR (修復成本 == 中 AND 集成風險分數 >= 61)
        OR (存在直接衝突 AND 修復成本 >= 中)
        THEN 推薦「放棄並重建」
        ELSE 推薦「繼續修復」
        ```

#### 2. 情境理解 (Contextual Understanding)
**指令：**
請閱讀 PR 號碼為 **$ARGUMENTS** 的描述和相關的程式碼變更（$ARGUMENTS 將作為 PR 號碼提供）。**啟用 ultrathink 模式**進行深度分析。
*   **獲取 PR 詳細資訊：** 使用 `gh pr view $ARGUMENTS --json` 來獲取 PR 的描述、標題和審核意見。
*   **獲取程式碼差異：** 使用 `gh pr diff $ARGUMENTS` 來獲取此 PR 的完整程式碼差異。
*   **目標識別：** **基於策略決策門的結果**深入理解此 PR 的主要目的：是修復一個 bug、開發一個新功能，還是進行重構？分析其技術複雜度和潛在影響範圍。

#### 3. CI 結果分析 (CI Result Analysis) - 技術修復重點
**指令：**
*   **檢查 CI 狀態：** 透過 GitHub CLI (`gh`) 檢查此 PR 的所有 CI 檢查結果。
*   **失敗定位：** 如果有任何 CI 檢查失敗，**立刻定位並提取相關的錯誤日誌**。
*   **[調用子代理進行深入 CI 分析]**：
    *   **子代理角色：** "你是一位資深軟體除錯專家。**啟用 ultrathink 模式**進行深度技術分析。"
    *   **子代理任務：** "你的唯一任務是分析以下 CI 錯誤日誌和相關的程式碼變更。**使用 ultrathink 模式**深入探討失敗的根本原因，考慮多種可能的成因，並提出 1-2 個具體且經過深思熟慮的修復方案。以簡潔的 Markdown 格式回報你的發現。"
    *   **子代理輸入：** [失敗檢查的名稱]、[完整的錯誤日誌]、[程式碼差異]
*   **主代理整合：** 接收子代理的分析結果，並將其整合到最終的行動方案中。

#### 4. 審核意見分類與關聯性分析 (Comment Categorization & Correlation Analysis)
**指令：**
*   **分析並分類所有審核意見：** **啟用 ultrathink 模式**，對於每一個審核意見，根據其性質將其分類為以下之一：
    *   `邏輯錯誤 (Logic Flaw)`：程式碼的根本性功能性問題。
    *   `程式碼風格 (Style Nitpick)`：格式、命名或其他非功能性問題。
    *   `提問與建議 (Question/Suggestion)`：需要開發者進一步澄清或可以考慮的優化。
    *   `安全性疑慮 (Security Concern)`：潛在的安全漏洞。
    *   `架構建議 (Architecture Suggestion)`：涉及重構、設計模式或架構改進的建議。
*   **建立關聯：**
    *   將 CI 的失敗日誌與其在程式碼變更中的**特定關聯部分**建立起來。
    *   同時，將每一個審核意見與其**對應的程式碼片段**關聯起來。

#### 5. 生成解決方案 (Solution Generation)
**指令：**
*   **策略性思考：** **啟用 ultrathink 模式**，綜合上述所有分析（PR 內容、CI 結果、審核意見、關聯性分析）來制定一個**詳細且可行的綜合修改計畫**。ultrathink 模式將提供額外的計算時間來徹底評估替代方案和複雜的技術決策。
*   **複雜問題子代理處理：** 對於被分類為 `邏輯錯誤` 或 `架構建議` 的意見，**[調用架構分析子代理]**：
    *   **子代理角色：** "你是一位經驗豐富的軟體架構師。**啟用 ultrathink 模式**進行深度架構分析。"
    *   **子代理任務：** "針對以下的審核意見和相關程式碼，**使用 ultrathink 模式**你的任務是提出至少兩種不同的修復或重構思路。深入考慮技術債務、可維護性、性能影響和擴展性。請以表格形式比較這兩種思路的優點、缺點和潛在影響，並給出你最推薦的方案及其詳細理由。"
    *   **子代理輸入：** [相關的審核意見]、[對應的程式碼片段]、[PR 整體脈絡]
*   **優先級判斷：** 擬定行動方案時，請**嚴格遵循以下優先級順序**：
    1.  **CI 失敗 (Blocking Issues)**
    2.  **安全性疑慮 (Security Concerns)**
    3.  **邏輯錯誤 (Logic Flaws)**
    4.  **架構建議 (Architecture Suggestions)**
    5.  **功能建議 (Feature Suggestions)**
    6.  **程式碼風格 (Code Style)**
*   **提供行動方案：** 在實際修改任何程式碼之前，**請先提供完整的修復行動方案**，並等待確認。

### 期望輸出格式

請將分析結果以清晰、可操作的 Markdown 格式呈現。**重要**：輸出格式根據意圖分類和策略決策門的結果而不同。

## 格式 A：核心重構 - 分階段降風險策略

#### 🎯 意圖識別：核心重構 (戰略價值無限大)

#### 📊 診斷結果
*   **PR 意圖**：[A. 核心重構] - [具體描述：框架升級/架構調整/技術債清理]
*   **戰略價值**：[說明長期效益和必要性]
*   **集成風險**：**[分數:等級]** (衝突:X + 核心:Y + 重疊:Z)
*   **風險分析**：[詳細說明風險來源和關鍵變更檔案]

#### 🚀 分階段降風險策略 (必須合併路徑)

##### **階段 1：PR拆分建議**
*   **拆分方案**：[是否可拆分為更小的獨立PR]
*   **建議順序**：[依賴升級 → 配置調整 → 邏輯遷移]
*   **獨立驗證點**：[每個階段的驗證標準]

##### **階段 2：功能開關策略**
*   **隔離方案**：[是否可用feature flag隔離新舊架構]
*   **回滾預案**：[緊急回滾的具體步驟]
*   **監控指標**：[需要監控的關鍵指標]

##### **階段 3：詳細測試計畫**
*   **CI擴充**：[除現有CI外的額外測試需求]
*   **手動測試**：[關鍵路徑的手動驗證清單]
*   **性能基準**：[升級前後的性能對比計畫]

##### **階段 4：分階段合併流程**
*   **合併時機**：[建議的合併時間窗口]
*   **溝通計畫**：[團隊通知和協調方案]
*   **監控計畫**：[合併後的48小時監控方案]

---

## 格式 B：戰術價值 - 建議「放棄並重建」

#### 🚨 戰略決策：建議放棄並重建

#### 📊 診斷結果
*   **PR 意圖**：[B. 功能開發 / C. 錯誤修復] - [具體功能/問題描述]
*   **修復成本**：[高/中] + **集成風險**：**[分數:等級]** (衝突:X + 核心:Y + 重疊:Z)
*   **成本效益分析**：修復成本 >> 重建成本(30分鐘)

#### 🔄 重建計畫
1. **記錄核心需求**：[從PR描述提取的業務需求]
2. **創建新分支**：`git checkout -b feature/[功能名]-v2`
3. **重新實現指導**：[基於最新main的實現步驟]
4. **關閉舊PR**：標註原因並提供新分支鏈接

---

## 格式 C：戰術價值 - 建議「繼續修復」

#### 🎯 整體摘要 (Overall Summary)
*   **一句話總結 PR 的當前狀態**。
*   **精準風險評估**：**[分數:等級]** (衝突:X + 核心:Y + 重疊:Z)
    例如：「此 PR 因 2 個測試失敗而阻塞，集成風險：**18:中風險** (衝突:0 + 核心:15 + 重疊:3)，值得修復。」

#### 🚨 高優先級待辦事項 (High-Priority To-Do)
*   **CI 問題：**
    *   明確指出哪個測試失敗，例如：「`test_authentication_flow` 測試失敗，錯誤日誌顯示：`AssertionError: User not logged in`。」
    *   根據日誌提供**可能的修復方向**，例如：「檢查 `authentication_middleware.py` 中用戶會話驗證邏輯，可能存在未正確處理空會話的情況。」
*   **重大問題：**
    *   列出所有被歸類為「邏輯錯誤」、「安全性疑慮」或「架構建議」的意見及 AI 建議的修改方案。
    *   格式：`[問題類型] - 審核意見原文`
    *   `建議方案：AI 擬定的詳細修改策略或程式碼範例`

#### ✅ 建議修改清單 (Suggested Modification Checklist)
*   以 Markdown 格式的清單呈現。
*   每一項都包含：
    *   `[位置] (file.py: line 55)`
    *   `[問題] (引用審核者的意見)`
    *   `[建議方案] (AI 擬定的修改策略或程式碼範例)`
    *   `[優先級] (P0-P2)`

#### ❓ 待澄清問題 (Questions to Clarify)
*   列出那些被歸類為「提問與建議 (Question/Suggestion)」的意見，提醒開發者需要先回覆這些問題以推進修復。

#### 📋 執行步驟建議 (Recommended Execution Steps)
**基於基準線風險評估的修復策略**：
1. **立即處理 CI 失敗**
2. **解決安全性問題**
3. **修復邏輯錯誤**
4. **考慮架構改進**
5. **優化程式碼風格**

#### 🔄 風險監控
**基於量化指標的持續監控**：
- **衝突檢測**：定期執行合併預演，監控直接衝突
- **核心檔案變更**：追蹤CI/CD、依賴、配置檔案的變更頻率
- **演進路徑分析**：監控與主幹的檔案重疊度變化
- **整體風險趨勢**：追蹤風險分數的變化軌跡

## 💾 HTML報告生成

**完成分析後，立即生成HTML格式報告：**

1. **HTML報告內容**：將上述完整分析結果轉換為專業HTML文檔
2. **文件命名**：`YYYY-MM-DD_PR{$ARGUMENTS}修復分析報告.html`
3. **保存路徑**：`./docs/pr-fix-plan/`
4. **技術要素**：
   - 響應式設計，支援桌面和移動設備
   - Mermaid圖表視覺化風險評估和執行流程
   - 互動式風險分數展示
   - 專業CSS樣式設計

```html
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovelWebsite PR{$ARGUMENTS} 修復分析報告 - {當前日期}</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        /* NovelWebsite專案專用樣式 */
        :root {
            --primary-color: #2563eb;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --bg-color: #f8fafc;
        }
        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-color);
            line-height: 1.6;
        }
        .risk-score {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 8px 0;
        }
        .risk-low { background: #dcfce7; color: var(--success-color); }
        .risk-medium { background: #fef3c7; color: var(--warning-color); }
        .risk-high { background: #fee2e2; color: var(--danger-color); }
        .risk-extreme { background: #fecaca; color: var(--danger-color); border: 2px solid var(--danger-color); }

        .intent-core { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; }
        .intent-feature { background: linear-gradient(135deg, #059669, #047857); color: white; }
        .intent-bug { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; }

        .mermaid { margin: 20px 0; }
        .checklist { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <!-- 動態插入分析結果內容 -->
    <!-- Mermaid風險評估圖表 -->
    <div class="mermaid">
        graph TB
            A[PR分析] --> B{意圖分類}
            B -->|核心重構| C[戰略價值無限大]
            B -->|功能開發| D[戰術價值評估]
            B -->|錯誤修復| E[戰術價值評估]

            C --> F[分階段降風險策略]
            D --> G{成本效益分析}
            E --> G

            G -->|高成本高風險| H[建議重建]
            G -->|可控成本風險| I[繼續修復]
    </div>

    <!-- 風險分數視覺化 -->
    <div class="mermaid">
        pie title 集成風險分析
            "直接衝突" : {衝突分數}
            "核心檔案" : {核心分數}
            "路徑重疊" : {重疊分數}
    </div>

    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
```

### 使用範例
您只需要在您的終端機中，導航到 NovelWebsite 專案目錄，然後輸入以下指令：
```bash
/project:pr-fix-plan 104
```
注意：此指令將會讀取專案的 `.claude/commands/pr-fix-plan.md` 檔案，並將 104 作為 PR 號碼傳遞給 Claude Code 進行處理，最後生成完整的HTML報告。

### NovelWebsite專案戰略優勢

#### 🧠 **核心突破：「意圖 vs 成本」智慧**
專為NovelWebsite專案定制：
- **框架升級**：Django/React/Next.js 升級自動識別為核心重構
- **搜索功能**：5分鐘用戶旅程相關功能優先處理
- **CI重構**：Tier 2架構調整視為戰略必要性

#### 🎯 **專案定制化風險評估**
- **後端風險**：Django配置、Catalog API、爬蟲引擎變更
- **前端風險**：React組件、搜索功能、響應式設計變更
- **基礎設施風險**：Docker、CI/CD、AWS部署配置變更

**這個版本完全針對NovelWebsite專案的技術棧和業務邏輯進行了定制化，確保最精準的分析和建議！**
