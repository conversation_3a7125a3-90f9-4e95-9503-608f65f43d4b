# .actrc - Act Configuration File for NovelWebsite Project
# =======================================================
# This file configures the default flags for act.
# It helps maintain consistency for local CI runs across different machines.

# -----------------------------------------------------------------------------
# RUNNER IMAGE CONFIGURATION
# -----------------------------------------------------------------------------
# Map the 'ubuntu-latest' and specific version runner labels to stable images
-P ubuntu-latest=catthehacker/ubuntu:act-22.04
-P ubuntu-22.04=catthehacker/ubuntu:act-22.04
-P ubuntu-20.04=catthehacker/ubuntu:act-20.04

# -----------------------------------------------------------------------------
# CONTAINER CONFIGURATION
# -----------------------------------------------------------------------------
# Enable container reuse for faster consecutive runs
--reuse

# Specify the container architecture for better cross-platform compatibility
--container-architecture=linux/amd64

# Use host network for service connectivity
--use-gitignore

# -----------------------------------------------------------------------------
# ENVIRONMENT & SECURITY
# -----------------------------------------------------------------------------
# Load environment variables from .env file
--env-file=.env

# Load secrets from .secrets file (if it exists)
--secret-file=.secrets

# -----------------------------------------------------------------------------
# OUTPUT & LOGGING
# -----------------------------------------------------------------------------
# Enable verbose output for debugging
--verbose

# -----------------------------------------------------------------------------
# RESOURCE LIMITS
# -----------------------------------------------------------------------------
# Set reasonable defaults for resource usage
--memory=2g
