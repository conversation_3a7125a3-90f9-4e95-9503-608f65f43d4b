#!/bin/bash

echo "🔍 尋找 GitHub Actions Runner 實例..."

# 方法 1: 根據標籤找 GitHub Runner
aws ec2 describe-instances \
  --filters "Name=tag:Name,Values=*github*runner*" "Name=instance-state-name,Values=running" \
  --query 'Reservations[].Instances[].{InstanceId:InstanceId,Name:Tags[?Key==`Name`].Value|[0],PublicIP:PublicIpAddress,PrivateIP:PrivateIpAddress,State:State.Name}' \
  --output table

echo ""
echo "如果沒有結果，嘗試其他標籤："

# 方法 2: 根據其他可能的標籤
aws ec2 describe-instances \
  --filters "Name=tag:Role,Values=*runner*" "Name=instance-state-name,Values=running" \
  --query 'Reservations[].Instances[].{InstanceId:InstanceId,Role:Tags[?Key==`Role`].Value|[0],PublicIP:PublicIpAddress,PrivateIP:PrivateIpAddress}' \
  --output table

echo ""
echo "📝 使用方式："
echo "ssh -i ~/.ssh/novel-bastion-key-v2.pem ec2-user@<PUBLIC_IP>"
