# 通用雜物
.DS_Store
.vscode/
node_modules/
.pnpm-store/
.venv/
venv/
__pycache__/
*.pyc
*.pyo

# 測試與 Cache
.pytest_cache/
coverage/
logs/
uploads/

# Git 與 CI/CD 不需進 image
.git/
.github/
.gitignore
*.log

# Docker 檔案自排除
docker-compose*.yml
Dockerfile*
infra/docker/legacy/
archive/

# 本地開發 secrets
.env
*.env

# 前端構建產物 (應在 Dockerfile 中按需構建)
apps/web-next/.next/
apps/web-next/out/
apps/web-next/build/
apps/web-sunset/build/
apps/web-sunset/.next/

# 排除整個 frontend/ 目錄 (防止 sunset 代碼意外包含)
frontend/

# 明確排除舊路徑，避免誤複製進鏡像
apps/web/

# 測試相關目錄
apps/*/coverage/
apps/*/__tests__/
apps/*/tests/
**/storybook-static/

# Monorepo 快取和臨時檔案
.turbo/
*.tsbuildinfo
.eslintcache

# IDE 和編輯器檔案
.idea/
*.swp
*.swo
.vscode/settings.json

# 大型資料檔案
*.csv
*.json.backup
*.sql
*.dump

# 本地開發工具
.act/
.secrets
